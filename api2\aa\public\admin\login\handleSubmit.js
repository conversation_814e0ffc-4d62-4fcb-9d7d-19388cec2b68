﻿// 刷新验证码
function refreshCaptcha() {
    fetch("/captcha_text.php?" + new Date().getTime())
        .then(response => response.text())
        .then(code => {
            document.getElementById("captcha-text").textContent = code;
        })
        .catch(error => {
            console.error("验证码加载失败:", error);
            document.getElementById("captcha-text").textContent = "加载失败";
        });
}

// 显示提示信息
function showAlert(message, type) {
    const alertContainer = document.getElementById("alert-container");
    if (!alertContainer) {
        console.error("找不到 alert-container 元素");
        return;
    }
    
    // 创建提示框
    const alertDiv = document.createElement("div");
    alertDiv.className = "alert alert-" + type;
    alertDiv.innerHTML = message;
    
    // 清空容器
    alertContainer.innerHTML = "";
    
    // 添加提示框
    alertContainer.appendChild(alertDiv);
    
    // 显示容器
    alertContainer.style.display = "block";
    
    // 3秒后自动隐藏
    setTimeout(function() {
        alertContainer.style.display = "none";
    }, 3000);
}

// 表单提交处理函数
function handleSubmit(e) {
    e.preventDefault(); // 阻止默认提交
    
    const username = document.getElementById("username").value;
    const password = document.getElementById("password").value;
    const captcha = document.getElementById("captcha").value;
    
    // 简单验证
    let isValid = true;
    
    if (!username) {
        document.getElementById("username-error").style.display = "block";
        document.getElementById("username").style.borderColor = "#dc3545";
        isValid = false;
    } else {
        document.getElementById("username-error").style.display = "none";
        document.getElementById("username").style.borderColor = "#ddd";
    }
    
    if (!password) {
        document.getElementById("password-error").style.display = "block";
        document.getElementById("password").style.borderColor = "#dc3545";
        isValid = false;
    } else {
        document.getElementById("password-error").style.display = "none";
        document.getElementById("password").style.borderColor = "#ddd";
    }
    
    if (!captcha) {
        document.getElementById("captcha-error").style.display = "block";
        document.getElementById("captcha").style.borderColor = "#dc3545";
        isValid = false;
    } else {
        document.getElementById("captcha-error").style.display = "none";
        document.getElementById("captcha").style.borderColor = "#ddd";
    }
    
    if (!isValid) {
        return false; // 验证失败，阻止提交
    }
    
    // 添加提交音效
    try {
        const audio = new Audio("data:audio/mpeg;base64,SUQzBAAAAAABEVRYWFgAAAAtAAADY29tbWVudABCaWdTb3VuZEJhbmsuY29tIC8gTGFzb25pY1N0dWRpb3MuY29tAFRYWFgAAAAhAAADZW5naW5lZXIAQmlnU291bmRCYW5rLmNvbQBUWFhYAAAAGwAAA3NvZnR3YXJlAExhdmY1OC43Ni4xMDAAAFRDT04AAAAbAAADZW5jb2RlZCBieQBMYXZmNTguNzYuMTAwAAAAAAAAAAAAAAD/+1AAAAA8YXVkaW8vbXBlZwAAAAAAAAAAAABUSVQyAAAAGQAAAzIwMjMtMDUtMDZUMTc6MjM6MTgAAAAAAAAAAAAA//tQxAADwAABpAAAACAAADSAAAAETEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//tQxBYDwAABpAAAACAAADSAAAAEVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//tQxBcAAAANIAAAAAA");
        audio.volume = 0.3;
        audio.play();
    } catch (e) {
        console.log("音频播放失败", e);
    }
    
    // 显示加载提示
    showAlert("正在登录，请稍候...", "success");
    
    // 使用 fetch API 代替 XMLHttpRequest
    fetch("login.php", {
        method: "POST",
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            "X-Requested-With": "XMLHttpRequest"
        },
        body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}&captcha=${encodeURIComponent(captcha)}`
    })
    .then(response => {
        // 检查响应状态
        if (response.redirected) {
            // 如果是重定向响应，直接跳转
            window.location.href = response.url;
            return null;
        }
        
        // 尝试解析 JSON 响应
        return response.text().then(text => {
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error("服务器响应解析错误:", e, text);
                // 如果响应不是 JSON 格式，直接提交表单
                document.getElementById("login-form").action = "login.php";
                document.getElementById("login-form").method = "post";
                document.getElementById("login-form").submit();
                return null;
            }
        });
    })
    .then(data => {
        if (data === null) {
            return; // 已经处理过重定向或提交表单
        }
        
        if (data.success) {
            // 登录成功
            showAlert("登录成功，正在跳转...", "success");
            setTimeout(function() {
                window.location.href = data.redirect || "/admin/dashboard";
            }, 1000);
        } else {
            // 登录失败，显示错误信息
            showAlert(data.message || "登录失败，请检查用户名和密码", "danger");
            // 刷新验证码
            refreshCaptcha();
        }
    })
    .catch(error => {
        console.error("请求失败:", error);
        showAlert("登录过程中发生错误，请重试", "danger");
        // 刷新验证码
        refreshCaptcha();
    });
    
    return false; // 阻止表单默认提交
}

// 页面加载完成后初始化
document.addEventListener("DOMContentLoaded", function() {
    // 刷新验证码
    refreshCaptcha();
    
    // 为验证码图片添加点击事件
    const captchaImg = document.getElementById("captcha-img");
    if (captchaImg) {
        captchaImg.addEventListener("click", refreshCaptcha);
    }
});