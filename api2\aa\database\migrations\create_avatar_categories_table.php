<?php

/**
 * Migration for creating the avatar_categories table
 * This table stores avatar categories with tenant isolation
 */
class CreateAvatarCategoriesTable
{
    /**
     * Run the migration
     */
    public function up()
    {
        $sql = "CREATE TABLE IF NOT EXISTS `avatar_categories` (
            `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
            `tenant_id` bigint(20) UNSIGNED NOT NULL COMMENT 'Reference to tenants table',
            `name` varchar(64) NOT NULL COMMENT 'Category name',
            `icon` varchar(64) DEFAULT NULL COMMENT 'Category icon',
            `sort` int(11) NOT NULL DEFAULT 0 COMMENT 'Sort order',
            `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '0=inactive, 1=active',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMAR<PERSON>EY (`id`),
            <PERSON>EY `tenant_id` (`tenant_id`),
            CONSTRAINT `avatar_categories_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        
        return $sql;
    }

    /**
     * Reverse the migration
     */
    public function down()
    {
        return "DROP TABLE IF EXISTS `avatar_categories`;";
    }
}
