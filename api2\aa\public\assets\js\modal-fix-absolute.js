/**
 * 模态框绝对修复脚本
 * 彻底修复所有页面模态框点击空白处关闭的问题
 * 与控制面板页面使用的modal-behavior-fix.js完全相同的修复方法
 */

// 立即执行函数，避免变量污染全局作用域
(function() {
    // 等待DOM加载完成
    document.addEventListener('DOMContentLoaded', function() {
        console.log('模态框绝对修复脚本已加载');
        
        // 移除全局点击事件
        removeGlobalClickEvents();
        
        // 修复所有模态框
        fixAllModals();
        
        // 重写模态框打开和关闭函数
        overrideModalFunctions();
        
        // 监听DOM变化，处理动态添加的模态框
        setupMutationObserver();
    });
    
    /**
     * 移除全局点击事件
     */
    function removeGlobalClickEvents() {
        // 移除window的点击事件
        window.onclick = null;
        
        // 移除document的点击事件处理程序
        const oldClick = document.onclick;
        document.onclick = function(event) {
            // 如果点击的是模态框，阻止默认行为
            if (event.target.classList.contains('modal')) {
                event.stopPropagation();
                event.preventDefault();
                console.log('点击了模态框空白处（全局事件），阻止关闭');
                return false;
            }
            
            // 否则执行原来的点击事件
            if (oldClick) {
                return oldClick(event);
            }
        };
        
        console.log('已移除全局点击事件');
    }
    
    /**
     * 修复所有模态框
     */
    function fixAllModals() {
        // 获取所有模态框
        const modals = document.querySelectorAll('.modal');
        
        if (modals.length > 0) {
            console.log('找到模态框：', modals.length, '个');
            
            // 为每个模态框添加点击事件
            modals.forEach(function(modal) {
                // 直接添加点击事件，不再克隆替换
                modal.addEventListener('click', function(e) {
                    // 如果点击的是模态框本身，阻止事件传播
                    if (e.target === this) {
                        e.stopPropagation();
                        e.preventDefault();
                        console.log('点击了模态框空白处，阻止关闭');
                        return false;
                    }
                });
                
                // 修复模态框内部的关闭按钮
                const closeButtons = modal.querySelectorAll('.close-btn, .modal-header .bi-x-lg, .modal-header .bi-x');
                closeButtons.forEach(function(button) {
                    // 获取按钮或其父元素的onclick属性
                    let onclickValue = button.getAttribute('onclick');
                    let parentButton = button.closest('button');
                    
                    if (!onclickValue && parentButton) {
                        onclickValue = parentButton.getAttribute('onclick');
                        button = parentButton; // 使用父按钮元素
                    }
                    
                    // 如果有onclick属性，确保它调用closeModal函数
                    if (onclickValue && onclickValue.includes('closeModal')) {
                        console.log('找到关闭按钮，onclick:', onclickValue);
                    } else {
                        // 如果没有onclick属性，添加一个
                        const modalId = modal.id;
                        if (modalId) {
                            button.setAttribute('onclick', `closeModal('${modalId}')`);
                            console.log('为关闭按钮添加onclick属性:', `closeModal('${modalId}')`);
                        }
                    }
                });
            });
        } else {
            console.log('未找到模态框');
        }
    }
    
    /**
     * 重写模态框打开和关闭函数
     */
    function overrideModalFunctions() {
        // 保存原始函数
        if (typeof window.originalOpenModal === 'undefined' && typeof window.openModal !== 'undefined') {
            window.originalOpenModal = window.openModal;
        }
        
        if (typeof window.originalCloseModal === 'undefined' && typeof window.closeModal !== 'undefined') {
            window.originalCloseModal = window.closeModal;
        }
        
        // 重写openModal函数
        window.openModal = function(modalId) {
            console.log('打开模态框：', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                // 显示模态框
                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
                
                // 确保模态框有点击事件处理
                const hasClickHandler = modal._hasClickHandler;
                if (!hasClickHandler) {
                    // 阻止模态框点击事件冒泡
                    modal.addEventListener('click', function(e) {
                        if (e.target === modal) {
                            e.stopPropagation();
                            e.preventDefault();
                            console.log('点击了模态框空白处，阻止关闭');
                            return false;
                        }
                    });
                    modal._hasClickHandler = true;
                }
                
                // 确保关闭按钮有正确的事件处理
                const closeButtons = modal.querySelectorAll('.close-btn, .modal-header .bi-x-lg, .modal-header .bi-x');
                closeButtons.forEach(function(button) {
                    let parentButton = button.closest('button');
                    if (parentButton && !parentButton._hasCloseHandler) {
                        // 添加新的点击事件，但不移除原有的onclick
                        parentButton.addEventListener('click', function(e) {
                            console.log('关闭按钮被点击（动态添加），模态框ID：', modalId);
                            closeModal(modalId);
                        });
                        parentButton._hasCloseHandler = true;
                    }
                });
            }
        };
        
        // 重写closeModal函数
        window.closeModal = function(modalId) {
            console.log('关闭模态框：', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        };
        
        console.log('已重写模态框打开和关闭函数');
    }
    
    /**
     * 设置MutationObserver监听DOM变化
     */
    function setupMutationObserver() {
        // 创建一个观察器实例
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // 检查是否有新的模态框被添加
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach(function(node) {
                        // 检查添加的节点是否是模态框
                        if (node.nodeType === 1 && node.classList && node.classList.contains('modal')) {
                            console.log('检测到新的模态框被添加:', node.id);
                            // 修复这个新的模态框
                            fixModal(node);
                        }
                        
                        // 检查添加的节点内部是否包含模态框
                        if (node.nodeType === 1 && node.querySelectorAll) {
                            const modals = node.querySelectorAll('.modal');
                            if (modals.length > 0) {
                                console.log('检测到新的模态框被添加到DOM中:', modals.length, '个');
                                modals.forEach(modal => fixModal(modal));
                            }
                        }
                    });
                }
            });
        });
        
        // 配置观察选项
        const config = {
            childList: true, // 观察目标子节点的变化
            subtree: true    // 观察所有后代节点的变化
        };
        
        // 开始观察document.body的变化
        observer.observe(document.body, config);
        
        console.log('已设置MutationObserver监听DOM变化');
    }
    
    /**
     * 修复单个模态框
     */
    function fixModal(modal) {
        if (!modal) return;
        
        // 阻止模态框点击事件冒泡
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                e.stopPropagation();
                e.preventDefault();
                console.log('点击了模态框空白处，阻止关闭');
                return false;
            }
        });
        
        // 修复模态框内部的关闭按钮
        const closeButtons = modal.querySelectorAll('.close-btn, .modal-header .bi-x-lg, .modal-header .bi-x');
        closeButtons.forEach(function(button) {
            // 获取按钮或其父元素的onclick属性
            let onclickValue = button.getAttribute('onclick');
            let parentButton = button.closest('button');
            
            if (!onclickValue && parentButton) {
                onclickValue = parentButton.getAttribute('onclick');
                button = parentButton; // 使用父按钮元素
            }
            
            // 如果有onclick属性，确保它调用closeModal函数
            if (onclickValue && onclickValue.includes('closeModal')) {
                console.log('找到关闭按钮，onclick:', onclickValue);
            } else {
                // 如果没有onclick属性，添加一个
                const modalId = modal.id;
                if (modalId) {
                    button.setAttribute('onclick', `closeModal('${modalId}')`);
                    console.log('为关闭按钮添加onclick属性:', `closeModal('${modalId}')`);
                }
            }
        });
        
        console.log('已修复模态框:', modal.id);
    }
})();
