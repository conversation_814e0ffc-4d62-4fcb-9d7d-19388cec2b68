﻿<?php
// 开启会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 启用错误报告
ini_set('display_errors', 1);
error_reporting(E_ALL);

// 设置字符编码
header('Content-Type: text/html; charset=utf-8');

// 记录请求信息
$logFile = __DIR__ . '/../../../storage/logs/unified_login.log';
$logDir = dirname($logFile);
if (!is_dir($logDir)) {
    mkdir($logDir, 0777, true);
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 处理登录请求
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - 统一登录请求开始\n", FILE_APPEND);
    file_put_contents($logFile, "POST 数据: " . print_r($_POST, true) . "\n", FILE_APPEND);
    file_put_contents($logFile, "SESSION 数据: " . print_r($_SESSION, true) . "\n", FILE_APPEND);
    
    // 检查是否是 AJAX 请求
    $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
    
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    $captcha = $_POST['captcha'] ?? '';
    
    file_put_contents($logFile, "用户名: $username, 密码: [已隐藏], 验证码: $captcha\n", FILE_APPEND);
    
    if (!$username || !$password || !$captcha) {
        file_put_contents($logFile, "错误: 用户名、密码或验证码为空\n", FILE_APPEND);
        
        if ($isAjax) {
            // 返回 JSON 响应
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['success' => false, 'message' => '用户名、密码和验证码不能为空'], JSON_UNESCAPED_UNICODE);
        } else {
            // 返回重定向
            header('Location: /login?error=' . urlencode('用户名、密码和验证码不能为空'));
        }
        exit;
    }
    
    // 验证验证码
    if (!isset($_SESSION['captcha']) || strtolower($_SESSION['captcha']) !== strtolower($captcha)) {
        file_put_contents($logFile, "错误: 验证码不正确. 会话验证码: " . ($_SESSION['captcha'] ?? '未设置') . ", 提交验证码: $captcha\n", FILE_APPEND);
        
        if ($isAjax) {
            // 返回 JSON 响应
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['success' => false, 'message' => '验证码错误或已过期'], JSON_UNESCAPED_UNICODE);
        } else {
            // 返回重定向
            header('Location: /login?error=' . urlencode('验证码错误或已过期'));
        }
        exit;
    }
    
    // 清除验证码
    unset($_SESSION['captcha']);
    unset($_SESSION['captcha_time']);

    // 数据库配置
    $config = [
        'host' => 'localhost',
        'database' => 'qq',
        'username' => 'qq',
        'password' => '123456',
        'charset' => 'utf8mb4',
        'prefix' => 'yh_'
    ];

    // 连接数据库
    try {
        $dsn = "mysql:host={$config['host']};dbname={$config['database']};charset={$config['charset']}";
        $pdo = new PDO($dsn, $config['username'], $config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        file_put_contents($logFile, "数据库连接成功\n", FILE_APPEND);
    } catch (PDOException $e) {
        file_put_contents($logFile, "数据库连接失败: " . $e->getMessage() . "\n", FILE_APPEND);
        $pdo = null;
    }

    // 迁移现有项目的登录逻辑：查询yh_admin表，密码使用MD5验证
    if ($pdo) {
        try {
            // 查询yh_admin表中的管理员账号
            $stmt = $pdo->prepare("SELECT * FROM {$config['prefix']}admin WHERE username = ? LIMIT 1");
            $stmt->execute([$username]);
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($admin) {
                // 验证密码（MD5加密）
                $inputPasswordMd5 = md5($password);

                if ($admin['password'] === $inputPasswordMd5) {
                    // 登录成功，设置会话
                    $_SESSION['user_id'] = $admin['id'];
                    $_SESSION['username'] = $admin['username'];
                    $_SESSION['name'] = $admin['name'];
                    $_SESSION['role'] = 'admin'; // 管理员角色
                    $_SESSION['identity'] = $admin['identity']; // 身份标识
                    $_SESSION['is_super'] = true;

                    file_put_contents($logFile, "管理员登录成功, 用户名: $username, ID: {$admin['id']}\n", FILE_APPEND);

                    if ($isAjax) {
                        // 返回 JSON 响应
                        header('Content-Type: application/json; charset=utf-8');
                        echo json_encode(['success' => true, 'message' => '登录成功', 'redirect' => '/dashboard', 'role' => 'admin'], JSON_UNESCAPED_UNICODE);
                    } else {
                        // 返回重定向
                        header('Location: /dashboard');
                    }
                    exit;
                } else {
                    // 密码错误
                    file_put_contents($logFile, "登录失败, 用户名: $username, 原因: 密码错误\n", FILE_APPEND);

                    if ($isAjax) {
                        header('Content-Type: application/json; charset=utf-8');
                        echo json_encode(['success' => false, 'message' => '用户名或密码错误'], JSON_UNESCAPED_UNICODE);
                    } else {
                        header('Location: /login?error=' . urlencode('用户名或密码错误'));
                    }
                    exit;
                }
            } else {
                // 用户不存在
                file_put_contents($logFile, "登录失败, 用户名: $username, 原因: 用户不存在\n", FILE_APPEND);

                if ($isAjax) {
                    header('Content-Type: application/json; charset=utf-8');
                    echo json_encode(['success' => false, 'message' => '用户名或密码错误'], JSON_UNESCAPED_UNICODE);
                } else {
                    header('Location: /login?error=' . urlencode('用户名或密码错误'));
                }
                exit;
            }
        } catch (Exception $e) {
            file_put_contents($logFile, "数据库查询失败: " . $e->getMessage() . "\n", FILE_APPEND);

            if ($isAjax) {
                header('Content-Type: application/json; charset=utf-8');
                echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试'], JSON_UNESCAPED_UNICODE);
            } else {
                header('Location: /login?error=' . urlencode('系统错误，请稍后重试'));
            }
            exit;
        }
    } else {
        // 数据库连接失败
        file_put_contents($logFile, "数据库连接失败，无法验证用户\n", FILE_APPEND);

        if ($isAjax) {
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试'], JSON_UNESCAPED_UNICODE);
        } else {
            header('Location: /login?error=' . urlencode('系统错误，请稍后重试'));
        }
        exit;
    }
} else {
    // 显示登录表单
    // 检查是否已经登录
    if (isset($_SESSION['user_id'])) {
        header('Location: /dashboard');
        exit;
    }
    
    // 获取网站设置信息用于登录页显示
    try {
        $stmt = $pdo->query("SELECT * FROM {$prefix}webconfig WHERE id = 1");
        $webConfig = $stmt->fetch(PDO::FETCH_ASSOC) ?: [];

        $stmt = $pdo->query("SELECT * FROM {$prefix}information WHERE id = 1");
        $infoConfig = $stmt->fetch(PDO::FETCH_ASSOC) ?: [];

        // 映射设置数据
        $loginSettings = [
            'site_name' => $webConfig['name'] ?? '去水印接口',
            'site_logo' => $infoConfig['site_logo'] ?? '/assets/images/logo.png',
            'site_favicon' => $infoConfig['site_favicon'] ?? '/assets/images/favicon.ico',
            'site_copyright' => '© ' . date('Y') . ' ' . ($webConfig['name'] ?? '管理系统'),
            'icp_number' => $infoConfig['beian_icp'] ?? '',
            'police_number' => $infoConfig['beian_police'] ?? '',
            'login_bg' => $infoConfig['login_bg'] ?? '/assets/images/login-bg.jpg',
        ];
    } catch (PDOException $e) {
        // 数据库连接失败，使用默认设置
        $loginSettings = [
            'site_name' => '去水印接口',
            'site_logo' => '/assets/images/logo.png',
            'site_favicon' => '/assets/images/favicon.ico',
            'site_copyright' => '© ' . date('Y') . ' 管理系统',
            'icp_number' => '',
            'police_number' => '',
            'login_bg' => '/assets/images/login-bg.jpg',
        ];
    }

    // 显示登录页面
    header('Content-Type: text/html; charset=utf-8');
    include __DIR__ . '/index.php';
    exit;
}