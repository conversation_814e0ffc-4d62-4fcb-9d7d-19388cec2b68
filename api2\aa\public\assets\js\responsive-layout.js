/**
 * 响应式布局JavaScript
 * 用于实现侧边栏收起后页面自适应的功能
 */

// DOM元素 - 使用window对象存储全局变量，避免重复声明
// 检查是否已经定义，如果没有才定义
if (typeof window.sidebar === 'undefined') {
    window.sidebar = null;
}
if (typeof window.mainContent === 'undefined') {
    window.mainContent = null;
}
if (typeof window.menuToggle === 'undefined') {
    window.menuToggle = null;
}
// 标记是否已经添加了事件监听器
if (typeof window.eventListenersAdded === 'undefined') {
    window.eventListenersAdded = false;
}

// 初始化
document.addEventListener("DOMContentLoaded", function() {
    console.log("响应式布局脚本已加载");

    // 获取DOM元素
    if (!window.sidebar) {
        window.sidebar = document.getElementById("sidebar");
    }
    if (!window.mainContent) {
        window.mainContent = document.getElementById("main-content");
    }
    if (!window.menuToggle) {
        window.menuToggle = document.getElementById("menu-toggle");
    }

    if (window.sidebar && window.mainContent && window.menuToggle) {
        console.log("找到侧边栏元素");

        // 检查本地存储中的侧边栏状态
        const sidebarCollapsed = localStorage.getItem("sidebarCollapsed") === "true";

        // 设置初始状态
        if (sidebarCollapsed) {
            window.sidebar.classList.add("collapsed");
            window.mainContent.classList.add("expanded");
        }

        // 只添加一次事件监听器
        if (!window.eventListenersAdded) {
            console.log("添加侧边栏切换事件监听器");

            // 侧边栏切换
            window.menuToggle.addEventListener("click", function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log("侧边栏切换按钮被点击");
                toggleSidebar();
            });

            // 窗口大小变化时调整布局
            window.addEventListener("resize", function() {
                adjustLayout();
            });

            // 标记已添加事件监听器
            window.eventListenersAdded = true;
        }

        // 初始调整布局
        adjustLayout();
    } else {
        console.error("未找到侧边栏元素");
    }
});

/**
 * 切换侧边栏状态
 */
function toggleSidebar() {
    console.log("执行toggleSidebar函数");

    if (window.sidebar && window.mainContent) {
        // 切换侧边栏状态
        const isCollapsed = window.sidebar.classList.contains("collapsed");
        console.log("当前侧边栏状态：", isCollapsed ? "已收起" : "已展开");

        window.sidebar.classList.toggle("collapsed");
        window.mainContent.classList.toggle("expanded");

        // 更新本地存储
        const newState = window.sidebar.classList.contains("collapsed");
        localStorage.setItem("sidebarCollapsed", newState);
        console.log("侧边栏状态已切换为：", newState ? "已收起" : "已展开");

        // 触发窗口大小变化事件，以便调整布局
        window.dispatchEvent(new Event('resize'));
    } else {
        console.error("切换侧边栏失败：未找到侧边栏元素");
    }
}

/**
 * 根据窗口大小调整布局
 */
function adjustLayout() {
    if (window.sidebar && window.mainContent) {
        const windowWidth = window.innerWidth;
        console.log("调整布局，窗口宽度：", windowWidth);

        // 小屏幕设备（手机）
        if (windowWidth < 768) {
            // 默认收起侧边栏
            window.sidebar.classList.add("collapsed");
            window.mainContent.classList.add("expanded");
            console.log("小屏幕设备布局已调整");
        }
        // 中等屏幕设备（平板）
        else if (windowWidth < 992) {
            // 默认收起侧边栏
            window.sidebar.classList.add("collapsed");
            window.mainContent.classList.add("expanded");
            console.log("中等屏幕设备布局已调整");
        }
        // 大屏幕设备（桌面）
        else {
            // 恢复正常状态
            document.body.classList.remove("sidebar-open");
            console.log("大屏幕设备布局已调整");
        }
    } else {
        console.error("调整布局失败：未找到侧边栏元素");
    }
}

/**
 * 调整网格布局
 * @param {string} selector - 网格容器选择器
 * @param {number} minWidth - 最小宽度
 */
function adjustGrid(selector, minWidth = 300) {
    const container = document.querySelector(selector);
    if (container) {
        const containerWidth = container.offsetWidth;
        const columns = Math.floor(containerWidth / minWidth);
        container.style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
    }
}
