/**
 * 通用JavaScript函数库
 * 包含模态框、提示信息等通用功能
 */

// DOM元素 - 使用window对象存储全局变量，避免重复声明
// 检查是否已经定义，如果没有才定义
if (typeof window.loadingOverlay === 'undefined') {
    window.loadingOverlay = null;
}
if (typeof window.toast === 'undefined') {
    window.toast = null;
}

// 初始化
document.addEventListener("DOMContentLoaded", function() {
    // 获取DOM元素
    if (!window.loadingOverlay) {
        window.loadingOverlay = document.getElementById("loading-overlay");
    }
    if (!window.toast) {
        window.toast = document.getElementById("toast");
    }

    // 检查本地存储中的侧边栏状态
    const sidebar = document.getElementById("sidebar");
    const mainContent = document.getElementById("main-content");
    const sidebarCollapsed = localStorage.getItem("sidebarCollapsed") === "true";

    if (sidebar && mainContent) {
        if (sidebarCollapsed) {
            sidebar.classList.add("collapsed");
            mainContent.classList.add("expanded");
        }

        // 侧边栏切换
        const menuToggle = document.getElementById("menu-toggle");
        if (menuToggle) {
            menuToggle.addEventListener("click", function() {
                sidebar.classList.toggle("collapsed");
                mainContent.classList.toggle("expanded");
                localStorage.setItem("sidebarCollapsed", sidebar.classList.contains("collapsed"));
            });
        }
    }

    // 不再允许点击模态框外部关闭模态框
    // 这个功能已经由 modal-precise-fix.js 处理
    /*
    window.addEventListener("click", function(event) {
        if (event.target.classList.contains("modal")) {
            event.target.style.display = "none";
            document.body.style.overflow = "auto";
        }
    });
    */

    // 隐藏加载动画
    if (window.loadingOverlay) {
        setTimeout(hideLoading, 500);
    }
});

/**
 * 显示加载动画
 */
function showLoading() {
    if (window.loadingOverlay) {
        window.loadingOverlay.classList.add("show");
        document.body.style.overflow = "hidden";
    }
}

/**
 * 隐藏加载动画
 */
function hideLoading() {
    if (window.loadingOverlay) {
        window.loadingOverlay.classList.remove("show");
        document.body.style.overflow = "auto";
    }
}

/**
 * 打开模态框
 * @param {string} modalId - 模态框ID
 */
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = "block";
        document.body.style.overflow = "hidden";
    }
}

/**
 * 关闭模态框
 * @param {string} modalId - 模态框ID
 */
function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = "none";
        document.body.style.overflow = "auto";
    }
}

/**
 * 显示消息提示
 * @param {string} message - 提示消息
 * @param {string} type - 提示类型：success, error, info
 */
function showToast(message, type = "success") {
    // 如果toast元素不存在，则创建一个
    if (!window.toast) {
        window.toast = document.createElement("div");
        window.toast.className = "toast";
        window.toast.id = "toast";

        const toastContent = document.createElement("div");
        toastContent.className = "toast-content";

        const successIcon = document.createElement("i");
        successIcon.className = "bi bi-check-circle-fill toast-icon success";

        const errorIcon = document.createElement("i");
        errorIcon.className = "bi bi-x-circle-fill toast-icon error";

        const infoIcon = document.createElement("i");
        infoIcon.className = "bi bi-info-circle-fill toast-icon info";

        const toastMessage = document.createElement("div");
        toastMessage.className = "toast-message";

        const toastProgress = document.createElement("div");
        toastProgress.className = "toast-progress";

        toastContent.appendChild(successIcon);
        toastContent.appendChild(errorIcon);
        toastContent.appendChild(infoIcon);
        toastContent.appendChild(toastMessage);
        window.toast.appendChild(toastContent);
        window.toast.appendChild(toastProgress);

        document.body.appendChild(window.toast);
    }

    // 获取toast元素中的子元素
    const toastMessage = window.toast.querySelector(".toast-message");
    const successIcon = window.toast.querySelector(".toast-icon.success");
    const errorIcon = window.toast.querySelector(".toast-icon.error");
    const infoIcon = window.toast.querySelector(".toast-icon.info");

    // 设置消息
    toastMessage.textContent = message;

    // 设置图标
    successIcon.style.display = "none";
    errorIcon.style.display = "none";
    infoIcon.style.display = "none";

    if (type === "success") {
        successIcon.style.display = "block";
    } else if (type === "error") {
        errorIcon.style.display = "block";
    } else if (type === "info") {
        infoIcon.style.display = "block";
    }

    // 显示消息提示
    window.toast.classList.add("show");

    // 3秒后自动隐藏
    setTimeout(() => {
        window.toast.classList.remove("show");
    }, 3000);
}

/**
 * 创建确认模态框
 * @param {string} title - 模态框标题
 * @param {string} message - 确认消息
 * @param {Function} confirmCallback - 确认回调函数
 * @param {string} confirmText - 确认按钮文本
 * @param {string} cancelText - 取消按钮文本
 * @param {string} confirmClass - 确认按钮样式类
 */
function createConfirmModal(title, message, confirmCallback, confirmText = "确定", cancelText = "取消", confirmClass = "btn-primary") {
    // 生成唯一ID
    const modalId = "confirm-modal-" + Date.now();

    // 创建模态框HTML
    const modalHTML = `
    <div class="modal" id="${modalId}">
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button class="close-btn" onclick="closeModal('${modalId}')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <p>${message}</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('${modalId}')">${cancelText}</button>
                <button class="btn ${confirmClass}" id="${modalId}-confirm">${confirmText}</button>
            </div>
        </div>
    </div>
    `;

    // 添加到文档
    document.body.insertAdjacentHTML("beforeend", modalHTML);

    // 获取模态框元素
    const modal = document.getElementById(modalId);
    const confirmButton = document.getElementById(`${modalId}-confirm`);

    // 添加确认按钮点击事件
    confirmButton.addEventListener("click", function() {
        closeModal(modalId);
        if (typeof confirmCallback === "function") {
            confirmCallback();
        }
    });

    // 打开模态框
    openModal(modalId);

    // 返回模态框ID，以便后续操作
    return modalId;
}

