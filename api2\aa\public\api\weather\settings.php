<?php
// 天气设置API端点
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只允许POST请求']);
    exit;
}

// 开启会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => '未登录']);
    exit;
}

try {
    // 加载配置
    $configPath = dirname(__DIR__, 3) . '/config/config.php';
    if (!file_exists($configPath)) {
        throw new Exception('配置文件不存在');
    }
    $config = require_once($configPath);
    $dbConfig = $config['database'];

    // 连接数据库
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $prefix = $dbConfig['prefix'];

    // 获取POST数据
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        throw new Exception('无效的JSON数据');
    }

    // 验证必要字段
    $allowedFields = [
        'weather_api_key',
        'weather_city',
        'weather_update_interval',
        'weather_display_format',
        'weather_units'
    ];

    $updateData = [];
    foreach ($allowedFields as $field) {
        if (isset($input[$field])) {
            $updateData[$field] = $input[$field];
        }
    }

    if (empty($updateData)) {
        throw new Exception('没有有效的设置数据');
    }

    // 构建更新SQL
    $setParts = [];
    $values = [];
    foreach ($updateData as $field => $value) {
        $setParts[] = "{$field} = ?";
        $values[] = $value;
    }

    $sql = "UPDATE {$prefix}information SET " . implode(', ', $setParts) . " WHERE id = 1";
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute($values);

    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => '天气设置保存成功',
            'data' => $updateData
        ]);
    } else {
        throw new Exception('数据库更新失败');
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
