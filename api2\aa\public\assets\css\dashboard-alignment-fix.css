/**
 * 控制面板对齐修复样式
 * 调整控制面板页面的对齐问题
 */

/* 账户信息部分 */

/* 用户名与手机号水平居中对齐 */
.stat-grid .stat-item:nth-child(1) .stat-value,
.stat-grid .stat-item:nth-child(2) .stat-value {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    height: 30px !important; /* 固定高度 */
    margin-top: 10px !important;
}

/* 邮箱特殊处理 */
.stat-grid .stat-item:nth-child(3) .stat-value-multiline {
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    height: auto !important; /* 自动高度 */
    margin-top: 10px !important;
    padding-top: 0 !important;
}

/* 账户状态水平居中对齐 */
.stat-grid .stat-item:nth-child(4) .stat-value {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    height: 30px !important; /* 固定高度 */
    margin-top: 10px !important;
}

/* 可创建小程序与已创建小程序水平居中对齐 */
.stat-grid .stat-item:nth-child(5) .stat-value,
.stat-grid .stat-item:nth-child(6) .stat-value {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    height: 30px !important; /* 固定高度 */
    margin-top: 10px !important;
}

/* 系统概况部分 */

/* 授权状态与授权类型水平居中对齐 */
.stat-card:nth-child(2) .stat-grid .stat-item:nth-child(1) .stat-value,
.stat-card:nth-child(2) .stat-grid .stat-item:nth-child(2) .stat-value {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    height: 30px !important; /* 固定高度 */
    margin-top: 10px !important;
}

/* 系统运行时间与运行环境水平居中对齐 */
.stat-card:nth-child(2) .stat-grid .stat-item:nth-child(3) .stat-value {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    height: 30px !important; /* 固定高度 */
    margin-top: 10px !important;
}

/* 运行环境特殊处理 */
.stat-card:nth-child(2) .stat-grid .stat-item:nth-child(4) .stat-value-multiline {
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    height: auto !important; /* 自动高度 */
    margin-top: 10px !important;
    padding-top: 0 !important;
}

/* 调整多行值的样式 */
.stat-value-multiline {
    flex-direction: column !important;
    justify-content: center !important;
    height: auto !important; /* 自动高度 */
}

.stat-value-multiline .stat-value {
    margin: 0 !important;
    height: auto !important;
    line-height: 1.5 !important;
    padding: 2px 0 !important;
}

/* 确保邮箱文本居中 */
.stat-grid .stat-item:nth-child(3) .stat-value-multiline .stat-value {
    text-align: center !important;
    width: 100% !important;
}

/* 确保运行环境文本居中 */
.stat-card:nth-child(2) .stat-grid .stat-item:nth-child(4) .stat-value-multiline .stat-value {
    text-align: center !important;
    width: 100% !important;
}

/* 确保所有值都垂直居中 */
.stat-value {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    min-height: 30px !important;
}

/* 调整系统时间的样式 */
#current-date, #current-time {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    height: auto !important;
    margin: 0 !important;
    line-height: 1.5 !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .stat-value,
    .stat-value-multiline,
    #current-date,
    #current-time {
        min-height: 25px !important;
    }
}
