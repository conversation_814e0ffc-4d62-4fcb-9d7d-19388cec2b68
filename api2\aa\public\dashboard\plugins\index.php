﻿<?php
// 插件中心页面
?>
<?php
// 包含身份验证检查
include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/auth-check.php');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件中心 - 情侣头像匹配系统</title>
    <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/header-scripts.php'); ?>
    <!-- 插件增强样式 -->
    <link rel="stylesheet" href="/assets/css/plugin-enhancements.css">
    <link rel="stylesheet" href="/assets/css/filter-styles.css">
<style>

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: "Microsoft YaHei", sans-serif;
    }

    body {
        background-color: #f5f7fa;
        color: #333;
        font-size: 14px;
        line-height: 1.5;
        overflow-x: hidden;
    }

    /* 加载动画 */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s;
    }

    .loading-overlay.show {
        opacity: 1;
        visibility: visible;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 107, 149, 0.3);
        border-top: 4px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* 布局样式 */
    .dashboard-container {
        display: flex;
        min-height: 100vh;
    }

    /* 侧边栏样式 */
    .sidebar {
        width: 250px;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: var(--white-color);
        padding: 20px 0;
        position: fixed;
        height: 100vh;
        overflow-y: auto;
        z-index: 1000;
        box-shadow: 2px 0 10px var(--shadow-color);
        transition: all var(--transition-speed);
    }

    .sidebar.collapsed {
        width: 70px;
    }

    .sidebar-header {
        padding: 0 20px 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        text-align: center;
        margin-bottom: 20px;
    }

    .sidebar-logo {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        margin-bottom: 10px;
        object-fit: cover;
        background-color: rgba(255, 255, 255, 0.2);
    }

    .sidebar-title {
        color: var(--white-color);
        font-size: 1.5rem;
        margin-bottom: 5px;
        font-weight: 600;
    }

    .sidebar-subtitle {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
    }

    .sidebar.collapsed .sidebar-title,
    .sidebar.collapsed .sidebar-subtitle {
        display: none;
    }

    .sidebar-menu {
        list-style: none;
        padding: 0;
    }

    .sidebar-menu li {
        margin-bottom: 5px;
    }

    .sidebar-menu li a {
        color: var(--white-color);
        text-decoration: none;
        display: block;
        padding: 12px 20px;
        transition: all var(--transition-speed);
        border-left: 4px solid transparent;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .sidebar-menu li a:hover,
    .sidebar-menu li a.active {
        background-color: rgba(255, 255, 255, 0.2);
        border-left-color: var(--white-color);
    }

    .sidebar-menu li a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
        font-size: 1.1rem;
    }

    .sidebar.collapsed .sidebar-menu li a span {
        display: none;
    }

    .sidebar.collapsed .sidebar-menu li a i {
        margin-right: 0;
        font-size: 1.3rem;
    }

    /* 主内容区域样式 */
    .main-content {
        flex: 1;
        margin-left: 250px;
        padding: 20px;
        transition: all var(--transition-speed);
    }

    .main-content.expanded {
        margin-left: 70px;
    }

    /* 头部样式 */
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }

    .menu-toggle {
        background: none;
        border: none;
        color: var(--dark-color);
        font-size: 1.5rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        transition: all var(--transition-speed);
    }

    .menu-toggle:hover {
        background-color: var(--light-color);
    }

    .user-info {
        display: flex;
        align-items: center;
        position: relative;
        cursor: pointer;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: var(--white-color);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 10px;
    }

    .user-name {
        font-weight: 500;
    }

    .dropdown-content {
        position: absolute;
        top: 100%;
        right: 0;
        background-color: var(--white-color);
        min-width: 160px;
        box-shadow: 0 8px 16px 0 var(--shadow-color);
        border-radius: var(--border-radius);
        padding: 10px 0;
        z-index: 1;
        display: none;
        animation: fadeIn 0.3s;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .user-info:hover .dropdown-content {
        display: block;
    }

    .dropdown-content a {
        color: var(--dark-color);
        padding: 10px 20px;
        text-decoration: none;
        display: block;
        transition: all var(--transition-speed);
    }

    .dropdown-content a:hover {
        background-color: var(--light-color);
    }

    .dropdown-content a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }

    /* 操作栏样式 */
    .action-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .search-container {
        display: flex;
        align-items: center;
        background-color: var(--white-color);
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: 0 2px 5px var(--shadow-color);
        width: 300px;
    }

    .search-input {
        flex: 1;
        border: none;
        padding: 10px 15px;
        outline: none;
        font-size: 14px;
    }

    .search-button {
        background-color: var(--primary-color);
        color: var(--white-color);
        border: none;
        padding: 10px 15px;
        cursor: pointer;
        transition: all var(--transition-speed);
    }

    .search-button:hover {
        background-color: #ff4f7e;
    }

    .filter-container {
        display: flex;
        align-items: center;
    }

    .filter-select {
        padding: 10px 15px;
        border: none;
        border-radius: var(--border-radius);
        background-color: var(--white-color);
        box-shadow: 0 2px 5px var(--shadow-color);
        outline: none;
        cursor: pointer;
        transition: all var(--transition-speed);
    }

    .filter-select:focus {
        box-shadow: 0 2px 10px var(--shadow-color);
    }

    /* 按钮样式 */
    .btn {
        display: inline-block;
        font-weight: 400;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        user-select: none;
        border: 1px solid transparent;
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: var(--border-radius);
        transition: all var(--transition-speed);
        cursor: pointer;
    }

    .btn-primary {
        color: var(--white-color);
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .btn-primary:hover {
        background-color: #ff4f7e;
        border-color: #ff4f7e;
    }

    .btn-secondary {
        color: var(--white-color);
        background-color: var(--gray-color);
        border-color: var(--gray-color);
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #545b62;
    }

    .btn-outline-primary {
        color: var(--primary-color);
        background-color: transparent;
        border-color: var(--primary-color);
    }

    .btn-outline-primary:hover {
        color: var(--white-color);
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    /* 插件网格样式 */
    .section-header {
        margin-bottom: 20px;
    }

    .section-header h2 {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--dark-color);
        position: relative;
        padding-left: 15px;
    }

    .section-header h2::before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 5px;
        height: 20px;
        background-color: var(--primary-color);
        border-radius: 3px;
    }

    .plugin-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .plugin-card {
        background-color: var(--white-color);
        border-radius: var(--border-radius);
        box-shadow: 0 2px 10px var(--shadow-color);
        overflow: hidden;
        transition: all var(--transition-speed);
        height: 100%;
        display: flex;
        flex-direction: column;
        animation: fadeInUp 0.5s;
    }

    @keyframes fadeInUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .plugin-card:hover {
        box-shadow: 0 5px 15px var(--shadow-color);
        transform: translateY(-5px);
    }

    .plugin-card-header {
        padding: 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .plugin-card-header h3 {
        margin: 0;
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--dark-color);
    }

    .plugin-badge {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
    }

    .plugin-badge.installed {
        background-color: rgba(40, 167, 69, 0.1);
        color: var(--success-color);
    }

    .plugin-badge.not-installed {
        background-color: rgba(108, 117, 125, 0.1);
        color: var(--gray-color);
    }

    .plugin-card-body {
        padding: 15px;
        flex-grow: 1;
    }

    .plugin-description {
        margin-bottom: 15px;
        color: var(--gray-color);
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .plugin-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }

    .plugin-category {
        background-color: #f8f9fa;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        color: var(--gray-color);
    }

    .plugin-version {
        font-size: 12px;
        color: var(--gray-color);
    }

    .plugin-price {
        margin-top: 10px;
    }

    .price {
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--primary-color);
    }

    .price-original {
        font-size: 0.9rem;
        color: var(--gray-color);
        text-decoration: line-through;
        margin-left: 5px;
    }

    .plugin-card-footer {
        padding: 15px;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    /* 模态框样式 */
    .modal {
        display: none;
        position: fixed;
        z-index: 1050;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0, 0, 0, 0.5);
        animation: fadeIn 0.3s;
    }

    .modal-content {
        background-color: var(--white-color);
        margin: 10% auto;
        width: 500px;
        max-width: 90%;
        border-radius: var(--border-radius);
        box-shadow: 0 5px 15px var(--shadow-color);
        animation: slideDown 0.3s;
    }

    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-50px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .modal-header {
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h3 {
        margin: 0;
        font-size: 1.2rem;
        color: var(--dark-color);
    }

    .close-btn {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--gray-color);
        transition: all var(--transition-speed);
    }

    .close-btn:hover {
        color: var(--dark-color);
    }

    .modal-body {
        padding: 20px;
    }

    .modal-footer {
        padding: 15px 20px;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }

    /* 插件详情样式 */
    .plugin-detail-header {
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .plugin-detail-header h4 {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--dark-color);
        margin: 0;
    }

    .plugin-detail-badges {
        display: flex;
        gap: 10px;
    }

    .plugin-detail-info {
        margin-bottom: 20px;
    }

    .plugin-detail-info p {
        margin-bottom: 10px;
    }

    .plugin-detail-features {
        margin-bottom: 20px;
    }

    .plugin-detail-features h5,
    .plugin-detail-requirements h5 {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 10px;
    }

    .plugin-detail-features ul {
        padding-left: 20px;
    }

    .plugin-detail-features li {
        margin-bottom: 5px;
    }

    /* 购买插件样式 */
    .purchase-info {
        text-align: center;
    }

    .purchase-info h4 {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 15px;
    }

    .purchase-price {
        margin-bottom: 20px;
    }

    .payment-methods h5 {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 15px;
    }

    .payment-options {
        display: flex;
        justify-content: center;
        gap: 20px;
    }

    .payment-option {
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        padding: 15px;
        border: 1px solid #eee;
        border-radius: var(--border-radius);
        transition: all var(--transition-speed);
    }

    .payment-option:hover {
        border-color: var(--primary-color);
    }

    .payment-option input {
        margin-bottom: 10px;
    }

    .payment-icon {
        font-size: 2rem;
        margin-bottom: 10px;
        color: var(--dark-color);
    }

    .payment-name {
        font-size: 0.9rem;
        color: var(--dark-color);
    }

    /* 消息提示样式 */
    .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: var(--white-color);
        border-radius: var(--border-radius);
        box-shadow: 0 5px 15px var(--shadow-color);
        padding: 15px 20px;
        display: flex;
        flex-direction: column;
        min-width: 300px;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transform: translateX(50px);
        transition: all var(--transition-speed);
    }

    .toast.show {
        opacity: 1;
        visibility: visible;
        transform: translateX(0);
    }

    .toast-content {
        display: flex;
        align-items: center;
    }

    .toast-icon {
        margin-right: 10px;
        font-size: 1.5rem;
    }

    .toast-icon.success {
        color: var(--success-color);
    }

    .toast-icon.error {
        color: var(--danger-color);
    }

    .toast-icon.info {
        color: var(--info-color);
    }

    .toast-message {
        font-weight: 500;
    }

    .toast-progress {
        height: 3px;
        background-color: var(--primary-color);
        margin-top: 10px;
        width: 100%;
        animation: progress 3s linear;
    }

    @keyframes progress {
        from { width: 100%; }
        to { width: 0%; }
    }

    /* 响应式样式 */
    @media (max-width: 768px) {
        .sidebar {
            width: 70px;
        }

        .sidebar-title,
        .sidebar-subtitle {
            display: none;
        }

        .sidebar-menu li a span {
            display: none;
        }

        .sidebar-menu li a i {
            margin-right: 0;
            font-size: 1.3rem;
        }

        .main-content {
            margin-left: 70px;
        }

        .action-bar {
            flex-direction: column;
            align-items: stretch;
            gap: 10px;
        }

        .search-container {
            width: 100%;
        }

        .plugin-grid {
            grid-template-columns: 1fr;
        }
    }
</style>

<style>
    /* 插件中心样式 */
    .plugins-container {
        animation: fadeInUp 0.5s;
    }

    .plugins-header {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: var(--white-color);
        padding: 25px;
        border-radius: var(--border-radius);
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(255, 107, 149, 0.3);
    }

    .plugins-header h2 {
        font-size: 1.8rem;
        font-weight: 600;
        margin-bottom: 5px;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .plugins-header p {
        opacity: 0.9;
    }

    .plugins-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;
    }

    .plugin-card {
        background-color: var(--white-color);
        border-radius: var(--border-radius);
        box-shadow: 0 5px 15px var(--shadow-color);
        overflow: hidden;
        transition: all var(--transition-speed);
        display: flex;
        flex-direction: column;
        animation: fadeInUp 0.5s;
    }

    .plugin-card:hover {
        box-shadow: 0 8px 25px var(--shadow-color);
        transform: translateY(-5px);
    }

    .plugin-logo {
        width: 80px;
        height: 80px;
        border-radius: 15px;
        overflow: hidden;
        margin: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .plugin-logo img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .plugin-info {
        padding: 0 20px 20px;
        flex: 1;
    }

    .plugin-info h3 {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 5px;
        color: var(--dark-color);
    }

    .plugin-meta {
        display: flex;
        gap: 15px;
        margin-bottom: 10px;
        font-size: 0.85rem;
        color: var(--gray-color);
    }

    .plugin-version {
        display: inline-block;
        padding: 2px 8px;
        background-color: rgba(23, 162, 184, 0.1);
        color: var(--info-color);
        border-radius: 20px;
    }

    .plugin-author {
        display: inline-block;
    }

    .plugin-description {
        margin-bottom: 15px;
        color: var(--gray-color);
        line-height: 1.5;
    }

    .plugin-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 15px;
    }

    .tag {
        display: inline-block;
        padding: 3px 10px;
        background-color: #f8f9fa;
        color: var(--gray-color);
        border-radius: 20px;
        font-size: 0.85rem;
    }

    .plugin-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        padding: 15px 20px;
        background-color: #f8f9fa;
        border-top: 1px solid #eee;
    }

    /* 按钮样式 */
    .btn {
        display: inline-block;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        user-select: none;
        border: 1px solid transparent;
        padding: 0.5rem 1rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: var(--border-radius);
        transition: all var(--transition-speed);
        cursor: pointer;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .btn:active {
        transform: translateY(0);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .btn-primary {
        color: var(--white-color);
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        border-color: var(--primary-color);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #ff4f7e 0%, #ffa5c0 100%);
        border-color: #ff4f7e;
    }

    .btn-outline-primary {
        color: var(--primary-color);
        background-color: transparent;
        border-color: var(--primary-color);
    }

    .btn-outline-primary:hover {
        color: var(--white-color);
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    /* 消息提示样式 */
    .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 300px;
        background-color: var(--white-color);
        border-radius: var(--border-radius);
        box-shadow: 0 5px 15px var(--shadow-color);
        overflow: hidden;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transform: translateX(30px);
        transition: all 0.3s;
    }

    .toast.show {
        opacity: 1;
        visibility: visible;
        transform: translateX(0);
    }

    .toast-content {
        display: flex;
        align-items: center;
        padding: 15px;
    }

    .toast-icon {
        font-size: 1.5rem;
        margin-right: 15px;
        display: none;
    }

    .toast-icon.success {
        color: var(--success-color);
    }

    .toast-icon.error {
        color: var(--danger-color);
    }

    .toast-icon.info {
        color: var(--info-color);
    }

    .toast-message {
        flex: 1;
        font-weight: 500;
    }

    .toast-progress {
        height: 3px;
        background-color: var(--primary-color);
        width: 100%;
        animation: progress 3s linear;
    }

    @keyframes progress {
        from { width: 100%; }
        to { width: 0%; }
    }

    /* 插件详情样式 */
    .plugin-detail {
        padding: 20px; /* 增加内边距 */
        animation: fadeIn 0.3s ease-out; /* 添加动画效果 */
    }

    .plugin-detail-header {
        margin-bottom: 25px; /* 增加底部间距 */
        padding-bottom: 20px; /* 增加底部内边距 */
        border-bottom: 1px solid #eee;
        position: relative; /* 为背景渐变做准备 */
        overflow: hidden; /* 确保背景不溢出 */
    }

    .plugin-detail-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(255, 107, 149, 0.05) 0%, rgba(255, 107, 149, 0) 100%);
        z-index: 0;
    }

    .plugin-detail-header h4 {
        font-size: 1.8rem; /* 增加字体大小 */
        font-weight: 600;
        margin-bottom: 15px; /* 增加底部间距 */
        color: var(--dark-color);
        position: relative; /* 确保文字在渐变上方 */
        z-index: 1;
    }

    .plugin-detail-badges {
        display: flex;
        gap: 12px; /* 增加间距 */
        position: relative; /* 确保徽章在渐变上方 */
        z-index: 1;
    }

    .plugin-detail-info {
        margin-bottom: 25px; /* 增加底部间距 */
        background-color: #f8f9fa; /* 添加背景色 */
        padding: 15px; /* 添加内边距 */
        border-radius: var(--border-radius); /* 添加圆角 */
        box-shadow: 0 2px 5px rgba(0,0,0,0.05); /* 添加阴影 */
    }

    .plugin-detail-info p {
        margin-bottom: 12px; /* 增加底部间距 */
        line-height: 1.6; /* 增加行高 */
        font-size: 1.05rem; /* 增加字体大小 */
    }

    .plugin-detail-info p:last-child {
        margin-bottom: 0; /* 最后一个段落不需要底部间距 */
    }

    .plugin-detail-info p strong {
        color: var(--dark-color); /* 加粗文字颜色 */
        margin-right: 5px; /* 添加右侧间距 */
    }

    .plugin-detail-features {
        margin-top: 25px; /* 增加顶部间距 */
        background-color: #fff; /* 添加背景色 */
        padding: 20px; /* 添加内边距 */
        border-radius: var(--border-radius); /* 添加圆角 */
        box-shadow: 0 3px 10px rgba(0,0,0,0.05); /* 添加阴影 */
        border: 1px solid #eee; /* 添加边框 */
    }

    .plugin-detail-features h5 {
        font-size: 1.2rem; /* 增加字体大小 */
        font-weight: 600;
        margin-bottom: 15px; /* 增加底部间距 */
        color: var(--primary-color);
        padding-bottom: 10px; /* 添加底部内边距 */
        border-bottom: 1px solid #eee; /* 添加底部边框 */
    }

    .plugin-detail-features ul {
        padding-left: 25px; /* 增加左侧间距 */
    }

    .plugin-detail-features li {
        margin-bottom: 10px; /* 增加底部间距 */
        line-height: 1.6; /* 增加行高 */
        font-size: 1.05rem; /* 增加字体大小 */
        position: relative; /* 为自定义列表样式做准备 */
    }

    .plugin-detail-features li::before {
        content: '✓'; /* 添加自定义列表样式 */
        color: var(--primary-color);
        position: absolute;
        left: -20px;
        font-weight: bold;
    }

    .plugin-detail-requirements,
    .plugin-detail-changelog,
    .plugin-detail-usage,
    .plugin-detail-support {
        margin-top: 25px; /* 增加顶部间距 */
        padding: 20px; /* 添加内边距 */
        border-radius: var(--border-radius); /* 添加圆角 */
        background-color: #fff; /* 添加背景色 */
        box-shadow: 0 3px 10px rgba(0,0,0,0.05); /* 添加阴影 */
        border: 1px solid #eee; /* 添加边框 */
    }

    .plugin-detail-requirements h5,
    .plugin-detail-changelog h5,
    .plugin-detail-usage h5,
    .plugin-detail-support h5 {
        font-size: 1.2rem; /* 增加字体大小 */
        font-weight: 600;
        margin-bottom: 15px; /* 增加底部间距 */
        color: var(--primary-color);
        padding-bottom: 10px; /* 添加底部内边距 */
        border-bottom: 1px solid #eee; /* 添加底部边框 */
    }

    .plugin-detail-changelog ul {
        padding-left: 25px; /* 增加左侧间距 */
    }

    .plugin-detail-changelog li {
        margin-bottom: 10px; /* 增加底部间距 */
        line-height: 1.6; /* 增加行高 */
        font-size: 1.05rem; /* 增加字体大小 */
    }

    .plugin-detail-usage p,
    .plugin-detail-requirements p,
    .plugin-detail-support p {
        line-height: 1.6; /* 增加行高 */
        font-size: 1.05rem; /* 增加字体大小 */
        margin-bottom: 10px; /* 增加底部间距 */
    }

    .plugin-detail-support a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500; /* 增加字体粗细 */
        transition: all 0.3s; /* 添加过渡效果 */
        padding: 2px 5px; /* 添加内边距 */
        border-radius: 3px; /* 添加圆角 */
    }

    .plugin-detail-support a:hover {
        text-decoration: none; /* 移除下划线 */
        background-color: rgba(255, 107, 149, 0.1); /* 添加背景色 */
    }

    /* 添加动画效果 */
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    /* 动画 */
    @keyframes fadeInUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style></head>
<body>
    <div class="loading-overlay" id="loading-overlay">
        <div class="spinner"></div>
    </div>

    <div class="dashboard-container">
        <!-- 引入通用头部导航 -->
        <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/header-nav.php'); ?>

            <!-- 面包屑导航 -->
            <?php
            // 设置当前页面标题
            $page_title = '插件列表';
            // 设置当前页面路径
            $page_path = [
                ['title' => '插件中心', 'url' => '/dashboard/plugins']
            ];
            // 包含面包屑导航组件
            include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/admin-breadcrumb.php');
            ?>



            <div class="action-bar">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="搜索插件...">
                    <button class="search-button"><i class="bi bi-search"></i></button>
                </div>
                <div class="filter-container">
                    <div class="filter-group">
                        <span class="filter-label">分类:</span>
                        <select id="category-filter" class="filter-select">
                            <option value="">全部分类</option>
                            <option value="头像处理">头像处理</option>
                            <option value="社交分享">社交分享</option>
                            <option value="数据分析">数据分析</option>
                            <option value="支付工具">支付工具</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 已安装插件 -->
            <div class="section-header">
                <h2>已安装插件</h2>
            </div>
            <div class="plugin-grid">
                <!-- 插件卡片1 -->
                <div class="plugin-card installed">
                    <div class="plugin-card-header">
                        <h3>AI头像优化</h3>
                        <span class="plugin-badge installed">已安装</span>
                    </div>
                    <div class="plugin-card-body">
                        <p class="plugin-description">使用先进的AI算法优化头像质量，提升匹配效果，支持多种风格调整。</p>
                        <div class="plugin-meta">
                            <span class="plugin-category">头像处理</span>
                            <span class="plugin-version">v1.2.0</span>
                        </div>
                    </div>
                    <div class="plugin-card-footer">
                        <button class="btn btn-outline-primary" onclick="showPluginDetails(1)"><i class="bi bi-info-circle"></i> 详情</button>
                    </div>
                </div>

                <!-- 插件卡片2 -->
                <div class="plugin-card installed">
                    <div class="plugin-card-header">
                        <h3>数据分析工具</h3>
                        <span class="plugin-badge installed">已安装</span>
                    </div>
                    <div class="plugin-card-body">
                        <p class="plugin-description">提供详细的用户行为分析和数据可视化，帮助优化运营策略和用户体验。</p>
                        <div class="plugin-meta">
                            <span class="plugin-category">数据分析</span>
                            <span class="plugin-version">v1.5.3</span>
                        </div>
                    </div>
                    <div class="plugin-card-footer">
                        <button class="btn btn-outline-primary" onclick="showPluginDetails(3)"><i class="bi bi-info-circle"></i> 详情</button>
                    </div>
                </div>
            </div>

            <!-- 未安装插件 -->
            <div class="section-header">
                <h2>未安装插件</h2>
            </div>
            <div class="plugin-grid">
                <!-- 插件卡片3 -->
                <div class="plugin-card not-installed">
                    <div class="plugin-card-header">
                        <h3>社交分享增强</h3>
                        <span class="plugin-badge not-installed">未安装</span>
                    </div>
                    <div class="plugin-card-body">
                        <p class="plugin-description">增强社交分享功能，支持微信、微博、QQ等多平台一键分享，提高用户粘性。</p>
                        <div class="plugin-meta">
                            <span class="plugin-category">社交分享</span>
                            <span class="plugin-version">v2.0.1</span>
                        </div>
                        <div class="plugin-price">
                            <span class="price">199</span>
                            <span class="price-original">299</span>
                        </div>
                    </div>
                    <div class="plugin-card-footer">
                        <button class="btn btn-outline-primary" onclick="showPluginDetails(2)"><i class="bi bi-info-circle"></i> 详情</button>
                        <button class="btn btn-primary" onclick="buyPlugin(2)"><i class="bi bi-cart-plus"></i> 购买</button>
                    </div>
                </div>

                <!-- 插件卡片4 -->
                <div class="plugin-card not-installed">
                    <div class="plugin-card-header">
                        <h3>支付通道整合</h3>
                        <span class="plugin-badge not-installed">未安装</span>
                    </div>
                    <div class="plugin-card-body">
                        <p class="plugin-description">整合多种支付方式，包括微信支付、支付宝、银联等，提供统一的支付接口和管理界面。</p>
                        <div class="plugin-meta">
                            <span class="plugin-category">支付工具</span>
                            <span class="plugin-version">v1.8.2</span>
                        </div>
                        <div class="plugin-price">
                            <span class="price">299</span>
                            <span class="price-original">399</span>
                        </div>
                    </div>
                    <div class="plugin-card-footer">
                        <button class="btn btn-outline-primary" onclick="showPluginDetails(4)"><i class="bi bi-info-circle"></i> 详情</button>
                        <button class="btn btn-primary" onclick="buyPlugin(4)"><i class="bi bi-cart-plus"></i> 购买</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 插件详情模态框 -->
    <div class="modal" id="plugin-detail-modal">
        <div class="modal-content" style="max-width: 800px; width: 95%;"> <!-- 增加宽度 -->
            <div class="modal-header">
                <h3>插件详情</h3>
                <button class="close-btn" onclick="closeModal('plugin-detail-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body" id="plugin-detail-content" style="max-height: 75vh; overflow-y: auto;"> <!-- 增加高度和滚动条 -->
                <!-- 插件详情内容将通过JavaScript动态填充 -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('plugin-detail-modal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 购买插件模态框 -->
    <div class="modal" id="buy-plugin-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>购买插件</h3>
                <button class="close-btn" onclick="closeModal('buy-plugin-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <div class="purchase-info">
                    <h4 id="buy-plugin-name">插件名称</h4>
                    <div class="purchase-price">
                        <span class="price" id="buy-plugin-price">0</span>
                    </div>
                    <div class="payment-methods">
                        <h5>选择支付方式</h5>
                        <div class="payment-options">
                            <label class="payment-option">
                                <input type="radio" name="payment-method" value="wechat" checked>
                                <div class="payment-icon"><i class="bi bi-wechat"></i></div>
                                <div class="payment-name">微信支付</div>
                            </label>
                            <label class="payment-option">
                                <input type="radio" name="payment-method" value="alipay">
                                <div class="payment-icon"><i class="bi bi-credit-card"></i></div>
                                <div class="payment-name">支付宝</div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('buy-plugin-modal')">取消</button>
                <button class="btn btn-primary" onclick="confirmPurchase()">确认购买</button>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div class="toast" id="toast">
        <div class="toast-content">
            <i class="bi bi-check-circle toast-icon success"></i>
            <i class="bi bi-x-circle toast-icon error"></i>
            <i class="bi bi-info-circle toast-icon info"></i>
            <div class="toast-message">操作成功</div>
        </div>
        <div class="toast-progress"></div>
    </div>
<script>
    // DOM元素
    const sidebar = document.getElementById("sidebar");
    const mainContent = document.getElementById("main-content");
    const menuToggle = document.getElementById("menu-toggle");
    const loadingOverlay = document.getElementById("loading-overlay");
    const categoryFilter = document.getElementById("category-filter");
    const toast = document.getElementById("toast");

    // 初始化
    document.addEventListener("DOMContentLoaded", function() {
        // 检查本地存储中的侧边栏状态
        const sidebarCollapsed = localStorage.getItem("sidebarCollapsed") === "true";
        if (sidebarCollapsed) {
            sidebar.classList.add("collapsed");
            mainContent.classList.add("expanded");
        }

        // 添加插件卡片的动画延迟
        const pluginCards = document.querySelectorAll(".plugin-card");
        pluginCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
        });

        // 隐藏加载动画
        hideLoading();
    });

    // 侧边栏切换
    menuToggle.addEventListener("click", function() {
        sidebar.classList.toggle("collapsed");
        mainContent.classList.toggle("expanded");
        localStorage.setItem("sidebarCollapsed", sidebar.classList.contains("collapsed"));
    });

    // 分类筛选
    categoryFilter.addEventListener("change", function() {
        const category = this.value;
        filterPlugins(category);
    });

    // 显示加载动画
    function showLoading() {
        loadingOverlay.classList.add("show");
    }

    // 隐藏加载动画
    function hideLoading() {
        loadingOverlay.classList.remove("show");
    }

    // 打开模态框
    function openModal(modalId) {
        document.getElementById(modalId).style.display = "block";
    }

    // 关闭模态框
    function closeModal(modalId) {
        document.getElementById(modalId).style.display = "none";
    }

    // 不再允许点击模态框外部关闭模态框
    // window.addEventListener("click", function(event) {
    //     if (event.target.classList.contains("modal")) {
    //         event.target.style.display = "none";
    //     }
    // });

    // 显示消息提示
    function showToast(message, type = "success") {
        const toastMessage = document.querySelector(".toast-message");
        const successIcon = document.querySelector(".toast-icon.success");
        const errorIcon = document.querySelector(".toast-icon.error");
        const infoIcon = document.querySelector(".toast-icon.info");

        // 设置消息
        toastMessage.textContent = message;

        // 设置图标
        successIcon.style.display = "none";
        errorIcon.style.display = "none";
        infoIcon.style.display = "none";

        if (type === "success") {
            successIcon.style.display = "block";
        } else if (type === "error") {
            errorIcon.style.display = "block";
        } else if (type === "info") {
            infoIcon.style.display = "block";
        }

        // 显示消息提示
        toast.classList.add("show");

        // 3秒后自动隐藏
        setTimeout(() => {
            toast.classList.remove("show");
        }, 3000);
    }

    // 筛选插件
    function filterPlugins(category) {
        showLoading();

        // 模拟API请求
        setTimeout(() => {
            const pluginCards = document.querySelectorAll(".plugin-card");

            pluginCards.forEach(card => {
                const cardCategory = card.querySelector(".plugin-category").textContent;

                if (category === "" || cardCategory === category) {
                    card.style.display = "flex";
                } else {
                    card.style.display = "none";
                }
            });

            hideLoading();
        }, 500);
    }

    // 显示插件详情
    function showPluginDetails(id) {
        showLoading();

        // 模拟API请求
        setTimeout(() => {
            hideLoading();

            // 根据插件ID设置不同的详情内容
            let pluginDetail = "";
            let isInstalled = false;
            let pluginPrice = "";
            let pluginName = "";

            // 获取插件操作按钮
            const pluginActionBtn = document.getElementById('plugin-action-btn');
            if (pluginActionBtn) {
                pluginActionBtn.style.display = 'none'; // 隐藏购买按钮
            }

            if (id === 1) {
                pluginName = "AI头像优化";
                pluginDetail = `
                    <div class="plugin-detail">
                        <div class="plugin-detail-header">
                            <h4>${pluginName}</h4>
                            <div class="plugin-detail-badges">
                                <span class="plugin-badge installed">已安装</span>
                                <span class="plugin-category">头像处理</span>
                            </div>
                        </div>
                        <div class="plugin-detail-info">
                            <p><strong>版本:</strong> 1.2.0</p>
                            <p><strong>作者:</strong> AI Labs</p>
                            <p><strong>更新日期:</strong> 2023-05-10</p>
                            <p><strong>描述:</strong> 使用先进的AI算法优化头像质量，提升匹配效果，支持多种风格调整。</p>
                        </div>
                        <div class="plugin-detail-features">
                            <h5>主要功能:</h5>
                            <ul>
                                <li>AI智能优化头像质量</li>
                                <li>自动识别并调整人脸位置</li>
                                <li>提供10+种艺术风格滤镜</li>
                                <li>批量处理功能</li>
                                <li>自定义参数调整</li>
                            </ul>
                        </div>
                        <div class="plugin-detail-requirements">
                            <h5>系统要求:</h5>
                            <p>PHP 7.4+, MySQL 5.7+, 2GB+ RAM</p>
                        </div>
                        <div class="plugin-detail-changelog">
                            <h5>更新日志:</h5>
                            <ul>
                                <li><strong>v1.2.0</strong> (2023-05-10): 添加批量处理功能，优化AI算法</li>
                                <li><strong>v1.1.5</strong> (2023-03-22): 修复人脸识别bug，提高处理速度</li>
                                <li><strong>v1.1.0</strong> (2023-02-15): 新增5种艺术风格滤镜</li>
                                <li><strong>v1.0.0</strong> (2023-01-01): 首次发布</li>
                            </ul>
                        </div>
                        <div class="plugin-detail-usage">
                            <h5>使用方法:</h5>
                            <p>安装后，在头像管理页面可以看到"AI优化"按钮，点击即可使用。也可以在批量处理页面选择多张头像进行处理。</p>
                        </div>
                        <div class="plugin-detail-support">
                            <h5>技术支持:</h5>
                            <p>邮箱: <EMAIL></p>
                            <p>文档: <a href="#" onclick="showToast('文档链接已复制到剪贴板', 'info'); return false;">https://docs.ailabs.com/avatar-optimizer</a></p>
                        </div>
                    </div>
                `;
                isInstalled = true;
            } else if (id === 2) {
                pluginName = "社交分享增强";
                pluginPrice = "199";
                pluginDetail = `
                    <div class="plugin-detail">
                        <div class="plugin-detail-header">
                            <h4>${pluginName}</h4>
                            <div class="plugin-detail-badges">
                                <span class="plugin-badge not-installed">未安装</span>
                                <span class="plugin-category">社交分享</span>
                            </div>
                        </div>
                        <div class="plugin-detail-info">
                            <p><strong>版本:</strong> 2.0.1</p>
                            <p><strong>作者:</strong> Social Tech</p>
                            <p><strong>更新日期:</strong> 2023-06-15</p>
                            <p><strong>价格:</strong> <span class="price">${pluginPrice}</span> <span class="price-original">299</span></p>
                            <p><strong>描述:</strong> 增强社交分享功能，支持微信、微博、QQ等多平台一键分享，提高用户粘性。</p>
                        </div>
                        <div class="plugin-detail-features">
                            <h5>主要功能:</h5>
                            <ul>
                                <li>支持微信、微博、QQ等多平台分享</li>
                                <li>自定义分享内容和图片</li>
                                <li>分享数据统计和分析</li>
                                <li>分享激励机制</li>
                                <li>好友邀请功能</li>
                            </ul>
                        </div>
                        <div class="plugin-detail-requirements">
                            <h5>系统要求:</h5>
                            <p>PHP 7.2+, MySQL 5.6+, 1GB+ RAM</p>
                        </div>
                        <div class="plugin-detail-changelog">
                            <h5>更新日志:</h5>
                            <ul>
                                <li><strong>v2.0.1</strong> (2023-06-15): 修复微信分享图片不显示问题</li>
                                <li><strong>v2.0.0</strong> (2023-05-20): 全新UI界面，新增好友邀请功能</li>
                                <li><strong>v1.5.2</strong> (2023-04-10): 新增抖音分享支持</li>
                                <li><strong>v1.0.0</strong> (2023-01-15): 首次发布</li>
                            </ul>
                        </div>
                        <div class="plugin-detail-usage">
                            <h5>使用方法:</h5>
                            <p>安装后，系统会自动在匹配结果页面添加社交分享按钮。管理员可在插件设置中自定义分享内容和图片。</p>
                        </div>
                        <div class="plugin-detail-support">
                            <h5>技术支持:</h5>
                            <p>邮箱: <EMAIL></p>
                            <p>文档: <a href="#" onclick="showToast('文档链接已复制到剪贴板', 'info'); return false;">https://docs.socialtech.com/share-enhancer</a></p>
                        </div>
                    </div>
                `;
                isInstalled = false;
            } else if (id === 3) {
                pluginName = "数据分析工具";
                pluginDetail = `
                    <div class="plugin-detail">
                        <div class="plugin-detail-header">
                            <h4>${pluginName}</h4>
                            <div class="plugin-detail-badges">
                                <span class="plugin-badge installed">已安装</span>
                                <span class="plugin-category">数据分析</span>
                            </div>
                        </div>
                        <div class="plugin-detail-info">
                            <p><strong>版本:</strong> 1.5.3</p>
                            <p><strong>作者:</strong> Data Insight</p>
                            <p><strong>更新日期:</strong> 2023-04-20</p>
                            <p><strong>描述:</strong> 提供详细的用户行为分析和数据可视化，帮助优化运营策略和用户体验。</p>
                        </div>
                        <div class="plugin-detail-features">
                            <h5>主要功能:</h5>
                            <ul>
                                <li>用户行为跟踪和分析</li>
                                <li>实时数据统计和报表</li>
                                <li>多维度数据可视化</li>
                                <li>用户画像生成</li>
                                <li>数据导出功能</li>
                            </ul>
                        </div>
                        <div class="plugin-detail-requirements">
                            <h5>系统要求:</h5>
                            <p>PHP 7.2+, MySQL 5.7+, 1.5GB+ RAM</p>
                        </div>
                        <div class="plugin-detail-changelog">
                            <h5>更新日志:</h5>
                            <ul>
                                <li><strong>v1.5.3</strong> (2023-04-20): 优化数据导出功能，新增Excel导出</li>
                                <li><strong>v1.5.0</strong> (2023-03-15): 新增用户画像功能</li>
                                <li><strong>v1.2.1</strong> (2023-02-08): 修复数据统计bug</li>
                                <li><strong>v1.0.0</strong> (2023-01-05): 首次发布</li>
                            </ul>
                        </div>
                        <div class="plugin-detail-usage">
                            <h5>使用方法:</h5>
                            <p>安装后，在控制面板会出现"数据分析"菜单，点击进入可查看各类数据报表和可视化图表。</p>
                        </div>
                        <div class="plugin-detail-support">
                            <h5>技术支持:</h5>
                            <p>邮箱: <EMAIL></p>
                            <p>文档: <a href="#" onclick="showToast('文档链接已复制到剪贴板', 'info'); return false;">https://docs.datainsight.com/analytics</a></p>
                        </div>
                    </div>
                `;
                isInstalled = true;
            } else if (id === 4) {
                pluginName = "支付通道整合";
                pluginPrice = "299";
                pluginDetail = `
                    <div class="plugin-detail">
                        <div class="plugin-detail-header">
                            <h4>${pluginName}</h4>
                            <div class="plugin-detail-badges">
                                <span class="plugin-badge not-installed">未安装</span>
                                <span class="plugin-category">支付工具</span>
                            </div>
                        </div>
                        <div class="plugin-detail-info">
                            <p><strong>版本:</strong> 1.8.2</p>
                            <p><strong>作者:</strong> Pay Solutions</p>
                            <p><strong>更新日期:</strong> 2023-07-05</p>
                            <p><strong>价格:</strong> <span class="price">${pluginPrice}</span> <span class="price-original">399</span></p>
                            <p><strong>描述:</strong> 整合多种支付方式，包括微信支付、支付宝、银联等，提供统一的支付接口和管理界面。</p>
                        </div>
                        <div class="plugin-detail-features">
                            <h5>主要功能:</h5>
                            <ul>
                                <li>支持微信支付、支付宝、银联等多种支付方式</li>
                                <li>统一的支付接口和回调处理</li>
                                <li>支付数据统计和分析</li>
                                <li>退款和订单管理</li>
                                <li>支付安全保障</li>
                            </ul>
                        </div>
                        <div class="plugin-detail-requirements">
                            <h5>系统要求:</h5>
                            <p>PHP 7.2+, MySQL 5.7+, 1GB+ RAM</p>
                        </div>
                        <div class="plugin-detail-changelog">
                            <h5>更新日志:</h5>
                            <ul>
                                <li><strong>v1.8.2</strong> (2023-07-05): 新增银联支付功能</li>
                                <li><strong>v1.8.0</strong> (2023-06-20): 优化支付流程，提高成功率</li>
                                <li><strong>v1.5.1</strong> (2023-04-15): 修复支付宝回调问题</li>
                                <li><strong>v1.0.0</strong> (2023-02-10): 首次发布</li>
                            </ul>
                        </div>
                        <div class="plugin-detail-usage">
                            <h5>使用方法:</h5>
                            <p>安装后，在系统设置中会出现"支付设置"选项，可以配置各种支付方式的参数。系统会自动在支付页面显示已启用的支付方式。</p>
                        </div>
                        <div class="plugin-detail-support">
                            <h5>技术支持:</h5>
                            <p>邮箱: <EMAIL></p>
                            <p>文档: <a href="#" onclick="showToast('文档链接已复制到剪贴板', 'info'); return false;">https://docs.paysolutions.com/payment-gateway</a></p>
                        </div>
                    </div>
                `;
                isInstalled = false;
            }

            // 填充插件详情内容
            document.getElementById("plugin-detail-content").innerHTML = pluginDetail;

            // 设置按钮状态
            const actionBtn = document.getElementById("plugin-action-btn");
            if (isInstalled) {
                actionBtn.style.display = "none";
            } else {
                actionBtn.style.display = "inline-block";
                actionBtn.textContent = "购买";
                actionBtn.onclick = function() {
                    closeModal("plugin-detail-modal");
                    buyPlugin(id, pluginName, pluginPrice);
                };
            }

            // 打开模态框
            openModal("plugin-detail-modal");
        }, 500);
    }

    // 购买插件
    function buyPlugin(id, name, price) {
        // 设置购买信息
        document.getElementById("buy-plugin-name").textContent = name || "插件" + id;
        document.getElementById("buy-plugin-price").textContent = price || "0";

        // 打开购买模态框
        openModal("buy-plugin-modal");
    }

    // 确认购买
    function confirmPurchase() {
        showLoading();

        // 模拟API请求
        setTimeout(() => {
            hideLoading();
            closeModal("buy-plugin-modal");
            showToast("购买成功！插件已添加到您的账户。", "success", true);
        }, 800);
    }
</script>

    <!-- 消息提示 -->
    <div class="toast" id="toast">
        <div class="toast-content">
            <i class="bi bi-check-circle-fill toast-icon success"></i>
            <i class="bi bi-x-circle-fill toast-icon error"></i>
            <i class="bi bi-info-circle-fill toast-icon info"></i>
            <div class="toast-message">操作成功</div>
        </div>
        <div class="toast-progress"></div>
    </div>

    <!-- 引入通用JS文件 -->
    <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/footer-scripts.php'); ?>

<script>
    // DOM元素
    const searchButton = document.querySelector(".search-button");
    // 避免重复声明
    if (typeof window.categoryFilter === 'undefined') {
        window.categoryFilter = document.getElementById("category-filter");
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 显示加载动画
        showLoading();

        // 添加插件卡片的动画延迟
        const pluginCards = document.querySelectorAll(".plugin-card");
        pluginCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
        });

        // 添加搜索功能
        if (searchButton) {
            searchButton.addEventListener("click", function() {
                searchPlugins();
            });
        }

        // 添加分类筛选功能
        if (categoryFilter) {
            categoryFilter.addEventListener("change", function() {
                const category = this.value;
                filterPlugins(category);
            });
        }

        // 隐藏加载动画
        setTimeout(hideLoading, 500);
    });

    // 侧边栏切换
    menuToggle.addEventListener('click', function() {
        sidebar.classList.toggle('collapsed');
        mainContent.classList.toggle('expanded');
        localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
    });

    // 显示加载动画
    function showLoading() {
        loadingOverlay.classList.add('show');
    }

    // 隐藏加载动画
    function hideLoading() {
        loadingOverlay.classList.remove('show');
    }

    // 显示消息提示
    function showToast(message, type = 'success') {
        const toastMessage = document.querySelector('.toast-message');
        const successIcon = document.querySelector('.toast-icon.success');
        const errorIcon = document.querySelector('.toast-icon.error');
        const infoIcon = document.querySelector('.toast-icon.info');

        // 设置消息
        toastMessage.textContent = message;

        // 设置图标
        successIcon.style.display = 'none';
        errorIcon.style.display = 'none';
        infoIcon.style.display = 'none';

        if (type === 'success') {
            successIcon.style.display = 'block';
        } else if (type === 'error') {
            errorIcon.style.display = 'block';
        } else if (type === 'info') {
            infoIcon.style.display = 'block';
        }

        // 显示消息提示
        toast.classList.add('show');

        // 3秒后自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }

    // 搜索插件
    function searchPlugins() {
        const searchInput = document.querySelector(".search-input");
        const searchTerm = searchInput.value.trim();

        if (!searchTerm) {
            showToast("请输入搜索关键词", "info");
            return;
        }

        showLoading();

        // 模拟搜索请求
        setTimeout(() => {
            hideLoading();
            showToast(`已搜索: ${searchTerm}`, "success");

            // 模拟筛选结果
            const pluginCards = document.querySelectorAll(".plugin-card");
            let found = false;

            pluginCards.forEach(card => {
                const title = card.querySelector("h3").textContent.toLowerCase();
                const description = card.querySelector(".plugin-description").textContent.toLowerCase();

                if (title.includes(searchTerm.toLowerCase()) || description.includes(searchTerm.toLowerCase())) {
                    card.style.display = "flex";
                    found = true;
                } else {
                    card.style.display = "none";
                }
            });

            if (!found) {
                showToast("未找到匹配的插件", "info");
            }
        }, 500);
    }

    // 筛选插件
    function filterPlugins(category) {
        showLoading();

        // 模拟筛选请求
        setTimeout(() => {
            hideLoading();

            if (category === "all") {
                showToast("显示所有插件", "info");

                // 显示所有插件
                const pluginCards = document.querySelectorAll(".plugin-card");
                pluginCards.forEach(card => {
                    card.style.display = "flex";
                });
            } else {
                showToast(`已筛选: ${category}类插件`, "success");

                // 筛选插件
                const pluginCards = document.querySelectorAll(".plugin-card");
                let found = false;

                pluginCards.forEach(card => {
                    const cardCategory = card.getAttribute("data-category");

                    if (cardCategory === category) {
                        card.style.display = "flex";
                        found = true;
                    } else {
                        card.style.display = "none";
                    }
                });

                if (!found) {
                    showToast(`未找到${category}类插件`, "info");
                }
            }
        }, 500);
    }

    // 显示插件详情
    function showPluginDetails(id) {
        showLoading();

        // 模拟API请求
        setTimeout(() => {
            hideLoading();

            // 获取插件信息
            const pluginCards = document.querySelectorAll(".plugin-card");
            let pluginName = "未知插件";
            let pluginDescription = "";
            let pluginPrice = "0";
            let pluginCategory = "";
            let pluginVersion = "1.0.0";
            let pluginAuthor = "未知";

            pluginCards.forEach(card => {
                if (card.getAttribute("data-id") == id) {
                    pluginName = card.querySelector("h3").textContent;
                    pluginDescription = card.querySelector(".plugin-description").textContent;
                    pluginPrice = card.querySelector(".plugin-price").textContent;
                    pluginCategory = card.getAttribute("data-category");
                }
            });

            // 打开详情模态框
            document.getElementById("detail-plugin-name").textContent = pluginName;
            document.getElementById("detail-plugin-description").textContent = pluginDescription;
            document.getElementById("detail-plugin-price").textContent = pluginPrice;
            document.getElementById("detail-plugin-category").textContent = pluginCategory;
            document.getElementById("detail-plugin-version").textContent = pluginVersion;
            document.getElementById("detail-plugin-author").textContent = pluginAuthor;

            openModal("plugin-detail-modal");
        }, 800);
    }

    // 安装插件
    function installPlugin(id) {
        showLoading();

        // 模拟API请求
        setTimeout(() => {
            hideLoading();
            showToast('插件安装成功', 'success', true);
        }, 800);
    }
</script>
<!-- 插件详情模态框 -->
<div class="modal" id="plugin-detail-modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>插件详情</h3>
            <button class="close-btn" onclick="closeModal('plugin-detail-modal')"><i class="bi bi-x-lg"></i></button>
        </div>
        <div class="modal-body">
            <div class="plugin-detail-container">
                <div class="plugin-detail-header">
                    <h4 id="detail-plugin-name">插件名称</h4>
                    <div class="plugin-detail-meta">
                        <span class="plugin-detail-price" id="detail-plugin-price">¥0</span>
                        <span class="plugin-detail-category" id="detail-plugin-category">分类</span>
                        <span class="plugin-detail-version" id="detail-plugin-version">版本: 1.0.0</span>
                        <span class="plugin-detail-author" id="detail-plugin-author">作者: 未知</span>
                    </div>
                </div>
                <div class="plugin-detail-description">
                    <h5>插件描述:</h5>
                    <p id="detail-plugin-description">插件描述内容</p>
                </div>
                <div class="plugin-detail-features">
                    <h5>主要功能:</h5>
                    <ul>
                        <li>功能特点1</li>
                        <li>功能特点2</li>
                        <li>功能特点3</li>
                        <li>功能特点4</li>
                        <li>功能特点5</li>
                    </ul>
                </div>
                <div class="plugin-detail-requirements">
                    <h5>系统要求:</h5>
                    <p>PHP 7.2+, MySQL 5.7+, 1GB+ RAM</p>
                </div>
                <div class="plugin-detail-screenshots">
                    <h5>插件截图:</h5>
                    <div class="screenshot-gallery">
                        <div class="screenshot">
                            <img src="/assets/images/plugin-screenshot-placeholder.jpg" alt="插件截图">
                        </div>
                        <div class="screenshot">
                            <img src="/assets/images/plugin-screenshot-placeholder.jpg" alt="插件截图">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeModal('plugin-detail-modal')">关闭</button>
            <button class="btn btn-primary" id="plugin-action-btn" onclick="buyPlugin(1, '插件名称', '¥99')">购买</button>
        </div>
    </div>
</div>

<style>
/* 插件详情样式 */
.plugin-detail-container {
    padding: 0 10px;
}

.plugin-detail-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.plugin-detail-header h4 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: var(--dark-color);
}

.plugin-detail-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    font-size: 0.9rem;
}

.plugin-detail-price {
    font-weight: 600;
    color: var(--primary-color);
}

.plugin-detail-category,
.plugin-detail-version,
.plugin-detail-author {
    color: var(--gray-color);
}

.plugin-detail-description,
.plugin-detail-features,
.plugin-detail-requirements {
    margin-bottom: 20px;
}

.plugin-detail-description h5,
.plugin-detail-features h5,
.plugin-detail-requirements h5,
.plugin-detail-screenshots h5 {
    font-size: 1.1rem;
    margin-bottom: 10px;
    color: var(--dark-color);
}

.plugin-detail-features ul {
    padding-left: 20px;
}

.plugin-detail-features li {
    margin-bottom: 5px;
}

.screenshot-gallery {
    display: flex;
    gap: 10px;
    overflow-x: auto;
    padding-bottom: 10px;
}

.screenshot {
    flex: 0 0 auto;
    width: 200px;
    height: 120px;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 5px var(--shadow-color);
}

.screenshot img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
</style>

<!-- 网站底部 -->
<?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/site-footer.php'); ?>

<script src="/assets/js/sidebar-toggle-fix.js"></script>
<script src="/assets/js/responsive-layout.js"></script>
<script src="/assets/js/avatar-theme-fix.js"></script>

<!-- 模态框精确修复脚本 -->
<script src="/assets/js/modal-precise-fix.js"></script>

<!-- 插件增强功能 -->
<script src="/assets/js/plugin-updates.js"></script>
</body>
</html>



