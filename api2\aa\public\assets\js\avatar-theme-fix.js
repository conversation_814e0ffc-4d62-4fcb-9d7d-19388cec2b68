/**
 * 头像主题颜色修复脚本
 * 确保头像颜色随主题变化
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('头像主题颜色修复脚本已加载');

    // 监听主题颜色变化
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                updateAvatarColor();
            }
        });
    });

    // 开始观察 document.documentElement 的 style 属性变化
    observer.observe(document.documentElement, { attributes: true, attributeFilter: ['style'] });

    // 初始更新头像颜色
    updateAvatarColor();

    // 更新头像颜色函数
    function updateAvatarColor() {
        // 获取当前主题颜色
        const primaryColor = getComputedStyle(document.documentElement).getPropertyValue('--primary-color').trim();
        
        console.log('当前主题颜色:', primaryColor);

        // 获取所有头像元素
        const avatars = document.querySelectorAll('.user-avatar');
        
        if (avatars.length > 0) {
            console.log('找到头像元素数量:', avatars.length);
            
            // 更新所有头像的背景颜色
            avatars.forEach(avatar => {
                avatar.style.backgroundColor = primaryColor;
                avatar.style.color = '#ffffff'; // 确保文字颜色为白色
            });
            
            console.log('头像颜色已更新为:', primaryColor);
        } else {
            console.error('未找到头像元素');
        }
    }

    // 监听本地存储变化
    window.addEventListener('storage', function(e) {
        if (e.key === 'primary-color') {
            console.log('检测到主题颜色变化:', e.newValue);
            updateAvatarColor();
        }
    });

    // 额外监听可能的主题切换事件
    document.addEventListener('themeChanged', function(e) {
        console.log('检测到主题变化事件');
        updateAvatarColor();
    });

    // 监听颜色选项点击
    const colorOptions = document.querySelectorAll('.color-option');
    if (colorOptions.length > 0) {
        colorOptions.forEach(option => {
            option.addEventListener('click', function() {
                // 延迟执行，确保主题颜色已更新
                setTimeout(updateAvatarColor, 50);
            });
        });
    }
});
