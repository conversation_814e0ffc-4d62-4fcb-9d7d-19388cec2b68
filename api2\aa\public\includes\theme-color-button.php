<?php
/**
 * 主题颜色修改按钮组件
 *
 * 在面包屑导航的最右侧添加主题颜色修改按钮
 * 参考小程序详情页面的主题颜色修改按钮设计
 */
?>

<!-- 主题颜色修改按钮 -->
<div class="theme-color-btn" id="theme-color-btn" title="修改主题颜色">
    <i class="bi bi-palette"></i>
    <span>主题</span>
</div>

<style>
/* 主题颜色修改按钮样式 */
.theme-color-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5px 12px;
    border-radius: 20px;
    background-color: var(--primary-color);
    color: white;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    margin-left: 10px;
    font-size: 14px;
}

.theme-color-btn i {
    margin-right: 5px;
}

.theme-color-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* 响应式调整 */
@media (max-width: 576px) {
    .theme-color-btn {
        padding: 4px 10px;
        font-size: 0.9rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取主题颜色按钮
    const themeColorBtn = document.getElementById('theme-color-btn');

    // 确保主题颜色选择器存在
    let themeColorPicker = document.getElementById('theme-color-picker');

    // 如果不存在，则创建
    if (!themeColorPicker) {
        // 创建主题颜色选择器
        themeColorPicker = document.createElement('div');
        themeColorPicker.className = 'theme-color-picker';
        themeColorPicker.id = 'theme-color-picker';

        // 设置内容
        themeColorPicker.innerHTML = `
            <div class="theme-color-header">
                <h5>选择主题颜色</h5>
                <button class="theme-color-close" id="theme-color-close">×</button>
            </div>
            <div class="theme-color-body">
                <div class="theme-color-option active" style="background-color: #ff6b95;" data-color="#ff6b95"></div>
                <div class="theme-color-option" style="background-color: #3498db;" data-color="#3498db"></div>
                <div class="theme-color-option" style="background-color: #2ecc71;" data-color="#2ecc71"></div>
                <div class="theme-color-option" style="background-color: #9b59b6;" data-color="#9b59b6"></div>
                <div class="theme-color-option" style="background-color: #e74c3c;" data-color="#e74c3c"></div>
                <div class="theme-color-option" style="background-color: #f39c12;" data-color="#f39c12"></div>
                <div class="theme-color-option" style="background-color: #1abc9c;" data-color="#1abc9c"></div>
                <div class="theme-color-option" style="background-color: #34495e;" data-color="#34495e"></div>
            </div>
        `;

        // 添加到文档
        document.body.appendChild(themeColorPicker);

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            /* 主题颜色选择器样式 */
            .theme-color-picker {
                position: fixed;
                top: 80px;
                right: 20px;
                background-color: white;
                border-radius: 10px;
                box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
                z-index: 9999;
                width: 250px;
                display: none;
                animation: slideInRight 0.3s ease-out;
                transition: all 0.3s ease;
                opacity: 0;
                transform: translateX(20px);
            }

            .theme-color-picker.show {
                opacity: 1;
                transform: translateX(0);
            }

            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            .theme-color-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px 20px;
                border-bottom: 1px solid #eee;
                background-color: var(--primary-color);
                color: white;
                border-radius: 10px 10px 0 0;
            }

            .theme-color-header h5 {
                margin: 0;
                font-size: 16px;
                font-weight: 600;
            }

            .theme-color-close {
                background: none;
                border: none;
                color: white;
                font-size: 20px;
                cursor: pointer;
                padding: 0;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: all 0.2s;
            }

            .theme-color-close:hover {
                background-color: rgba(255, 255, 255, 0.2);
            }

            .theme-color-body {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 15px;
                padding: 20px;
            }

            .theme-color-option {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                cursor: pointer;
                transition: all 0.3s;
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                position: relative;
            }

            .theme-color-option:hover {
                transform: scale(1.1);
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            }

            .theme-color-option.active {
                border: 3px solid #fff;
                box-shadow: 0 0 0 2px var(--primary-color);
            }

            .theme-color-option.active:after {
                content: '✓';
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                color: white;
                font-size: 18px;
                font-weight: bold;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }

            /* 响应式调整 */
            @media (max-width: 576px) {
                .theme-color-picker {
                    width: 280px;
                }

                .theme-color-body {
                    gap: 10px;
                }

                .theme-color-option {
                    width: 35px;
                    height: 35px;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 获取关闭按钮和颜色选项
    const themeColorClose = document.getElementById('theme-color-close');
    const themeColorOptions = document.querySelectorAll('.theme-color-option');

    // 点击按钮显示颜色选择器
    themeColorBtn.addEventListener('click', function(e) {
        e.stopPropagation(); // 阻止事件冒泡
        themeColorPicker.style.display = 'block';

        // 添加动画效果
        setTimeout(() => {
            themeColorPicker.classList.add('show');
        }, 10);
    });

    // 点击关闭按钮隐藏颜色选择器
    if (themeColorClose) {
        themeColorClose.addEventListener('click', function(e) {
            e.stopPropagation(); // 阻止事件冒泡
            themeColorPicker.classList.remove('show');

            // 等待动画完成后隐藏
            setTimeout(() => {
                themeColorPicker.style.display = 'none';
            }, 300);
        });
    }

    // 点击颜色选项更改主题颜色
    themeColorOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            e.stopPropagation(); // 阻止事件冒泡

            const color = this.getAttribute('data-color');

            // 更新活动状态
            themeColorOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');

            // 更新主题颜色
            document.documentElement.style.setProperty('--primary-color', color);

            // 计算RGB值
            const rgbColor = hexToRgb(color);
            document.documentElement.style.setProperty('--primary-color-rgb', rgbColor);

            // 计算辅助色
            const secondaryColor = adjustColor(color, 30);
            document.documentElement.style.setProperty('--secondary-color', secondaryColor);

            // 计算暗色版本
            const darkColor = adjustColor(color, -30);
            document.documentElement.style.setProperty('--primary-dark', darkColor);

            // 保存颜色到本地存储
            localStorage.setItem('primary-color', color);

            // 更新主题按钮颜色
            themeColorBtn.style.backgroundColor = color;

            // 更新主题颜色选择器头部颜色
            const themeColorHeader = document.querySelector('.theme-color-header');
            if (themeColorHeader) {
                themeColorHeader.style.backgroundColor = color;
            }

            // 更新侧边栏颜色
            const sidebar = document.querySelector('.miniprogram-sidebar') || document.querySelector('.sidebar');
            if (sidebar) {
                sidebar.style.background = `linear-gradient(135deg, ${color} 0%, ${secondaryColor} 100%)`;
            }

            // 关闭颜色选择器
            themeColorPicker.classList.remove('show');

            // 等待动画完成后隐藏
            setTimeout(() => {
                themeColorPicker.style.display = 'none';
            }, 300);

            // 显示提示
            if (typeof showToast === 'function') {
                showToast('success', '主题颜色已更新');
            }
        });
    });

    // 点击其他区域关闭颜色选择器
    document.addEventListener('click', function(event) {
        if (themeColorPicker && themeColorPicker.style.display === 'block' &&
            !themeColorPicker.contains(event.target) &&
            event.target !== themeColorBtn &&
            !themeColorBtn.contains(event.target)) {

            // 移除显示类，触发动画
            themeColorPicker.classList.remove('show');

            // 等待动画完成后隐藏
            setTimeout(() => {
                themeColorPicker.style.display = 'none';
            }, 300);
        }
    });

    // 加载保存的主题颜色
    const savedColor = localStorage.getItem('primary-color');
    if (savedColor) {
        // 更新主题颜色
        document.documentElement.style.setProperty('--primary-color', savedColor);

        // 计算RGB值
        const rgbColor = hexToRgb(savedColor);
        document.documentElement.style.setProperty('--primary-color-rgb', rgbColor);

        // 计算辅助色
        const secondaryColor = adjustColor(savedColor, 30);
        document.documentElement.style.setProperty('--secondary-color', secondaryColor);

        // 计算暗色版本
        const darkColor = adjustColor(savedColor, -30);
        document.documentElement.style.setProperty('--primary-dark', darkColor);

        // 更新主题按钮颜色
        themeColorBtn.style.backgroundColor = savedColor;

        // 更新主题颜色选择器头部颜色
        const themeColorHeader = document.querySelector('.theme-color-header');
        if (themeColorHeader) {
            themeColorHeader.style.backgroundColor = savedColor;
        }

        // 更新活动状态
        themeColorOptions.forEach(option => {
            if (option.getAttribute('data-color') === savedColor) {
                option.classList.add('active');
            } else {
                option.classList.remove('active');
            }
        });
    }

    // 辅助函数：十六进制颜色转RGB
    function hexToRgb(hex) {
        // 移除#号
        hex = hex.replace('#', '');

        // 解析RGB值
        const r = parseInt(hex.substring(0, 2), 16);
        const g = parseInt(hex.substring(2, 4), 16);
        const b = parseInt(hex.substring(4, 6), 16);

        return `${r}, ${g}, ${b}`;
    }

    // 辅助函数：调整颜色
    function adjustColor(color, amount) {
        // 移除#号
        color = color.replace('#', '');

        // 将十六进制颜色转换为RGB
        let r = parseInt(color.substring(0, 2), 16);
        let g = parseInt(color.substring(2, 4), 16);
        let b = parseInt(color.substring(4, 6), 16);

        // 调整RGB值
        r = Math.max(0, Math.min(255, r + amount));
        g = Math.max(0, Math.min(255, g + amount));
        b = Math.max(0, Math.min(255, b + amount));

        // 将RGB转换回十六进制
        return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    }
});
</script>
