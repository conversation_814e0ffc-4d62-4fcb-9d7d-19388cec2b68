/**
 * 模态框简单修复脚本
 * 修复模态框点击空白处关闭的问题
 * 确保模态框可以正常打开和关闭
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('模态框简单修复脚本已加载');
    
    // 修复所有模态框
    fixAllModals();
    
    // 重写模态框打开和关闭函数
    overrideModalFunctions();
});

/**
 * 修复所有模态框
 */
function fixAllModals() {
    // 获取所有模态框
    const modals = document.querySelectorAll('.modal');
    
    if (modals.length > 0) {
        console.log('找到模态框：', modals.length, '个');
        
        // 为每个模态框添加点击事件
        modals.forEach(modal => {
            // 阻止模态框点击事件冒泡
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    e.stopPropagation();
                    e.preventDefault();
                    console.log('点击了模态框空白处，阻止关闭');
                    return false;
                }
            });
        });
    } else {
        console.log('未找到模态框');
    }
}

/**
 * 重写模态框打开和关闭函数
 */
function overrideModalFunctions() {
    // 保存原始函数
    if (typeof window.originalOpenModal === 'undefined' && typeof window.openModal === 'function') {
        window.originalOpenModal = window.openModal;
    }
    
    if (typeof window.originalCloseModal === 'undefined' && typeof window.closeModal === 'function') {
        window.originalCloseModal = window.closeModal;
    }
    
    // 重写openModal函数
    window.openModal = function(modalId) {
        console.log('打开模态框：', modalId);
        const modal = document.getElementById(modalId);
        if (modal) {
            // 显示模态框
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
            
            // 确保模态框有点击事件处理
            if (!modal._hasClickHandler) {
                // 阻止模态框点击事件冒泡
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        e.stopPropagation();
                        e.preventDefault();
                        console.log('点击了模态框空白处，阻止关闭');
                        return false;
                    }
                });
                modal._hasClickHandler = true;
            }
        }
    };
    
    // 重写closeModal函数
    window.closeModal = function(modalId) {
        console.log('关闭模态框：', modalId);
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    };
}
