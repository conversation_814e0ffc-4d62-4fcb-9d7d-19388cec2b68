/**
 * 统计值修复脚本
 * 确保控制面板页面服务信息板块的值正确显示
 */

document.addEventListener('DOMContentLoaded', function() {
    // 修复服务信息板块的值
    const statValues = document.querySelectorAll('.stat-value');
    
    // 检查是否有空值，如果有则填充默认值
    statValues.forEach(value => {
        if (!value.textContent.trim()) {
            // 根据标签内容设置默认值
            const label = value.closest('.stat-item').querySelector('.stat-label');
            if (label) {
                const labelText = label.textContent.trim();
                
                if (labelText.includes('授权时长')) {
                    value.textContent = '永久';
                } else if (labelText.includes('当前版本')) {
                    value.textContent = 'v1.0.2';
                } else if (labelText.includes('服务期限')) {
                    value.textContent = '2024-12-31';
                } else if (labelText.includes('系统时间')) {
                    // 系统时间由另一个脚本处理
                } else if (labelText.includes('邮箱')) {
                    value.textContent = '<EMAIL>';
                } else if (labelText.includes('用户名')) {
                    value.textContent = 'admin';
                } else if (labelText.includes('角色')) {
                    value.textContent = '管理员';
                } else if (labelText.includes('运行环境')) {
                    if (value.parentElement.classList.contains('stat-value-multiline')) {
                        const values = value.parentElement.querySelectorAll('.stat-value');
                        if (values.length >= 2) {
                            values[0].textContent = 'PHP 7.4';
                            values[1].textContent = 'MySQL 5.7';
                        }
                    } else {
                        value.textContent = 'PHP 7.4 + MySQL 5.7';
                    }
                }
            }
        }
    });
    
    // 确保所有的stat-value-with-btn中的按钮正确显示
    const statValueWithBtns = document.querySelectorAll('.stat-value-with-btn');
    statValueWithBtns.forEach(container => {
        const value = container.querySelector('.stat-value');
        const button = container.querySelector('.btn');
        
        if (value && button) {
            // 确保值和按钮之间有足够的间距
            value.style.marginBottom = '8px';
            
            // 确保按钮样式正确
            button.classList.add('btn-sm');
            if (!button.classList.contains('btn-primary') && !button.classList.contains('btn-outline-primary')) {
                button.classList.add('btn-outline-primary');
            }
        }
    });
    
    console.log('统计值修复脚本已加载');
});
