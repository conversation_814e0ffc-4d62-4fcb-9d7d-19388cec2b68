﻿<?php
// 开启会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 启用错误报告
ini_set('display_errors', 1);
error_reporting(E_ALL);

// 设置字符编码
header('Content-Type: text/html; charset=utf-8');

// 记录请求信息
$logFile = __DIR__ . '/../../../storage/logs/direct_login.log';
$logDir = dirname($logFile);
if (!is_dir($logDir)) {
    mkdir($logDir, 0777, true);
}

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 处理登录请求
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - 直接登录请求开始\n", FILE_APPEND);
    file_put_contents($logFile, "POST 数据: " . print_r($_POST, true) . "\n", FILE_APPEND);
    file_put_contents($logFile, "SESSION 数据: " . print_r($_SESSION, true) . "\n", FILE_APPEND);
    
    // 检查是否是 AJAX 请求
    $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
    
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    $captcha = $_POST['captcha'] ?? '';
    
    file_put_contents($logFile, "用户名: $username, 密码: [已隐藏], 验证码: $captcha\n", FILE_APPEND);
    
    if (!$username || !$password || !$captcha) {
        file_put_contents($logFile, "错误: 用户名、密码或验证码为空\n", FILE_APPEND);
        
        if ($isAjax) {
            // 返回 JSON 响应
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['success' => false, 'message' => '用户名、密码和验证码不能为空'], JSON_UNESCAPED_UNICODE);
        } else {
            // 返回重定向
            header('Location: /admin/login?error=' . urlencode('用户名、密码和验证码不能为空'));
        }
        exit;
    }
    
    // 验证验证码
    if (!isset($_SESSION['captcha']) || strtolower($_SESSION['captcha']) !== strtolower($captcha)) {
        file_put_contents($logFile, "错误: 验证码不正确. 会话验证码: " . ($_SESSION['captcha'] ?? '未设置') . ", 提交验证码: $captcha\n", FILE_APPEND);
        
        if ($isAjax) {
            // 返回 JSON 响应
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['success' => false, 'message' => '验证码错误或已过期'], JSON_UNESCAPED_UNICODE);
        } else {
            // 返回重定向
            header('Location: /admin/login?error=' . urlencode('验证码错误或已过期'));
        }
        exit;
    }
    
    // 清除验证码
    unset($_SESSION['captcha']);
    unset($_SESSION['captcha_time']);
    
    // 简单的用户验证（示例）
    if ($username === 'admin' && $password === 'admin') {
        // 设置会话
        $_SESSION['admin_id'] = 1;
        $_SESSION['admin_username'] = $username;
        $_SESSION['admin_is_super'] = true;
        
        file_put_contents($logFile, "登录成功, 用户名: $username\n", FILE_APPEND);
        
        if ($isAjax) {
            // 返回 JSON 响应
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['success' => true, 'message' => '登录成功', 'redirect' => '/admin/dashboard'], JSON_UNESCAPED_UNICODE);
        } else {
            // 返回重定向
            header('Location: /admin/dashboard');
        }
        exit;
    } else {
        file_put_contents($logFile, "错误: 用户名或密码不正确\n", FILE_APPEND);
        
        if ($isAjax) {
            // 返回 JSON 响应
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode(['success' => false, 'message' => '用户名或密码不正确'], JSON_UNESCAPED_UNICODE);
        } else {
            // 返回重定向
            header('Location: /admin/login?error=' . urlencode('用户名或密码不正确'));
        }
        exit;
    }
} else {
    // 显示登录表单
    // 检查是否已经登录
    if (isset($_SESSION['admin_id'])) {
        header('Location: /admin/dashboard');
        exit;
    }
    
    // 显示登录页面
    header('Content-Type: text/html; charset=utf-8');
    readfile(__DIR__ . '/index.html');
    exit;
}