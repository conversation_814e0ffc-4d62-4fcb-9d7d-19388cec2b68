/**
 * 统一提示信息函数 - 支持自动刷新功能
 * 使所有页面的提示信息函数与续费成功提示信息函数一致
 */

// 显示消息提示 - 支持新的options API
function showToast(message, type = 'success', options = {}) {
    console.log('toast-unified.js showToast被调用:', { message, type, options });

    // 兼容旧的API（第三个参数是boolean）
    if (typeof options === 'boolean') {
        options = { autoRefresh: options };
    }

    // 移除现有的提示
    const existingToasts = document.querySelectorAll('.toast');
    existingToasts.forEach(toast => {
        if (document.body.contains(toast)) {
            document.body.removeChild(toast);
        }
    });

    // 创建新提示
    const toast = document.createElement('div');
    toast.className = 'toast';

    const toastContent = document.createElement('div');
    toastContent.className = 'toast-content';

    const icon = document.createElement('i');
    if (type === 'success') {
        icon.className = 'bi bi-check-circle-fill toast-icon success';
        icon.style.display = 'block';
        icon.style.color = 'var(--success-color)';
    } else if (type === 'error') {
        icon.className = 'bi bi-x-circle-fill toast-icon error';
        icon.style.display = 'block';
        icon.style.color = 'var(--danger-color)';
    } else {
        icon.className = 'bi bi-info-circle-fill toast-icon info';
        icon.style.display = 'block';
        icon.style.color = 'var(--info-color)';
    }

    const toastMessage = document.createElement('div');
    toastMessage.className = 'toast-message';
    toastMessage.textContent = message;

    const toastProgress = document.createElement('div');
    toastProgress.className = 'toast-progress';

    toastContent.appendChild(icon);
    toastContent.appendChild(toastMessage);
    toast.appendChild(toastContent);
    toast.appendChild(toastProgress);

    document.body.appendChild(toast);

    // 显示提示
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);

    // 设置显示时间
    let duration = options.duration || 2000;

    // 如果是成功消息且需要刷新，在进度条80%时刷新
    console.log('检查自动刷新条件:', {
        type,
        'type === success': type === 'success',
        'options.autoRefresh': options.autoRefresh,
        '条件满足': type === 'success' && options.autoRefresh
    });

    if (type === 'success' && options.autoRefresh) {
        console.log('进入自动刷新逻辑');
        // 直接基于CSS动画时间计算，确保与进度条同步
        const progressAnimationTime = 2000; // 与CSS中的2s保持一致
        const refreshTime = progressAnimationTime * 0.8; // 进度条80%时刷新（1600ms）

        // 重新设置duration为进度条时间，确保toast不会提前消失
        duration = progressAnimationTime;

        console.log(`将在${refreshTime}ms后刷新页面（进度条80%时）`);

        // 在75%时添加视觉提示（1500ms）
        setTimeout(() => {
            console.log('显示即将刷新提示');
            toastMessage.textContent = message + ' 即将刷新...';
        }, progressAnimationTime * 0.75);

        // 在80%时刷新页面（1600ms）
        setTimeout(() => {
            console.log('开始刷新页面');
            if (options.refreshCallback && typeof options.refreshCallback === 'function') {
                options.refreshCallback();
            } else {
                location.reload();
            }
        }, refreshTime);

        // 不需要自动隐藏，因为页面会刷新
        return;
    }

    // 自动隐藏提示（仅非自动刷新的情况）
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, duration);
}

// 当文档加载完成后，替换所有页面的showToast函数
document.addEventListener('DOMContentLoaded', function() {
    // 将全局的showToast函数替换为我们的统一函数
    window.showToast = showToast;

    console.log('Toast函数已统一，支持自动刷新功能');
});
