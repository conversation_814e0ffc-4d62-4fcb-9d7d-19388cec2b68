# 本地资源文件下载指南

为了避免使用CDN资源导致的加载问题，请下载以下资源文件并放置在指定目录中。

## 已有的本地资源

项目中已经包含以下本地资源：
- Bootstrap CSS: `/assets/vendor/bootstrap/bootstrap.min.css`
- Bootstrap JS: `/assets/vendor/bootstrap/bootstrap.bundle.min.js`
- Bootstrap Icons CSS: `/assets/vendor/bootstrap-icons/bootstrap-icons.css`
- Bootstrap Icons 字体: `/assets/vendor/bootstrap-icons/fonts/bootstrap-icons.woff` 和 `.woff2`
- Animate CSS: `/assets/vendor/animate/animate.min.css`

## 需要下载的资源

### 1. Highlight.js

**下载地址**：
- https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js

**放置位置**：
- `/assets/vendor/highlight/highlight.min.js`

**说明**：
- 已创建占位文件，但需要下载完整版本以确保功能正常

### 2. 其他可能需要的资源

如果您发现网站中还有其他使用CDN的资源，请按照以下格式添加到本地：

1. 创建适当的目录结构，例如：`/assets/vendor/[库名称]/`
2. 下载资源文件并放置在创建的目录中
3. 修改HTML文件中的引用路径，将CDN路径替换为本地路径

## 注意事项

- 确保下载的文件版本与原CDN版本一致，以避免兼容性问题
- 下载完成后，请测试网站功能是否正常
- 如果遇到问题，可以检查浏览器控制台中的错误信息

## 资源文件结构

```
public/assets/vendor/
├── animate/
│   └── animate.min.css
├── bootstrap/
│   ├── bootstrap.bundle.min.js
│   └── bootstrap.min.css
├── bootstrap-icons/
│   ├── bootstrap-icons.css
│   └── fonts/
│       ├── bootstrap-icons.woff
│       └── bootstrap-icons.woff2
├── highlight/
│   ├── atom-one-dark.min.css
│   └── highlight.min.js (需下载)
└── chart/
    └── chart.min.js
```
