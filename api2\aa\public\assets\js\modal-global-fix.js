/**
 * 模态框全局修复脚本
 * 修复所有页面的模态框问题
 */

document.addEventListener('DOMContentLoaded', function() {
    // 修复模态框点击空白处关闭的问题
    const modals = document.querySelectorAll('.modal');
    
    modals.forEach(modal => {
        // 移除原有的点击事件
        modal.removeEventListener('click', closeModalOnOutsideClick);
        
        // 添加新的点击事件，只有点击右上角的×才会关闭
        modal.addEventListener('click', function(e) {
            // 如果点击的是模态框本身，不做任何操作
            if (e.target === this) {
                e.stopPropagation();
            }
        });
    });
    
    // 修复账户管理页面编辑按钮点击没有弹窗模态框的问题
    const editButtons = document.querySelectorAll('.edit');
    
    editButtons.forEach(button => {
        // 移除原有的onclick属性
        const onclickValue = button.getAttribute('onclick');
        if (onclickValue) {
            const match = onclickValue.match(/openEditModal\((\d+)\)/);
            if (match) {
                const id = match[1];
                button.removeAttribute('onclick');
                
                // 添加新的点击事件
                button.addEventListener('click', function() {
                    openEditModal(id);
                });
            }
        }
    });
    
    // 修复重置密码按钮点击事件
    const resetButtons = document.querySelectorAll('.reset-pwd');
    
    resetButtons.forEach(button => {
        // 移除原有的onclick属性
        const onclickValue = button.getAttribute('onclick');
        if (onclickValue) {
            const match = onclickValue.match(/openResetPwdModal\((\d+)\)/);
            if (match) {
                const id = match[1];
                button.removeAttribute('onclick');
                
                // 添加新的点击事件
                button.addEventListener('click', function() {
                    openResetPwdModal(id);
                });
            }
        }
    });
    
    // 修复新增账户按钮点击事件
    const addAccountBtn = document.getElementById('add-account-btn');
    if (addAccountBtn) {
        addAccountBtn.addEventListener('click', function() {
            openModal('add-account-modal');
        });
    }
    
    // 修复关闭按钮点击事件
    const closeButtons = document.querySelectorAll('.close-btn');
    
    closeButtons.forEach(button => {
        // 移除原有的onclick属性
        const onclickValue = button.getAttribute('onclick');
        if (onclickValue) {
            const match = onclickValue.match(/closeModal\('(.+)'\)/);
            if (match) {
                const modalId = match[1];
                button.removeAttribute('onclick');
                
                // 添加新的点击事件
                button.addEventListener('click', function() {
                    closeModal(modalId);
                });
            }
        }
    });
    
    // 修复模态框中的取消按钮点击事件
    const cancelButtons = document.querySelectorAll('.modal-footer .btn-secondary');
    
    cancelButtons.forEach(button => {
        // 移除原有的onclick属性
        const onclickValue = button.getAttribute('onclick');
        if (onclickValue) {
            const match = onclickValue.match(/closeModal\('(.+)'\)/);
            if (match) {
                const modalId = match[1];
                button.removeAttribute('onclick');
                
                // 添加新的点击事件
                button.addEventListener('click', function() {
                    closeModal(modalId);
                });
            }
        }
    });
    
    // 添加表单验证
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        const submitButton = form.parentElement.parentElement.querySelector('.btn-primary');
        if (submitButton) {
            const originalOnclick = submitButton.getAttribute('onclick');
            if (originalOnclick) {
                submitButton.removeAttribute('onclick');
                
                // 添加新的点击事件
                submitButton.addEventListener('click', function() {
                    // 检查表单是否有必填项未填
                    const requiredInputs = form.querySelectorAll('[required]');
                    let isValid = true;
                    
                    requiredInputs.forEach(input => {
                        if (!input.value.trim()) {
                            isValid = false;
                            input.style.borderColor = 'red';
                            
                            // 添加输入事件，当用户输入时恢复正常样式
                            input.addEventListener('input', function() {
                                if (this.value.trim()) {
                                    this.style.borderColor = '';
                                }
                            });
                        } else {
                            input.style.borderColor = '';
                        }
                    });
                    
                    if (!isValid) {
                        showToast('请填写必填项', 'error');
                        return;
                    }
                    
                    // 执行原有的点击事件
                    eval(originalOnclick);
                });
            }
        }
    });
    
    console.log('模态框全局修复脚本已加载');
});

// 辅助函数
function closeModalOnOutsideClick(e) {
    if (e.target === this) {
        this.style.display = 'none';
    }
}
