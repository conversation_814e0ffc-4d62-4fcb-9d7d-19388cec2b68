﻿<?php
// 包含身份验证检查
include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/auth-check.php');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账户管理 - 情侣头像匹配系统</title>
    <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/header-scripts.php'); ?>
    <link rel="stylesheet" href="/assets/css/accounts-fix.css">
    <link rel="stylesheet" href="/assets/css/accounts-modal-fix.css">
<style>

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: "Microsoft YaHei", sans-serif;
    }

    body {
        background-color: #f5f7fa;
        color: #333;
        font-size: 14px;
        line-height: 1.5;
        overflow-x: hidden;
    }

    /* 加载动画 */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s;
    }

    .loading-overlay.show {
        opacity: 1;
        visibility: visible;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 107, 149, 0.3);
        border-top: 4px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* 布局样式 */
    .dashboard-container {
        display: flex;
        min-height: 100vh;
    }

    /* 侧边栏样式 */
    .sidebar {
        width: 250px;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: var(--white-color);
        padding: 20px 0;
        position: fixed;
        height: 100vh;
        overflow-y: auto;
        z-index: 1000;
        box-shadow: 2px 0 10px var(--shadow-color);
        transition: all var(--transition-speed);
    }

    .sidebar.collapsed {
        width: 70px;
    }

    .sidebar-header {
        padding: 0 20px 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        text-align: center;
        margin-bottom: 20px;
    }

    .sidebar-logo {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        margin-bottom: 10px;
        object-fit: cover;
        background-color: rgba(255, 255, 255, 0.2);
    }

    .sidebar-title {
        color: var(--white-color);
        font-size: 1.5rem;
        margin-bottom: 5px;
        font-weight: 600;
    }

    .sidebar-subtitle {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
    }

    .sidebar.collapsed .sidebar-title,
    .sidebar.collapsed .sidebar-subtitle {
        display: none;
    }

    .sidebar-menu {
        list-style: none;
        padding: 0;
    }

    .sidebar-menu li {
        margin-bottom: 5px;
    }

    .sidebar-menu li a {
        color: var(--white-color);
        text-decoration: none;
        display: block;
        padding: 12px 20px;
        transition: all var(--transition-speed);
        border-left: 4px solid transparent;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .sidebar-menu li a:hover,
    .sidebar-menu li a.active {
        background-color: rgba(255, 255, 255, 0.2);
        border-left-color: var(--white-color);
    }

    .sidebar-menu li a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
        font-size: 1.1rem;
    }

    .sidebar.collapsed .sidebar-menu li a span {
        display: none;
    }

    .sidebar.collapsed .sidebar-menu li a i {
        margin-right: 0;
        font-size: 1.3rem;
    }

    /* 主内容区域样式 */
    .main-content {
        flex: 1;
        margin-left: 250px;
        padding: 20px;
        transition: all var(--transition-speed);
    }

    .main-content.expanded {
        margin-left: 70px;
    }

    /* 头部样式 */
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }

    .menu-toggle {
        background: none;
        border: none;
        color: var(--dark-color);
        font-size: 1.5rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        transition: all var(--transition-speed);
    }

    .menu-toggle:hover {
        background-color: var(--light-color);
    }

    .user-info {
        display: flex;
        align-items: center;
        position: relative;
        cursor: pointer;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: var(--white-color);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 10px;
    }

    .user-name {
        font-weight: 500;
    }

    .dropdown-content {
        position: absolute;
        top: 100%;
        right: 0;
        background-color: var(--white-color);
        min-width: 160px;
        box-shadow: 0 8px 16px 0 var(--shadow-color);
        border-radius: var(--border-radius);
        padding: 10px 0;
        z-index: 1;
        display: none;
        animation: fadeIn 0.3s;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .user-info:hover .dropdown-content {
        display: block;
    }

    .dropdown-content a {
        color: var(--dark-color);
        padding: 10px 20px;
        text-decoration: none;
        display: block;
        transition: all var(--transition-speed);
    }

    .dropdown-content a:hover {
        background-color: var(--light-color);
    }

    .dropdown-content a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }

    /* 操作栏样式 */
    .action-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .search-container {
        display: flex;
        align-items: center;
        background-color: var(--white-color);
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: 0 2px 5px var(--shadow-color);
        width: 300px;
    }

    .search-input {
        flex: 1;
        border: none;
        padding: 10px 15px;
        outline: none;
        font-size: 14px;
    }

    .search-button {
        background-color: var(--primary-color);
        color: var(--white-color);
        border: none;
        padding: 10px 15px;
        cursor: pointer;
        transition: all var(--transition-speed);
    }

    .search-button:hover {
        background-color: #ff4f7e;
    }

    /* 按钮样式 */
    .btn {
        display: inline-block;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        user-select: none;
        border: 1px solid transparent;
        padding: 0.5rem 1rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: var(--border-radius);
        transition: all var(--transition-speed);
        cursor: pointer;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .btn:active {
        transform: translateY(0);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .btn-primary {
        color: var(--white-color);
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        border-color: var(--primary-color);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #ff4f7e 0%, #ffa5c0 100%);
        border-color: #ff4f7e;
    }

    .btn-secondary {
        color: var(--white-color);
        background-color: var(--gray-color);
        border-color: var(--gray-color);
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #545b62;
    }

    .btn-danger {
        color: var(--white-color);
        background-color: var(--danger-color);
        border-color: var(--danger-color);
    }

    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }

    .btn-outline-primary {
        color: var(--primary-color);
        background-color: transparent;
        border-color: var(--primary-color);
    }

    .btn-outline-primary:hover {
        color: var(--white-color);
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }

    /* 表格样式 */
    .table-container {
        background-color: var(--white-color);
        border-radius: var(--border-radius);
        box-shadow: 0 2px 5px var(--shadow-color);
        overflow: hidden;
        margin-bottom: 20px;
        animation: fadeInUp 0.5s;
    }

    @keyframes fadeInUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .table {
        width: 100%;
        border-collapse: collapse;
    }

    .table th,
    .table td {
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid #eee;
    }

    .table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
    }

    .table tr:last-child td {
        border-bottom: none;
    }

    .table tr:hover td {
        background-color: #f8f9fa;
    }

    .status {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
    }

    .status.active {
        background-color: rgba(40, 167, 69, 0.1);
        color: var(--success-color);
    }

    .status.inactive {
        background-color: rgba(220, 53, 69, 0.1);
        color: var(--danger-color);
    }

    .actions {
        display: flex;
        gap: 5px;
    }

    .actions button {
        width: auto;
        height: 32px;
        border-radius: 16px;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all var(--transition-speed);
        padding: 0 12px;
    }

    .actions button i {
        margin-right: 5px;
    }

    .actions .edit {
        background-color: rgba(255, 193, 7, 0.1);
        color: var(--warning-color);
    }

    .actions .edit:hover {
        background-color: var(--warning-color);
        color: var(--white-color);
    }

    .actions .reset-pwd {
        background-color: rgba(23, 162, 184, 0.1);
        color: var(--info-color);
    }

    .actions .reset-pwd:hover {
        background-color: var(--info-color);
        color: var(--white-color);
    }

    /* 分页样式 */
    .pagination-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;
    }

    .pagination-info {
        color: var(--gray-color);
    }

    .pagination-info span {
        font-weight: 500;
        color: var(--dark-color);
    }

    .pagination-controls {
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .pagination {
        display: flex;
        align-items: center;
    }

    .pagination button {
        width: 32px;
        height: 32px;
        border-radius: 4px;
        border: 1px solid #eee;
        background-color: var(--white-color);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all var(--transition-speed);
        margin: 0 2px;
    }

    .pagination button:hover:not(:disabled) {
        background-color: #f8f9fa;
    }

    .pagination button.active {
        background-color: var(--primary-color);
        color: var(--white-color);
        border-color: var(--primary-color);
    }

    .pagination button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .pagination-jump {
        display: flex;
        align-items: center;
        margin-left: 10px;
    }

    .pagination-jump span {
        margin: 0 5px;
        color: var(--gray-color);
    }

    .pagination-jump input {
        width: 40px;
        height: 32px;
        border-radius: 4px;
        border: 1px solid #eee;
        text-align: center;
        padding: 0 5px;
    }

    .pagination-jump button {
        width: auto;
        padding: 0 10px;
    }

    .pagination-size {
        display: flex;
        align-items: center;
    }

    .pagination-size span {
        margin: 0 5px;
        color: var(--gray-color);
    }

    .pagination-size select {
        height: 32px;
        border-radius: 4px;
        border: 1px solid #eee;
        padding: 0 5px;
    }

    /* 模态框样式 */
    .modal {
        display: none;
        position: fixed;
        z-index: 1050;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0, 0, 0, 0.5);
        animation: fadeIn 0.3s;
    }

    .modal-content {
        background-color: var(--white-color);
        margin: 5% auto;
        width: 600px;
        max-width: 90%;
        border-radius: var(--border-radius);
        box-shadow: 0 5px 15px var(--shadow-color);
        animation: slideDown 0.3s;
    }

    .modal-body {
        padding: 20px;
        max-height: 70vh;
        overflow-y: auto;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: var(--dark-color);
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: var(--border-radius);
        font-size: 14px;
        transition: all var(--transition-speed);
        background-color: #f9f9f9;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(255, 107, 149, 0.25);
        outline: none;
        background-color: var(--white-color);
    }

    .form-group.checkbox {
        display: flex;
        align-items: center;
    }

    .form-group.checkbox input {
        width: auto;
        margin-right: 10px;
    }

    .required {
        color: var(--danger-color);
    }

    /* 消息提示样式 */
    .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 300px;
        background-color: var(--white-color);
        border-radius: var(--border-radius);
        box-shadow: 0 5px 15px var(--shadow-color);
        overflow: hidden;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transform: translateX(30px);
        transition: all 0.3s;
    }

    .toast.show {
        opacity: 1;
        visibility: visible;
        transform: translateX(0);
    }

    .toast-content {
        display: flex;
        align-items: center;
        padding: 15px;
    }

    .toast-icon {
        font-size: 1.5rem;
        margin-right: 15px;
        display: none;
    }

    .toast-icon.success {
        color: var(--success-color);
    }

    .toast-icon.error {
        color: var(--danger-color);
    }

    .toast-icon.info {
        color: var(--info-color);
    }

    .toast-message {
        flex: 1;
        font-weight: 500;
    }

    .toast-progress {
        height: 3px;
        background-color: var(--primary-color);
        width: 100%;
        animation: progress 3s linear;
    }

    @keyframes progress {
        from { width: 100%; }
        to { width: 0%; }
    }

    /* 响应式样式 */
    @media (max-width: 768px) {
        .sidebar {
            width: 70px;
        }

        .sidebar-title,
        .sidebar-subtitle {
            display: none;
        }

        .sidebar-menu li a span {
            display: none;
        }

        .sidebar-menu li a i {
            margin-right: 0;
            font-size: 1.3rem;
        }

        .main-content {
            margin-left: 70px;
        }

        .action-bar {
            flex-direction: column;
            align-items: stretch;
            gap: 10px;
        }

        .search-container {
            width: 100%;
        }

        .pagination-container {
            flex-direction: column;
            gap: 10px;
        }

        .pagination-controls {
            flex-direction: column;
            gap: 10px;
        }
    }
</style>

</head>
<body>
    <div class="loading-overlay" id="loading-overlay">
        <div class="spinner"></div>
    </div>

    <div class="dashboard-container">
        <!-- 引入通用头部导航 -->
        <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/header-nav.php'); ?>

            <!-- 面包屑导航 -->
            <?php
            // 设置当前页面标题
            $page_title = '列表';
            // 设置当前页面路径
            $page_path = [
                ['title' => '账户管理', 'url' => '/dashboard/accounts']
            ];
            // 包含面包屑导航组件
            include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/admin-breadcrumb.php');
            ?>

            <div class="action-bar">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="搜索用户名或手机号...">
                    <button class="search-button"><i class="bi bi-search"></i></button>
                </div>
                <button class="btn btn-primary" id="add-account-btn">新增账户</button>
            </div>

            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>手机号码</th>
                            <th>邮箱</th>
                            <th>可创建小程序</th>
                            <th>已创建小程序</th>
                            <th>状态</th>
                            <th>有效期</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>test001</td>
                            <td>***********</td>
                            <td><EMAIL></td>
                            <td>5</td>
                            <td>3</td>
                            <td><span class="status active">启用</span></td>
                            <td>2024-12-31</td>
                            <td>2023-01-15</td>
                            <td class="actions">
                                <button class="edit" onclick="openEditModal(1)" title="编辑"><i class="bi bi-pencil"></i> <span class="btn-text">编辑</span></button>
                                <button class="reset-pwd" onclick="openResetPwdModal(1)" title="重置密码"><i class="bi bi-key"></i> <span class="btn-text">重置密码</span></button>
                            </td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>test002</td>
                            <td>***********</td>
                            <td><EMAIL></td>
                            <td>3</td>
                            <td>1</td>
                            <td><span class="status inactive">禁用</span></td>
                            <td>2024-06-30</td>
                            <td>2023-02-20</td>
                            <td class="actions">
                                <button class="edit" onclick="openEditModal(2)" title="编辑"><i class="bi bi-pencil"></i> <span class="btn-text">编辑</span></button>
                                <button class="reset-pwd" onclick="openResetPwdModal(2)" title="重置密码"><i class="bi bi-key"></i> <span class="btn-text">重置密码</span></button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="pagination-container">
                <div class="pagination-info">
                    显示 <span>1-10</span> 条，共 <span>25</span> 条
                </div>
                <div class="pagination-controls">
                    <div class="pagination">
                        <button disabled><i class="bi bi-chevron-left"></i></button>
                        <button class="active">1</button>
                        <button>2</button>
                        <button>3</button>
                        <button><i class="bi bi-chevron-right"></i></button>
                        <div class="pagination-jump">
                            <span>跳至</span>
                            <input type="number" min="1" max="3" value="1">
                            <span>页</span>
                            <button>GO</button>
                        </div>
                    </div>
                    <div class="pagination-size">
                        <span>每页显示</span>
                        <select>
                            <option value="10" selected>10</option>
                            <option value="20">20</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span>条</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增账户模态框 -->
    <div class="modal" id="add-account-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>新增账户</h3>
                <button class="close-btn" onclick="closeModal('add-account-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <form id="add-account-form">
                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="username">用户名 <span class="required">*</span></label>
                                <input type="text" id="username" required>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label for="password">密码 <span class="required">*</span></label>
                                <input type="password" id="password" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="phone">手机号码 <span class="required">*</span></label>
                                <input type="tel" id="phone" required>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label for="email">邮箱 <span class="required">*</span></label>
                                <input type="email" id="email" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="max-platforms-number">可创建小程序数量 <span class="required">*</span></label>
                                <div style="display: flex; align-items: center;">
                                    <input type="number" id="max-platforms-number" min="1" value="3" style="flex: 1; margin-right: 10px;" required>
                                    <div class="form-group checkbox" style="margin-bottom: 0;">
                                        <input type="checkbox" id="unlimited-platforms" onchange="toggleUnlimitedPlatforms()">
                                        <label for="unlimited-platforms">不限制</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label for="expiry-date">到期日期 <span class="required">*</span></label>
                                <div style="display: flex; align-items: center;">
                                    <input type="date" id="expiry-date" style="flex: 1; margin-right: 10px;" required>
                                    <div class="form-group checkbox" style="margin-bottom: 0;">
                                        <input type="checkbox" id="permanent-account" onchange="togglePermanentAccount()">
                                        <label for="permanent-account">永久</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group checkbox">
                        <input type="checkbox" id="account-status" checked>
                        <label for="account-status">启用账户</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('add-account-modal')">取消</button>
                <button class="btn btn-primary" onclick="addAccount()">确定</button>
            </div>
        </div>
    </div>

    <!-- 编辑账户模态框 -->
    <div class="modal" id="edit-account-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑账户</h3>
                <button class="close-btn" onclick="closeModal('edit-account-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <form id="edit-account-form">
                    <input type="hidden" id="edit-account-id">

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="edit-username">用户名 <span class="required">*</span></label>
                                <input type="text" id="edit-username" required>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label for="edit-phone">手机号码 <span class="required">*</span></label>
                                <input type="tel" id="edit-phone" required>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="edit-email">邮箱 <span class="required">*</span></label>
                                <input type="email" id="edit-email" required>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group checkbox">
                                <input type="checkbox" id="edit-account-status">
                                <label for="edit-account-status">启用账户</label>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-col">
                            <div class="form-group">
                                <label for="edit-max-platforms-number">可创建小程序数量 <span class="required">*</span></label>
                                <div style="display: flex; align-items: center;">
                                    <input type="number" id="edit-max-platforms-number" min="1" value="3" style="flex: 1; margin-right: 10px;" required>
                                    <div class="form-group checkbox" style="margin-bottom: 0;">
                                        <input type="checkbox" id="edit-unlimited-platforms" onchange="toggleEditUnlimitedPlatforms()">
                                        <label for="edit-unlimited-platforms">不限制</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-col">
                            <div class="form-group">
                                <label for="edit-expiry-date">到期日期 <span class="required">*</span></label>
                                <div style="display: flex; align-items: center;">
                                    <input type="date" id="edit-expiry-date" style="flex: 1; margin-right: 10px;" required>
                                    <div class="form-group checkbox" style="margin-bottom: 0;">
                                        <input type="checkbox" id="edit-permanent-account" onchange="toggleEditPermanentAccount()">
                                        <label for="edit-permanent-account">永久</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('edit-account-modal')">取消</button>
                <button class="btn btn-primary" onclick="updateAccount()">保存</button>
            </div>
        </div>
    </div>

    <!-- 重置密码模态框 -->
    <div class="modal" id="reset-pwd-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>重置密码</h3>
                <button class="close-btn" onclick="closeModal('reset-pwd-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <p>确定要重置该账户的密码吗？</p>
                <input type="hidden" id="reset-account-id">
                <div class="form-row">
                    <div class="form-col">
                        <div class="form-group">
                            <label for="new-password">新密码 <span class="required">*</span></label>
                            <input type="password" id="new-password" required>
                        </div>
                    </div>
                    <div class="form-col">
                        <div class="form-group">
                            <label for="confirm-password">确认密码 <span class="required">*</span></label>
                            <input type="password" id="confirm-password" required>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('reset-pwd-modal')">取消</button>
                <button class="btn btn-primary" onclick="resetPassword()">确定</button>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div class="toast" id="toast">
        <div class="toast-content">
            <i class="bi bi-check-circle-fill toast-icon success"></i>
            <i class="bi bi-x-circle-fill toast-icon error"></i>
            <i class="bi bi-info-circle-fill toast-icon info"></i>
            <div class="toast-message">操作成功</div>
        </div>
        <div class="toast-progress"></div>
    </div>

    <!-- 引入通用JS文件 -->
    <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/footer-scripts.php'); ?>
<script>
    // DOM元素
    const addAccountBtn = document.getElementById("add-account-btn");
    const searchButton = document.querySelector(".search-button");

    // 初始化
    document.addEventListener("DOMContentLoaded", function() {
        // 显示加载动画
        showLoading();

        // 添加表格行的动画延迟
        const tableRows = document.querySelectorAll(".table tbody tr");
        tableRows.forEach((row, index) => {
            row.style.animationDelay = `${index * 0.1}s`;
        });

        // 添加状态切换功能
        const statusElements = document.querySelectorAll(".status");
        statusElements.forEach(status => {
            status.addEventListener("click", function() {
                toggleStatus(this);
            });
        });

        // 添加搜索功能
        if (searchButton) {
            searchButton.addEventListener("click", function() {
                searchAccounts();
            });
        }

        // 初始化有效期字段
        toggleExpiryDateField();
        toggleEditExpiryDateField();

        // 隐藏加载动画
        setTimeout(hideLoading, 500);
    });

    // 新增账户按钮点击事件
    if (addAccountBtn) {
        addAccountBtn.addEventListener("click", function() {
            openModal("add-account-modal");
        });
    }

    // 搜索账户
    function searchAccounts() {
        const searchInput = document.querySelector(".search-input");
        const searchTerm = searchInput.value.trim();

        if (!searchTerm) {
            showToast("请输入搜索关键词", "info");
            return;
        }

        showLoading();

        // 模拟搜索请求
        setTimeout(() => {
            hideLoading();
            showToast(`已搜索: ${searchTerm}`, "success");
        }, 500);
    }

    // 切换状态
    function toggleStatus(element) {
        showLoading();

        // 获取当前状态
        const isActive = element.classList.contains("active");
        const accountId = element.getAttribute("data-id") || element.closest("tr").getAttribute("data-id");

        // 模拟API请求
        setTimeout(() => {
            hideLoading();

            // 切换状态
            if (isActive) {
                element.classList.remove("active");
                element.classList.add("inactive");
                element.textContent = "禁用";
                showToast("账户已禁用", "info");
            } else {
                element.classList.remove("inactive");
                element.classList.add("active");
                element.textContent = "启用";
                showToast("账户已启用", "success");
            }
        }, 500);
    }

    // 打开编辑模态框
    function openEditModal(id) {
        console.log('打开编辑模态框，ID：', id);
        showLoading();

        // 模拟API请求获取账户数据
        setTimeout(() => {
            hideLoading();

            // 模拟数据
            const accountData = {
                id: id,
                username: id === 1 ? "test001" : "test002",
                phone: id === 1 ? "***********" : "***********",
                email: id === 1 ? "<EMAIL>" : "<EMAIL>",
                maxPlatforms: id === 1 ? "unlimited" : 3,
                expiryDate: id === 1 ? "permanent" : "2024-06-30",
                status: id === 1 ? 1 : 0
            };

            // 填充表单
            document.getElementById("edit-account-id").value = accountData.id;
            document.getElementById("edit-username").value = accountData.username;
            document.getElementById("edit-phone").value = accountData.phone;
            document.getElementById("edit-email").value = accountData.email;

            // 处理可创建小程序数量
            const maxPlatformsInput = document.getElementById("edit-max-platforms-number");
            const unlimitedCheckbox = document.getElementById("edit-unlimited-platforms");

            if (accountData.maxPlatforms === "unlimited") {
                unlimitedCheckbox.checked = true;
                maxPlatformsInput.value = "不限制";
                maxPlatformsInput.disabled = true;
                maxPlatformsInput.style.backgroundColor = "#f5f5f5";
                maxPlatformsInput.style.color = "#666";
                maxPlatformsInput.removeAttribute("required");
            } else {
                unlimitedCheckbox.checked = false;
                maxPlatformsInput.value = accountData.maxPlatforms;
                maxPlatformsInput.disabled = false;
                maxPlatformsInput.style.backgroundColor = "";
                maxPlatformsInput.style.color = "";
                maxPlatformsInput.setAttribute("required", "required");
            }

            // 处理有效期
            const expiryDateInput = document.getElementById("edit-expiry-date");
            const permanentCheckbox = document.getElementById("edit-permanent-account");

            if (accountData.expiryDate === "permanent") {
                permanentCheckbox.checked = true;
                expiryDateInput.value = "永久";
                expiryDateInput.disabled = true;
                expiryDateInput.style.backgroundColor = "#f5f5f5";
                expiryDateInput.style.color = "#666";
                expiryDateInput.removeAttribute("required");
            } else {
                permanentCheckbox.checked = false;
                expiryDateInput.value = accountData.expiryDate;
                expiryDateInput.disabled = false;
                expiryDateInput.style.backgroundColor = "";
                expiryDateInput.style.color = "";
                expiryDateInput.setAttribute("required", "required");
            }

            document.getElementById("edit-account-status").checked = accountData.status === 1;

            // 打开模态框
            openModal("edit-account-modal");
        }, 500);
    }

    // 更新账户
    function updateAccount() {
        console.log('更新账户函数被调用');
        const username = document.getElementById("edit-username").value;
        const phone = document.getElementById("edit-phone").value;
        const email = document.getElementById("edit-email").value;
        const maxPlatformsInput = document.getElementById("edit-max-platforms-number");
        const unlimitedCheckbox = document.getElementById("edit-unlimited-platforms");
        const expiryDateInput = document.getElementById("edit-expiry-date");
        const permanentCheckbox = document.getElementById("edit-permanent-account");
        const accountStatus = document.getElementById("edit-account-status").checked;

        if (!username || !phone || !email) {
            showToast("请填写必填项", "error");
            return;
        }

        // 检查可创建小程序数量
        if (!unlimitedCheckbox.checked && (!maxPlatformsInput.value || maxPlatformsInput.value <= 0)) {
            showToast("请输入有效的可创建小程序数量", "error");
            return;
        }

        // 检查到期日期
        if (!permanentCheckbox.checked && !expiryDateInput.value) {
            showToast("请选择到期日期", "error");
            return;
        }

        showLoading();
        // 模拟API请求
        setTimeout(() => {
            hideLoading();
            closeModal("edit-account-modal");
            showToast("账户更新成功", "success", true); // 更新成功后刷新页面

            // 刷新页面或更新UI
            // location.reload();
        }, 800);
    }

    // 打开重置密码模态框
    function openResetPwdModal(id) {
        document.getElementById("reset-account-id").value = id;
        openModal("reset-pwd-modal");
    }

    // 重置密码
    function resetPassword() {
        const newPassword = document.getElementById("new-password").value;
        const confirmPassword = document.getElementById("confirm-password").value;

        if (!newPassword || !confirmPassword) {
            showToast("请输入密码", "error");
            return;
        }

        if (newPassword !== confirmPassword) {
            showToast("两次输入的密码不一致", "error");
            return;
        }

        showLoading();
        // 模拟API请求
        setTimeout(() => {
            hideLoading();
            closeModal("reset-pwd-modal");
            showToast("密码重置成功", "success", true); // 重置成功后刷新页面

            // 清空表单
            document.getElementById("new-password").value = "";
            document.getElementById("confirm-password").value = "";
        }, 800);
    }

    // 切换不限制小程序数量
    function toggleUnlimitedPlatforms() {
        const unlimitedCheckbox = document.getElementById("unlimited-platforms");
        const maxPlatformsInput = document.getElementById("max-platforms-number");

        if (unlimitedCheckbox.checked) {
            maxPlatformsInput.value = "不限制";
            maxPlatformsInput.disabled = true;
            maxPlatformsInput.style.backgroundColor = "#f5f5f5";
            maxPlatformsInput.style.color = "#666";
            maxPlatformsInput.removeAttribute("required");
        } else {
            maxPlatformsInput.value = "3";
            maxPlatformsInput.disabled = false;
            maxPlatformsInput.style.backgroundColor = "";
            maxPlatformsInput.style.color = "";
            maxPlatformsInput.setAttribute("required", "required");
        }
    }

    // 切换永久账户
    function togglePermanentAccount() {
        const permanentCheckbox = document.getElementById("permanent-account");
        const expiryDateInput = document.getElementById("expiry-date");

        if (permanentCheckbox.checked) {
            expiryDateInput.value = "永久";
            expiryDateInput.disabled = true;
            expiryDateInput.style.backgroundColor = "#f5f5f5";
            expiryDateInput.style.color = "#666";
            expiryDateInput.removeAttribute("required");
        } else {
            // 设置默认日期为一年后
            const oneYearLater = new Date();
            oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);
            expiryDateInput.value = oneYearLater.toISOString().split('T')[0];
            expiryDateInput.disabled = false;
            expiryDateInput.style.backgroundColor = "";
            expiryDateInput.style.color = "";
            expiryDateInput.setAttribute("required", "required");
        }
    }

    // 切换编辑模态框中的不限制小程序数量
    function toggleEditUnlimitedPlatforms() {
        const unlimitedCheckbox = document.getElementById("edit-unlimited-platforms");
        const maxPlatformsInput = document.getElementById("edit-max-platforms-number");

        if (unlimitedCheckbox.checked) {
            maxPlatformsInput.value = "不限制";
            maxPlatformsInput.disabled = true;
            maxPlatformsInput.style.backgroundColor = "#f5f5f5";
            maxPlatformsInput.style.color = "#666";
            maxPlatformsInput.removeAttribute("required");
        } else {
            maxPlatformsInput.value = "3";
            maxPlatformsInput.disabled = false;
            maxPlatformsInput.style.backgroundColor = "";
            maxPlatformsInput.style.color = "";
            maxPlatformsInput.setAttribute("required", "required");
        }
    }

    // 切换编辑模态框中的永久账户
    function toggleEditPermanentAccount() {
        const permanentCheckbox = document.getElementById("edit-permanent-account");
        const expiryDateInput = document.getElementById("edit-expiry-date");

        if (permanentCheckbox.checked) {
            expiryDateInput.value = "永久";
            expiryDateInput.disabled = true;
            expiryDateInput.style.backgroundColor = "#f5f5f5";
            expiryDateInput.style.color = "#666";
            expiryDateInput.removeAttribute("required");
        } else {
            // 设置默认日期为一年后
            const oneYearLater = new Date();
            oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);
            expiryDateInput.value = oneYearLater.toISOString().split('T')[0];
            expiryDateInput.disabled = false;
            expiryDateInput.style.backgroundColor = "";
            expiryDateInput.style.color = "";
            expiryDateInput.setAttribute("required", "required");
        }
    }

    // 添加账户
    function addAccount() {
        console.log('添加账户函数被调用');
        const username = document.getElementById("username").value;
        const password = document.getElementById("password").value;
        const phone = document.getElementById("phone").value;
        const email = document.getElementById("email").value;
        const maxPlatformsInput = document.getElementById("max-platforms-number");
        const unlimitedCheckbox = document.getElementById("unlimited-platforms");
        const expiryDateInput = document.getElementById("expiry-date");
        const permanentCheckbox = document.getElementById("permanent-account");
        const accountStatus = document.getElementById("account-status").checked;

        // 表单验证
        if (!username || !password || !phone || !email) {
            showToast("请填写必填项", "error");
            return;
        }

        // 检查可创建小程序数量
        if (!unlimitedCheckbox.checked && (!maxPlatformsInput.value || maxPlatformsInput.value <= 0)) {
            showToast("请输入有效的可创建小程序数量", "error");
            return;
        }

        // 检查到期日期
        if (!permanentCheckbox.checked && !expiryDateInput.value) {
            showToast("请选择到期日期", "error");
            return;
        }

        showLoading();
        // 模拟API请求
        setTimeout(() => {
            hideLoading();
            closeModal("add-account-modal");
            showToast("账户添加成功", "success", true); // 添加成功后刷新页面

            // 清空表单
            document.getElementById("username").value = "";
            document.getElementById("password").value = "";
            document.getElementById("phone").value = "";
            document.getElementById("email").value = "";

            // 重置可创建小程序数量
            maxPlatformsInput.value = "3";
            maxPlatformsInput.disabled = false;
            maxPlatformsInput.style.backgroundColor = "";
            maxPlatformsInput.style.color = "";
            unlimitedCheckbox.checked = false;

            // 重置到期日期
            const oneYearLater = new Date();
            oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);
            expiryDateInput.value = oneYearLater.toISOString().split('T')[0];
            expiryDateInput.disabled = false;
            expiryDateInput.style.backgroundColor = "";
            expiryDateInput.style.color = "";
            permanentCheckbox.checked = false;

            // 重置账户状态
            document.getElementById("account-status").checked = true;

            // 刷新页面或更新UI
            // location.reload();
        }, 800);
    }
</script>

<script src="/assets/js/accounts-edit-fix.js"></script>

<!-- 网站底部 -->
<?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/site-footer.php'); ?>

<script src="/assets/js/sidebar-toggle-fix.js"></script>
<script src="/assets/js/responsive-layout.js"></script>

<!-- 模态框精确修复脚本 -->
<script src="/assets/js/modal-precise-fix.js"></script>

</body>
</html>













