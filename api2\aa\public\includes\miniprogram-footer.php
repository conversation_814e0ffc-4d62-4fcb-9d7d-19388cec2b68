<?php
// 获取系统设置
function getSystemSettings() {
    // 模拟从数据库获取系统设置
    // 在实际应用中，这里应该从数据库中读取设置
    return [
        'site_name' => '情侣头像匹配系统',
        'site_copyright' => '© ' . date('Y') . ' 情侣头像匹配系统',
        'icp_number' => '粤ICP备12345678号',
        'police_number' => '粤公网安备12345678号',
    ];
}

// 获取设置
$settings = getSystemSettings();
?>

<!-- 网站底部 -->
<div class="site-footer">
    <div class="footer-content">
        <div class="copyright">
            <?php echo htmlspecialchars($settings['site_copyright']); ?>
        </div>
        <div class="beian">
            <?php if (!empty($settings['icp_number'])): ?>
            <a href="https://beian.miit.gov.cn/" target="_blank" class="beian-link">
                <i class="bi bi-shield-check"></i> <?php echo htmlspecialchars($settings['icp_number']); ?>
            </a>
            <?php endif; ?>

            <?php if (!empty($settings['police_number'])): ?>
            <a href="http://www.beian.gov.cn/portal/index" target="_blank" class="beian-link">
                <i class="bi bi-shield-lock"></i> <?php echo htmlspecialchars($settings['police_number']); ?>
            </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
/* 底部样式 */
.site-footer {
    background-color: var(--white-color);
    border-top: 1px solid var(--light-color);
    padding: 15px 20px;
    text-align: center;
    font-size: 0.9rem;
    color: var(--gray-color);
    margin-top: 30px;
    width: 100%;
    position: relative;
    bottom: 0;
    left: 0;
    z-index: 10;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    max-width: 1200px;
    margin: 0 auto;
}

.copyright {
    margin-bottom: 5px;
    text-align: center;
}

.beian {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
    text-align: center;
}

.beian-link {
    color: var(--gray-color);
    text-decoration: none;
    transition: color 0.3s;
    display: flex;
    align-items: center;
    gap: 5px;
}

.beian-link:hover {
    color: var(--primary-color);
}

.beian-link i {
    font-size: 1rem;
}

/* 响应式调整 */
@media (min-width: 768px) {
    .footer-content {
        flex-direction: column;
        justify-content: center;
    }

    .copyright {
        margin-bottom: 10px;
    }
}
</style>
