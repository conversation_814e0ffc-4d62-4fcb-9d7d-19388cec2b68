/**
 * 小程序平台统计图表功能
 * 提供平台使用统计、用户数据分析等功能
 */

// 在DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('平台统计功能已加载');
    
    // 初始化平台统计图表
    initPlatformStatistics();
    
    // 初始化平台数据筛选器
    setupStatisticsFilters();
    
    // 添加图表导出按钮
    setupStatisticsExport();
});

/**
 * 初始化平台统计图表
 */
function initPlatformStatistics() {
    // 检查是否存在统计容器
    const userStatsContainer = document.getElementById('platform-user-stats');
    const orderStatsContainer = document.getElementById('platform-order-stats');
    const activeStatsContainer = document.getElementById('platform-active-stats');
    
    if (userStatsContainer) {
        createUserStatisticsChart(userStatsContainer);
    }
    
    if (orderStatsContainer) {
        createOrderStatisticsChart(orderStatsContainer);
    }
    
    if (activeStatsContainer) {
        createActiveStatisticsChart(activeStatsContainer);
    }
}

/**
 * 创建用户统计图表
 * @param {HTMLElement} container - 图表容器
 */
function createUserStatisticsChart(container) {
    // 创建canvas元素
    const canvas = document.createElement('canvas');
    canvas.id = 'userStatsChart';
    container.appendChild(canvas);
    
    // 获取上下文
    const ctx = canvas.getContext('2d');
    
    // 模拟数据
    const labels = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
    const newUsers = [120, 150, 180, 220, 250, 300, 350, 380, 400, 450, 480, 500];
    const totalUsers = [120, 270, 450, 670, 920, 1220, 1570, 1950, 2350, 2800, 3280, 3780];
    
    // 创建图表
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: '新增用户',
                    data: newUsers,
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(255, 99, 132, 1)',
                    tension: 0.4
                },
                {
                    label: '累计用户',
                    data: totalUsers,
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(54, 162, 235, 1)',
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '用户增长趋势',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '用户数'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: '月份'
                    }
                }
            }
        }
    });
    
    console.log('用户统计图表已创建');
}

/**
 * 创建订单统计图表
 * @param {HTMLElement} container - 图表容器
 */
function createOrderStatisticsChart(container) {
    // 创建canvas元素
    const canvas = document.createElement('canvas');
    canvas.id = 'orderStatsChart';
    container.appendChild(canvas);
    
    // 获取上下文
    const ctx = canvas.getContext('2d');
    
    // 模拟数据
    const labels = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
    const orderCounts = [65, 80, 95, 110, 130, 150, 170, 190, 210, 230, 250, 270];
    const orderAmounts = [1950, 2400, 2850, 3300, 3900, 4500, 5100, 5700, 6300, 6900, 7500, 8100];
    
    // 创建图表
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: '订单数量',
                    data: orderCounts,
                    backgroundColor: 'rgba(255, 159, 64, 0.2)',
                    borderColor: 'rgba(255, 159, 64, 1)',
                    borderWidth: 2,
                    yAxisID: 'y'
                },
                {
                    label: '订单金额 (元)',
                    data: orderAmounts,
                    backgroundColor: 'rgba(153, 102, 255, 0.2)',
                    borderColor: 'rgba(153, 102, 255, 1)',
                    borderWidth: 2,
                    type: 'line',
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '订单统计',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '订单数量'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false
                    },
                    title: {
                        display: true,
                        text: '订单金额 (元)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: '月份'
                    }
                }
            }
        }
    });
    
    console.log('订单统计图表已创建');
}

/**
 * 创建活跃度统计图表
 * @param {HTMLElement} container - 图表容器
 */
function createActiveStatisticsChart(container) {
    // 创建canvas元素
    const canvas = document.createElement('canvas');
    canvas.id = 'activeStatsChart';
    container.appendChild(canvas);
    
    // 获取上下文
    const ctx = canvas.getContext('2d');
    
    // 模拟数据
    const labels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    const dailyActive = [320, 302, 301, 334, 390, 430, 410];
    const weeklyActive = [1200, 1300, 1400, 1500, 1600, 1700, 1800];
    const monthlyActive = [3000, 3100, 3200, 3300, 3400, 3500, 3600];
    
    // 创建图表
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: '日活跃用户',
                    data: dailyActive,
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(75, 192, 192, 1)',
                    tension: 0.4
                },
                {
                    label: '周活跃用户',
                    data: weeklyActive,
                    backgroundColor: 'rgba(255, 206, 86, 0.2)',
                    borderColor: 'rgba(255, 206, 86, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(255, 206, 86, 1)',
                    tension: 0.4
                },
                {
                    label: '月活跃用户',
                    data: monthlyActive,
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(54, 162, 235, 1)',
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '用户活跃度统计',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '用户数'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: '日期'
                    }
                }
            }
        }
    });
    
    console.log('活跃度统计图表已创建');
}

/**
 * 设置统计数据筛选器
 */
function setupStatisticsFilters() {
    // 检查是否存在筛选器容器
    const filterContainer = document.getElementById('statistics-filters');
    if (!filterContainer) return;
    
    // 创建时间范围筛选器
    const timeRangeFilter = document.createElement('div');
    timeRangeFilter.className = 'filter-group';
    timeRangeFilter.innerHTML = `
        <label for="time-range">时间范围:</label>
        <select id="time-range" class="filter-select">
            <option value="7days">最近7天</option>
            <option value="30days" selected>最近30天</option>
            <option value="90days">最近90天</option>
            <option value="year">今年</option>
            <option value="all">全部</option>
        </select>
    `;
    
    // 创建平台筛选器
    const platformFilter = document.createElement('div');
    platformFilter.className = 'filter-group';
    platformFilter.innerHTML = `
        <label for="platform-filter">平台:</label>
        <select id="platform-filter" class="filter-select">
            <option value="all" selected>全部平台</option>
            <option value="1">情侣头像匹配小程序1</option>
            <option value="2">情侣头像匹配小程序2</option>
            <option value="3">情侣头像匹配小程序3</option>
        </select>
    `;
    
    // 创建应用按钮
    const applyButton = document.createElement('button');
    applyButton.className = 'btn btn-primary';
    applyButton.textContent = '应用筛选';
    applyButton.addEventListener('click', applyStatisticsFilters);
    
    // 添加到筛选器容器
    filterContainer.appendChild(timeRangeFilter);
    filterContainer.appendChild(platformFilter);
    filterContainer.appendChild(applyButton);
    
    console.log('统计数据筛选器已设置');
}

/**
 * 应用统计数据筛选
 */
function applyStatisticsFilters() {
    const timeRange = document.getElementById('time-range').value;
    const platformId = document.getElementById('platform-filter').value;
    
    console.log(`应用筛选: 时间范围=${timeRange}, 平台ID=${platformId}`);
    
    // 显示加载动画
    showLoading();
    
    // 模拟API请求
    setTimeout(() => {
        // 更新图表数据
        updateChartsData(timeRange, platformId);
        
        // 隐藏加载动画
        hideLoading();
        
        // 显示成功消息
        showToast('统计数据已更新', 'success');
    }, 800);
}

/**
 * 更新图表数据
 * @param {string} timeRange - 时间范围
 * @param {string} platformId - 平台ID
 */
function updateChartsData(timeRange, platformId) {
    // 获取图表实例
    const userChart = Chart.getChart('userStatsChart');
    const orderChart = Chart.getChart('orderStatsChart');
    const activeChart = Chart.getChart('activeStatsChart');
    
    if (userChart) {
        // 根据筛选条件生成新数据
        const newData = generateRandomData(12, 100, 500);
        const totalData = generateCumulativeData(newData);
        
        // 更新图表数据
        userChart.data.datasets[0].data = newData;
        userChart.data.datasets[1].data = totalData;
        userChart.update();
    }
    
    if (orderChart) {
        // 根据筛选条件生成新数据
        const orderCounts = generateRandomData(12, 50, 300);
        const orderAmounts = orderCounts.map(count => count * 30);
        
        // 更新图表数据
        orderChart.data.datasets[0].data = orderCounts;
        orderChart.data.datasets[1].data = orderAmounts;
        orderChart.update();
    }
    
    if (activeChart) {
        // 根据筛选条件生成新数据
        const dailyActive = generateRandomData(7, 300, 500);
        const weeklyActive = generateRandomData(7, 1000, 2000);
        const monthlyActive = generateRandomData(7, 3000, 4000);
        
        // 更新图表数据
        activeChart.data.datasets[0].data = dailyActive;
        activeChart.data.datasets[1].data = weeklyActive;
        activeChart.data.datasets[2].data = monthlyActive;
        activeChart.update();
    }
    
    console.log('图表数据已更新');
}

/**
 * 生成随机数据
 * @param {number} count - 数据点数量
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {Array} 随机数据数组
 */
function generateRandomData(count, min, max) {
    return Array.from({length: count}, () => Math.floor(Math.random() * (max - min + 1)) + min);
}

/**
 * 生成累计数据
 * @param {Array} data - 原始数据
 * @returns {Array} 累计数据数组
 */
function generateCumulativeData(data) {
    let sum = 0;
    return data.map(value => {
        sum += value;
        return sum;
    });
}

/**
 * 设置统计数据导出功能
 */
function setupStatisticsExport() {
    // 检查是否存在导出按钮容器
    const exportContainer = document.getElementById('statistics-export');
    if (!exportContainer) return;
    
    // 创建导出按钮
    exportContainer.innerHTML = `
        <div class="dropdown">
            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="bi bi-download"></i> 导出数据
            </button>
            <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                <li><a class="dropdown-item" href="#" data-format="excel">Excel</a></li>
                <li><a class="dropdown-item" href="#" data-format="csv">CSV</a></li>
                <li><a class="dropdown-item" href="#" data-format="pdf">PDF</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="#" data-format="image">图表图片</a></li>
            </ul>
        </div>
    `;
    
    // 添加导出按钮事件
    exportContainer.querySelectorAll('.dropdown-item').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const format = this.getAttribute('data-format');
            exportStatisticsData(format);
        });
    });
    
    console.log('统计数据导出功能已设置');
}

/**
 * 导出统计数据
 * @param {string} format - 导出格式
 */
function exportStatisticsData(format) {
    console.log(`导出统计数据: 格式=${format}`);
    
    // 显示加载动画
    showLoading();
    
    // 模拟导出过程
    setTimeout(() => {
        // 隐藏加载动画
        hideLoading();
        
        // 显示成功消息
        showToast(`统计数据已导出为${format.toUpperCase()}格式`, 'success');
    }, 800);
}
