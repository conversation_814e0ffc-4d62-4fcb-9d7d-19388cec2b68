<?php
// 获取系统设置
function getSystemSettings() {
    // 模拟从数据库获取系统设置
    // 在实际应用中，这里应该从数据库中读取设置
    return [
        'site_name' => '情侣头像匹配系统',
        'site_copyright' => '© ' . date('Y') . ' 情侣头像匹配系统',
        'icp_number' => '粤ICP备12345678号',
        'police_number' => '粤公网安备12345678号',
        'support_email' => '<EMAIL>'
    ];
}

// 获取设置
$siteSettings = getSystemSettings();
?>

<footer class="main-footer">
    <div class="footer-content">
        <div class="footer-info">
            <p class="copyright"><?php echo isset($siteSettings['site_copyright']) ? $siteSettings['site_copyright'] : '© ' . date('Y') . ' 情侣头像匹配系统'; ?></p>
            <?php if(isset($siteSettings['icp_number']) && !empty($siteSettings['icp_number'])): ?>
            <p class="icp-info"><a href="https://beian.miit.gov.cn/" target="_blank"><?php echo $siteSettings['icp_number']; ?></a></p>
            <?php endif; ?>
            <?php if(isset($siteSettings['police_number']) && !empty($siteSettings['police_number'])): ?>
            <p class="police-info">
                <img src="/assets/images/police-icon.png" alt="公安备案">
                <a href="http://www.beian.gov.cn/portal/registerSystemInfo" target="_blank"><?php echo $siteSettings['police_number']; ?></a>
            </p>
            <?php endif; ?>
        </div>
        <?php if(isset($siteSettings['support_email']) && !empty($siteSettings['support_email'])): ?>
        <div class="footer-contact">
            <p>技术支持: <a href="mailto:<?php echo $siteSettings['support_email']; ?>"><?php echo $siteSettings['support_email']; ?></a></p>
        </div>
        <?php endif; ?>
    </div>
</footer>

<style>
    .main-footer {
        background-color: rgba(var(--primary-color-rgb), 0.05);
        padding: 15px 0;
        text-align: center;
        font-size: 0.9em;
        color: var(--text-muted);
        border-top: 1px solid rgba(var(--primary-color-rgb), 0.1);
        margin-top: 30px;
        width: 100%;
    }

    .footer-content {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .footer-info {
        margin-bottom: 10px;
    }

    .footer-info p {
        margin: 5px 0;
    }

    .footer-info a, .footer-contact a {
        color: var(--primary-color);
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .footer-info a:hover, .footer-contact a:hover {
        text-decoration: underline;
    }

    .police-info {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .police-info img {
        height: 16px;
        margin-right: 5px;
    }

    @media (min-width: 768px) {
        .footer-content {
            flex-direction: row;
            justify-content: space-between;
            padding: 0 20px;
        }

        .footer-info {
            margin-bottom: 0;
            text-align: left;
        }
    }
</style>
