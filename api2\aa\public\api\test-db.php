<?php
// 测试数据库连接

// 设置响应头
header('Content-Type: application/json');

try {
    // 包含数据库配置
    $configPath = __DIR__ . '/../../config/config.php';
    $config = require_once($configPath);
    
    if (!$config || !is_array($config)) {
        throw new Exception('配置文件加载失败');
    }
    
    $dbConfig = $config['database'];
    
    // 测试数据库连接
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    
    echo json_encode([
        'step' => 'before_connection',
        'dsn' => $dsn,
        'username' => $dbConfig['username'],
        'database' => $dbConfig['database']
    ]);
    
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 测试查询
    $stmt = $pdo->query("SELECT 1 as test");
    $result = $stmt->fetch();
    
    // 检查表是否存在
    $prefix = $dbConfig['prefix'];
    $tables = [];
    
    $checkTables = ['record', 'business', 'order'];
    foreach ($checkTables as $table) {
        $tableName = $prefix . $table;
        $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$tableName]);
        $exists = $stmt->fetch() !== false;
        $tables[$tableName] = $exists;
    }
    
    echo json_encode([
        'success' => true,
        'connection' => 'ok',
        'test_query' => $result,
        'tables' => $tables,
        'prefix' => $prefix
    ]);
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error_type' => 'database',
        'error' => $e->getMessage(),
        'code' => $e->getCode()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error_type' => 'general',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
