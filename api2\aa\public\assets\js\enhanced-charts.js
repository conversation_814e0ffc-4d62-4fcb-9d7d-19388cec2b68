/**
 * 增强的图表功能
 * 为图表添加交互性、数据筛选和导出功能
 */

// 等待图表初始化完成后再执行
function initEnhancedChartsWhenReady() {
    // 检查图表是否已创建
    if (window.chartInstances && Object.keys(window.chartInstances).length > 0) {
        console.log('图表已创建，开始初始化增强功能...');

        try {
            // 初始化所有图表的增强功能
            initEnhancedCharts();

            // 添加图表筛选器事件监听
            setupChartFilters();

            // 添加图表导出按钮
            setupChartExport();

            // 确保图表容器有正确的样式
            setupChartContainers();

            console.log('增强图表功能初始化完成');
        } catch (error) {
            console.error('初始化增强图表功能失败:', error);
        }
    } else {
        console.log('图表尚未创建，等待中...');
        // 如果图表还没创建，继续等待
        setTimeout(initEnhancedChartsWhenReady, 500);
    }
}

// 在DOM加载完成后开始检查
document.addEventListener('DOMContentLoaded', function() {
    console.log('增强图表功能已加载，等待图表创建...');
    setTimeout(initEnhancedChartsWhenReady, 1000);
});

/**
 * 设置图表容器样式
 */
function setupChartContainers() {
    console.log('设置图表容器样式');

    // 为所有图表容器添加交互式类
    document.querySelectorAll('.section-content').forEach(container => {
        container.style.position = 'relative';
        console.log('为容器添加相对定位:', container);
    });

    // 为所有图表canvas添加交互式容器类
    document.querySelectorAll('canvas[id$="Chart"]').forEach(canvas => {
        const parent = canvas.parentNode;
        parent.classList.add('interactive-chart-container');
        console.log('为图表添加交互式容器类:', canvas.id);
    });

    // 调整section-header样式
    document.querySelectorAll('.section-header').forEach(header => {
        header.style.display = 'flex';
        header.style.alignItems = 'center';
        header.style.flexWrap = 'wrap';
        header.style.gap = '10px';
        console.log('调整section-header样式');
    });
}

/**
 * 初始化增强的图表功能
 */
function initEnhancedCharts() {
    // 检查Chart.js是否已加载
    if (typeof Chart === 'undefined') {
        console.warn('Chart.js未加载，无法初始化增强图表功能');
        return;
    }

    // 扩展Chart.js默认配置
    const originalInit = Chart.prototype.initialize;
    Chart.prototype.initialize = function() {
        // 调用原始初始化方法
        originalInit.apply(this, arguments);

        // 添加增强的交互选项
        enhanceChartInteraction(this);

        console.log('图表已增强:', this.canvas.id);
    };

    // 为已存在的图表添加增强功能
    const existingCharts = Object.values(Chart.instances || {});
    if (existingCharts.length > 0) {
        console.log(`找到${existingCharts.length}个现有图表，添加增强功能`);
        existingCharts.forEach(chart => {
            enhanceChartInteraction(chart);
        });
    }
}

/**
 * 为图表添加增强的交互功能
 * @param {Chart} chart - Chart.js实例
 */
function enhanceChartInteraction(chart) {
    // 确保图表选项已初始化
    chart.options = chart.options || {};
    chart.options.plugins = chart.options.plugins || {};

    // 增强工具提示
    chart.options.plugins.tooltip = {
        ...chart.options.plugins.tooltip,
        mode: 'index',
        intersect: false,
        callbacks: {
            label: function(context) {
                let label = context.dataset.label || '';
                if (label) {
                    label += ': ';
                }
                if (context.parsed.y !== null) {
                    label += context.formattedValue;
                }
                return label;
            }
        },
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        titleColor: '#fff',
        bodyColor: '#fff',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        borderWidth: 1,
        padding: 10,
        displayColors: true,
        boxWidth: 10,
        boxHeight: 10,
        boxPadding: 3,
        usePointStyle: true
    };

    // 增强图例
    chart.options.plugins.legend = {
        ...chart.options.plugins.legend,
        display: true,
        position: 'top',
        align: 'center',
        labels: {
            boxWidth: 12,
            padding: 15,
            usePointStyle: true,
            pointStyle: 'circle'
        },
        onClick: function(e, legendItem, legend) {
            // 默认图例点击行为
            const index = legendItem.datasetIndex;
            const ci = legend.chart;
            const meta = ci.getDatasetMeta(index);

            // 切换数据集可见性
            meta.hidden = meta.hidden === null ? !ci.data.datasets[index].hidden : null;

            // 更新图表
            ci.update();

            // 记录用户交互
            console.log(`用户切换了数据集可见性: ${legendItem.text}`);
        }
    };

    // 增强动画
    chart.options.animation = {
        ...chart.options.animation,
        duration: 1000,
        easing: 'easeOutQuart'
    };

    // 增强响应式
    chart.options.responsive = true;
    chart.options.maintainAspectRatio = false;

    // 更新图表以应用新选项
    chart.update();

    // 为图表容器添加交互类
    if (chart.canvas && chart.canvas.parentNode) {
        chart.canvas.parentNode.classList.add('interactive-chart-container');
    }
}

/**
 * 设置图表筛选器
 */
function setupChartFilters() {
    console.log('设置图表筛选器');

    // 为每个图表添加筛选器
    document.querySelectorAll('canvas[id$="Chart"]').forEach(canvas => {
        const chartId = canvas.id;
        console.log('为图表添加筛选器:', chartId);

        // 找到图表的section容器
        const section = canvas.closest('.section');
        if (!section) {
            console.warn('找不到图表的section容器:', chartId);
            return;
        }

        // 找到section-header
        const sectionHeader = section.querySelector('.section-header');
        if (!sectionHeader) {
            console.warn('找不到图表的section-header:', chartId);
            return;
        }

        // 创建筛选器容器
        const filterContainer = document.createElement('div');
        filterContainer.className = 'chart-filters';

        // 创建筛选器组
        const filterGroup = document.createElement('div');
        filterGroup.className = 'filter-group';

        // 创建标签
        const label = document.createElement('label');
        label.setAttribute('for', `${chartId}-timerange`);
        label.textContent = '时间范围:';

        // 创建选择框
        const select = document.createElement('select');
        select.id = `${chartId}-timerange`;
        select.className = 'chart-filter';

        // 创建选项
        const options = [
            { value: 'day', text: '今日', selected: true },
            { value: 'week', text: '本周' },
            { value: 'month', text: '本月' },
            { value: 'year', text: '今年' }
        ];

        options.forEach(optionData => {
            const option = document.createElement('option');
            option.value = optionData.value;
            option.textContent = optionData.text;
            if (optionData.selected) option.selected = true;
            select.appendChild(option);
        });

        // 创建应用按钮
        const button = document.createElement('button');
        button.className = 'btn btn-sm btn-primary filter-apply';
        button.setAttribute('data-chart', chartId);
        button.textContent = '应用';

        // 组装元素
        filterGroup.appendChild(label);
        filterGroup.appendChild(select);
        filterGroup.appendChild(button);
        filterContainer.appendChild(filterGroup);

        // 将筛选器添加到section-header中
        sectionHeader.appendChild(filterContainer);

        // 添加筛选器应用按钮事件
        filterContainer.querySelector('.filter-apply').addEventListener('click', function() {
            const timeRange = document.getElementById(`${chartId}-timerange`).value;
            applyChartFilter(chartId, timeRange);
        });

        // 恢复保存的状态
        setTimeout(() => {
            restoreChartFilterState(chartId);
        }, 100);

        console.log('筛选器已添加到图表:', chartId);
    });
}

/**
 * 应用图表筛选
 * @param {string} chartId - 图表ID
 * @param {string} timeRange - 时间范围
 */
function applyChartFilter(chartId, timeRange) {
    console.log(`应用筛选: 图表=${chartId}, 时间范围=${timeRange}`);

    // 获取图表实例（兼容不同版本的Chart.js）
    let chart = null;

    // 首先尝试从全局变量获取
    if (window.chartInstances && window.chartInstances[chartId]) {
        chart = window.chartInstances[chartId];
    }
    // Chart.js v3+
    else if (typeof Chart.getChart === 'function') {
        chart = Chart.getChart(chartId);
    }
    // Chart.js v2 或其他版本
    else if (Chart.instances) {
        const canvas = document.getElementById(chartId);
        if (canvas) {
            // 在Chart.js v2中，需要通过canvas元素找到图表实例
            for (let id in Chart.instances) {
                if (Chart.instances[id].canvas === canvas) {
                    chart = Chart.instances[id];
                    break;
                }
            }
        }
    }

    if (!chart) {
        console.warn(`找不到图表: ${chartId}`);
        console.log('可用的图表实例:', window.chartInstances || Chart.instances);
        return;
    }

    // 保存选择状态到localStorage
    localStorage.setItem(`chart_${chartId}_timeRange`, timeRange);

    // 根据时间范围获取真实数据
    fetchChartData(chartId, timeRange).then(chartData => {
        // 添加调试信息
        console.log(`图表 ${chartId} 获取到数据:`, chartData);
        console.log(`数据标签:`, chartData.labels);
        console.log(`数据值:`, chartData.datasets[0] ? chartData.datasets[0].data : 'no data');

        // 更新图表数据
        chart.data.labels = chartData.labels;
        chart.data.datasets.forEach((dataset, i) => {
            if (chartData.datasets[i]) {
                dataset.data = chartData.datasets[i].data;
                console.log(`更新数据集 ${i}:`, chartData.datasets[i].data);
            }
        });

        // 强制更新图表
        chart.update('active');

        // 如果数据仍然不显示，尝试重新渲染
        setTimeout(() => {
            if (typeof chart.resize === 'function') {
                chart.resize();
            }
            chart.update();
        }, 100);

        console.log(`图表 ${chartId} 已更新`);

        // 显示成功消息
        showToast(`已更新图表数据 (${getTimeRangeText(timeRange)})`, 'success');
    }).catch(error => {
        console.error('获取图表数据失败:', error);
        showToast('获取图表数据失败', 'error');
    });
}

/**
 * 获取时间范围的中文文本
 * @param {string} timeRange - 时间范围
 * @returns {string} 中文文本
 */
function getTimeRangeText(timeRange) {
    const textMap = {
        'day': '今日',
        'week': '本周',
        'month': '本月',
        'year': '今年'
    };
    return textMap[timeRange] || timeRange;
}

/**
 * 恢复图表筛选器状态
 * @param {string} chartId - 图表ID
 */
function restoreChartFilterState(chartId) {
    const savedTimeRange = localStorage.getItem(`chart_${chartId}_timeRange`);
    if (savedTimeRange) {
        const select = document.getElementById(`${chartId}-timerange`);
        if (select) {
            select.value = savedTimeRange;
            console.log(`恢复图表 ${chartId} 的时间范围选择: ${savedTimeRange}`);
        }
    }
}

/**
 * 从服务器获取图表数据
 * @param {string} chartId - 图表ID
 * @param {string} timeRange - 时间范围
 * @returns {Promise} 返回图表数据
 */
async function fetchChartData(chartId, timeRange) {
    try {
        const response = await fetch('../api/chart-data.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                chartId: chartId,
                timeRange: timeRange
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        return data;
    } catch (error) {
        console.error('获取图表数据失败:', error);

        // 如果获取失败，返回模拟数据
        return generateFallbackData(chartId, timeRange);
    }
}

/**
 * 生成备用数据（当服务器请求失败时）
 * @param {string} chartId - 图表ID
 * @param {string} timeRange - 时间范围
 * @returns {Object} 图表数据
 */
function generateFallbackData(chartId, timeRange) {
    let labels, data;

    switch (timeRange) {
        case 'day':
            labels = ['00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00'];
            break;
        case 'week':
            labels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
            break;
        case 'month':
            labels = Array.from({length: 30}, (_, i) => `${i+1}日`);
            break;
        case 'year':
            labels = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
            break;
        default:
            labels = ['数据1', '数据2', '数据3', '数据4', '数据5'];
    }

    // 根据图表类型生成不同的数据
    let datasets = [];

    if (chartId === 'callChart') {
        datasets = [{
            data: labels.map(() => Math.floor(Math.random() * 200) + 50)
        }];
    } else if (chartId === 'userChart') {
        datasets = [{
            data: [Math.floor(Math.random() * 50) + 20, Math.floor(Math.random() * 30) + 10]
        }];
    } else if (chartId === 'orderChart') {
        datasets = [{
            data: labels.map(() => Math.floor(Math.random() * 100) + 10)
        }];
    } else {
        datasets = [{
            data: labels.map(() => Math.floor(Math.random() * 100))
        }];
    }

    return {
        labels: labels,
        datasets: datasets
    };
}

/**
 * 设置图表导出功能
 */
function setupChartExport() {
    console.log('设置图表导出功能');

    // 为每个图表添加导出按钮
    document.querySelectorAll('canvas[id$="Chart"]').forEach(canvas => {
        const chartId = canvas.id;
        console.log('为图表添加导出按钮:', chartId);

        // 找到图表的父容器（section-content）
        let sectionContent = canvas.closest('.section-content');
        if (!sectionContent) {
            console.warn('找不到图表的section-content容器:', chartId);
            sectionContent = canvas.parentNode;
        }

        // 确保容器有相对定位
        sectionContent.style.position = 'relative';

        // 创建导出按钮容器
        const exportContainer = document.createElement('div');
        exportContainer.className = 'chart-export';

        // 创建下拉菜单容器
        const dropdown = document.createElement('div');
        dropdown.className = 'dropdown';

        // 创建下拉按钮
        const button = document.createElement('button');
        button.className = 'btn btn-sm btn-outline-secondary dropdown-toggle';
        button.type = 'button';
        button.id = `${chartId}-export`;
        button.setAttribute('data-bs-toggle', 'dropdown');
        button.setAttribute('aria-expanded', 'false');

        // 创建按钮图标和文本
        const icon = document.createElement('i');
        icon.className = 'bi bi-download';
        button.appendChild(icon);
        button.appendChild(document.createTextNode(' 导出'));

        // 添加悬停效果
        button.addEventListener('mouseenter', function() {
            this.style.color = '#000';
        });
        button.addEventListener('mouseleave', function() {
            this.style.color = '';
        });

        // 创建下拉菜单
        const menu = document.createElement('ul');
        menu.className = 'dropdown-menu dropdown-menu-end';
        menu.setAttribute('aria-labelledby', `${chartId}-export`);

        // 创建菜单项
        const menuItems = [
            { format: 'image', text: '图片 (PNG)' },
            { format: 'csv', text: 'CSV' },
            { format: 'json', text: 'JSON' }
        ];

        menuItems.forEach(item => {
            const li = document.createElement('li');
            const a = document.createElement('a');
            a.className = 'dropdown-item';
            a.href = '#';
            a.setAttribute('data-format', item.format);
            a.setAttribute('data-chart', chartId);
            a.textContent = item.text;
            li.appendChild(a);
            menu.appendChild(li);
        });

        // 组装元素
        dropdown.appendChild(button);
        dropdown.appendChild(menu);
        exportContainer.appendChild(dropdown);

        // 将导出按钮添加到section-content容器
        sectionContent.appendChild(exportContainer);

        // 添加导出按钮事件
        exportContainer.querySelectorAll('.dropdown-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const format = this.getAttribute('data-format');
                const chartId = this.getAttribute('data-chart');
                exportChartData(chartId, format);
            });
        });

        console.log('导出按钮已添加到图表:', chartId);
    });
}

/**
 * 导出图表数据
 * @param {string} chartId - 图表ID
 * @param {string} format - 导出格式 (image, csv, json)
 */
function exportChartData(chartId, format) {
    console.log(`导出图表: ${chartId}, 格式: ${format}`);

    // 获取图表实例
    const chart = Chart.getChart(chartId);
    if (!chart) {
        console.warn(`找不到图表: ${chartId}`);
        return;
    }

    // 根据格式导出数据
    switch (format) {
        case 'image':
            exportChartAsImage(chart);
            break;
        case 'csv':
            exportChartAsCSV(chart);
            break;
        case 'json':
            exportChartAsJSON(chart);
            break;
        default:
            console.warn(`不支持的导出格式: ${format}`);
    }
}

/**
 * 将图表导出为图片
 * @param {Chart} chart - Chart.js实例
 */
function exportChartAsImage(chart) {
    // 获取图表的数据URL
    const imageURL = chart.toBase64Image();

    // 创建下载链接
    const downloadLink = document.createElement('a');
    downloadLink.href = imageURL;
    downloadLink.download = `${chart.canvas.id}-${new Date().toISOString().slice(0, 10)}.png`;

    // 触发下载
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);

    // 显示成功消息
    showToast('图表已导出为PNG图片', 'success');
}

/**
 * 将图表导出为CSV
 * @param {Chart} chart - Chart.js实例
 */
function exportChartAsCSV(chart) {
    // 准备CSV数据
    const labels = chart.data.labels;
    const datasets = chart.data.datasets;

    // 创建CSV头部
    let csvContent = 'data:text/csv;charset=utf-8,';
    csvContent += 'Category,' + datasets.map(ds => ds.label).join(',') + '\r\n';

    // 添加数据行
    labels.forEach((label, i) => {
        csvContent += label + ',' + datasets.map(ds => ds.data[i]).join(',') + '\r\n';
    });

    // 创建下载链接
    const encodedUri = encodeURI(csvContent);
    const downloadLink = document.createElement('a');
    downloadLink.href = encodedUri;
    downloadLink.download = `${chart.canvas.id}-${new Date().toISOString().slice(0, 10)}.csv`;

    // 触发下载
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);

    // 显示成功消息
    showToast('图表数据已导出为CSV', 'success');
}

/**
 * 将图表导出为JSON
 * @param {Chart} chart - Chart.js实例
 */
function exportChartAsJSON(chart) {
    // 准备JSON数据
    const chartData = {
        title: chart.options.plugins.title?.text || chart.canvas.id,
        labels: chart.data.labels,
        datasets: chart.data.datasets.map(ds => ({
            label: ds.label,
            data: ds.data
        })),
        exportDate: new Date().toISOString()
    };

    // 转换为JSON字符串
    const jsonString = JSON.stringify(chartData, null, 2);
    const dataStr = 'data:text/json;charset=utf-8,' + encodeURIComponent(jsonString);

    // 创建下载链接
    const downloadLink = document.createElement('a');
    downloadLink.href = dataStr;
    downloadLink.download = `${chart.canvas.id}-${new Date().toISOString().slice(0, 10)}.json`;

    // 触发下载
    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);

    // 显示成功消息
    showToast('图表数据已导出为JSON', 'success');
}

/**
 * 显示提示消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 (success, error, info)
 */
function showToast(message, type = 'info') {
    // 检查是否存在其他toast库（避免无限递归）
    if (typeof window.globalToast === 'function' && window.globalToast !== showToast) {
        window.globalToast(message, type);
        return;
    }

    // 移除现有的toast
    const existingToasts = document.querySelectorAll('.custom-toast');
    existingToasts.forEach(toast => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    });

    // 创建自定义toast
    const toast = document.createElement('div');
    toast.className = `custom-toast ${type}`;

    // 创建内容容器
    const toastContent = document.createElement('div');
    toastContent.className = 'toast-content';

    // 创建图标
    const icon = document.createElement('i');
    let iconClass, iconSymbol;

    switch(type) {
        case 'success':
            iconClass = 'check-circle-fill';
            iconSymbol = '✓';
            break;
        case 'error':
            iconClass = 'x-circle-fill';
            iconSymbol = '✗';
            break;
        case 'warning':
            iconClass = 'exclamation-triangle-fill';
            iconSymbol = '⚠';
            break;
        default:
            iconClass = 'info-circle-fill';
            iconSymbol = 'ℹ';
    }

    icon.className = `bi bi-${iconClass} toast-icon ${type}`;

    // 创建消息
    const messageSpan = document.createElement('span');
    messageSpan.className = 'toast-message';
    messageSpan.textContent = message;

    // 创建进度条
    const progress = document.createElement('div');
    progress.className = `toast-progress ${type}`;

    // 组装元素
    toastContent.appendChild(icon);
    toastContent.appendChild(messageSpan);
    toast.appendChild(toastContent);
    toast.appendChild(progress);

    // 添加到文档
    document.body.appendChild(toast);

    // 显示toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);

    // 根据类型设置不同的持续时间
    let duration;
    switch(type) {
        case 'success':
            duration = 1000; // 成功消息1秒，更快
            break;
        case 'error':
            duration = 4000; // 错误消息4秒，更长
            break;
        case 'warning':
            duration = 3000; // 警告消息3秒
            break;
        default:
            duration = 2500; // 信息消息2.5秒
    }

    // 设置进度条动画时间
    progress.style.animationDuration = `${duration}ms`;

    // 移除自动刷新逻辑，改为仅显示成功消息
    // 图表数据更新不需要刷新页面

    // 指定时间后隐藏
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, duration);
}
