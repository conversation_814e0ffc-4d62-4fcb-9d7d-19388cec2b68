/**
 * 模态框最终修复脚本
 * 彻底修复所有页面模态框的问题
 */

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('模态框最终修复脚本已加载');
    
    // 重写openModal函数
    window.originalOpenModal = window.openModal;
    
    window.openModal = function(modalId) {
        console.log('打开模态框：', modalId);
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'block';
        }
    };
    
    // 重写closeModal函数
    window.originalCloseModal = window.closeModal;
    
    window.closeModal = function(modalId) {
        console.log('关闭模态框：', modalId);
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'none';
        }
    };
    
    // 修复模态框点击事件
    fixModalClickEvents();
    
    // 修复模态框按钮点击事件
    fixModalButtonEvents();
    
    // 添加表单验证
    addFormValidation();
});

// 修复模态框点击事件
function fixModalClickEvents() {
    // 获取所有模态框
    const modals = document.querySelectorAll('.modal');
    
    if (modals.length > 0) {
        console.log('找到模态框：', modals.length, '个');
        
        // 为每个模态框添加点击事件
        modals.forEach(modal => {
            // 移除原有的点击事件
            modal.onclick = null;
            
            // 添加新的点击事件
            modal.addEventListener('click', function(e) {
                // 如果点击的是模态框本身，不做任何操作
                if (e.target === this) {
                    e.stopPropagation();
                    console.log('点击了模态框空白处，阻止关闭');
                }
            });
        });
    } else {
        console.log('未找到模态框');
    }
}

// 修复模态框按钮点击事件
function fixModalButtonEvents() {
    // 修复关闭按钮点击事件
    const closeButtons = document.querySelectorAll('.close-btn');
    
    if (closeButtons.length > 0) {
        console.log('找到关闭按钮：', closeButtons.length, '个');
        
        closeButtons.forEach(button => {
            // 移除原有的onclick属性
            const onclickValue = button.getAttribute('onclick');
            if (onclickValue) {
                const match = onclickValue.match(/closeModal\(['"](.+)['"]\)/);
                if (match) {
                    const modalId = match[1];
                    button.removeAttribute('onclick');
                    
                    // 添加新的点击事件
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('关闭按钮被点击，模态框ID：', modalId);
                        closeModal(modalId);
                    });
                    
                    console.log('已为关闭按钮添加点击事件，模态框ID：', modalId);
                }
            }
        });
    } else {
        console.log('未找到关闭按钮');
    }
    
    // 修复模态框中的取消按钮点击事件
    const cancelButtons = document.querySelectorAll('.modal-footer .btn-secondary');
    
    if (cancelButtons.length > 0) {
        console.log('找到取消按钮：', cancelButtons.length, '个');
        
        cancelButtons.forEach(button => {
            // 移除原有的onclick属性
            const onclickValue = button.getAttribute('onclick');
            if (onclickValue) {
                const match = onclickValue.match(/closeModal\(['"](.+)['"]\)/);
                if (match) {
                    const modalId = match[1];
                    button.removeAttribute('onclick');
                    
                    // 添加新的点击事件
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('取消按钮被点击，模态框ID：', modalId);
                        closeModal(modalId);
                    });
                    
                    console.log('已为取消按钮添加点击事件，模态框ID：', modalId);
                }
            }
        });
    } else {
        console.log('未找到取消按钮');
    }
}

// 添加表单验证
function addFormValidation() {
    // 获取所有模态框中的确定按钮
    const submitButtons = document.querySelectorAll('.modal-footer .btn-primary');
    
    if (submitButtons.length > 0) {
        console.log('找到确定按钮：', submitButtons.length, '个');
        
        submitButtons.forEach(button => {
            // 获取原有的onclick属性
            const onclickValue = button.getAttribute('onclick');
            if (onclickValue) {
                button.removeAttribute('onclick');
                
                // 添加新的点击事件
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // 获取模态框
                    const modal = button.closest('.modal');
                    if (!modal) return;
                    
                    // 获取表单
                    const form = modal.querySelector('form');
                    if (!form) {
                        // 如果没有表单，直接执行原有的点击事件
                        eval(onclickValue);
                        return;
                    }
                    
                    // 检查表单是否有必填项未填
                    const requiredInputs = form.querySelectorAll('[required]');
                    let isValid = true;
                    
                    requiredInputs.forEach(input => {
                        if (input.disabled) return; // 跳过禁用的输入框
                        
                        if (!input.value.trim()) {
                            isValid = false;
                            input.style.borderColor = 'red';
                            
                            // 添加输入事件，当用户输入时恢复正常样式
                            input.addEventListener('input', function() {
                                if (this.value.trim()) {
                                    this.style.borderColor = '';
                                }
                            });
                        } else {
                            input.style.borderColor = '';
                        }
                    });
                    
                    if (!isValid) {
                        showToast('请填写必填项', 'error');
                        return;
                    }
                    
                    // 执行原有的点击事件
                    eval(onclickValue);
                });
                
                console.log('已为确定按钮添加点击事件');
            }
        });
    } else {
        console.log('未找到确定按钮');
    }
}
