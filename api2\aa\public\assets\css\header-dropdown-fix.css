/**
 * 头部下拉菜单修复样式
 * 修复所有页面的头像和下拉菜单样式问题
 */

/* 重置头像样式 */
.header .user-avatar {
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    background-color: var(--primary-color) !important; /* 使用主题变量 */
    color: #ffffff !important; /* 强制使用白色文字 */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-weight: bold !important;
    margin-right: 10px !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
    font-size: 16px !important;
    text-shadow: none !important;
    border: none !important;
    transition: background-color 0.3s ease !important; /* 添加过渡效果 */
}

/* 确保用户信息区域正确定位 */
.header .user-info {
    display: flex !important;
    align-items: center !important;
    position: relative !important;
    cursor: pointer !important;
    margin-left: auto !important;
    z-index: 99999 !important; /* 使用非常高的z-index */
}

/* 确保下拉菜单正确显示 */
.header .dropdown-content {
    position: absolute !important;
    top: calc(100% - 5px) !important; /* 减少与头像的间距 */
    right: 0 !important;
    left: auto !important;
    width: 160px !important;
    background-color: #ffffff !important;
    box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.1) !important;
    border-radius: 8px !important;
    padding: 10px 0 !important;
    z-index: 100000 !important; /* 使用比用户信息更高的z-index */
    display: none !important;
    margin-top: 0 !important; /* 移除顶部边距 */
    border: 1px solid #eee !important;
    pointer-events: auto !important; /* 确保可以点击 */
}

/* 添加一个连接区域，确保鼠标移动时不会触发菜单消失 */
.header .user-info::after {
    content: '' !important;
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    height: 15px !important; /* 连接区域高度 */
    background-color: transparent !important;
    z-index: 99999 !important;
}

/* 显示下拉菜单 */
.header .user-info:hover .dropdown-content {
    display: block !important;
}

/* 下拉菜单项样式 */
.header .dropdown-content a {
    color: #333333 !important;
    padding: 10px 20px !important;
    text-decoration: none !important;
    display: block !important;
    transition: background-color 0.3s !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    font-weight: normal !important;
    pointer-events: auto !important; /* 确保可以点击 */
    cursor: pointer !important;
}

.header .dropdown-content a:hover {
    background-color: #f8f9fa !important;
}

.header .dropdown-content a i {
    margin-right: 10px !important;
    width: 20px !important;
    text-align: center !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
}

/* 确保头部区域不会遮挡下拉菜单 */
.header {
    overflow: visible !important;
    position: relative !important;
    z-index: 99998 !important;
}

/* 确保其他元素不会遮挡下拉菜单 */
.main-content > *:not(.header) {
    z-index: 1 !important;
    position: relative !important;
}

/* 确保模态框在最上层 */
.modal {
    z-index: 100001 !important;
}

/* 确保toast在最上层 */
.toast {
    z-index: 100002 !important;
}

/* 修复可能的遮挡问题 */
.action-bar,
.content-header,
.content-body,
.pagination-container,
.table-container {
    position: relative !important;
    z-index: 1 !important;
}

/* 确保用户名文字颜色 */
.header .user-name {
    color: #333333 !important;
    font-weight: 500 !important;
    font-size: 14px !important;
}

/* 修复特定页面的问题 */
body {
    overflow-x: hidden !important;
}

/* 确保下拉菜单在所有页面上都可见 */
.dropdown-content {
    visibility: visible !important;
    opacity: 1 !important;
}
