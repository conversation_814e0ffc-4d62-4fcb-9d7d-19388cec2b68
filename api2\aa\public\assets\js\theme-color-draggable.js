/**
 * 主题颜色修改图标拖动功能
 * 使主题颜色修改图标可以拖动到页面任意位置
 */

document.addEventListener('DOMContentLoaded', function() {
    // 获取主题颜色修改图标
    const themeColorIcon = document.getElementById('theme-color-icon');

    if (!themeColorIcon) return;

    // 初始化拖动功能
    initDraggable(themeColorIcon);

    // 从localStorage加载上次保存的位置
    loadPosition(themeColorIcon);
});

/**
 * 初始化拖动功能
 * @param {HTMLElement} element - 要拖动的元素
 */
function initDraggable(element) {
    let isDragging = false;
    let offsetX, offsetY;

    // 设置元素的初始样式
    element.style.position = 'fixed';
    element.style.cursor = 'move';
    element.style.userSelect = 'none';
    element.style.zIndex = '9999';

    // 添加拖动开始事件
    element.addEventListener('mousedown', startDrag);
    element.addEventListener('touchstart', startDrag, { passive: false });

    /**
     * 开始拖动
     * @param {Event} e - 事件对象
     */
    function startDrag(e) {
        e.preventDefault();

        // 如果是点击了子元素，不启动拖动
        if (e.target !== element && element.contains(e.target)) {
            // 如果点击的是图标本身，允许拖动
            if (e.target.tagName === 'I' && e.target.classList.contains('bi-palette')) {
                // 继续拖动
            } else {
                // 其他子元素不启动拖动
                return;
            }
        }

        isDragging = true;

        // 计算鼠标/触摸点与元素边界的偏移量
        if (e.type === 'mousedown') {
            offsetX = e.clientX - element.getBoundingClientRect().left;
            offsetY = e.clientY - element.getBoundingClientRect().top;
        } else {
            offsetX = e.touches[0].clientX - element.getBoundingClientRect().left;
            offsetY = e.touches[0].clientY - element.getBoundingClientRect().top;
        }

        // 添加移动和结束事件监听器
        document.addEventListener('mousemove', drag);
        document.addEventListener('touchmove', drag, { passive: false });
        document.addEventListener('mouseup', stopDrag);
        document.addEventListener('touchend', stopDrag);

        // 添加拖动时的样式
        element.classList.add('dragging');
    }

    /**
     * 拖动过程
     * @param {Event} e - 事件对象
     */
    function drag(e) {
        if (!isDragging) return;

        e.preventDefault();

        let clientX, clientY;

        if (e.type === 'mousemove') {
            clientX = e.clientX;
            clientY = e.clientY;
        } else {
            clientX = e.touches[0].clientX;
            clientY = e.touches[0].clientY;
        }

        // 计算新位置
        let newLeft = clientX - offsetX;
        let newTop = clientY - offsetY;

        // 确保元素不会超出视口
        const maxLeft = window.innerWidth - element.offsetWidth;
        const maxTop = window.innerHeight - element.offsetHeight;

        newLeft = Math.max(0, Math.min(newLeft, maxLeft));
        newTop = Math.max(0, Math.min(newTop, maxTop));

        // 设置新位置
        element.style.left = newLeft + 'px';
        element.style.top = newTop + 'px';
    }

    /**
     * 停止拖动
     */
    function stopDrag() {
        if (!isDragging) return;

        isDragging = false;

        // 移除事件监听器
        document.removeEventListener('mousemove', drag);
        document.removeEventListener('touchmove', drag);
        document.removeEventListener('mouseup', stopDrag);
        document.removeEventListener('touchend', stopDrag);

        // 移除拖动时的样式
        element.classList.remove('dragging');

        // 保存位置到localStorage
        savePosition(element);
    }
}

/**
 * 保存元素位置到localStorage
 * @param {HTMLElement} element - 要保存位置的元素
 */
function savePosition(element) {
    const position = {
        left: element.style.left,
        top: element.style.top
    };

    localStorage.setItem('themeColorIconPosition', JSON.stringify(position));
}

/**
 * 从localStorage加载元素位置
 * @param {HTMLElement} element - 要加载位置的元素
 */
function loadPosition(element) {
    const savedPosition = localStorage.getItem('themeColorIconPosition');

    if (savedPosition) {
        try {
            const position = JSON.parse(savedPosition);

            // 设置位置
            element.style.left = position.left;
            element.style.top = position.top;
        } catch (error) {
            console.error('加载主题颜色图标位置失败:', error);

            // 设置默认位置
            setDefaultPosition(element);
        }
    } else {
        // 设置默认位置
        setDefaultPosition(element);
    }
}

/**
 * 设置元素的默认位置
 * @param {HTMLElement} element - 要设置默认位置的元素
 */
function setDefaultPosition(element) {
    element.style.right = '20px';
    element.style.bottom = '20px';
    element.style.left = 'auto';
    element.style.top = 'auto';
}
