/**
 * 侧边栏切换修复脚本
 * 修复侧边栏切换按钮在不同页面的问题
 */

// 确保全局变量只定义一次
if (typeof window.sidebarToggleFixed === 'undefined') {
    window.sidebarToggleFixed = false;
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('侧边栏切换修复脚本已加载');
    
    // 如果已经修复过，则不再执行
    if (window.sidebarToggleFixed) {
        console.log('侧边栏切换已经修复过，跳过');
        return;
    }
    
    // 获取DOM元素
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');
    const menuToggle = document.getElementById('menu-toggle');
    
    if (sidebar && mainContent && menuToggle) {
        console.log('找到侧边栏元素');
        
        // 移除所有现有的点击事件
        const oldMenuToggle = menuToggle.cloneNode(true);
        menuToggle.parentNode.replaceChild(oldMenuToggle, menuToggle);
        
        // 添加新的点击事件
        oldMenuToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('侧边栏切换按钮被点击');
            
            // 切换侧边栏状态
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
            
            // 保存状态到本地存储
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
            console.log('侧边栏状态已保存:', sidebar.classList.contains('collapsed'));
        });
        
        // 从本地存储中恢复状态
        const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        console.log('从本地存储中恢复侧边栏状态:', sidebarCollapsed);
        
        if (sidebarCollapsed) {
            sidebar.classList.add('collapsed');
            mainContent.classList.add('expanded');
        } else {
            sidebar.classList.remove('collapsed');
            mainContent.classList.remove('expanded');
        }
        
        // 标记为已修复
        window.sidebarToggleFixed = true;
        console.log('侧边栏切换已修复');
    } else {
        console.error('未找到侧边栏元素');
    }
});

