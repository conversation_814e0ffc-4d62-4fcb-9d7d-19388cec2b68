/**
 * 小程序平台批量操作功能
 * 提供批量启用/禁用/删除平台等功能
 */

// 在DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('平台批量操作功能已加载');

    // 初始化批量操作功能
    initBatchOperations();
});

/**
 * 初始化批量操作功能
 */
function initBatchOperations() {
    // 添加表格行选择功能
    setupRowSelection();

    // 添加批量操作按钮
    setupBatchActionButtons();

    // 添加全选/取消全选功能
    setupSelectAllCheckbox();
}

/**
 * 设置表格行选择功能
 */
function setupRowSelection() {
    // 检查是否存在表格
    const table = document.querySelector('.table');
    if (!table) return;

    // 为表格行添加点击事件
    const tableRows = table.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        // 为每行添加data-id属性（如果没有）
        if (!row.hasAttribute('data-id')) {
            const idCell = row.querySelector('td:first-child');
            if (idCell) {
                row.setAttribute('data-id', idCell.textContent.trim());
            }
        }

        // 为复选框添加点击事件
        const checkbox = row.querySelector('.platform-checkbox');
        if (checkbox) {
            checkbox.addEventListener('change', function() {
                if (this.checked) {
                    row.classList.add('selected');
                } else {
                    row.classList.remove('selected');
                }

                // 更新批量操作按钮状态
                updateBatchActionButtons();
            });
        }
    });

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .table tr.selected {
            background-color: rgba(255, 107, 149, 0.1);
        }
    `;
    document.head.appendChild(style);

    console.log('表格行选择功能已设置');
}

/**
 * 设置全选/取消全选功能
 */
function setupSelectAllCheckbox() {
    const selectAllCheckbox = document.getElementById('select-all');
    if (!selectAllCheckbox) return;

    selectAllCheckbox.addEventListener('change', function() {
        const platformCheckboxes = document.querySelectorAll('.platform-checkbox');
        platformCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;

            // 更新行样式
            const row = checkbox.closest('tr');
            if (this.checked) {
                row.classList.add('selected');
            } else {
                row.classList.remove('selected');
            }
        });

        // 更新批量操作按钮状态
        updateBatchActionButtons();
    });

    console.log('全选/取消全选功能已设置');
}

/**
 * 设置批量操作按钮
 */
function setupBatchActionButtons() {
    // 查找已存在的批量操作按钮
    const batchEnableBtn = document.getElementById('batch-enable-btn');
    const batchDisableBtn = document.getElementById('batch-disable-btn');
    const batchDeleteBtn = document.getElementById('batch-delete-btn');

    if (batchEnableBtn) {
        batchEnableBtn.addEventListener('click', batchEnablePlatforms);
    }

    if (batchDisableBtn) {
        batchDisableBtn.addEventListener('click', batchDisablePlatforms);
    }

    if (batchDeleteBtn) {
        batchDeleteBtn.addEventListener('click', batchDeletePlatforms);
    }

    console.log('批量操作按钮已设置');
}

/**
 * 更新批量操作按钮状态
 */
function updateBatchActionButtons() {
    const selectedRows = document.querySelectorAll('.platform-checkbox:checked');
    const batchEnableBtn = document.getElementById('batch-enable-btn');
    const batchDisableBtn = document.getElementById('batch-disable-btn');
    const batchDeleteBtn = document.getElementById('batch-delete-btn');

    const hasSelected = selectedRows.length > 0;

    if (batchEnableBtn) {
        batchEnableBtn.disabled = !hasSelected;
    }

    if (batchDisableBtn) {
        batchDisableBtn.disabled = !hasSelected;
    }

    if (batchDeleteBtn) {
        batchDeleteBtn.disabled = !hasSelected;
    }
}

/**
 * 获取选中的平台ID
 * @returns {Array} 选中的平台ID数组
 */
function getSelectedPlatformIds() {
    const selectedCheckboxes = document.querySelectorAll('.platform-checkbox:checked');
    return Array.from(selectedCheckboxes).map(checkbox => checkbox.getAttribute('data-id'));
}

/**
 * 批量启用平台
 */
function batchEnablePlatforms() {
    const selectedIds = getSelectedPlatformIds();
    if (selectedIds.length === 0) return;

    console.log('批量启用平台:', selectedIds);

    // 显示确认对话框
    showConfirmDialog(
        '批量启用平台',
        `确定要启用选中的 ${selectedIds.length} 个平台吗？`,
        () => {
            // 显示加载动画
            showLoading();

            // 模拟API请求
            setTimeout(() => {
                // 更新UI
                selectedIds.forEach(id => {
                    const row = document.querySelector(`tr[data-id="${id}"]`);
                    if (row) {
                        const statusCell = row.querySelector('td:nth-child(4)');
                        if (statusCell) {
                            const statusSpan = statusCell.querySelector('.status');
                            if (statusSpan) {
                                statusSpan.className = 'status active';
                                statusSpan.textContent = '已启用';
                            }
                        }
                    }
                });

                // 隐藏加载动画
                hideLoading();

                // 取消选中所有行
                uncheckAllRows();

                // 显示成功消息
                showToast(`已成功启用 ${selectedIds.length} 个平台`, 'success');
            }, 800);
        }
    );
}

/**
 * 批量禁用平台
 */
function batchDisablePlatforms() {
    const selectedIds = getSelectedPlatformIds();
    if (selectedIds.length === 0) return;

    console.log('批量禁用平台:', selectedIds);

    // 显示确认对话框
    showConfirmDialog(
        '批量禁用平台',
        `确定要禁用选中的 ${selectedIds.length} 个平台吗？`,
        () => {
            // 显示加载动画
            showLoading();

            // 模拟API请求
            setTimeout(() => {
                // 更新UI
                selectedIds.forEach(id => {
                    const row = document.querySelector(`tr[data-id="${id}"]`);
                    if (row) {
                        const statusCell = row.querySelector('td:nth-child(4)');
                        if (statusCell) {
                            const statusSpan = statusCell.querySelector('.status');
                            if (statusSpan) {
                                statusSpan.className = 'status inactive';
                                statusSpan.textContent = '已禁用';
                            }
                        }
                    }
                });

                // 隐藏加载动画
                hideLoading();

                // 取消选中所有行
                uncheckAllRows();

                // 显示成功消息
                showToast(`已成功禁用 ${selectedIds.length} 个平台`, 'success');
            }, 800);
        }
    );
}

/**
 * 批量删除平台
 */
function batchDeletePlatforms() {
    const selectedIds = getSelectedPlatformIds();
    if (selectedIds.length === 0) return;

    console.log('批量删除平台:', selectedIds);

    // 显示确认对话框
    showConfirmDialog(
        '批量删除平台',
        `确定要删除选中的 ${selectedIds.length} 个平台吗？此操作将把平台移至回收站。`,
        () => {
            // 显示加载动画
            showLoading();

            // 模拟API请求
            setTimeout(() => {
                // 更新UI
                selectedIds.forEach(id => {
                    const row = document.querySelector(`tr[data-id="${id}"]`);
                    if (row) {
                        // 添加淡出动画
                        row.style.transition = 'opacity 0.5s ease';
                        row.style.opacity = '0';

                        // 动画结束后移除行
                        setTimeout(() => {
                            row.remove();
                        }, 500);
                    }
                });

                // 隐藏加载动画
                hideLoading();

                // 显示成功消息
                showToast(`已成功删除 ${selectedIds.length} 个平台`, 'success');
            }, 800);
        }
    );
}

/**
 * 取消选中所有行
 */
function uncheckAllRows() {
    const selectAllCheckbox = document.getElementById('select-all');
    if (selectAllCheckbox) {
        selectAllCheckbox.checked = false;
    }

    const platformCheckboxes = document.querySelectorAll('.platform-checkbox');
    platformCheckboxes.forEach(checkbox => {
        checkbox.checked = false;
        const row = checkbox.closest('tr');
        row.classList.remove('selected');
    });

    // 更新批量操作按钮状态
    updateBatchActionButtons();
}

/**
 * 显示确认对话框
 * @param {string} title - 对话框标题
 * @param {string} message - 对话框消息
 * @param {Function} confirmCallback - 确认回调函数
 */
function showConfirmDialog(title, message, confirmCallback) {
    // 检查是否存在确认对话框
    let confirmModal = document.getElementById('batch-confirm-modal');

    // 如果不存在，创建一个
    if (!confirmModal) {
        confirmModal = document.createElement('div');
        confirmModal.className = 'modal';
        confirmModal.id = 'batch-confirm-modal';
        confirmModal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="confirm-modal-title"></h3>
                    <button class="close-btn" id="close-confirm-modal"><i class="bi bi-x-lg"></i></button>
                </div>
                <div class="modal-body">
                    <p id="confirm-modal-message"></p>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="cancel-confirm-modal">取消</button>
                    <button class="btn btn-primary" id="confirm-modal-confirm">确定</button>
                </div>
            </div>
        `;
        document.body.appendChild(confirmModal);

        // 添加关闭和取消按钮事件
        document.getElementById('close-confirm-modal').addEventListener('click', function() {
            confirmModal.style.display = 'none';
        });

        document.getElementById('cancel-confirm-modal').addEventListener('click', function() {
            confirmModal.style.display = 'none';
        });
    }

    // 设置对话框内容
    document.getElementById('confirm-modal-title').textContent = title;
    document.getElementById('confirm-modal-message').textContent = message;

    // 设置确认按钮事件
    const confirmButton = document.getElementById('confirm-modal-confirm');
    confirmButton.onclick = function() {
        document.getElementById('batch-confirm-modal').style.display = 'none';
        if (typeof confirmCallback === 'function') {
            confirmCallback();
        }
    };

    // 显示对话框
    confirmModal.style.display = 'block';
}

/**
 * 显示加载动画
 */
function showLoading() {
    // 检查是否存在加载动画
    let loadingElement = document.getElementById('loading-overlay');

    // 如果不存在，创建一个
    if (!loadingElement) {
        loadingElement = document.createElement('div');
        loadingElement.id = 'loading-overlay';
        loadingElement.className = 'loading-overlay';
        loadingElement.innerHTML = `
            <div class="loading-spinner">
                <div class="spinner"></div>
                <p>处理中...</p>
            </div>
        `;
        document.body.appendChild(loadingElement);

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            }

            .loading-spinner {
                background-color: white;
                padding: 20px;
                border-radius: 5px;
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            .spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid var(--primary-color);
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }

    // 显示加载动画
    loadingElement.style.display = 'flex';
}

/**
 * 隐藏加载动画
 */
function hideLoading() {
    const loadingElement = document.getElementById('loading-overlay');
    if (loadingElement) {
        loadingElement.style.display = 'none';
    }
}

/**
 * 显示提示消息
 * @param {string} message - 提示消息
 * @param {string} type - 提示类型（success, error, info）
 */
function showToast(message, type = 'info') {
    // 检查是否存在提示元素
    let toastElement = document.getElementById('toast-notification');

    // 如果不存在，创建一个
    if (!toastElement) {
        toastElement = document.createElement('div');
        toastElement.id = 'toast-notification';
        toastElement.className = 'toast-notification';
        document.body.appendChild(toastElement);

        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .toast-notification {
                position: fixed;
                bottom: 20px;
                right: 20px;
                padding: 10px 20px;
                border-radius: 4px;
                color: white;
                font-weight: bold;
                z-index: 9999;
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .toast-success {
                background-color: var(--success-color, #28a745);
            }

            .toast-error {
                background-color: var(--danger-color, #dc3545);
            }

            .toast-info {
                background-color: var(--info-color, #17a2b8);
            }
        `;
        document.head.appendChild(style);
    }

    // 设置提示类型
    toastElement.className = 'toast-notification';
    toastElement.classList.add(`toast-${type}`);

    // 设置提示消息
    toastElement.textContent = message;

    // 显示提示
    toastElement.style.opacity = '1';

    // 3秒后隐藏提示
    setTimeout(() => {
        toastElement.style.opacity = '0';
    }, 3000);
}
