/**
 * 情侣头像匹配系统 - UI增强样式
 */

/* 页面过渡效果 */
.page-transition {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--primary-color);
  z-index: 9999;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.5s ease;
}

.page-transition.active {
  opacity: 0.8;
  pointer-events: all;
}

/* 滚动动画 */
.scroll-anim {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.scroll-anim.animated {
  opacity: 1;
  transform: translateY(0);
}

.scroll-anim-delay-1 {
  transition-delay: 0.1s;
}

.scroll-anim-delay-2 {
  transition-delay: 0.2s;
}

.scroll-anim-delay-3 {
  transition-delay: 0.3s;
}

.scroll-anim-delay-4 {
  transition-delay: 0.4s;
}

.scroll-anim-delay-5 {
  transition-delay: 0.5s;
}

/* 卡片悬停效果 */
.hover-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* 加载动画 */
.loading-spinner {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-top-color: var(--primary-color);
  animation: spin 1s infinite linear;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-dots {
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-dots .dot {
  width: 8px;
  height: 8px;
  margin: 0 4px;
  border-radius: 50%;
  background-color: var(--primary-color);
  animation: dot-pulse 1.5s infinite ease-in-out;
}

.loading-dots .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dots .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes dot-pulse {
  0%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 提示工具 */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltip-text {
  visibility: hidden;
  width: 120px;
  background-color: var(--dark-color);
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  margin-left: -60px;
  opacity: 0;
  transition: opacity 0.3s;
}

.tooltip .tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--dark-color) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* 成功/错误反馈 */
.feedback-animation {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.feedback-animation.show {
  opacity: 1;
}

.feedback-icon {
  font-size: 60px;
  margin-bottom: 20px;
}

.feedback-icon.success {
  color: var(--success-color);
}

.feedback-icon.error {
  color: var(--danger-color);
}

.feedback-message {
  font-size: 18px;
  font-weight: bold;
  color: var(--text-color);
}

/* 按钮波纹效果 */
button, .btn {
  position: relative;
  overflow: hidden;
}

.ripple {
  position: absolute;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.4);
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
}

@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* 输入框焦点效果 */
.form-control:focus {
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.25);
  border-color: var(--primary-color);
}

/* 卡片内容淡入效果 */
.fade-in-card .card-content {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.fade-in-card:hover .card-content {
  opacity: 1;
  transform: translateY(0);
}

/* 图标旋转效果 */
.rotate-icon-hover {
  transition: transform 0.3s ease;
}

.rotate-icon-hover:hover {
  transform: rotate(360deg);
}

/* 脉冲效果 */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 闪光效果 */
.shine {
  position: relative;
  overflow: hidden;
}

.shine::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(30deg);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) rotate(30deg);
  }
  100% {
    transform: translateX(100%) rotate(30deg);
  }
}

/* 响应式设计优化 */
@media (max-width: 768px) {
  .responsive-hide {
    display: none;
  }
  
  .responsive-stack {
    flex-direction: column !important;
  }
  
  .responsive-full-width {
    width: 100% !important;
  }
  
  .responsive-center {
    text-align: center !important;
  }
  
  .responsive-padding {
    padding: 10px !important;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-color);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}
