<?php
// 开启会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查用户是否已登录
if (!isset($_SESSION['username'])) {
    http_response_code(401);
    echo json_encode(['error' => '未授权访问']);
    exit;
}

// 包含数据库配置
$configPath = __DIR__ . '/../../config/config.php';
if (!file_exists($configPath)) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '配置文件不存在: ' . $configPath]);
    exit;
}

$config = require_once($configPath);

if (!$config || !is_array($config)) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '配置文件加载失败或格式错误']);
    exit;
}

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['chartId']) || !isset($input['timeRange'])) {
        throw new Exception('缺少必要参数');
    }

    $chartId = $input['chartId'];
    $timeRange = $input['timeRange'];

    // 连接数据库
    $dbConfig = $config['database'];
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 根据图表ID和时间范围获取数据
    $prefix = $dbConfig['prefix']; // 使用配置中的前缀
    $chartData = getChartData($pdo, $prefix, $chartId, $timeRange);

    echo json_encode($chartData);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}



/**
 * 获取图表数据
 */
function getChartData($pdo, $prefix, $chartId, $timeRange) {
    switch ($chartId) {
        case 'callChart':
            return getCallChartData($pdo, $prefix, $timeRange);
        case 'userChart':
            return getUserChartData($pdo, $prefix, $timeRange);
        case 'orderChart':
            return getOrderChartData($pdo, $prefix, $timeRange);
        default:
            throw new Exception('未知的图表ID');
    }
}

/**
 * 获取调用统计图表数据
 */
function getCallChartData($pdo, $prefix, $timeRange) {
    // 检查表是否存在
    $tableName = $prefix . 'record';
    $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
    $stmt->execute([$tableName]);
    $tableExists = $stmt->fetch() !== false;

    if (!$tableExists) {
        throw new Exception("数据表 {$tableName} 不存在");
    }

    $labels = [];
    $data = [];

    switch ($timeRange) {
        case 'day':
            // 今日每小时数据
            $labels = ['00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00'];
            $today = date('Y-m-d');

            foreach ([0, 3, 6, 9, 12, 15, 18, 21] as $hour) {
                $startTime = strtotime("$today $hour:00:00");
                $endTime = strtotime("$today " . ($hour + 3) . ":00:00");

                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM {$prefix}record WHERE intime >= ? AND intime < ?");
                $stmt->execute([$startTime, $endTime]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $data[] = (int)($result['count'] ?? 0);
            }
            break;
            
        case 'week':
            // 本周每日数据
            $labels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
            $startOfWeek = strtotime('monday this week');

            for ($i = 0; $i < 7; $i++) {
                $date = date('Y-m-d', strtotime("+$i day", $startOfWeek));
                $startTime = strtotime("$date 00:00:00");
                $endTime = strtotime("$date 23:59:59");

                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM {$prefix}record WHERE intime >= ? AND intime <= ?");
                $stmt->execute([$startTime, $endTime]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $data[] = (int)($result['count'] ?? 0);
            }
            break;
            
        case 'month':
            // 本月每日数据
            $labels = [];
            $data = [];
            $daysInMonth = date('t');
            $currentMonth = date('Y-m');

            for ($day = 1; $day <= $daysInMonth; $day++) {
                $labels[] = $day . '日';
                $date = "$currentMonth-" . str_pad($day, 2, '0', STR_PAD_LEFT);
                $startTime = strtotime("$date 00:00:00");
                $endTime = strtotime("$date 23:59:59");

                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM {$prefix}record WHERE intime >= ? AND intime <= ?");
                $stmt->execute([$startTime, $endTime]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $data[] = (int)($result['count'] ?? 0);
            }
            break;
            
        case 'year':
            // 今年每月数据
            $labels = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
            $currentYear = date('Y');

            for ($month = 1; $month <= 12; $month++) {
                $startTime = strtotime("$currentYear-$month-01 00:00:00");
                $endTime = strtotime("$currentYear-$month-" . date('t', $startTime) . " 23:59:59");

                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM {$prefix}record WHERE intime >= ? AND intime <= ?");
                $stmt->execute([$startTime, $endTime]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $data[] = (int)($result['count'] ?? 0);
            }
            break;
    }
    
    return [
        'labels' => $labels,
        'datasets' => [
            [
                'data' => $data
            ]
        ]
    ];
}

/**
 * 获取用户统计图表数据
 */
function getUserChartData($pdo, $prefix, $timeRange) {
    // 检查表是否存在
    $tableName = $prefix . 'business';
    $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
    $stmt->execute([$tableName]);
    $tableExists = $stmt->fetch() !== false;

    if (!$tableExists) {
        throw new Exception("数据表 {$tableName} 不存在");
    }

    // 用户图表通常是饼图，显示用户类型分布
    $stmt = $pdo->prepare("
        SELECT
            COUNT(CASE WHEN type = 1 THEN 1 END) as point_users,
            COUNT(CASE WHEN type = 2 THEN 1 END) as monthly_users
        FROM {$prefix}business
    ");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    return [
        'labels' => ['包点用户', '包月用户'],
        'datasets' => [
            [
                'data' => [
                    (int)($result['point_users'] ?? 0),
                    (int)($result['monthly_users'] ?? 0)
                ]
            ]
        ]
    ];
}

/**
 * 获取订单统计图表数据
 */
function getOrderChartData($pdo, $prefix, $timeRange) {
    // 检查表是否存在
    $tableName = $prefix . 'order';
    $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
    $stmt->execute([$tableName]);
    $tableExists = $stmt->fetch() !== false;

    if (!$tableExists) {
        throw new Exception("数据表 {$tableName} 不存在");
    }

    $labels = [];
    $data = [];

    switch ($timeRange) {
        case 'day':
            // 今日每小时订单数据
            $labels = ['00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00'];
            $today = date('Y-m-d');
            
            foreach ([0, 3, 6, 9, 12, 15, 18, 21] as $hour) {
                $startTime = strtotime("$today $hour:00:00");
                $endTime = strtotime("$today " . ($hour + 3) . ":00:00");
                
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM {$prefix}order WHERE intime >= ? AND intime < ?");
                $stmt->execute([$startTime, $endTime]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $data[] = (int)($result['count'] ?? 0);
            }
            break;
            
        case 'week':
            // 本周每日订单数据
            $labels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
            $startOfWeek = strtotime('monday this week');
            
            for ($i = 0; $i < 7; $i++) {
                $date = date('Y-m-d', strtotime("+$i day", $startOfWeek));
                $startTime = strtotime("$date 00:00:00");
                $endTime = strtotime("$date 23:59:59");
                
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM {$prefix}order WHERE intime >= ? AND intime <= ?");
                $stmt->execute([$startTime, $endTime]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $data[] = (int)($result['count'] ?? 0);
            }
            break;
            
        case 'month':
            // 本月每日订单数据
            $labels = [];
            $data = [];
            $daysInMonth = date('t');
            $currentMonth = date('Y-m');

            for ($day = 1; $day <= $daysInMonth; $day++) {
                $labels[] = $day . '日';
                $date = "$currentMonth-" . str_pad($day, 2, '0', STR_PAD_LEFT);
                $startTime = strtotime("$date 00:00:00");
                $endTime = strtotime("$date 23:59:59");

                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM {$prefix}order WHERE intime >= ? AND intime <= ?");
                $stmt->execute([$startTime, $endTime]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $data[] = (int)($result['count'] ?? 0);
            }
            break;
            
        case 'year':
            // 今年每月订单数据
            $labels = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
            $currentYear = date('Y');

            for ($month = 1; $month <= 12; $month++) {
                $startTime = strtotime("$currentYear-$month-01 00:00:00");
                $endTime = strtotime("$currentYear-$month-" . date('t', $startTime) . " 23:59:59");

                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM {$prefix}order WHERE intime >= ? AND intime <= ?");
                $stmt->execute([$startTime, $endTime]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $data[] = (int)($result['count'] ?? 0);
            }
            break;
    }
    
    return [
        'labels' => $labels,
        'datasets' => [
            [
                'data' => $data
            ]
        ]
    ];
}
?>
