﻿<?php
// 开启会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 启用错误报告
ini_set('display_errors', 1);
error_reporting(E_ALL);

// 记录请求信息
$logFile = __DIR__ . '/../../../storage/logs/settings.log';
$logDir = dirname($logFile);
if (!is_dir($logDir)) {
    mkdir($logDir, 0777, true);
}

file_put_contents($logFile, date('Y-m-d H:i:s') . " - 系统设置页面请求开始\n", FILE_APPEND);
file_put_contents($logFile, "SESSION 数据: " . print_r($_SESSION, true) . "\n", FILE_APPEND);

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    // 未登录，重定向到登录页面
    header('Location: /login');
    exit;
}

/**
 * 简单的邮件测试函数
 * @param string $email 邮箱地址
 * @param string $host SMTP服务器
 * @return array
 */
function sendSimpleTestEmail($email, $host) {
    $subject = '邮箱配置测试邮件';
    $message = '
    <html>
    <head><title>邮箱配置测试</title></head>
    <body>
        <h2>📧 邮箱配置测试</h2>
        <p>这是一封测试邮件，用于验证邮箱配置。</p>
        <p><strong>测试时间：</strong>' . date('Y-m-d H:i:s') . '</p>
        <p><strong>SMTP服务器：</strong>' . htmlspecialchars($host) . '</p>
        <p>如果您收到这封邮件，说明基本配置正确。</p>
    </body>
    </html>';

    $headers = [
        'MIME-Version: 1.0',
        'Content-type: text/html; charset=UTF-8',
        'From: AA管理系统 <' . $email . '>',
        'Reply-To: ' . $email
    ];

    $result = mail($email, $subject, $message, implode("\r\n", $headers));

    return [
        'success' => $result,
        'message' => $result ? '简单邮件测试发送成功（使用系统邮件功能）' : '邮件发送失败，请检查服务器邮件配置'
    ];
}

// 检查用户是否有权限访问系统设置
$isAdmin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
if (!$isAdmin) {
    // 无权限，重定向到控制面板
    header('Location: /dashboard');
    exit;
}

// 获取用户信息
$username = $_SESSION['username'] ?? '管理员';
$userInitial = mb_substr($username, 0, 1, 'UTF-8');

// 加载统一配置文件
$configPath = dirname(__DIR__, 4) . '/config/config.php';
if (!file_exists($configPath)) {
    die('配置文件不存在: ' . $configPath);
}
$config = require_once($configPath);
$dbConfig = $config['database'];

// 连接数据库
try {
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $prefix = $dbConfig['prefix'];

    // 获取网站基本设置
    $stmt = $pdo->prepare("SELECT * FROM {$prefix}webpz WHERE id = 1");
    $stmt->execute();
    $webConfig = $stmt->fetch(PDO::FETCH_ASSOC);

    // 获取系统信息设置
    $stmt = $pdo->prepare("SELECT * FROM {$prefix}information WHERE id = 1");
    $stmt->execute();
    $infoConfig = $stmt->fetch(PDO::FETCH_ASSOC);

    // 如果记录不存在，创建默认记录
    if (!$webConfig) {
        $stmt = $pdo->prepare("INSERT INTO {$prefix}webpz (id, name, keyword, contents, qq, qun, khdurl, lbtgg) VALUES (1, '', '', '', '', '', '', '')");
        $stmt->execute();
        $webConfig = ['id' => 1, 'name' => '', 'keyword' => '', 'contents' => '', 'qq' => '', 'qun' => '', 'khdurl' => '', 'lbtgg' => ''];
    }

    if (!$infoConfig) {
        $stmt = $pdo->prepare("INSERT INTO {$prefix}information (id, notice, homegg, tjcode, Cleankey, mycard, beian_icp, beian_police, system_start_date, service_agreement, privacy_policy) VALUES (1, '', '', '', '', '', '', '', '2024-01-01', '', '')");
        $stmt->execute();
        $infoConfig = ['id' => 1, 'notice' => '', 'homegg' => '', 'tjcode' => '', 'Cleankey' => '', 'mycard' => '', 'beian_icp' => '', 'beian_police' => '', 'system_start_date' => '2024-01-01', 'service_agreement' => '', 'privacy_policy' => ''];
    }

    // 映射数据库字段到注册设置数组格式，保持UI完全不变
    $settings = [
        // 注册基本设置
        'register_type' => $webConfig['regtype'] ?? '1',
        'register_time_type' => $webConfig['regtime'] ?? '0',
        'register_give_points' => $infoConfig['give'] ?? '500',
        'register_invite_reward' => $infoConfig['yqjl'] ?? '0.1',

        // 邮箱设置
        'register_email_enable' => $webConfig['email'] ?? '1',
        'register_email_username' => $webConfig['emailname'] ?? '',
        'register_email_authcode' => $webConfig['authcode'] ?? '',
        'register_email_smtp' => $webConfig['emaismtp'] ?? '',

        // 保留必要的系统信息用于页面显示
        'site_name' => $webConfig['name'] ?? '去水印接口',
        'site_logo' => $infoConfig['site_logo'] ?? '/assets/images/logo.png',
        'site_favicon' => $infoConfig['site_favicon'] ?? '/assets/images/favicon.ico',
        'site_copyright' => $infoConfig['site_copyright'] ?? ('© ' . date('Y') . ' ' . ($webConfig['name'] ?? '去水印接口')),
        'icp_number' => $infoConfig['beian_icp'] ?? '',
        'police_number' => $infoConfig['beian_police'] ?? '',
    ];

} catch (PDOException $e) {
    // 数据库连接失败，使用默认数据
    $settings = [
        // 注册基本设置
        'register_type' => '1',
        'register_time_type' => '0',
        'register_give_points' => '500',
        'register_invite_reward' => '0.1',

        // 邮箱设置
        'register_email_enable' => '1',
        'register_email_username' => '',
        'register_email_authcode' => '',
        'register_email_smtp' => '',

        // 保留必要的系统信息用于页面显示
        'site_name' => '去水印接口',
        'site_logo' => '/assets/images/logo.png',
        'site_favicon' => '/assets/images/favicon.ico',
        'site_copyright' => '© ' . date('Y') . ' 去水印接口',
        'icp_number' => '',
        'police_number' => '',
    ];
}

// 处理各种重置请求
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    try {
        switch ($_POST['action']) {
            case 'reset_basic_settings':
                // 重置基本设置到默认值
                $defaultBasicSettings = [
                    'name' => '去水印接口管理系统',
                    'contents' => '专业的去水印接口服务平台，提供高质量的图片和视频去水印服务。',
                    'keyword' => '去水印,接口,API,图片处理,视频处理'
                ];

                // 更新webpz表
                $stmt = $pdo->prepare("UPDATE {$dbConfig['prefix']}webpz SET name = ?, contents = ?, keyword = ? WHERE id = 1");
                $result = $stmt->execute([
                    $defaultBasicSettings['name'],
                    $defaultBasicSettings['contents'],
                    $defaultBasicSettings['keyword']
                ]);

                if ($result) {
                    echo json_encode(['success' => true, 'message' => '基本设置已重置为默认值并保存到数据库']);
                } else {
                    echo json_encode(['success' => false, 'message' => '基本设置重置失败，数据库更新出错']);
                }
                break;

            case 'reset_appearance_files':
                // 重置外观设置到默认值
                $defaultAppearanceSettings = [
                    'site_logo' => '/assets/defaults/logo.png',
                    'site_favicon' => '/assets/defaults/favicon.ico',
                    'login_bg' => '/assets/defaults/login-bg.png'
                ];

                // 删除自定义上传的文件
                $uploadDir = dirname(__DIR__, 2) . '/assets/images/';
                $filesToDelete = ['logo.png', 'favicon.ico', 'login-bg.png'];
                $deletedFiles = [];

                foreach ($filesToDelete as $file) {
                    $filePath = $uploadDir . $file;
                    if (file_exists($filePath)) {
                        if (unlink($filePath)) {
                            $deletedFiles[] = $file;
                        }
                    }
                }

                // 更新数据库为默认路径
                $stmt = $pdo->prepare("UPDATE {$dbConfig['prefix']}information SET site_logo = ?, site_favicon = ?, login_bg = ? WHERE id = 1");
                $result = $stmt->execute([
                    $defaultAppearanceSettings['site_logo'],
                    $defaultAppearanceSettings['site_favicon'],
                    $defaultAppearanceSettings['login_bg']
                ]);

                if ($result) {
                    echo json_encode([
                        'success' => true,
                        'message' => '外观设置已重置为默认值并保存到数据库',
                        'deleted_files' => $deletedFiles,
                        'default_paths' => $defaultAppearanceSettings
                    ]);
                } else {
                    echo json_encode(['success' => false, 'message' => '外观设置重置失败，数据库更新出错']);
                }
                break;

            case 'reset_security_settings':
                // 重置安全设置到默认值（密钥配置）
                $defaultSecuritySettings = [
                    'api_key' => '',
                    'secret_key' => '',
                    'jwt_secret' => '',
                    'encryption_key' => '',
                    'webhook_secret' => ''
                ];

                // 更新information表的安全相关字段
                $stmt = $pdo->prepare("UPDATE {$dbConfig['prefix']}information SET
                    api_key = ?,
                    secret_key = ?,
                    jwt_secret = ?,
                    encryption_key = ?,
                    webhook_secret = ?
                    WHERE id = 1");
                $result = $stmt->execute(array_values($defaultSecuritySettings));

                if ($result) {
                    echo json_encode(['success' => true, 'message' => '密钥配置已重置为默认值并保存到数据库']);
                } else {
                    echo json_encode(['success' => false, 'message' => '密钥配置重置失败，数据库更新出错']);
                }
                break;

            case 'reset_advanced_settings':
                // 重置页面设置到默认值
                $defaultAdvancedSettings = [
                    'tjcode' => '',
                    'service_agreement' => '<h2>服务协议</h2>

<h3>1. 服务条款的接受</h3>
<p>欢迎使用我们的解析服务。通过注册、访问或使用本服务，您表示同意遵守本服务协议的所有条款和条件。</p>

<h3>2. 服务描述</h3>
<p>本平台提供视频解析服务，帮助用户获取各大视频平台的视频资源链接。我们致力于为用户提供稳定、高效的解析服务。</p>

<h3>3. 用户责任</h3>
<p>3.1 您必须年满18周岁或在法定监护人同意下使用本服务。</p>
<p>3.2 您承诺提供真实、准确的注册信息。</p>
<p>3.3 您不得将本服务用于任何非法用途或侵犯他人权益的行为。</p>
<p>3.4 您应妥善保管账户信息，对账户下的所有活动承担责任。</p>

<h3>4. 服务限制</h3>
<p>4.1 我们保留随时修改、暂停或终止服务的权利。</p>
<p>4.2 用户应合理使用服务，不得进行恶意攻击或滥用。</p>
<p>4.3 我们不保证服务的绝对稳定性和可用性。</p>

<h3>5. 知识产权</h3>
<p>本平台的所有内容、技术和服务均受知识产权法保护。未经授权，不得复制、修改或分发。</p>

<h3>6. 免责声明</h3>
<p>6.1 本服务仅提供技术支持，不对解析内容的合法性负责。</p>
<p>6.2 用户使用解析服务获取的内容应遵守相关法律法规。</p>
<p>6.3 我们不对因使用本服务而产生的任何直接或间接损失承担责任。</p>

<h3>7. 协议修改</h3>
<p>我们保留随时修改本协议的权利。修改后的协议将在网站上公布，继续使用服务即表示接受修改后的条款。</p>

<h3>8. 联系我们</h3>
<p>如有任何问题，请通过网站提供的联系方式与我们联系。</p>',
                    'privacy_policy' => '<h2>隐私政策</h2>

<h3>1. 信息收集</h3>
<p>1.1 我们收集您主动提供的信息，包括但不限于：</p>
<ul>
<li>注册时提供的用户名、邮箱、QQ号等基本信息</li>
<li>使用服务时产生的日志信息</li>
<li>您主动提供的其他信息</li>
</ul>

<p>1.2 我们自动收集的信息包括：</p>
<ul>
<li>设备信息（IP地址、浏览器类型等）</li>
<li>使用行为数据（访问时间、使用频率等）</li>
</ul>

<h3>2. 信息使用</h3>
<p>我们使用收集的信息用于：</p>
<ul>
<li>提供和改进我们的服务</li>
<li>处理您的请求和交易</li>
<li>发送服务相关通知</li>
<li>防范欺诈和滥用行为</li>
<li>遵守法律法规要求</li>
</ul>

<h3>3. 信息共享</h3>
<p>3.1 我们不会向第三方出售、交易或转让您的个人信息。</p>
<p>3.2 在以下情况下，我们可能会共享您的信息：</p>
<ul>
<li>获得您的明确同意</li>
<li>法律法规要求</li>
<li>保护我们的权利和财产</li>
<li>紧急情况下保护用户安全</li>
</ul>

<h3>4. 信息安全</h3>
<p>4.1 我们采用行业标准的安全措施保护您的个人信息。</p>
<p>4.2 我们使用加密技术传输和存储敏感信息。</p>
<p>4.3 我们定期审查和更新安全措施。</p>

<h3>5. Cookie使用</h3>
<p>我们使用Cookie和类似技术来改善用户体验、分析网站使用情况。您可以通过浏览器设置管理Cookie。</p>

<h3>6. 数据保留</h3>
<p>我们仅在必要期间保留您的个人信息。当信息不再需要时，我们将安全删除或匿名化处理。</p>

<h3>7. 您的权利</h3>
<p>您有权：</p>
<ul>
<li>访问和更新您的个人信息</li>
<li>删除您的账户和相关数据</li>
<li>限制或反对某些数据处理</li>
<li>数据可携带权</li>
</ul>

<h3>8. 未成年人保护</h3>
<p>我们不会故意收集未满18周岁未成年人的个人信息。如发现此类情况，我们将立即删除相关信息。</p>

<h3>9. 政策更新</h3>
<p>我们可能会不时更新本隐私政策。重大变更将通过网站公告或邮件通知您。</p>

<h3>10. 联系我们</h3>
<p>如对本隐私政策有任何疑问，请通过网站提供的联系方式与我们联系。</p>'
                ];

                // 更新information表的页面设置字段
                $stmt = $pdo->prepare("UPDATE {$dbConfig['prefix']}information SET
                    tjcode = ?,
                    service_agreement = ?,
                    privacy_policy = ?
                    WHERE id = 1");
                $result = $stmt->execute(array_values($defaultAdvancedSettings));

                if ($result) {
                    echo json_encode(['success' => true, 'message' => '页面设置已重置为默认值并保存到数据库']);
                } else {
                    echo json_encode(['success' => false, 'message' => '页面设置重置失败，数据库更新出错']);
                }
                break;

            case 'send_test_email':
                // 发送测试邮件
                $emailUsername = $_POST['email_username'] ?? '';
                $emailAuthcode = $_POST['email_authcode'] ?? '';
                $emailSmtp = $_POST['email_smtp'] ?? '';

                // 设置响应头
                header('Content-Type: application/json; charset=utf-8');

                if (empty($emailUsername) || empty($emailAuthcode) || empty($emailSmtp)) {
                    echo json_encode(['success' => false, 'message' => '邮箱配置信息不完整']);
                    exit;
                }

                // 验证邮箱格式
                if (!filter_var($emailUsername, FILTER_VALIDATE_EMAIL)) {
                    echo json_encode(['success' => false, 'message' => '邮箱用户名格式不正确']);
                    exit;
                }

                try {
                    // 清理输出缓冲区，避免PHP Warning影响JSON响应
                    if (ob_get_level()) {
                        ob_clean();
                    }

                    // 直接使用aa项目的Mailer类
                    $mailerPath = dirname(__DIR__, 4) . '/app/Utils/Mailer.php';
                    $loggerPath = dirname(__DIR__, 4) . '/app/Utils/Logger.php';

                    if (!file_exists($mailerPath)) {
                        echo json_encode(['success' => false, 'message' => 'Mailer类文件不存在：' . $mailerPath]);
                        exit;
                    }

                    if (!file_exists($loggerPath)) {
                        echo json_encode(['success' => false, 'message' => 'Logger类文件不存在：' . $loggerPath]);
                        exit;
                    }

                    require_once($loggerPath);
                    require_once($mailerPath);

                    if (!class_exists('\App\Utils\Mailer')) {
                        echo json_encode(['success' => false, 'message' => 'Mailer类加载失败']);
                        exit;
                    }

                    // 创建邮件实例
                    $mailer = \App\Utils\Mailer::getInstance();

                    // 验证和清理SMTP服务器地址
                    $cleanHost = trim($emailSmtp);
                    if (empty($cleanHost)) {
                        echo json_encode(['success' => false, 'message' => 'SMTP服务器地址不能为空']);
                        exit;
                    }

                    // 记录调试信息
                    error_log("邮件测试 - SMTP服务器: " . $cleanHost);
                    error_log("邮件测试 - 用户名: " . $emailUsername);

                    // 设置邮件配置
                    $mailer->setHost($cleanHost)
                           ->setPort(587)
                           ->setUsername($emailUsername)
                           ->setPassword($emailAuthcode)
                           ->setFrom('AA管理系统', $emailUsername);

                    // 邮件内容
                    $subject = '邮箱配置测试邮件';
                    $body = '
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                        <h2 style="color: #4F46E5;">📧 邮箱配置测试</h2>
                        <p>恭喜！您的邮箱配置测试成功。</p>
                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <p><strong>测试时间：</strong>' . date('Y-m-d H:i:s') . '</p>
                            <p><strong>发送邮箱：</strong>' . htmlspecialchars($emailUsername) . '</p>
                            <p><strong>SMTP服务器：</strong>' . htmlspecialchars($emailSmtp) . '</p>
                        </div>
                        <p>如果您收到这封邮件，说明邮箱配置正确。</p>
                    </div>';

                    // 发送邮件（发送给自己）
                    $result = $mailer->send($emailUsername, $subject, $body);

                    if ($result) {
                        echo json_encode(['success' => true, 'message' => '测试邮件发送成功！请检查邮箱收件箱']);
                    } else {
                        // 获取更详细的错误信息
                        $lastError = error_get_last();
                        $errorMsg = '测试邮件发送失败';

                        if ($lastError && strpos($lastError['message'], 'fsockopen') !== false) {
                            if (strpos($lastError['message'], 'getaddrinfo failed') !== false) {
                                $errorMsg .= '：无法解析SMTP服务器地址，请检查服务器地址是否正确';
                            } elseif (strpos($lastError['message'], 'Connection refused') !== false) {
                                $errorMsg .= '：连接被拒绝，请检查端口号和SSL设置';
                            } elseif (strpos($lastError['message'], 'timeout') !== false) {
                                $errorMsg .= '：连接超时，请检查网络连接';
                            } else {
                                $errorMsg .= '：网络连接失败';
                            }
                        } else {
                            $errorMsg .= '，请检查邮箱用户名和授权码';
                        }

                        echo json_encode(['success' => false, 'message' => $errorMsg]);
                    }

                } catch (\Throwable $e) {
                    // 如果Mailer类失败，尝试使用简单的邮件测试
                    try {
                        $testResult = sendSimpleTestEmail($emailUsername, $cleanHost);
                        echo json_encode($testResult);
                    } catch (\Exception $simpleError) {
                        echo json_encode([
                            'success' => false,
                            'message' => '邮件发送失败：' . $e->getMessage()
                        ]);
                    }
                }
                exit;

            default:
                echo json_encode(['success' => false, 'message' => '未知的操作']);
        }
        exit;

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => '重置操作时发生错误: ' . $e->getMessage()
        ]);
        exit;
    }
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 记录表单提交
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - 系统设置表单提交\n", FILE_APPEND);
    file_put_contents($logFile, "POST 数据: " . print_r($_POST, true) . "\n", FILE_APPEND);

    // 处理文件上传（移到表单提交处理的开始）
    $uploadUpdates = [];

    // 处理网站Logo上传 - 统一保存为logo.png
    if (isset($_FILES['site_logo']) && $_FILES['site_logo']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = dirname(__DIR__, 2) . '/assets/images/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $uploadFile = $uploadDir . 'logo.png';

        if (move_uploaded_file($_FILES['site_logo']['tmp_name'], $uploadFile)) {
            $uploadUpdates['site_logo'] = '/assets/images/logo.png';
            file_put_contents($logFile, date('Y-m-d H:i:s') . " - Logo上传成功: /assets/images/logo.png\n", FILE_APPEND);
        }
    }

    // 处理网站图标上传 - 统一保存为favicon.ico
    if (isset($_FILES['site_favicon']) && $_FILES['site_favicon']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = dirname(__DIR__, 2) . '/assets/images/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $uploadFile = $uploadDir . 'favicon.ico';

        if (move_uploaded_file($_FILES['site_favicon']['tmp_name'], $uploadFile)) {
            $uploadUpdates['site_favicon'] = '/assets/images/favicon.ico';
            file_put_contents($logFile, date('Y-m-d H:i:s') . " - 图标上传成功: /assets/images/favicon.ico\n", FILE_APPEND);
        }
    }

    // 处理登录背景上传 - 统一保存为login-bg.png
    if (isset($_FILES['login_bg']) && $_FILES['login_bg']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = dirname(__DIR__, 2) . '/assets/images/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        $uploadFile = $uploadDir . 'login-bg.png';

        if (move_uploaded_file($_FILES['login_bg']['tmp_name'], $uploadFile)) {
            $uploadUpdates['login_bg'] = '/assets/images/login-bg.png';
            file_put_contents($logFile, date('Y-m-d H:i:s') . " - 登录背景上传成功: /assets/images/login-bg.png\n", FILE_APPEND);
        }
    }

    file_put_contents($logFile, "上传更新: " . print_r($uploadUpdates, true) . "\n", FILE_APPEND);

    try {
        // 更新数据库中的注册设置
        if (isset($pdo) && $pdo) {
            // 更新webpz表中的注册相关设置
            $stmt = $pdo->prepare("UPDATE {$prefix}webpz SET email = ?, emailname = ?, authcode = ?, regtype = ?, regtime = ?, emaismtp = ? WHERE id = 1");
            $stmt->execute([
                $_POST['register_email_enable'] ?? '1',
                $_POST['register_email_username'] ?? '',
                $_POST['register_email_authcode'] ?? '',
                $_POST['register_type'] ?? '1',
                $_POST['register_time_type'] ?? '0',
                $_POST['register_email_smtp'] ?? ''
            ]);

            // 更新information表中的注册相关设置
            $stmt = $pdo->prepare("UPDATE {$prefix}information SET give = ?, yqjl = ? WHERE id = 1");
            $stmt->execute([
                $_POST['register_give_points'] ?? '500',
                $_POST['register_invite_reward'] ?? '0.1'
            ]);

            $saveSuccess = true;
        }

        // 更新内存中的设置数组（保持原有逻辑）
        foreach ($_POST as $key => $value) {
            if (array_key_exists($key, $settings)) {
                $settings[$key] = $value;
            }
        }

    } catch (PDOException $e) {
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - 数据库更新失败: " . $e->getMessage() . "\n", FILE_APPEND);
        // 即使数据库更新失败，也更新内存中的设置
        foreach ($_POST as $key => $value) {
            if (array_key_exists($key, $settings)) {
                $settings[$key] = $value;
            }
        }
    }







    // 在实际应用中，这里应该将设置保存到数据库
    // 模拟保存成功
    $saveSuccess = true;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册设置 - <?php echo htmlspecialchars($settings['site_name']); ?></title>

    <!-- 动态favicon，添加时间戳强制刷新缓存 -->
    <link rel="icon" type="image/x-icon" href="<?php echo htmlspecialchars($settings['site_favicon']); ?>?v=<?php echo time(); ?>">
    <link rel="shortcut icon" href="<?php echo htmlspecialchars($settings['site_favicon']); ?>?v=<?php echo time(); ?>">
    <link rel="apple-touch-icon" href="<?php echo htmlspecialchars($settings['site_favicon']); ?>?v=<?php echo time(); ?>">

    <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/header-scripts.php'); ?>
    <link href="/assets/css/header-info.css" rel="stylesheet">
    <style>
        /* 基础样式 */
        :root {
            --primary-color: #ff6b95;
            --primary-color-rgb: 255, 107, 149;
            --secondary-color: #ffa5c0;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --gray-color: #6c757d;
            --white-color: #ffffff;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --transition-speed: 0.3s;
            --border-radius: 8px;
            --animate-delay: 0.1s;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }

        body {
            background-color: #f5f7fa;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: hidden;
        }

        /* 加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 107, 149, 0.3);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 布局样式 */
        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--white-color);
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 2px 0 10px var(--shadow-color);
            transition: all var(--transition-speed);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            margin-bottom: 20px;
        }

        .sidebar-logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 10px;
            object-fit: cover;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .sidebar-title {
            color: var(--white-color);
            font-size: 1.5rem;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .sidebar-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .sidebar.collapsed .sidebar-title,
        .sidebar.collapsed .sidebar-subtitle {
            display: none;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu li a {
            color: var(--white-color);
            text-decoration: none;
            display: block;
            padding: 12px 20px;
            transition: all var(--transition-speed);
            border-left: 4px solid transparent;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.2);
            border-left-color: var(--white-color);
        }

        .sidebar-menu li a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }

        .sidebar.collapsed .sidebar-menu li a span {
            display: none;
        }

        .sidebar.collapsed .sidebar-menu li a i {
            margin-right: 0;
            font-size: 1.3rem;
        }

        /* 菜单分组样式 */
        .menu-section {
            margin-bottom: 5px;
        }

        .menu-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 20px;
            color: var(--white-color);
            text-decoration: none;
            transition: all var(--transition-speed);
            border-left: 4px solid transparent;
            font-weight: 500;
        }

        .menu-header:hover,
        .menu-header.active {
            background-color: rgba(255, 255, 255, 0.2);
            border-left-color: var(--white-color);
            color: var(--white-color);
        }

        .menu-header i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }

        .toggle-icon {
            transition: transform var(--transition-speed);
            font-size: 0.8rem;
        }

        .menu-header.active .toggle-icon {
            transform: rotate(180deg);
        }

        /* 子菜单样式 - 与仪表盘一致 */
        .menu-section {
            position: relative;
        }

        .menu-section .menu-header {
            position: relative;
        }

        .menu-section .toggle-icon {
            position: absolute;
            right: 20px;
            transition: transform 0.3s ease;
        }

        .menu-section.active .toggle-icon {
            transform: rotate(180deg);
        }

        .submenu {
            list-style: none;
            padding: 0;
            margin: 0;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background-color: rgba(0, 0, 0, 0.1);
        }

        .menu-section.active .submenu {
            max-height: 500px;
        }

        /* 确保默认展开的菜单正确显示 */
        .menu-section .submenu[style*="display: block"] {
            max-height: 500px !important;
        }

        .submenu li a {
            padding: 10px 20px 10px 50px;
            font-size: 0.9rem;
            border-left: 2px solid transparent;
        }

        .submenu li a:hover {
            background-color: rgba(255, 255, 255, 0.15);
            border-left-color: rgba(255, 255, 255, 0.5);
        }

        .sidebar.collapsed .submenu {
            display: none;
        }

        .submenu li a {
            padding: 10px 20px 10px 50px;
            font-size: 0.9rem;
            border-left: 4px solid transparent;
        }

        .submenu li a:hover,
        .submenu li a.active {
            background-color: rgba(255, 255, 255, 0.15);
            border-left-color: var(--white-color);
        }





        /* 主内容区域样式 */
        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 20px;
            transition: all var(--transition-speed);
        }

        .main-content.expanded {
            margin-left: 70px;
        }







        /* 头部样式 */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        /* 天气信息和人生哲理组件样式 */
        .header-info-content {
            display: flex;
            align-items: center;
            margin: 0 auto;
            flex: 1;
            padding: 0 20px;
            color: var(--text-color, #333);
            font-size: 14px;
            max-width: 1200px;
            justify-content: space-between;
            gap: 15px;
            min-height: 40px;
        }

        .wisdom-quote {
            display: flex;
            align-items: center;
            animation: fadeIn 0.5s;
            flex: 1;
            min-width: 0;
            max-width: 70%;
        }

        .wisdom-quote span {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: block;
            max-width: 100%;
        }

        .weather-info {
            display: flex;
            align-items: center;
            white-space: nowrap;
            flex-shrink: 0;
            min-width: 150px;
        }

        .wisdom-quote, .weather-info {
            font-size: 0.9rem;
            color: var(--gray-color);
        }

        .wisdom-quote i, .weather-info i {
            margin-right: 8px;
            font-size: 16px;
            color: var(--primary-color);
            flex-shrink: 0;
        }

        .info-actions {
            display: flex;
            gap: 10px;
            margin-left: 10px;
        }

        .info-actions button {
            background: rgba(var(--primary-color-rgb, 255, 107, 149), 0.1);
            border: none;
            color: var(--primary-color);
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            flex-shrink: 0;
        }

        .info-actions button:hover {
            background: rgba(var(--primary-color-rgb, 255, 107, 149), 0.2);
            transform: rotate(15deg);
        }

        .info-actions button:active {
            background: rgba(var(--primary-color-rgb, 255, 107, 149), 0.3);
            transform: scale(0.95);
        }

        /* 响应式处理 */
        @media (max-width: 768px) {
            .header-info-content {
                max-width: 100%;
                gap: 15px;
                padding: 0 10px;
            }

            .wisdom-quote {
                max-width: 60%;
            }

            .wisdom-quote span {
                font-size: 13px;
            }

            .weather-info {
                min-width: 120px;
                font-size: 13px;
            }
        }

        @media (max-width: 480px) {
            .wisdom-quote {
                display: none;
            }

            .header-info-content {
                justify-content: center;
            }

            .weather-info {
                margin-right: 0;
                max-width: 150px;
            }
        }

        .menu-toggle {
            background: none;
            border: none;
            color: var(--dark-color);
            font-size: 1.5rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            transition: all var(--transition-speed);
        }

        .menu-toggle:hover {
            background-color: var(--light-color);
        }

        .user-info {
            display: flex;
            align-items: center;
            position: relative;
            cursor: pointer;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: var(--white-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }

        .user-name {
            color: var(--dark-color);
            font-weight: 500;
            margin-right: 5px;
        }

        .user-info::after {
            content: '\F282';
            font-family: 'bootstrap-icons';
            font-size: 0.8rem;
            color: var(--gray-color);
            margin-left: 5px;
        }

        .dropdown-content {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: var(--white-color);
            min-width: 160px;
            box-shadow: 0 8px 16px var(--shadow-color);
            border-radius: var(--border-radius);
            z-index: 1000;
            display: none;
            padding: 8px 0;
            margin-top: 5px;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .user-info:hover .dropdown-content {
            display: block;
        }

        .dropdown-content a {
            color: var(--dark-color);
            padding: 8px 16px;
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: background-color var(--transition-speed);
        }

        .dropdown-content a:hover {
            background-color: var(--light-color);
            color: var(--primary-color);
        }

        .dropdown-content a i {
            margin-right: 8px;
            width: 16px;
            text-align: center;
        }

        /* 修复模态框和通知消息的z-index */
        .modal {
            z-index: 1050 !important;
        }

        .modal-backdrop {
            z-index: 1040 !important;
        }





        .toast-container {
            z-index: 99999 !important;
        }

        /* 确保通知消息在模态框之上 */
        .toast, .alert, .notification, .toast-unified {
            z-index: 99999 !important;
            position: fixed !important;
        }

        /* 统一通知样式的z-index */
        .toast-unified, .toast-unified.show {
            z-index: 99999 !important;
            position: fixed !important;
        }

        /* 所有可能的通知样式 */
        .swal2-container, .swal2-popup {
            z-index: 99999 !important;
        }

        /* Bootstrap通知 */
        .alert-dismissible, .alert {
            z-index: 99999 !important;
        }

        /* 自动刷新通知的z-index */
        .auto-refresh-toast, .auto-refresh-notification {
            z-index: 99999 !important;
            position: fixed !important;
        }

        /* 所有可能的通知容器 */
        [class*="toast"], [class*="notification"], [class*="alert"] {
            z-index: 99999 !important;
        }

        /* 天气设置模态框CSS样式 */
        .modal {
            z-index: 1050 !important;
        }

        .modal-dialog {
            z-index: 1050 !important;
        }

        /* 确保模态框内容正确显示 */
        #weatherSettingsModal {
            z-index: 1050 !important;
        }

        #weatherSettingsModal .modal-dialog {
            z-index: 1050 !important;
        }























        /* 调整模态框宽度 - 稍微缩小 */
        #weatherSettingsModal .modal-dialog {
            max-width: 520px !important; /* 从默认的540px缩小到520px */
        }

        /* 修复模态框内容显示 */
        #weatherSettingsModal .modal-content {
            background-color: #fff !important;
            border: 1px solid #dee2e6 !important;
            border-radius: 0.375rem !important;
        }

        #weatherSettingsModal .modal-header {
            background-color: #f8f9fa !important;
            border-bottom: 1px solid #dee2e6 !important;
        }

        #weatherSettingsModal .modal-body {
            padding: 0.75rem !important; /* 减少顶部和底部内边距 */
        }

        #weatherSettingsModal .modal-footer {
            background-color: #f8f9fa !important;
            border-top: 1px solid #dee2e6 !important;
            padding: 0.75rem !important; /* 减少底部内边距 */
        }

        #weatherSettingsModal .modal-header {
            background-color: #f8f9fa !important;
            border-bottom: 1px solid #dee2e6 !important;
            padding: 0.75rem 1rem !important; /* 减少顶部内边距 */
        }

        /* 去除模态框内标签的加粗样式 */
        #weatherSettingsModal .form-label {
            font-weight: normal !important;
        }

        /* 调整帮助文本字体大小 */
        #weatherSettingsModal .form-text {
            font-size: 0.75rem !important; /* 更小的字体 */
        }

        /* 调整模态框左侧白边宽度 - 往右缩小一点点 */
        #weatherSettingsModal .modal-body {
            padding-left: 0.75rem !important; /* 稍微增加左边距 */
            padding-right: 1rem !important;
        }

        /* 天气API服务商和API密钥输入框 - 文字调大一点点 */
        #weatherSettingsModal #weatherApiProvider,
        #weatherSettingsModal #weatherApiKey {
            font-weight: 550 !important;
            font-size: 0.95rem !important; /* 稍微大一点 */
        }

        /* 默认城市和缓存时间输入框 - 文字减细一点点 */
        #weatherSettingsModal #defaultCity,
        #weatherSettingsModal #cacheTime {
            font-weight: 450 !important; /* 减细一点点 */
            font-size: 0.875rem !important; /* 保持默认大小 */
        }

        /* 其他输入框保持默认 */
        #weatherSettingsModal .form-control:not(#defaultCity):not(#cacheTime):not(#weatherApiKey),
        #weatherSettingsModal .form-select:not(#weatherApiProvider) {
            font-weight: 500 !important;
        }

        /* 取消按钮样式 - 与仪表盘页面一致：默认粉色，悬停灰色 */
        #weatherSettingsModal .btn-secondary {
            background-color: #ff6b95 !important; /* 粉色 */
            border-color: #ff6b95 !important;
            color: #fff !important;
        }

        #weatherSettingsModal .btn-secondary:hover {
            background-color: #6c757d !important; /* 悬停时灰色 */
            border-color: #6c757d !important;
            color: #fff !important;
        }

        /* 城市建议框样式 */
        .city-suggestion:hover {
            background-color: #f8f9fa;
            color: var(--primary-color);
        }

        .city-suggestion:last-child {
            border-bottom: none !important;
        }

        /* 设置页面特定样式 */
        .settings-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--dark-color);
        }

        .settings-subtitle {
            font-size: 1.2rem;
            font-weight: 500;
            margin-bottom: 15px;
            color: var(--dark-color);
            border-bottom: 1px solid var(--light-color);
            padding-bottom: 10px;
        }

        .settings-card {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: 0 4px 10px var(--shadow-color);
            padding: 20px;
            margin-bottom: 20px;
            animation: fadeInUp 0.5s;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-weight: 500;
            margin-bottom: 8px;
            display: block;
        }

        .form-control {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid rgba(255, 107, 149, 0.3);
            border-radius: var(--border-radius);
            transition: all var(--transition-speed);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(255, 107, 149, 0.2);
            outline: none;
        }

        .form-text {
            font-size: 0.85rem;
            color: var(--gray-color);
            margin-top: 5px;
        }

        .form-check {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .form-check-input {
            margin-right: 10px;
        }

        .form-check-label {
            font-weight: normal;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 500;
            transition: all var(--transition-speed);
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: var(--white-color);
        }

        .btn-primary:hover {
            background-color: #e05a84;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(255, 107, 149, 0.3);
        }

        .btn-secondary {
            background-color: var(--light-color);
            color: var(--dark-color);
        }

        .btn-secondary:hover {
            background-color: #e2e6ea;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .preview-image {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: var(--border-radius);
            margin-top: 10px;
            border: 1px solid var(--light-color);
        }

        .preview-bg {
            width: 100%;
            height: 150px;
            object-fit: cover;
            border-radius: var(--border-radius);
            margin-top: 10px;
            border: 1px solid var(--light-color);
        }

        .nav-tabs {
            display: flex;
            list-style: none;
            padding: 0;
            margin-bottom: 20px;
            border-bottom: 1px solid var(--light-color);
        }

        .nav-tabs .nav-item {
            margin-right: 10px;
        }

        .nav-tabs .nav-link {
            display: block;
            padding: 10px 15px;
            text-decoration: none;
            color: var(--gray-color);
            border-bottom: 2px solid transparent;
            transition: all var(--transition-speed);
        }

        .nav-tabs .nav-link:hover {
            color: var(--primary-color);
        }

        .nav-tabs .nav-link.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .tab-content {
            padding: 20px 0;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
            animation: fadeIn 0.5s;
        }

        .alert {
            padding: 15px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            animation: fadeIn 0.5s;
        }

        .alert-success {
            background-color: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.2);
            color: #28a745;
        }

        .alert-danger {
            background-color: rgba(220, 53, 69, 0.1);
            border: 1px solid rgba(220, 53, 69, 0.2);
            color: #dc3545;
        }

        /* 动画 */
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .sidebar {
                width: 0;
                padding: 0;
            }

            .sidebar.collapsed {
                width: 70px;
            }

            .main-content {
                margin-left: 0;
            }

            .main-content.expanded {
                margin-left: 70px;
            }
        }
    </style>
</head>
<body>
    <div class="loading-overlay" id="loading-overlay">
        <div class="spinner"></div>
    </div>

    <div class="dashboard-container">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <img src="<?php echo htmlspecialchars($settings['site_logo'] ?? '/assets/images/logo.png'); ?>" alt="Logo" class="sidebar-logo" onerror="this.src='data:image/svg+xml;utf8,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 24 24%22 fill=%22%23ffffff%22%3E%3Cpath d=%22M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z%22/%3E%3C/svg%3E'">
                <h3 class="sidebar-title"><?php echo htmlspecialchars($settings['site_name'] ?? '去水印接口'); ?></h3>
                <p class="sidebar-subtitle">管理系统</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="../"><i class="bi bi-speedometer2"></i> <span>仪表盘</span></a></li>

                <!-- 全局设置 -->
                <?php if ($isAdmin): ?>
                <li class="menu-section active">
                    <a href="#" class="menu-header"><i class="bi bi-gear"></i> <span>全局设置</span> <i class="bi bi-chevron-down toggle-icon"></i></a>
                    <ul class="submenu">
                        <li><a href="../"><i class="bi bi-globe"></i> <span>网站设置</span></a></li>
                        <li><a href="./" class="active"><i class="bi bi-person-plus"></i> <span>注册设置</span></a></li>
                        <li><a href="/dashboard/settings/payment"><i class="bi bi-credit-card"></i> <span>支付设置</span></a></li>
                        <li><a href="/dashboard/settings/login"><i class="bi bi-box-arrow-in-right"></i> <span>登录设置</span></a></li>
                        <li><a href="/dashboard/settings/api"><i class="bi bi-code-slash"></i> <span>接口设置</span></a></li>
                        <li><a href="/dashboard/settings/homepage"><i class="bi bi-house"></i> <span>网站首页</span></a></li>
                    </ul>
                </li>
                <?php endif; ?>

                <!-- 用户管理 -->
                <li class="menu-section">
                    <a href="#" class="menu-header"><i class="bi bi-people"></i> <span>用户管理</span> <i class="bi bi-chevron-down toggle-icon"></i></a>
                    <ul class="submenu">
                        <li><a href="/dashboard/accounts/users.php"><i class="bi bi-list"></i> <span>用户列表</span></a></li>
                        <li><a href="/dashboard/accounts/increase"><i class="bi bi-plus-circle"></i> <span>账户加款</span></a></li>
                        <li><a href="/dashboard/accounts/package"><i class="bi bi-gift"></i> <span>添加套餐</span></a></li>
                    </ul>
                </li>

                <!-- 订单管理 -->
                <li class="menu-section">
                    <a href="#" class="menu-header"><i class="bi bi-cart3"></i> <span>订单管理</span> <i class="bi bi-chevron-down toggle-icon"></i></a>
                    <ul class="submenu">
                        <li><a href="/dashboard/orders"><i class="bi bi-receipt"></i> <span>订单列表</span></a></li>
                        <li><a href="/dashboard/orders/calls"><i class="bi bi-telephone"></i> <span>调用记录</span></a></li>
                        <li><a href="/dashboard/orders/ranking"><i class="bi bi-trophy"></i> <span>用户排行</span></a></li>
                        <li><a href="/dashboard/orders/errors"><i class="bi bi-exclamation-triangle"></i> <span>错误排行</span></a></li>
                    </ul>
                </li>

                <!-- 套餐管理 -->
                <li class="menu-section">
                    <a href="#" class="menu-header"><i class="bi bi-box"></i> <span>套餐管理</span> <i class="bi bi-chevron-down toggle-icon"></i></a>
                    <ul class="submenu">
                        <li><a href="/dashboard/platforms/add"><i class="bi bi-plus"></i> <span>添加套餐</span></a></li>
                        <li><a href="/dashboard/platforms"><i class="bi bi-list"></i> <span>套餐列表</span></a></li>
                    </ul>
                </li>

                <!-- 到期提醒 -->
                <li class="menu-section">
                    <a href="#" class="menu-header"><i class="bi bi-bell"></i> <span>到期提醒</span> <i class="bi bi-chevron-down toggle-icon"></i></a>
                    <ul class="submenu">
                        <li><a href="/dashboard/recycle"><i class="bi bi-gear"></i> <span>到期提醒配置</span></a></li>
                        <li><a href="/dashboard/recycle/logs"><i class="bi bi-journal-text"></i> <span>到期提醒记录</span></a></li>
                    </ul>
                </li>

                <!-- 系统管理 -->
                <?php if ($isAdmin): ?>
                <li class="menu-section">
                    <a href="#" class="menu-header"><i class="bi bi-tools"></i> <span>系统管理</span> <i class="bi bi-chevron-down toggle-icon"></i></a>
                    <ul class="submenu">
                        <li><a href="/dashboard/plugins"><i class="bi bi-info-circle"></i> <span>功能介绍</span></a></li>
                        <li><a href="/dashboard/plugins/update"><i class="bi bi-arrow-up-circle"></i> <span>系统更新</span></a></li>
                    </ul>
                </li>
                <?php endif; ?>

                <li><a href="/logout.php"><i class="bi bi-box-arrow-right"></i> <span>退出登录</span></a></li>
            </ul>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="header">
                <button class="menu-toggle" id="menu-toggle"><i class="bi bi-list"></i></button>
                <div class="header-info-content" id="header-info-content">
                    <div class="wisdom-quote">
                        <i class="bi bi-quote"></i>
                        <span id="wisdom-text">加载中...</span>
                    </div>
                    <div class="weather-info">
                        <i class="bi bi-cloud-sun"></i>
                        <span id="weather-text">加载中...</span>
                    </div>
                    <div class="info-actions">
                        <button id="refresh-info" title="刷新"><i class="bi bi-arrow-clockwise"></i></button>
                        <button id="toggle-info-type" title="切换显示内容"><i class="bi bi-shuffle"></i></button>
                        <button id="settings-info" title="设置" style="cursor: pointer; pointer-events: auto;"><i class="bi bi-gear"></i></button>
                    </div>


                    <!-- 天气设置模态框 - 完整复制自仪表盘页面 -->
                    <div class="modal fade" id="weatherSettingsModal" tabindex="-1" aria-labelledby="weatherSettingsModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="weatherSettingsModalLabel">
                                        <i class="bi bi-cloud-sun me-2"></i>天气设置
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <form id="weatherSettingsForm">
                                        <div class="mb-3">
                                            <label for="weatherApiProvider" class="form-label">天气API服务商</label>
                                            <select class="form-select" id="weatherApiProvider" name="api_provider" onchange="updateApiProviderInfo()">
                                                <option value="openweather">OpenWeatherMap</option>
                                            </select>
                                            <div id="apiProviderInfo" class="mt-2">
                                                <div class="form-text">
                                                    <i class="bi bi-info-circle"></i>
                                                    <span id="providerDescription">选择API服务商后将显示官网链接</span>
                                                    <a id="providerLink" href="#" target="_blank" class="ms-2" style="display: none;">
                                                        <i class="bi bi-box-arrow-up-right"></i> 前往官网
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-3" id="apiKeySection">
                                            <label for="weatherApiKey" class="form-label" id="apiKeyLabel">API密钥</label>
                                            <input type="text" class="form-control" id="weatherApiKey" name="api_key" placeholder="请输入API密钥">
                                            <div class="form-text" id="apiKeyHelp">
                                                请到对应服务商官网申请免费API密钥
                                                <a href="../api/api-guide.html" target="_blank" style="margin-left: 10px;">📖 获取指南</a>
                                            </div>
                                        </div>


                                        <div class="mb-3">
                                            <label for="defaultCity" class="form-label">默认城市</label>
                                            <div class="position-relative">
                                                <input type="text" class="form-control" id="defaultCity" name="default_city"
                                                       placeholder="请输入城市名称，如：北京、上海、广州"
                                                       autocomplete="off"
                                                       oninput="showCitySuggestions(this.value)">
                                                <div id="citySuggestions" class="position-absolute w-100 bg-white border rounded shadow-sm"
                                                     style="top: 100%; z-index: 1000; display: none; max-height: 200px; overflow-y: auto;">
                                                </div>
                                            </div>
                                            <div class="form-text">
                                                <i class="bi bi-geo-alt"></i>
                                                此设置将影响顶部导航栏显示的天气信息，支持中文城市名称。
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="weatherEnabled" name="enabled" checked>
                                                <label class="form-check-label" for="weatherEnabled">
                                                    启用天气功能
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="cacheTime" class="form-label">缓存时间（分钟）</label>
                                            <input type="number" class="form-control" id="cacheTime" name="cache_duration" value="30" min="5" max="1440">
                                            <div class="form-text">设置天气数据缓存时间，减少API调用次数</div>
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" onclick="closeWeatherModal()">取消</button>
                                    <button type="button" class="btn btn-primary" onclick="saveWeatherSettings()">保存设置</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="user-info dropdown">
                    <div class="user-avatar"><?php echo htmlspecialchars($userInitial); ?></div>
                    <span class="user-name"><?php echo htmlspecialchars($username); ?></span>
                    <div class="dropdown-content">
                        <a href="/profile/index.php"><i class="bi bi-person"></i> 个人资料</a>
                        <a href="/dashboard/settings"><i class="bi bi-gear"></i> 设置</a>
                        <a href="/logout.php"><i class="bi bi-box-arrow-right"></i> 退出登录</a>
                    </div>
                </div>
            </div>

            <!-- 面包屑导航 -->
            <?php
            // 设置当前页面标题
            $page_title = '注册设置';
            // 设置当前页面路径
            $page_path = [
                ['title' => '全局设置', 'url' => '../']
            ];
            // 包含面包屑导航组件
            include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/admin-breadcrumb.php');
            ?>

            <h1 class="settings-title">注册设置</h1>

            <?php if (isset($saveSuccess) && $saveSuccess): ?>
            <div class="alert alert-success">
                <i class="bi bi-check-circle-fill"></i> 设置已成功保存！
            </div>
            <?php endif; ?>

            <!-- 设置选项卡 -->
            <ul class="nav-tabs" id="settingsTabs">
                <li class="nav-item">
                    <a class="nav-link active" href="#basic" data-tab="basic">注册设置</a>
                </li>
            </ul>

            <!-- 设置表单 -->
            <form action="" method="post" enctype="multipart/form-data">
                <div class="tab-content">
                    <!-- 注册设置 -->
                    <div class="tab-pane active" id="basic-tab">
                        <div class="settings-card">
                            <h2 class="settings-subtitle">注册基本设置</h2>
                            <div class="form-group">
                                <label for="register_type" class="form-label">注册类型</label>
                                <select id="register_type" name="register_type" class="form-control form-select">
                                    <option value="1" <?php echo ($settings['register_type'] ?? '1') == '1' ? 'selected' : ''; ?>>零售计点</option>
                                    <option value="2" <?php echo ($settings['register_type'] ?? '1') == '2' ? 'selected' : ''; ?>>包年包月</option>
                                    <option value="3" <?php echo ($settings['register_type'] ?? '1') == '3' ? 'selected' : ''; ?>>关闭注册</option>
                                </select>
                                <div class="form-text">选择用户注册后的账户类型，关闭注册将禁止新用户注册</div>
                            </div>
                            <div class="form-group">
                                <label for="register_time_type" class="form-label">赠送时长类型</label>
                                <select id="register_time_type" name="register_time_type" class="form-control form-select">
                                    <option value="0" <?php echo ($settings['register_time_type'] ?? '0') == '0' ? 'selected' : ''; ?>>月</option>
                                    <option value="1" <?php echo ($settings['register_time_type'] ?? '0') == '1' ? 'selected' : ''; ?>>年</option>
                                </select>
                                <div class="form-text">注册类型为零售计点时赠送时长无效</div>
                            </div>
                            <div class="form-group">
                                <label for="register_give_points" class="form-label">赠送点数</label>
                                <input type="number" id="register_give_points" name="register_give_points" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['register_give_points'] ?? '500'); ?>" min="0">
                                <div class="form-text">新用户注册时赠送的点数，计点模式下有效</div>
                            </div>
                            <div class="form-group">
                                <label for="register_invite_reward" class="form-label">邀请奖励</label>
                                <input type="number" id="register_invite_reward" name="register_invite_reward" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['register_invite_reward'] ?? '0.1'); ?>" step="0.1" min="0">
                                <div class="form-text">邀请新用户注册的奖励金额，设为0则不奖励</div>
                            </div>
                        </div>

                        <div class="settings-card">
                            <h2 class="settings-subtitle">邮箱验证设置</h2>
                            <div class="form-group">
                                <label for="register_email_enable" class="form-label">邮箱验证</label>
                                <select id="register_email_enable" name="register_email_enable" class="form-control form-select">
                                    <option value="0" <?php echo ($settings['register_email_enable'] ?? '1') == '0' ? 'selected' : ''; ?>>关闭</option>
                                    <option value="1" <?php echo ($settings['register_email_enable'] ?? '1') == '1' ? 'selected' : ''; ?>>开启</option>
                                </select>
                                <div class="form-text">是否启用邮箱验证功能</div>
                            </div>
                            <div class="form-group">
                                <label for="register_email_username" class="form-label">邮箱用户名</label>
                                <input type="email" id="register_email_username" name="register_email_username" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['register_email_username'] ?? ''); ?>">
                                <div class="form-text">发送验证邮件的邮箱地址</div>
                            </div>
                            <div class="form-group">
                                <label for="register_email_authcode" class="form-label">邮箱授权码</label>
                                <input type="password" id="register_email_authcode" name="register_email_authcode" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['register_email_authcode'] ?? ''); ?>">
                                <div class="form-text">邮箱的SMTP授权码，用于发送验证邮件</div>
                            </div>
                            <div class="form-group">
                                <label for="register_email_smtp" class="form-label">SMTP服务器</label>
                                <input type="text" id="register_email_smtp" name="register_email_smtp" class="form-control"
                                       value="<?php echo htmlspecialchars($settings['register_email_smtp'] ?? ''); ?>"
                                       placeholder="如：smtp.qq.com">
                                <div class="form-text">
                                    邮箱的SMTP服务器地址<br>
                                    <small class="text-muted">
                                        常用配置：QQ邮箱(smtp.qq.com) | 163邮箱(smtp.163.com) | Gmail(smtp.gmail.com)
                                    </small>
                                </div>
                            </div>

                        </div>

                        <!-- 保存按钮 -->
                        <div class="settings-card">
                            <div class="form-group">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">保存设置</button>
                                    <button type="button" class="btn btn-outline-primary" onclick="sendTestEmail()">
                                        <i class="bi bi-envelope-check"></i> 邮件测试
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 消息提示 -->
    <div class="toast" id="toast">
        <div class="toast-content">
            <i class="bi bi-check-circle-fill toast-icon success"></i>
            <i class="bi bi-x-circle-fill toast-icon error"></i>
            <i class="bi bi-info-circle-fill toast-icon info"></i>
            <div class="toast-message">操作成功</div>
        </div>
        <div class="toast-progress"></div>
    </div>

    <!-- 保存成功模态框 -->
    <div class="modal" id="save-success-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>保存成功</h3>
                <button class="close-btn" onclick="closeModal('save-success-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <p>系统设置已成功保存！</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="closeModal('save-success-modal')">确定</button>
            </div>
        </div>
    </div>

    <!-- 引入Bootstrap和其他JS库 -->
    <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/footer-scripts-settings.php'); ?>
    <script src="/assets/js/settings-reset-fix.js"></script>

    <!-- 最小化修复脚本：只解决头像遮罩和模态框误触问题 -->
    <script src="/assets/js/minimal-fix.js"></script>


    <script>
        // 全局变量
        let selectedCity = null;
        let isSubmitting = false; // 防止重复提交的全局标志













        // 页面加载完成后初始化天气设置按钮
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成，开始初始化天气设置按钮');

            // 查找天气设置按钮
            console.log('开始查找天气设置按钮...');
            const settingsBtn = document.getElementById('settings-info');
            const allButtons = document.querySelectorAll('button');
            const weatherButtons = document.querySelectorAll('[id*="settings"], [id*="weather"]');

            console.log('找到的元素:');
            console.log('- settings-info按钮:', settingsBtn);
            console.log('- 所有按钮数量:', allButtons.length);
            console.log('- 设置按钮数量:', weatherButtons.length);

            if (settingsBtn) {
                console.log('找到天气设置按钮，开始初始化...');

                // 确保按钮可见和可点击
                settingsBtn.style.cursor = 'pointer';
                settingsBtn.style.pointerEvents = 'auto';
                settingsBtn.style.display = 'flex';
                settingsBtn.style.visibility = 'visible';

                console.log('按钮样式已设置:', {
                    cursor: settingsBtn.style.cursor,
                    pointerEvents: settingsBtn.style.pointerEvents,
                    display: settingsBtn.style.display,
                    visibility: settingsBtn.style.visibility
                });

                // 移除所有现有的事件监听器
                const newBtn = settingsBtn.cloneNode(true);
                settingsBtn.parentNode.replaceChild(newBtn, settingsBtn);

                // 添加点击事件监听器
                newBtn.addEventListener('click', function(e) {
                    console.log('=== 天气设置按钮点击事件触发 ===');
                    console.log('事件对象:', e);
                    console.log('目标元素:', e.target);
                    e.preventDefault();
                    e.stopPropagation();
                    toggleWeatherSettings(e);
                });

                newBtn.addEventListener('mousedown', function(e) {
                    console.log('天气设置按钮 mousedown 事件');
                });

                newBtn.addEventListener('mouseup', function(e) {
                    console.log('天气设置按钮 mouseup 事件');
                });

                newBtn.addEventListener('mouseover', function(e) {
                    console.log('天气设置按钮 mouseover 事件');
                });

                console.log('天气设置按钮已初始化，事件监听器已添加');
            } else {
                console.error('未找到天气设置按钮');
            }

            // 初始化刷新和切换按钮
            const refreshBtn = document.getElementById('refresh-info');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    refreshWeatherInfo();
                });
            }

            const toggleBtn = document.getElementById('toggle-info-type');
            if (toggleBtn) {
                toggleBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    toggleInfoType();
                });
            }

            // 初始化天气信息和哲理句子
            initWeatherAndWisdom();

            // 初始化显示状态
            const currentType = localStorage.getItem('infoType') || 'both';
            updateDisplayState(currentType);
        });

        // 初始化天气信息和哲理句子
        function initWeatherAndWisdom() {
            // 初始化哲理句子
            const wisdomText = document.getElementById('wisdom-text');
            if (wisdomText) {
                const quotes = [
                    '今天是个好日子，适合管理系统设置',
                    '保持学习的心态，每天都有进步',
                    '优雅的配置，简洁的管理',
                    '细心配置，用心管理',
                    '系统设置如诗，优雅而简洁'
                ];
                const randomQuote = quotes[Math.floor(Math.random() * quotes.length)];
                wisdomText.textContent = randomQuote;
            }

            // 初始化天气信息
            const weatherText = document.getElementById('weather-text');
            if (weatherText) {
                // 获取设置中的城市
                const settings = JSON.parse(localStorage.getItem('weatherSettings')) || {
                    city: '北京'
                };

                // 显示默认天气信息
                const conditions = ['晴', '多云', '阴'];
                const randomCondition = conditions[Math.floor(Math.random() * conditions.length)];
                const randomTemp = Math.floor(Math.random() * 15) + 15; // 15-29度
                weatherText.textContent = `${settings.city} ${randomCondition} ${randomTemp}°C`;
            }
        }

        // 刷新天气信息
        function refreshWeatherInfo() {
            const weatherText = document.getElementById('weather-text');
            if (!weatherText) return;

            // 显示刷新状态
            weatherText.textContent = '刷新天气中...';

            // 获取设置中的城市
            const settings = JSON.parse(localStorage.getItem('weatherSettings')) || {
                city: '北京'
            };

            // 强制请求真实天气数据（不使用缓存）
            const timestamp = new Date().getTime();
            fetch(`../../api/weather.php?city=${encodeURIComponent(settings.city)}&t=${timestamp}`)
                .then(response => response.json())
                .then(data => {
                    let weatherDisplay = '';

                    if (data.success) {
                        const temp = data.data.temperature || '25';
                        const condition = data.data.condition || '晴';
                        const city = data.city || settings.city;
                        weatherDisplay = `${city} ${condition} ${temp}°C`;
                        console.log('天气刷新成功:', data);
                    } else {
                        weatherDisplay = `${settings.city} 获取失败`;
                        console.error('天气API返回错误:', data.message);
                    }

                    weatherText.textContent = weatherDisplay;

                    // 添加淡入动画
                    weatherText.style.animation = 'none';
                    setTimeout(() => {
                        weatherText.style.animation = 'fadeIn 0.5s';
                    }, 10);
                })
                .catch(error => {
                    console.error('刷新天气失败:', error);
                    weatherText.textContent = `${settings.city} 刷新失败`;
                });
        }

        // 天气设置按钮点击事件处理函数 - 完整复制自仪表盘页面
        function toggleWeatherSettings(event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            console.log('天气设置按钮被点击，打开模态框');

            // 加载当前设置
            loadWeatherSettings();

            // 打开模态框
            const modalElement = document.getElementById('weatherSettingsModal');
            if (modalElement) {
                console.log('找到模态框元素，准备显示');

                // 确保模态框没有被其他实例占用
                const existingModal = bootstrap.Modal.getInstance(modalElement);
                if (existingModal) {
                    existingModal.dispose();
                }

                // 创建新的模态框实例，使用Bootstrap标准配置
                const modal = new bootstrap.Modal(modalElement, {
                    backdrop: 'static',  // 点击空白处不关闭
                    keyboard: false,     // ESC键不关闭
                    focus: true
                });

                // 添加事件监听器
                modalElement.addEventListener('shown.bs.modal', function () {
                    console.log('模态框已显示');
                    // 调试模态框元素
                    setTimeout(() => {
                        debugModalElements();
                    }, 100);
                });

                modalElement.addEventListener('hidden.bs.modal', function () {
                    console.log('模态框已隐藏');
                });

                modal.show();
                console.log('模态框show()方法已调用');
            } else {
                console.error('未找到天气设置模态框');
            }
        }

        // 加载天气设置 - 完整复制自仪表盘页面
        function loadWeatherSettings() {
            fetch('../../api/weather-settings.php', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => {
                console.log('API响应状态:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text(); // 先获取文本，然后尝试解析JSON
            })
            .then(text => {
                console.log('API响应内容:', text);
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        document.getElementById('weatherApiProvider').value = data.settings.api_provider || 'openweather';
                        document.getElementById('weatherApiKey').value = data.settings.api_key || '';



                        // 检查输入框是否已有用户输入，如果有则不覆盖
                        const cityInput = document.getElementById('defaultCity');
                        if (!cityInput.value || cityInput.value.length <= 2) {
                            // 只有当输入框为空或内容很短时才使用数据库值
                            cityInput.value = data.settings.default_city || '北京';
                        }
                        console.log('城市输入框当前值:', cityInput.value);

                        document.getElementById('weatherEnabled').checked = data.settings.enabled !== false;
                        document.getElementById('cacheTime').value = data.settings.cache_duration || 30;

                        // 初始化API服务商信息
                        updateApiProviderInfo();
                    } else {
                        console.error('API返回错误:', data.message);
                    }
                } catch (parseError) {
                    console.error('JSON解析失败:', parseError);
                    console.error('响应内容:', text);
                    // 使用默认值
                    document.getElementById('weatherApiProvider').value = 'openweather';
                    document.getElementById('weatherApiKey').value = '';
                    document.getElementById('defaultCity').value = '北京';
                    document.getElementById('weatherEnabled').checked = true;
                    document.getElementById('cacheTime').value = 30;
                }
            })
            .catch(error => {
                console.error('加载天气设置失败:', error);
                // 使用默认值
                document.getElementById('weatherApiProvider').value = 'openweather';
                document.getElementById('weatherApiKey').value = '';
                document.getElementById('defaultCity').value = '北京';
                document.getElementById('weatherEnabled').checked = true;
                document.getElementById('cacheTime').value = 30;
            });
        }

        // 保存天气设置 - 完整复制自仪表盘页面
        function saveWeatherSettings() {
            const apiKey = document.getElementById('weatherApiKey').value.trim();
            // 优先使用全局变量中的城市，如果没有则使用输入框的值
            const defaultCity = window.selectedCity || document.getElementById('defaultCity').value.trim();

            console.log('保存的城市值:', defaultCity);
            console.log('全局变量城市:', window.selectedCity);
            console.log('输入框城市:', document.getElementById('defaultCity').value);

            // 验证必填字段
            if (!apiKey) {
                if (typeof showToast === 'function') {
                    showToast('请输入API密钥', 'warning');
                } else {
                    alert('请输入API密钥');
                }
                return;
            }

            if (!defaultCity) {
                if (typeof showToast === 'function') {
                    showToast('请输入默认城市', 'warning');
                } else {
                    alert('请输入默认城市');
                }
                return;
            }

            const formData = {
                api_provider: document.getElementById('weatherApiProvider').value,
                api_key: apiKey,
                default_city: defaultCity,
                enabled: document.getElementById('weatherEnabled').checked,
                cache_duration: parseInt(document.getElementById('cacheTime').value)
            };

            fetch('../../api/weather-settings.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新localStorage中的天气设置
                    const weatherSettings = {
                        city: formData.default_city,
                        api_provider: formData.api_provider,
                        api_key: formData.api_key,
                        enabled: formData.enabled,
                        cache_duration: formData.cache_duration
                    };
                    localStorage.setItem('weatherSettings', JSON.stringify(weatherSettings));

                    // 关闭模态框
                    closeWeatherModal();

                    // 使用自动刷新通知，参考仪表盘更新版本按钮逻辑
                    console.log('🚀 直接调用showAutoRefreshToast - 天气设置保存');
                    if (typeof window.showAutoRefreshToast === 'function') {
                        window.showAutoRefreshToast('天气设置保存成功', {
                            refreshCallback: function() {
                                console.log('天气设置保存完成，直接刷新页面');
                                // 直接刷新页面，确保设置生效
                                location.reload();
                            }
                        });
                    } else {
                        console.error('❌ showAutoRefreshToast函数不存在');
                        // 备用方案
                        if (typeof showToast === 'function') {
                            showToast('天气设置保存成功', 'success');
                        } else {
                            alert('天气设置保存成功');
                        }
                        setTimeout(() => location.reload(), 1000);
                    }
                } else {
                    if (typeof showToast === 'function') {
                        showToast(data.message || '保存失败', 'error');
                    }
                }
            })
            .catch(error => {
                console.error('保存天气设置失败:', error);
                if (typeof showToast === 'function') {
                    showToast('保存失败', 'error');
                }
            });
        }

        // 更新API服务商信息 - 完整复制自仪表盘页面
        function updateApiProviderInfo() {
            const provider = document.getElementById('weatherApiProvider').value;
            const description = document.getElementById('providerDescription');
            const link = document.getElementById('providerLink');
            const apiKeyLabel = document.getElementById('apiKeyLabel');
            const apiKeyHelp = document.getElementById('apiKeyHelp');
            const apiKeyInput = document.getElementById('weatherApiKey');
            const privateKeySection = document.getElementById('privateKeySection');

            const providerInfo = {
                'openweather': {
                    name: 'OpenWeatherMap',
                    description: '全球领先的天气数据服务商，免费额度1000次/天',
                    url: 'https://openweathermap.org/api',
                    keyLabel: 'API密钥',
                    keyPlaceholder: '请输入OpenWeatherMap API密钥',
                    keyHelp: '请到 <a href="https://openweathermap.org/api" target="_blank">OpenWeatherMap</a> 申请免费API密钥',
                    needPrivateKey: false
                }
            };

            if (providerInfo[provider]) {
                const info = providerInfo[provider];
                description.textContent = info.description;
                link.href = info.url;
                link.style.display = 'inline';

                // 更新API密钥字段
                if (apiKeyLabel) apiKeyLabel.textContent = info.keyLabel;
                if (apiKeyInput) apiKeyInput.placeholder = info.keyPlaceholder;
                if (apiKeyHelp) {
                    // 清空现有内容
                    apiKeyHelp.innerHTML = '';

                    // 创建OpenWeatherMap链接
                    apiKeyHelp.appendChild(document.createTextNode('请到 '));
                    const link = document.createElement('a');
                    link.href = 'https://openweathermap.org/api';
                    link.target = '_blank';
                    link.textContent = 'OpenWeatherMap';
                    apiKeyHelp.appendChild(link);
                    apiKeyHelp.appendChild(document.createTextNode(' 申请免费API密钥'));
                }
            } else {
                description.textContent = '选择API服务商后将显示官网链接';
                link.style.display = 'none';
            }
        }

        // 城市搜索建议 - 完整复制自仪表盘页面
        function showCitySuggestions(value) {
            const suggestions = document.getElementById('citySuggestions');

            if (!value || value.length < 1) {
                suggestions.style.display = 'none';
                return;
            }

            // 显示加载状态
            suggestions.innerHTML = '<div class="px-3 py-2 text-muted">搜索中...</div>';
            suggestions.style.display = 'block';

            // 调用城市搜索API
            fetch(`../../api/cities.php?q=${encodeURIComponent(value)}`)
                .then(response => response.json())
                .then(data => {
                    const cities = data.cities || [];

                    if (cities.length === 0) {
                        suggestions.style.display = 'none';
                        return;
                    }

                    // 生成建议列表
                    suggestions.innerHTML = '';
                    cities.slice(0, 8).forEach(city => {
                        const div = document.createElement('div');
                        div.className = 'px-3 py-2';
                        div.style.cursor = 'pointer';
                        div.textContent = city;

                        // 使用onclick属性，更直接
                        div.onclick = function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log('点击城市项:', city);

                            // 直接设置输入框值
                            const input = document.getElementById('defaultCity');
                            if (input) {
                                input.value = city;
                                input.setAttribute('value', city);
                                console.log('直接设置输入框值为:', city);
                            }

                            // 隐藏建议框
                            const suggestions = document.getElementById('citySuggestions');
                            if (suggestions) {
                                suggestions.style.display = 'none';
                                suggestions.innerHTML = '';
                            }

                            // 存储到全局变量
                            window.selectedCity = city;

                            return false;
                        };

                        div.addEventListener('mouseenter', function() {
                            this.style.backgroundColor = '#f8f9fa';
                        });
                        div.addEventListener('mouseleave', function() {
                            this.style.backgroundColor = '';
                        });

                        suggestions.appendChild(div);
                    });

                    suggestions.style.display = 'block';
                })
                .catch(error => {
                    console.error('城市搜索失败:', error);
                    suggestions.innerHTML = '<div class="px-3 py-2 text-muted">搜索失败</div>';
                });
        }

        // 全局变量存储选中的城市
        window.selectedCity = '';

        // 选择城市 - 全新实现
        function selectCity(city) {
            console.log('=== 选择城市开始 ===');
            console.log('选择的城市:', city);

            // 存储到全局变量
            window.selectedCity = city;

            // 直接操作DOM
            const input = document.getElementById('defaultCity');
            const suggestions = document.getElementById('citySuggestions');

            if (input) {
                // 方法1：直接设置innerHTML
                input.outerHTML = `<input type="text" class="form-control" id="defaultCity" name="default_city" value="${city}" placeholder="请输入城市名称，如：北京、上海、广州" autocomplete="off" oninput="showCitySuggestions(this.value)">`;

                console.log('输入框已重新创建，值为:', city);
            }

            if (suggestions) {
                suggestions.style.display = 'none';
                suggestions.innerHTML = '';
            }

            // 验证设置是否成功
            setTimeout(() => {
                const newInput = document.getElementById('defaultCity');
                console.log('验证：输入框当前值为:', newInput ? newInput.value : 'null');
            }, 100);

            console.log('=== 选择城市结束 ===');
        }

        // 调试函数：检查模态框内的元素
        function debugModalElements() {
            console.log('=== 模态框元素调试 ===');
            const modal = document.getElementById('weatherSettingsModal');
            const input = document.getElementById('defaultCity');
            const suggestions = document.getElementById('citySuggestions');

            console.log('模态框:', modal);
            console.log('输入框:', input);
            console.log('建议框:', suggestions);

            if (input) {
                console.log('输入框当前值:', input.value);
                console.log('输入框是否可见:', input.offsetParent !== null);
            }

            if (suggestions) {
                console.log('建议框是否可见:', suggestions.style.display);
                console.log('建议框内容:', suggestions.innerHTML);
            }
        }

        // 关闭天气设置模态框
        function closeWeatherModal() {
            const modalElement = document.getElementById('weatherSettingsModal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }
        }

        // 切换信息类型
        function toggleInfoType() {
            console.log('切换按钮被点击');
            // 获取当前类型
            const currentType = localStorage.getItem('infoType') || 'both';
            let newType;

            // 循环切换类型
            switch (currentType) {
                case 'both':
                    newType = 'wisdom';
                    break;
                case 'wisdom':
                    newType = 'weather';
                    break;
                case 'weather':
                    newType = 'both';
                    break;
                default:
                    newType = 'both';
            }

            // 保存新类型
            localStorage.setItem('infoType', newType);

            // 更新显示
            updateDisplayState(newType);
        }

        // 更新显示状态
        function updateDisplayState(type) {
            const wisdomQuote = document.querySelector('.wisdom-quote');
            const weatherInfo = document.querySelector('.weather-info');

            if (!wisdomQuote || !weatherInfo) return;

            console.log('更新显示状态为:', type);

            // 先重置所有样式
            wisdomQuote.style.display = '';
            weatherInfo.style.display = '';
            wisdomQuote.style.opacity = '';
            weatherInfo.style.opacity = '';

            // 根据类型设置显示状态
            switch (type) {
                case 'wisdom':
                    wisdomQuote.style.display = 'flex';
                    weatherInfo.style.display = 'none';
                    break;
                case 'weather':
                    wisdomQuote.style.display = 'none';
                    weatherInfo.style.display = 'flex';
                    break;
                case 'both':
                default:
                    wisdomQuote.style.display = 'flex';
                    weatherInfo.style.display = 'flex';
                    break;
            }

            // 添加淡入动画
            setTimeout(() => {
                if (wisdomQuote.style.display !== 'none') {
                    wisdomQuote.style.animation = 'fadeIn 0.5s';
                }
                if (weatherInfo.style.display !== 'none') {
                    weatherInfo.style.animation = 'fadeIn 0.5s';
                }
            }, 10);
        }



        // 点击其他地方关闭建议框
        document.addEventListener('click', function(e) {
            const suggestions = document.getElementById('citySuggestions');
            const input = document.getElementById('defaultCity');

            if (suggestions && input && !input.contains(e.target) && !suggestions.contains(e.target)) {
                suggestions.style.display = 'none';
            }
        });

        // DOM元素
        const settingsTabs = document.getElementById('settingsTabs');
        const tabLinks = document.querySelectorAll('.nav-link');
        const tabPanes = document.querySelectorAll('.tab-pane');



        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化选项卡
            initTabs();

            // 初始化文件上传预览
            initFilePreview();

            // 初始化SMTP自动填充功能
            autoFillSMTP();

            // 侧边栏菜单功能
            console.log('初始化侧边栏菜单，找到菜单头数量:', document.querySelectorAll('.menu-header').length);

            // 确保默认展开的菜单正确显示
            document.querySelectorAll('.menu-header.active').forEach(activeHeader => {
                const menuSection = activeHeader.parentElement;
                menuSection.classList.add('active');
                console.log('设置默认展开菜单:', activeHeader.textContent.trim());
            });

            document.querySelectorAll('.menu-header').forEach((header, index) => {
                console.log('菜单头', index, ':', header.textContent.trim());
                header.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('点击菜单:', this.textContent.trim());
                    const menuSection = this.parentElement;
                    const isActive = menuSection.classList.contains('active');
                    console.log('菜单区域是否激活:', isActive);

                    // 关闭所有其他子菜单
                    document.querySelectorAll('.menu-section').forEach(section => {
                        section.classList.remove('active');
                    });

                    // 切换当前子菜单
                    if (!isActive) {
                        console.log('打开菜单:', this.textContent.trim());
                        menuSection.classList.add('active');
                    } else {
                        console.log('关闭菜单:', this.textContent.trim());
                    }
                });
            });

            // 菜单切换按钮功能
            const menuToggle = document.getElementById('menu-toggle');
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.querySelector('.main-content');

            console.log('🔍 菜单切换元素检查:');
            console.log('- 切换按钮:', menuToggle);
            console.log('- 侧边栏:', sidebar);
            console.log('- 主内容:', mainContent);

            if (menuToggle && sidebar && mainContent) {
                console.log('✅ 菜单切换功能已初始化');

                // 使用捕获阶段和多种事件类型确保能捕获到点击
                menuToggle.addEventListener('click', function(e) {
                    console.log('🔄 菜单切换按钮被点击 (click)');
                    e.preventDefault();
                    e.stopPropagation();
                    e.stopImmediatePropagation();

                    sidebar.classList.toggle('collapsed');
                    mainContent.classList.toggle('expanded');

                    console.log('📊 切换后状态:');
                    console.log('- 侧边栏collapsed:', sidebar.classList.contains('collapsed'));
                    console.log('- 主内容expanded:', mainContent.classList.contains('expanded'));
                }, true); // 使用捕获阶段

                // 添加mousedown事件作为备用
                menuToggle.addEventListener('mousedown', function(e) {
                    console.log('🔄 菜单切换按钮被点击 (mousedown)');
                    e.preventDefault();
                    e.stopPropagation();
                    e.stopImmediatePropagation();

                    sidebar.classList.toggle('collapsed');
                    mainContent.classList.toggle('expanded');

                    console.log('📊 切换后状态 (mousedown):');
                    console.log('- 侧边栏collapsed:', sidebar.classList.contains('collapsed'));
                    console.log('- 主内容expanded:', mainContent.classList.contains('expanded'));
                }, true);

            } else {
                console.error('❌ 菜单切换功能初始化失败，缺少必要元素');
            }

            // 强力菜单切换修复 - 直接绑定到document
            document.addEventListener('click', function(e) {
                if (e.target && (e.target.id === 'menu-toggle' || e.target.closest('#menu-toggle'))) {
                    console.log('🔄 强力菜单切换被触发');
                    e.preventDefault();
                    e.stopPropagation();
                    e.stopImmediatePropagation();

                    const sidebar = document.getElementById('sidebar');
                    const mainContent = document.querySelector('.main-content');

                    if (sidebar && mainContent) {
                        sidebar.classList.toggle('collapsed');
                        mainContent.classList.toggle('expanded');
                        console.log('📊 强力切换后状态:');
                        console.log('- 侧边栏collapsed:', sidebar.classList.contains('collapsed'));
                        console.log('- 主内容expanded:', mainContent.classList.contains('expanded'));
                    }
                }
            }, true);



            // 如果有保存成功的消息，显示标准成功通知（仅在非AJAX请求时）
            <?php if (isset($saveSuccess) && $saveSuccess && !isset($_SERVER['HTTP_X_REQUESTED_WITH'])): ?>
            setTimeout(() => {
                if (typeof showToast === 'function') {
                    showToast('系统设置已成功保存！', 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    alert('系统设置已成功保存！');
                    setTimeout(() => location.reload(), 1000);
                }
            }, 600);
            <?php endif; ?>
        });

        // 初始化选项卡
        function initTabs() {
            // 获取URL中的选项卡参数
            const urlParams = new URLSearchParams(window.location.search);
            const tabParam = urlParams.get('tab');

            // 如果URL中有选项卡参数，则激活对应的选项卡
            if (tabParam) {
                activateTab(tabParam);
            } else {
                // 如果没有选项卡参数，则更新面包屑导航为默认选项卡的文本
                const defaultTab = document.querySelector('.nav-link.active');
                if (defaultTab) {
                    updateBreadcrumb(defaultTab.textContent);
                }
            }

            // 为选项卡添加点击事件
            tabLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const tab = this.getAttribute('data-tab');
                    activateTab(tab);
                    // 更新URL，但不刷新页面
                    history.pushState(null, null, `?tab=${tab}`);
                });
            });
        }

        // 激活选项卡
        function activateTab(tab) {
            // 移除所有选项卡的激活状态
            tabLinks.forEach(link => {
                link.classList.remove('active');
            });
            tabPanes.forEach(pane => {
                pane.classList.remove('active');
            });

            // 激活指定的选项卡
            const activeLink = document.querySelector(`.nav-link[data-tab="${tab}"]`);
            const activePane = document.getElementById(`${tab}-tab`);

            if (activeLink && activePane) {
                activeLink.classList.add('active');
                activePane.classList.add('active');

                // 更新面包屑导航
                updateBreadcrumb(activeLink.textContent);
            }
        }

        // 更新面包屑导航
        function updateBreadcrumb(tabName) {
            const breadcrumbTitle = document.querySelector('.breadcrumb-title');
            if (breadcrumbTitle) {
                breadcrumbTitle.textContent = tabName;
            }
        }

        // 自动填充SMTP服务器
        function autoFillSMTP() {
            const emailInput = document.getElementById('register_email_username');
            const smtpInput = document.getElementById('register_email_smtp');

            if (!emailInput || !smtpInput) return;

            emailInput.addEventListener('blur', function() {
                const email = this.value.trim();
                if (!email || smtpInput.value.trim()) return; // 如果已有SMTP配置则不自动填充

                const domain = email.split('@')[1];
                if (!domain) return;

                const smtpConfigs = {
                    'qq.com': 'smtp.qq.com',
                    'foxmail.com': 'smtp.qq.com',
                    '163.com': 'smtp.163.com',
                    '126.com': 'smtp.126.com',
                    'gmail.com': 'smtp.gmail.com',
                    'outlook.com': 'smtp-mail.outlook.com',
                    'hotmail.com': 'smtp-mail.outlook.com',
                    'sina.com': 'smtp.sina.com',
                    'sohu.com': 'smtp.sohu.com'
                };

                if (smtpConfigs[domain]) {
                    smtpInput.value = smtpConfigs[domain];
                    showToast(`已自动填充${domain}的SMTP服务器配置`, 'success');
                }
            });
        }

        // 发送测试邮件
        function sendTestEmail() {
            const emailUsername = document.getElementById('register_email_username').value;
            const emailAuthcode = document.getElementById('register_email_authcode').value;
            const emailSmtp = document.getElementById('register_email_smtp').value;

            if (!emailUsername || !emailAuthcode || !emailSmtp) {
                showToast('请先完善邮箱配置信息', 'error');
                return;
            }

            // 显示加载状态
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="bi bi-hourglass-split"></i> 发送中...';
            button.disabled = true;

            // 发送AJAX请求
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '', true);
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

            xhr.onload = function() {
                button.innerHTML = originalText;
                button.disabled = false;

                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            showToast('测试邮件发送成功！请检查邮箱收件箱', 'success');
                        } else {
                            showToast('测试邮件发送失败：' + response.message, 'error');
                        }
                    } catch (e) {
                        console.error('JSON解析失败:', e);
                        console.error('响应内容:', xhr.responseText);
                        showToast('响应解析失败，请检查控制台查看详细信息', 'error');
                    }
                } else {
                    showToast('请求失败，状态码：' + xhr.status, 'error');
                }
            };

            xhr.onerror = function() {
                button.innerHTML = originalText;
                button.disabled = false;
                showToast('网络错误，请重试', 'error');
            };

            // 使用邮箱用户名作为测试邮箱地址
            const formData = `action=send_test_email&email_username=${encodeURIComponent(emailUsername)}&email_authcode=${encodeURIComponent(emailAuthcode)}&email_smtp=${encodeURIComponent(emailSmtp)}`;
            xhr.send(formData);
        }

        // 初始化文件上传预览
        function initFilePreview() {
            // Logo预览
            const logoInput = document.getElementById('site_logo');
            if (logoInput) {
                logoInput.addEventListener('change', function() {
                    previewImage(this, 'preview-image');
                });
            }

            // 图标预览
            const faviconInput = document.getElementById('site_favicon');
            if (faviconInput) {
                faviconInput.addEventListener('change', function() {
                    previewImage(this, 'preview-image');
                });
            }

            // 背景预览
            const bgInput = document.getElementById('login_bg');
            if (bgInput) {
                bgInput.addEventListener('change', function() {
                    previewImage(this, 'preview-bg');
                });
            }
        }

        // 预览图片
        function previewImage(input, previewClass) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    // 查找最近的预览图片元素
                    const previewContainer = input.parentElement.querySelector(`.${previewClass}`);

                    // 如果没有预览元素，则创建一个
                    if (!previewContainer) {
                        const container = document.createElement('div');
                        container.className = 'mt-2';

                        const label = document.createElement('p');
                        label.textContent = '预览：';

                        const img = document.createElement('img');
                        img.className = previewClass;
                        img.src = e.target.result;
                        img.alt = '预览';

                        container.appendChild(label);
                        container.appendChild(img);
                        input.parentElement.appendChild(container);
                    } else {
                        // 如果已有预览元素，则更新src
                        previewContainer.src = e.target.result;
                    }
                };

                reader.readAsDataURL(input.files[0]);
            }
        }

        // 表单提交前验证 - 确保只绑定一次，选择主表单而不是天气设置表单
        const form = document.querySelector('form:not(#weatherSettingsForm)');
        console.log('🔍 检查主表单元素:', form);
        console.log('🔍 主表单是否已绑定处理器:', form ? form.hasAttribute('data-submit-handler-bound') : 'form不存在');

        if (form && !form.hasAttribute('data-submit-handler-bound')) {
            console.log('✅ 绑定表单提交处理器');
            form.setAttribute('data-submit-handler-bound', 'true');
            form.addEventListener('submit', function(e) {
                console.log('📝 表单提交事件触发，事件对象:', e);
            // 阻止默认提交行为
            e.preventDefault();

            // 强力防重复提交检查
            if (isSubmitting || sessionStorage.getItem('settingsSubmitting') === 'true') {
                console.log('⚠️ 设置正在保存中，忽略重复提交');
                return false;
            }

            // 设置全局和本地提交标志
            isSubmitting = true;
            sessionStorage.setItem('settingsSubmitting', 'true');
            console.log('🚀 开始保存设置...');

            // 设置过期时间（15秒后自动清除）
            setTimeout(() => {
                isSubmitting = false;
                sessionStorage.removeItem('settingsSubmitting');
                console.log('⏰ 提交标志已自动清除');
            }, 15000);

            // 显示加载动画
            showLoading();

            // 获取表单数据
            const formData = new FormData(this);

            // 使用 AJAX 提交表单，避免页面刷新
            console.log('🚀 开始AJAX提交表单');
            console.log('📍 提交URL:', this.action);
            console.log('📦 表单数据:', formData);

            const xhr = new XMLHttpRequest();
            xhr.open('POST', this.action, true);
            xhr.onload = function() {
                console.log('✅ 设置保存请求完成');
                console.log('📊 响应状态码:', xhr.status);
                console.log('📄 响应内容:', xhr.responseText);
                console.log('📋 响应头:', xhr.getAllResponseHeaders());

                // 隐藏加载动画
                hideLoading();

                // 清除所有提交标志
                isSubmitting = false;
                sessionStorage.removeItem('settingsSubmitting');
                console.log('🔓 提交标志已清除');

                // 检查响应状态
                if (xhr.status === 200) {
                    // 显示成功提示并刷新页面
                    console.log('✅ 系统设置保存成功，开始刷新流程');

                if (typeof showToast === 'function') {
                    showToast('注册设置已成功保存！', 'success');
                } else {
                    alert('系统设置已成功保存！');
                }

                // 如果是外观设置，先强制刷新favicon
                const activeTab = document.querySelector('.nav-link.active');
                const currentTabId = activeTab ? activeTab.getAttribute('data-tab') : '';

                console.log('🔍 当前激活的选项卡:', currentTabId);
                console.log('🔍 激活的选项卡元素:', activeTab);

                setTimeout(() => {
                    console.log('⏰ 开始执行刷新逻辑，当前选项卡:', currentTabId);

                    if (currentTabId === 'appearance') {
                        console.log('🎨 外观设置保存完成，开始特殊处理流程');
                        const timestamp = new Date().getTime();
                        console.log('⏰ 生成时间戳:', timestamp);

                        // 检查函数是否存在
                        if (typeof updatePageFaviconAfterSave === 'function') {
                            console.log('🔧 调用updatePageFaviconAfterSave函数');
                            updatePageFaviconAfterSave(timestamp);
                        } else {
                            console.log('⚠️ updatePageFaviconAfterSave函数不存在');
                        }

                        setTimeout(() => {
                            console.log('🔄 执行页面重载 (外观设置)');
                            window.location.reload(true); // 强制从服务器重新加载
                        }, 200);
                    } else {
                        console.log('📄 其他设置保存完成，直接重载页面');
                        console.log('🔄 执行页面重载 (其他设置)');
                        window.location.reload(true);
                    }
                }, 800); // 减少延迟时间
                } else {
                    // 请求失败
                    console.log('❌ 设置保存失败，状态码:', xhr.status);
                    showToast('保存设置失败，请重试！', 'error');
                }
            };
            xhr.onerror = function() {
                console.log('❌ 设置保存请求失败');

                // 隐藏加载动画
                hideLoading();

                // 显示错误提示
                showToast('保存设置失败，请重试！', 'error');

                // 清除所有提交标志
                isSubmitting = false;
                sessionStorage.removeItem('settingsSubmitting');
                console.log('🔓 错误时提交标志已清除');
            };
            xhr.send(formData);
            });
        }
    </script>
    <!-- 网站底部 -->
    <div class="site-footer">
        <div class="footer-content">
            <div class="copyright">
                <?php echo htmlspecialchars($settings['site_copyright']); ?>
            </div>
            <div class="beian">
                <?php if (!empty($settings['icp_number'])): ?>
                <a href="https://beian.miit.gov.cn/" target="_blank" class="beian-link">
                    <i class="bi bi-shield-check"></i> <?php echo htmlspecialchars($settings['icp_number']); ?>
                </a>
                <?php endif; ?>
                <?php if (!empty($settings['police_number'])): ?>
                <a href="http://www.beian.gov.cn/portal/index" target="_blank" class="beian-link">
                    <i class="bi bi-shield-lock"></i> <?php echo htmlspecialchars($settings['police_number']); ?>
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <style>
    /* 底部样式 */
    .site-footer {
        background-color: var(--white-color);
        border-top: 1px solid var(--light-color);
        padding: 15px 20px;
        text-align: center;
        font-size: 0.9rem;
        color: var(--gray-color);
        margin-top: 30px;
        width: 100%;
        position: relative;
        bottom: 0;
        left: 0;
        z-index: 10;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    }

    .footer-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 8px;
        max-width: 1200px;
        margin: 0 auto;
    }

    .copyright {
        margin-bottom: 5px;
        text-align: center;
    }

    .beian {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
        justify-content: center;
        text-align: center;
    }

    .beian-link {
        color: var(--gray-color);
        text-decoration: none;
        transition: color 0.3s;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .beian-link:hover {
        color: var(--primary-color);
    }

    .beian-link i {
        font-size: 1rem;
    }
    </style>

    <!-- 文件上传预览脚本 -->
    <script>
        // 为文件输入添加预览和文件名显示功能
        document.addEventListener('DOMContentLoaded', function() {
            // 网站Logo上传预览
            const logoInput = document.getElementById('site_logo');
            if (logoInput) {
                logoInput.addEventListener('change', function() {
                    if (this.files && this.files[0]) {
                        console.log('网站Logo将保存为: logo.png');

                        // 预览图片
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const logoPreview = document.getElementById('logoPreview');
                            if (logoPreview) {
                                logoPreview.src = e.target.result;
                            }
                        };
                        reader.readAsDataURL(this.files[0]);
                    }
                });
            }

            // 网站图标上传预览
            const faviconInput = document.getElementById('site_favicon');
            if (faviconInput) {
                faviconInput.addEventListener('change', function() {
                    if (this.files && this.files[0]) {
                        console.log('网站图标将保存为: favicon.ico');

                        // 预览图片
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const faviconPreview = document.getElementById('faviconPreview');
                            if (faviconPreview) {
                                faviconPreview.src = e.target.result;
                            }

                            // 注释掉立即更新页面favicon的逻辑，只在保存后才更新
                            // setTimeout(() => {
                            //     const timestamp = new Date().getTime();
                            //     updatePageFaviconPreview(e.target.result, timestamp);
                            // }, 100);
                        };
                        reader.readAsDataURL(this.files[0]);
                    }
                });
            }

            // 强制更新页面favicon预览函数
            function updatePageFaviconPreview(dataUrl, timestamp) {
                // 移除现有的favicon链接
                const existingFavicons = document.querySelectorAll('link[rel*="icon"]');
                existingFavicons.forEach(link => link.remove());

                // 创建新的favicon链接，使用预览数据
                const faviconTypes = [
                    { rel: 'icon', type: 'image/x-icon' },
                    { rel: 'shortcut icon', type: 'image/x-icon' },
                    { rel: 'apple-touch-icon', type: 'image/x-icon' }
                ];

                faviconTypes.forEach(favicon => {
                    const link = document.createElement('link');
                    link.rel = favicon.rel;
                    link.type = favicon.type;
                    link.href = dataUrl; // 使用预览数据URL
                    document.head.appendChild(link);
                });

                console.log('页面favicon预览已更新');
            }

            // 保存后强制更新页面favicon函数
            function updatePageFaviconAfterSave(timestamp) {
                // 移除现有的favicon链接
                const existingFavicons = document.querySelectorAll('link[rel*="icon"]');
                existingFavicons.forEach(link => link.remove());

                // 创建新的favicon链接，使用最新的文件
                const faviconTypes = [
                    { rel: 'icon', type: 'image/x-icon' },
                    { rel: 'shortcut icon', type: 'image/x-icon' },
                    { rel: 'apple-touch-icon', type: 'image/x-icon' }
                ];

                faviconTypes.forEach(favicon => {
                    const link = document.createElement('link');
                    link.rel = favicon.rel;
                    link.type = favicon.type;
                    link.href = '/assets/images/favicon.ico?v=' + timestamp;
                    document.head.appendChild(link);
                });

                console.log('保存后页面favicon已强制更新，时间戳:', timestamp);
            }

            // 登录背景上传预览
            const loginBgInput = document.getElementById('login_bg');
            if (loginBgInput) {
                loginBgInput.addEventListener('change', function() {
                    if (this.files && this.files[0]) {
                        console.log('登录背景将保存为: login-bg.png');

                        // 预览图片
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const loginBgPreview = document.getElementById('loginBgPreview');
                            if (loginBgPreview) {
                                loginBgPreview.src = e.target.result;
                            }
                        };
                        reader.readAsDataURL(this.files[0]);
                    }
                });
            }
        });
    </script>

    <!-- 自动刷新通知系统 -->
    <script src="/assets/js/auto-refresh-toast.js"></script>
</body>
</html>