/**
 * 模态框专用修复脚本
 * 只修复点击空白处关闭的问题，不影响其他功能
 */

// 立即执行函数，避免变量污染全局作用域
(function() {
    // 等待页面加载完成
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        // 如果页面已经加载完成，立即执行
        fixModals();
    } else {
        // 否则等待页面加载完成
        document.addEventListener('DOMContentLoaded', fixModals);
    }
    
    // 为了确保在所有脚本加载后执行，也添加window.onload事件
    window.addEventListener('load', fixModals);
    
    function fixModals() {
        console.log('模态框专用修复脚本已加载');
        
        // 移除window的点击事件
        window.onclick = null;
        
        // 获取所有模态框
        const modals = document.querySelectorAll('.modal');
        
        // 为每个模态框添加点击事件
        modals.forEach(function(modal) {
            // 添加新的点击事件，但不阻止原有事件
            modal.addEventListener('click', function(event) {
                // 如果点击的是模态框本身（空白区域），阻止事件传播
                if (event.target === this) {
                    event.stopPropagation();
                    event.preventDefault();
                    console.log('点击了模态框空白处，阻止关闭');
                    return false;
                }
            });
        });
        
        // 监听DOM变化，处理动态添加的模态框
        setupMutationObserver();
    }
    
    /**
     * 设置MutationObserver监听DOM变化
     */
    function setupMutationObserver() {
        // 创建一个观察器实例
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // 检查是否有新的模态框被添加
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach(function(node) {
                        // 检查添加的节点是否是模态框
                        if (node.nodeType === 1 && node.classList && node.classList.contains('modal')) {
                            console.log('检测到新的模态框被添加:', node.id);
                            // 修复这个新的模态框
                            fixModal(node);
                        }
                        
                        // 检查添加的节点内部是否包含模态框
                        if (node.nodeType === 1 && node.querySelectorAll) {
                            const modals = node.querySelectorAll('.modal');
                            if (modals.length > 0) {
                                console.log('检测到新的模态框被添加到DOM中:', modals.length, '个');
                                modals.forEach(modal => fixModal(modal));
                            }
                        }
                    });
                }
            });
        });
        
        // 配置观察选项
        const config = {
            childList: true, // 观察目标子节点的变化
            subtree: true    // 观察所有后代节点的变化
        };
        
        // 开始观察document.body的变化
        observer.observe(document.body, config);
        
        console.log('已设置MutationObserver监听DOM变化');
    }
    
    /**
     * 修复单个模态框
     */
    function fixModal(modal) {
        if (!modal) return;
        
        // 添加点击事件，阻止冒泡
        modal.addEventListener('click', function(event) {
            // 如果点击的是模态框本身（空白区域），阻止事件传播
            if (event.target === this) {
                event.stopPropagation();
                event.preventDefault();
                console.log('点击了模态框空白处，阻止关闭');
                return false;
            }
        });
        
        console.log('已修复模态框:', modal.id);
    }
})();
