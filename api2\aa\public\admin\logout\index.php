<?php
// ????
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// ??????
ini_set('display_errors', 1);
error_reporting(E_ALL);

// ??????
$logFile = __DIR__ . '/../../../storage/logs/logout.log';
$logDir = dirname($logFile);
if (!is_dir($logDir)) {
    mkdir($logDir, 0777, true);
}

file_put_contents($logFile, date('Y-m-d H:i:s') . " - ????????\n", FILE_APPEND);
file_put_contents($logFile, "SESSION ??: " . print_r($_SESSION, true) . "\n", FILE_APPEND);

// ????
unset($_SESSION['admin_id']);
unset($_SESSION['admin_username']);
unset($_SESSION['admin_is_super']);

file_put_contents($logFile, "?????\n", FILE_APPEND);

// ????????
header('Location: /admin/login');
exit;