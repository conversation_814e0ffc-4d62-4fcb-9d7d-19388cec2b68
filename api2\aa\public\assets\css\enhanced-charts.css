/**
 * 增强图表样式
 * 为图表添加交互式样式、筛选器和导出按钮样式
 */

/* 交互式图表容器 */
.interactive-chart-container {
    position: relative;
    height: 100%;
    min-height: 250px;
    padding: 10px;
    transition: all 0.3s ease;
}

.interactive-chart-container:hover {
    box-shadow: 0 0 10px rgba(var(--primary-color-rgb), 0.2);
}

/* 图表筛选器 */
.chart-filters {
    display: flex;
    align-items: center;
    margin-left: auto; /* 将筛选器推到右侧 */
    padding-left: 15px;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-group label {
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0;
    white-space: nowrap;
}

.chart-filter {
    padding: 3px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: var(--white-color);
    font-size: 0.85rem;
    color: var(--dark-color);
    transition: all 0.3s ease;
    min-width: 80px;
}

.chart-filter:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

.filter-apply {
    padding: 3px 10px;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    margin-left: 5px;
}

.filter-apply:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(var(--primary-color-rgb), 0.3);
}

/* 调整section-header以适应筛选器 */
.section-header {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .chart-filters {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .section-header h3 {
        margin-bottom: 10px;
    }
}

/* 图表导出按钮 */
.chart-export {
    position: absolute;
    top: 5px;
    right: 5px;
    z-index: 100; /* 增加z-index确保按钮在最上层 */
    opacity: 0.8;
    transition: opacity 0.3s ease;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    padding: 2px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.interactive-chart-container:hover .chart-export {
    opacity: 1;
}

/* 确保图表容器有相对定位，这样绝对定位的导出按钮才能正确定位 */
.section-content {
    position: relative;
}

.chart-export .dropdown-toggle {
    padding: 5px 10px;
    font-size: 0.85rem;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.chart-export .dropdown-menu {
    min-width: 120px;
    padding: 5px 0;
    font-size: 0.9rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.chart-export .dropdown-item {
    padding: 8px 15px;
    transition: all 0.2s ease;
}

.chart-export .dropdown-item:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1);
}

/* 自定义Toast样式 */
.custom-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 300px;
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    z-index: 9999;
    opacity: 0;
    transform: translateX(30px);
    transition: all 0.3s ease;
}

.custom-toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast-content {
    display: flex;
    align-items: center;
    padding: 15px;
}

.toast-icon {
    font-size: 1.5rem;
    margin-right: 15px;
}

.toast-icon.success {
    color: #28a745;
}

.toast-icon.error {
    color: #dc3545;
}

.toast-icon.warning {
    color: #ffc107;
}

.toast-icon.info {
    color: #17a2b8;
}

.toast-message {
    flex: 1;
    font-weight: 500;
}

.toast-progress {
    height: 3px;
    width: 100%;
    animation: progress 3s linear forwards;
}

.toast-progress.success {
    background-color: #28a745;
    animation-duration: 1s; /* 成功消息更快 */
}

.toast-progress.error {
    background-color: #dc3545;
    animation-duration: 4s;
}

.toast-progress.warning {
    background-color: #ffc107;
    animation-duration: 3s;
}

.toast-progress.info {
    background-color: #17a2b8;
    animation-duration: 2.5s;
}

@keyframes progress {
    from { width: 100%; }
    to { width: 0%; }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .chart-filters {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .filter-apply {
        width: 100%;
    }

    .chart-export {
        top: 5px;
        right: 5px;
    }

    .custom-toast {
        width: 90%;
        max-width: 300px;
        right: 5%;
    }
}

/* 图表工具提示自定义样式 */
#chartjs-tooltip {
    opacity: 0;
    position: absolute;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border-radius: 3px;
    transition: all 0.3s ease;
    pointer-events: none;
    transform: translate(-50%, 0);
}

/* 图表图例交互样式 */
.chart-legend-item {
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    margin-right: 15px;
    padding: 5px 10px;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.chart-legend-item:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1);
}

.chart-legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
}

.chart-legend-text {
    font-size: 0.9rem;
    font-weight: 500;
}

/* 图表加载状态 */
.chart-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 5;
}

.chart-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(var(--primary-color-rgb), 0.1);
    border-left-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
