<?php
// 检查天气设置存储情况

header('Content-Type: application/json');

try {
    // 包含数据库配置
    $configPath = __DIR__ . '/../../config/config.php';
    $config = require_once($configPath);
    
    $dbConfig = $config['database'];
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $prefix = $dbConfig['prefix'];
    
    // 获取天气设置
    $stmt = $pdo->prepare("SELECT * FROM {$prefix}weather_settings ORDER BY id DESC LIMIT 1");
    $stmt->execute();
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$settings) {
        echo json_encode([
            'success' => false,
            'error' => '没有找到天气设置记录',
            'table_exists' => true
        ]);
        exit;
    }
    
    // 检查API密钥
    $apiKey = $settings['api_key'] ?? '';
    $privateKey = $settings['private_key'] ?? '';
    
    echo json_encode([
        'success' => true,
        'settings' => [
            'id' => $settings['id'],
            'api_provider' => $settings['api_provider'],
            'api_key_length' => strlen($apiKey),
            'api_key_first_10' => substr($apiKey, 0, 10),
            'api_key_last_10' => substr($apiKey, -10),
            'private_key_length' => strlen($privateKey),
            'default_city' => $settings['default_city'],
            'enabled' => $settings['enabled'],
            'cache_duration' => $settings['cache_duration'],
            'created_at' => $settings['created_at'] ?? null,
            'updated_at' => $settings['updated_at'] ?? null
        ],
        'raw_api_key' => $apiKey, // 临时显示完整密钥用于调试
        'table_structure' => getTableStructure($pdo, $prefix)
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function getTableStructure($pdo, $prefix) {
    try {
        $stmt = $pdo->query("DESCRIBE {$prefix}weather_settings");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return ['error' => $e->getMessage()];
    }
}
?>
