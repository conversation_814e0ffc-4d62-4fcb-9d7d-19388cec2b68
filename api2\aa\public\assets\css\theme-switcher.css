/**
 * 情侣头像匹配系统 - 主题切换样式
 */

/* 主题变量 - 亮色主题 */
:root {
  --primary-color: #ff6b95;
  --primary-color-rgb: 255, 107, 149;
  --secondary-color: #8a6fd6;
  --success-color: #28c76f;
  --info-color: #00cfe8;
  --warning-color: #ff9f43;
  --danger-color: #ea5455;
  --light-color: #f8f8f8;
  --dark-color: #4b4b4b;

  --bg-color: #f8f9fa;
  --card-bg: #ffffff;
  --text-color: #333333;
  --text-muted: #6c757d;
  --border-color: #e9ecef;
  --input-bg: #ffffff;
  --input-border: #ced4da;
  --shadow-color: rgba(0, 0, 0, 0.1);

  --sidebar-bg: #ffffff;
  --sidebar-text: #333333;
  --sidebar-active: #ff6b95;
  --sidebar-hover: #f8f8f8;

  --header-bg: #ffffff;
  --header-text: #333333;

  --modal-bg: #ffffff;
  --modal-text: #333333;

  --transition-speed: 0.3s;
}

/* 主题变量 - 暗色主题 */
[data-theme="dark"] {
  --primary-color: #ff6b95;
  --primary-color-rgb: 255, 107, 149;
  --secondary-color: #8a6fd6;
  --success-color: #28c76f;
  --info-color: #00cfe8;
  --warning-color: #ff9f43;
  --danger-color: #ea5455;
  --light-color: #343a40;
  --dark-color: #f8f8f8;

  --bg-color: #121212;
  --card-bg: #1e1e1e;
  --text-color: #f8f8f8;
  --text-muted: #adb5bd;
  --border-color: #2d3748;
  --input-bg: #2d3748;
  --input-border: #4a5568;
  --shadow-color: rgba(0, 0, 0, 0.3);

  --sidebar-bg: #1e1e1e;
  --sidebar-text: #f8f8f8;
  --sidebar-active: #ff6b95;
  --sidebar-hover: #2d3748;

  --header-bg: #1e1e1e;
  --header-text: #f8f8f8;

  --modal-bg: #1e1e1e;
  --modal-text: #f8f8f8;
}

/* 主题切换按钮 */
.theme-switcher {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 30px;
  margin: 0 10px;
}

.theme-switcher input {
  opacity: 0;
  width: 0;
  height: 0;
}

.theme-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 30px;
}

.theme-slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .theme-slider {
  background-color: var(--primary-color);
}

input:focus + .theme-slider {
  box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .theme-slider:before {
  transform: translateX(30px);
}

.theme-slider .sun-icon,
.theme-slider .moon-icon {
  position: absolute;
  top: 6px;
  font-size: 16px;
  transition: .4s;
}

.theme-slider .sun-icon {
  left: 8px;
  color: #f8d568;
  opacity: 1;
}

.theme-slider .moon-icon {
  right: 8px;
  color: #ffffff;
  opacity: 0;
}

input:checked + .theme-slider .sun-icon {
  opacity: 0;
}

input:checked + .theme-slider .moon-icon {
  opacity: 1;
}

/* 颜色选择器 */
.color-picker {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.color-option {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.active {
  border-color: var(--text-color);
}

/* 主题设置面板 */
.theme-settings {
  position: fixed;
  right: -300px;
  top: 70px;
  width: 300px;
  height: auto;
  background-color: var(--card-bg);
  border-radius: 10px 0 0 10px;
  box-shadow: 0 0 15px var(--shadow-color);
  padding: 20px;
  z-index: 1000;
  transition: right 0.3s ease;
}

.theme-settings.show {
  right: 0;
}

.theme-settings-toggle {
  position: absolute;
  left: -50px;
  top: 20px;
  width: 50px;
  height: 50px;
  background-color: var(--primary-color);
  border-radius: 10px 0 0 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  font-size: 20px;
  box-shadow: 0 0 10px var(--shadow-color);
}

.theme-settings-toggle:hover {
  background-color: var(--secondary-color);
}

/* 拖动时的样式 */
.theme-settings-toggle.dragging {
  opacity: 0.8;
  box-shadow: 0 0 15px var(--primary-color);
  transition: none;
}

.theme-settings h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: var(--text-color);
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 10px;
}

.theme-option {
  margin-bottom: 20px;
}

.theme-option-title {
  font-weight: bold;
  margin-bottom: 10px;
  color: var(--text-color);
}

/* 应用主题样式 */
body {
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color var(--transition-speed), color var(--transition-speed);
}

.card, .modal-content, .dropdown-menu {
  background-color: var(--card-bg);
  color: var(--text-color);
  transition: background-color var(--transition-speed), color var(--transition-speed);
}

.sidebar {
  background-color: var(--sidebar-bg);
  color: var(--sidebar-text);
  transition: background-color var(--transition-speed), color var(--transition-speed);
}

.header {
  background-color: var(--header-bg);
  color: var(--header-text);
  transition: background-color var(--transition-speed), color var(--transition-speed);
}

input, select, textarea {
  background-color: var(--input-bg);
  color: var(--text-color);
  border-color: var(--input-border);
  transition: background-color var(--transition-speed), color var(--transition-speed), border-color var(--transition-speed);
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-secondary {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-success {
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-danger {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.text-muted {
  color: var(--text-muted) !important;
}

.border {
  border-color: var(--border-color) !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-color);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}
