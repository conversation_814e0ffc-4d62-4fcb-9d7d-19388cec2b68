﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>平台管理 - 情侣头像匹配系统</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: "Microsoft YaHei", sans-serif;
            background-color: #f5f7fa;
            color: #333;
            min-height: 100vh;
        }
        
        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }
        
        /* 侧边栏样式 */
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #ff6b95 0%, #ffa5c0 100%);
            color: white;
            padding: 0;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            position: fixed;
            height: 100%;
            overflow-y: auto;
            z-index: 100;
            transition: all 0.3s;
        }
        
        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .sidebar-logo {
            width: 60px;
            height: 60px;
            margin-bottom: 10px;
            animation: pulse 3s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .sidebar-title {
            font-size: 18px;
            margin-bottom: 5px;
        }
        
        .sidebar-subtitle {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 15px 0;
        }
        
        .sidebar-menu li {
            margin-bottom: 5px;
        }
        
        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            text-decoration: none;
            transition: all 0.3s;
            border-left: 4px solid transparent;
        }
        
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background-color: rgba(255,255,255,0.1);
            border-left-color: white;
        }
        
        .sidebar-menu a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
            font-size: 18px;
        }
        
        /* 主内容区样式 */
        .main-content {
            flex: 1;
            padding: 20px;
            margin-left: 250px;
            transition: all 0.3s;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            color: #333;
        }
        
        .platform-info {
            display: flex;
            align-items: center;
        }
        
        .platform-logo {
            width: 40px;
            height: 40px;
            border-radius: 5px;
            background-color: #ff6b95;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
            overflow: hidden;
        }
        
        .platform-logo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .platform-name {
            font-weight: 500;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #ff6b95;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .user-name {
            font-weight: 500;
        }
        
        .dropdown {
            position: relative;
            display: inline-block;
        }
        
        .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            background-color: white;
            min-width: 160px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            z-index: 1;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .dropdown:hover .dropdown-content {
            display: block;
        }
        
        .dropdown-content a {
            color: #333;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            transition: all 0.3s;
        }
        
        .dropdown-content a:hover {
            background-color: #f5f7fa;
        }
        
        /* 标签页样式 */
        .tabs {
            display: flex;
            border-bottom: 1px solid #eee;
            margin-bottom: 20px;
            background-color: white;
            border-radius: 5px 5px 0 0;
            overflow: hidden;
        }
        
        .tab {
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s;
            border-bottom: 2px solid transparent;
            font-weight: 500;
        }
        
        .tab.active {
            border-bottom-color: #ff6b95;
            color: #ff6b95;
        }
        
        .tab-content {
            display: none;
            background-color: white;
            border-radius: 0 0 5px 5px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        /* 表单样式 */
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #ff6b95;
            box-shadow: 0 0 0 2px rgba(255, 107, 149, 0.2);
        }
        
        .form-text {
            font-size: 12px;
            color: #777;
            margin-top: 5px;
        }
        
        /* 按钮样式 */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #ff6b95 0%, #ffa5c0 100%);
            color: white;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #ff5a8a 0%, #ff95b5 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 149, 0.3);
        }
        
        .btn-secondary {
            background-color: #f5f7fa;
            color: #333;
            border: 1px solid #ddd;
        }
        
        .btn-secondary:hover {
            background-color: #e9ecef;
        }
        
        /* 加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f5f7fa;
            border-top: 5px solid #ff6b95;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="loading-overlay" id="loading-overlay">
        <div class="spinner"></div>
        <h2>正在加载平台管理...</h2>
        <p>请稍候，系统正在准备您的管理界面</p>
    </div>

    <div class="dashboard-container">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <img src="/assets/images/logo.png" alt="Logo" class="sidebar-logo" onerror="this.src='data:image/svg+xml;utf8,%3Csvg xmlns=\\\'http://www.w3.org/2000/svg\\\' viewBox=\\\'0 0 24 24\\\' fill=\\\'%23ffffff\\\'%3E%3Cpath d=\\\'M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\\\'/%3E%3C/svg%3E'">
                <h3 class="sidebar-title">情侣头像匹配系统</h3>
                <p class="sidebar-subtitle">平台管理</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="#" class="active" onclick="switchTab('basic')"><i>📋</i> 基本设置</a></li>
                <li><a href="#" onclick="switchTab('payment')"><i>💰</i> 支付设置</a></li>
                <li><a href="#" onclick="switchTab('notification')"><i>🔔</i> 通知设置</a></li>
                <li><a href="#" onclick="switchTab('template')"><i>🖼️</i> 模板管理</a></li>
                <li><a href="#" onclick="switchTab('user')"><i>👥</i> 用户管理</a></li>
                <li><a href="#" onclick="switchTab('order')"><i>📦</i> 订单管理</a></li>
                <li><a href="#" onclick="switchTab('statistics')"><i>📊</i> 数据统计</a></li>
                <li><a href="/dashboard/platforms"><i>🔙</i> 返回平台列表</a></li>
            </ul>
        </div>

        <!-- 主内容区 -->
        <div class="main-content" id="main-content">
            <div class="header">
                <div class="platform-info">
                    <div class="platform-logo">
                        <img src="" alt="Logo" id="platform-logo-img" onerror="this.src='data:image/svg+xml;utf8,%3Csvg xmlns=\\\'http://www.w3.org/2000/svg\\\' viewBox=\\\'0 0 24 24\\\' fill=\\\'%23ffffff\\\'%3E%3Cpath d=\\\'M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\\\'/%3E%3C/svg%3E'">
                    </div>
                    <h1 id="platform-name">平台管理</h1>
                </div>
                <div class="user-info dropdown">
                    <div class="user-avatar">A</div>
                    <span class="user-name">管理员</span>
                    <div class="dropdown-content">
                        <a href="/dashboard/profile">个人资料</a>
                        <a href="/dashboard/change-password">修改密码</a>
                        <a href="/logout">退出登录</a>
                    </div>
                </div>
            </div>
            
            <!-- 标签页内容 -->
            <div class="tab-content active" id="tab-basic">
                <h2>基本设置</h2>
                <form id="basic-form">
                    <div class="form-group">
                        <label for="platform-name-input" class="form-label">平台名称</label>
                        <input type="text" id="platform-name-input" class="form-control" placeholder="请输入平台名称">
                        <small class="form-text">平台名称将显示在小程序中</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="platform-description" class="form-label">平台描述</label>
                        <textarea id="platform-description" class="form-control" placeholder="请输入平台描述" rows="3"></textarea>
                        <small class="form-text">简要描述平台的功能和特点</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="platform-logo-upload" class="form-label">平台Logo</label>
                        <div class="custom-file-upload">
                            <input type="file" id="platform-logo-upload" accept="image/*">
                            <label for="platform-logo-upload">
                                <i>📁</i>
                                <span>选择文件</span>
                            </label>
                            <div class="file-preview" id="logo-preview-container">
                                <img id="logo-preview" src="" alt="Logo预览" style="display: none;">
                            </div>
                        </div>
                        <small class="form-text">建议尺寸: 200x200px, 格式: PNG/JPG</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="platform-status" class="form-label">状态</label>
                        <div class="toggle-switch-container">
                            <label class="toggle-switch">
                                <input type="checkbox" id="platform-status" checked>
                                <span class="toggle-slider"></span>
                            </label>
                            <span class="toggle-label">启用平台</span>
                        </div>
                        <small class="form-text">启用后，平台将对用户可见</small>
                    </div>
                    
                    <button type="button" class="btn btn-primary" onclick="saveBasicSettings()">保存设置</button>
                </form>
            </div>
            
            <div class="tab-content" id="tab-payment">
                <h2>支付设置</h2>
                <p>此处将显示支付设置内容</p>
            </div>
            
            <div class="tab-content" id="tab-notification">
                <h2>通知设置</h2>
                <p>此处将显示通知设置内容</p>
            </div>
            
            <div class="tab-content" id="tab-template">
                <h2>模板管理</h2>
                <p>此处将显示模板管理内容</p>
            </div>
            
            <div class="tab-content" id="tab-user">
                <h2>用户管理</h2>
                <p>此处将显示用户管理内容</p>
            </div>
            
            <div class="tab-content" id="tab-order">
                <h2>订单管理</h2>
                <p>此处将显示订单管理内容</p>
            </div>
            
            <div class="tab-content" id="tab-statistics">
                <h2>数据统计</h2>
                <p>此处将显示数据统计内容</p>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后隐藏加载动画
        window.addEventListener('load', function() {
            setTimeout(function() {
                document.getElementById('loading-overlay').style.display = 'none';
            }, 1000); // 延迟 1 秒后隐藏，让用户看到加载动画
            
            // 获取URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const platformId = urlParams.get('id');
            const platformName = urlParams.get('name');
            
            // 设置平台名称
            if (platformName) {
                document.getElementById('platform-name').textContent = platformName;
                document.getElementById('platform-name-input').value = platformName;
            }
            
            // 模拟加载平台数据
            if (platformId) {
                // 这里可以发送请求获取平台数据
                console.log('加载平台ID:', platformId);
                
                // 模拟设置平台Logo
                document.getElementById('platform-logo-img').src = '/assets/images/platform' + platformId + '.png';
            }
        });
        
        // 侧边栏菜单项点击事件
        document.querySelectorAll('.sidebar-menu a').forEach(item => {
            item.addEventListener('click', function(e) {
                // 移除所有活跃状态
                document.querySelectorAll('.sidebar-menu a').forEach(i => {
                    i.classList.remove('active');
                });
                
                // 添加活跃状态
                this.classList.add('active');
            });
        });
        
        // 切换标签页
        function switchTab(tabId) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 显示当前标签页内容
            document.getElementById('tab-' + tabId).classList.add('active');
        }
        
        // 文件上传预览
        document.getElementById('platform-logo-upload').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('logo-preview');
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            }
        });
        
        // 保存基本设置
        function saveBasicSettings() {
            // 获取表单数据
            const name = document.getElementById('platform-name-input').value;
            const description = document.getElementById('platform-description').value;
            const logo = document.getElementById('platform-logo-upload').files[0];
            const status = document.getElementById('platform-status').checked ? 'active' : 'inactive';
            
            // 简单验证
            if (!name) {
                alert('请填写平台名称');
                return;
            }
            
            // 模拟提交到服务器
            console.log({
                name,
                description,
                logo: logo ? logo.name : null,
                status
            });
            
            alert('保存设置成功');
            
            // 更新页面标题
            document.getElementById('platform-name').textContent = name;
            
            // 如果有上传新Logo，更新Logo显示
            if (logo) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('platform-logo-img').src = e.target.result;
                };
                reader.readAsDataURL(logo);
            }
        }
    </script>
</body>
</html>