<?php
/**
 * 管理面板面包屑导航组件
 *
 * 使用方法:
 * 1. 在包含此文件前设置 $page_title 变量为当前页面标题
 * 2. 可选: 设置 $page_path 数组，包含当前页面的路径
 *    例如: $page_path = [['title' => '账户管理', 'url' => '/dashboard/accounts']];
 */

// 获取当前页面标题
$current_page = isset($page_title) ? $page_title : '控制面板';

// 获取当前页面路径
$current_path = isset($page_path) ? $page_path : [];
?>

<nav aria-label="breadcrumb" class="admin-breadcrumb mb-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/dashboard/"><i class="bi bi-house-door"></i> 仪表盘</a></li>
        <?php foreach ($current_path as $path): ?>
            <li class="breadcrumb-item"><a href="<?php echo $path['url']; ?>"><?php echo htmlspecialchars($path['title']); ?></a></li>
        <?php endforeach; ?>
        <li class="breadcrumb-item active" aria-current="page"><?php echo htmlspecialchars($current_page); ?></li>
        <li class="breadcrumb-item theme-btn-container">
            <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/theme-color-button.php'); ?>
        </li>
    </ol>
</nav>

<style>
/* 面包屑导航样式 */
.admin-breadcrumb {
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    padding: 10px 15px;
    box-shadow: 0 2px 5px var(--shadow-color);
    margin-bottom: 20px;
}

.breadcrumb {
    margin-bottom: 0;
    padding: 0;
    background-color: transparent;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s;
}

.breadcrumb-item a:hover {
    color: var(--secondary-color);
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: var(--dark-color);
    font-weight: 500;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: var(--gray-color);
}

/* 主题颜色按钮容器 */
.theme-btn-container {
    margin-left: auto !important;
    display: flex;
    align-items: center;
}

.theme-btn-container::before {
    display: none !important;
}

/* 响应式调整 */
@media (max-width: 576px) {
    .admin-breadcrumb {
        padding: 8px 10px;
        font-size: 0.9rem;
    }

    .theme-color-btn {
        width: 28px;
        height: 28px;
        font-size: 0.9rem;
    }
}
</style>
