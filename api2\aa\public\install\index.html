﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安装向导 - 情侣头像匹配系统</title>
    <style>
        body {
            font-family: "Microsoft YaHei", sans-serif;
            background-color: #f8f9fa;
            background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 1440 320\'%3E%3Cpath fill=\'%23FFA5C0\' fill-opacity=\'0.2\' d=\'M0,96L48,112C96,128,192,160,288,186.7C384,213,480,235,576,224C672,213,768,171,864,149.3C960,128,1056,128,1152,149.3C1248,171,1344,213,1392,234.7L1440,256L1440,0L1392,0C1344,0,1248,0,1152,0C1056,0,960,0,864,0C768,0,672,0,576,0C480,0,384,0,288,0C192,0,96,0,48,0L0,0Z\'%3E%3C/path%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-size: 100% 300px;
            min-height: 100vh;
            padding: 50px 0;
            margin: 0;
        }
        
        .install-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            background-color: white;
            margin-bottom: 30px;
            animation: fadeIn 0.5s ease-out forwards;
        }
        
        .card-header {
            background: linear-gradient(135deg, #ff6b95 0%, #ffa5c0 100%);
            color: white;
            border-bottom: none;
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
        }
        
        .card-header h3 {
            position: relative;
            z-index: 1;
            margin: 0;
            font-weight: 600;
        }
        
        .card-body {
            padding: 2rem;
        }
        
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin: 2rem 0;
            position: relative;
            z-index: 1;
        }
        
        .step-indicator:before {
            content: "";
            position: absolute;
            top: 25px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: #e9ecef;
            z-index: -1;
        }
        
        .step {
            flex: 1;
            text-align: center;
            padding: 0 10px;
            position: relative;
        }
        
        .step-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #f8f9fa;
            border: 2px solid #dee2e6;
            margin: 0 auto 15px;
            font-weight: bold;
            position: relative;
            z-index: 2;
        }
        
        .step.active .step-icon {
            background-color: #ff6b95;
            border-color: #ff6b95;
            color: white;
            box-shadow: 0 0 0 5px rgba(255, 107, 149, 0.3);
            transform: scale(1.2);
            animation: bounce 1s;
        }
        
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {transform: translateY(0);}
            40% {transform: translateY(-20px);}
            60% {transform: translateY(-10px);}
        }
        
        .step-title {
            font-weight: 500;
            margin-bottom: 5px;
            color: #6c757d;
        }
        
        .step.active .step-title {
            color: #ff6b95;
            font-weight: 700;
        }
        
        .loader-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 30px;
        }
        
        .loader {
            width: 80px;
            height: 80px;
            border: 5px solid rgba(255, 107, 149, 0.3);
            border-radius: 50%;
            border-top-color: #ff6b95;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .status-message {
            font-size: 1.2rem;
            margin-top: 20px;
            font-weight: 500;
        }
        
        .floating-hearts {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: -1;
        }
        
        .heart {
            position: absolute;
            width: 20px;
            height: 20px;
            background: url("data:image/svg+xml;utf8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'%23ff6b95\'%3E%3Cpath d=\'M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\'/%3E%3C/svg%3E") no-repeat center center;
            opacity: 0;
            animation: floatUp 4s linear infinite;
        }
        
        @keyframes floatUp {
            0% {
                transform: translateY(100vh) scale(0);
                opacity: 0;
            }
            20% {
                opacity: 1;
            }
            80% {
                opacity: 1;
            }
            100% {
                transform: translateY(-20vh) scale(1.5);
                opacity: 0;
            }
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #888;
            font-size: 0.9rem;
            animation: fadeIn 1s ease-out forwards;
            animation-delay: 1s;
            opacity: 0;
        }
        
        p {
            margin: 0;
            padding: 0;
        }
    </style>
</head>
<body>
    <div class="floating-hearts"></div>
    
    <div class="install-container">
        <div class="card">
            <div class="card-header">
                <h3>情侣头像匹配系统 - 安装向导</h3>
            </div>
            <div class="card-body">
                <div class="step-indicator">
                    <div class="step active">
                        <div class="step-icon">1</div>
                        <div class="step-title">环境检查</div>
                    </div>
                    <div class="step">
                        <div class="step-icon">2</div>
                        <div class="step-title">数据库配置</div>
                    </div>
                    <div class="step">
                        <div class="step-icon">3</div>
                        <div class="step-title">系统配置</div>
                    </div>
                    <div class="step">
                        <div class="step-icon">4</div>
                        <div class="step-title">安装完成</div>
                    </div>
                </div>
                
                <div class="loader-container">
                    <div class="loader"></div>
                    <h3 class="status-message">正在加载安装向导...</h3>
                    <p id="status-detail">请稍候，我们正在为您准备安装环境</p>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>? 2023 情侣头像匹配系统 | 让爱更有仪式感</p>
        </div>
    </div>

    <script>
        // 创建漂浮的心形元素
        function createFloatingHearts() {
            const container = document.querySelector(".floating-hearts");
            const heartCount = 10;
            
            for (let i = 0; i < heartCount; i++) {
                const heart = document.createElement("div");
                heart.classList.add("heart");
                heart.style.left = Math.random() * 100 + "vw";
                heart.style.animationDelay = Math.random() * 5 + "s";
                heart.style.opacity = Math.random() * 0.5 + 0.5;
                heart.style.transform = `scale(${Math.random() * 0.5 + 0.5})`;
                container.appendChild(heart);
            }
        }
        
        // 页面加载完成后执行
        document.addEventListener("DOMContentLoaded", function() {
            // 创建漂浮的心形
            createFloatingHearts();
            
            // 模拟加载过程
            const statusMessage = document.querySelector(".status-message");
            const statusDetail = document.getElementById("status-detail");
            
            setTimeout(() => {
                statusMessage.textContent = "准备就绪";
                statusDetail.textContent = "正在跳转到安装向导...";
                
                // 跳转到安装处理页面
                setTimeout(() => {
                    window.location.href = "/install.php";
                }, 1500);
            }, 2000);
            
            // 添加音效
            try {
                const audio = new Audio("data:audio/mpeg;base64,SUQzBAAAAAABEVRYWFgAAAAtAAADY29tbWVudABCaWdTb3VuZEJhbmsuY29tIC8gTGFzb25pY1N0dWRpb3MuY29tAFRYWFgAAAAhAAADZW5naW5lZXIAQmlnU291bmRCYW5rLmNvbQBUWFhYAAAAGwAAA3NvZnR3YXJlAExhdmY1OC43Ni4xMDAAAFRDT04AAAAbAAADZW5jb2RlZCBieQBMYXZmNTguNzYuMTAwAAAAAAAAAAAAAAD/+1AAAAA8YXVkaW8vbXBlZwAAAAAAAAAAAABUSVQyAAAAGQAAAzIwMjMtMDUtMDZUMTc6MjM6MTgAAAAAAAAAAAAA//tQxAADwAABpAAAACAAADSAAAAETEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//tQxBYDwAABpAAAACAAADSAAAAEVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//tQxBcAAAANIAAAAAA");
                audio.volume = 0.3;
                audio.play();
            } catch (e) {
                console.log("音频播放失败", e);
            }
        });
    </script>
</body>
</html>
