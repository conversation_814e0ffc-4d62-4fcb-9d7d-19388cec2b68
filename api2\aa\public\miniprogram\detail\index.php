<?php
// 包含身份验证检查
include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/auth-check.php');

// 获取平台ID
$platformId = isset($_GET['id']) && $_GET['id'] !== 'null' && $_GET['id'] !== '' ? intval($_GET['id']) : 1;

// 调试信息
error_log("访问小程序详情页面，原始ID参数: " . (isset($_GET['id']) ? $_GET['id'] : 'undefined'));
error_log("处理后的平台ID: {$platformId}");

// 确保ID有效
if ($platformId <= 0) {
    $platformId = 1;
    error_log("ID无效，已设置为默认值1");
}

// 记录日志
$logFile = $_SERVER['DOCUMENT_ROOT'] . '/storage/logs/miniprogram.log';
$logDir = dirname($logFile);
if (!is_dir($logDir)) {
    mkdir($logDir, 0777, true);
}

// 记录详细的请求信息
$requestInfo = [
    'time' => date('Y-m-d H:i:s'),
    'url' => $_SERVER['REQUEST_URI'],
    'method' => $_SERVER['REQUEST_METHOD'],
    'ip' => $_SERVER['REMOTE_ADDR'],
    'user_agent' => $_SERVER['HTTP_USER_AGENT'],
    'get_params' => $_GET,
    'platform_id' => $platformId
];

file_put_contents(
    $logFile,
    date('Y-m-d H:i:s') . " - 访问小程序详情页面\n" .
    "请求信息: " . json_encode($requestInfo, JSON_UNESCAPED_UNICODE) . "\n",
    FILE_APPEND
);

// 模拟平台数据
$platform = [
    'id' => $platformId,
    'name' => '情侣头像匹配小程序' . $platformId,
    'appid' => 'wx' . str_pad($platformId, 10, '0', STR_PAD_LEFT),
    'secret' => md5('secret' . $platformId),
    'status' => $platformId % 2 == 0 ? 0 : 1,
    'created_at' => '2023-01-15 14:30:25',
    'account_name' => '测试账户' . ceil($platformId / 2),
    'description' => '这是一个情侣头像匹配小程序，提供多种风格的头像匹配服务。',
    'stats' => [
        'today_users' => rand(10, 100),
        'total_users' => rand(1000, 10000),
        'today_matches' => rand(20, 200),
        'total_matches' => rand(2000, 20000),
        'today_orders' => rand(5, 50),
        'total_orders' => rand(500, 5000),
        'today_income' => rand(100, 1000) / 100,
        'total_income' => rand(10000, 100000) / 100
    ],
    'platforms' => [
        'wechat' => ['active' => true, 'users' => rand(500, 5000), 'matches' => rand(1000, 10000), 'income' => rand(5000, 50000) / 100],
        'qq' => ['active' => true, 'users' => rand(300, 3000), 'matches' => rand(600, 6000), 'income' => rand(3000, 30000) / 100],
        'alipay' => ['active' => true, 'users' => rand(200, 2000), 'matches' => rand(400, 4000), 'income' => rand(2000, 20000) / 100],
        'douyin' => ['active' => true, 'users' => rand(150, 1500), 'matches' => rand(300, 3000), 'income' => rand(1500, 15000) / 100],
        'kuaishou' => ['active' => false, 'users' => rand(100, 1000), 'matches' => rand(200, 2000), 'income' => rand(1000, 10000) / 100],
        'baidu' => ['active' => false, 'users' => rand(50, 500), 'matches' => rand(100, 1000), 'income' => rand(500, 5000) / 100],
        '360' => ['active' => false, 'users' => rand(30, 300), 'matches' => rand(60, 600), 'income' => rand(300, 3000) / 100]
    ]
];

// 获取当前日期
$currentDate = date('Y-m-d');

// 生成过去7天的日期
$dates = [];
for ($i = 6; $i >= 0; $i--) {
    $dates[] = date('m-d', strtotime("-$i days"));
}

// 生成各平台的随机数据
$platformsData = [];
$platformsIncome = [];
$platformColors = [
    'wechat' => '#07c160',
    'qq' => '#12b7f5',
    'alipay' => '#1677ff',
    'douyin' => '#fe2c55',
    'kuaishou' => '#ff9000',
    'baidu' => '#2932e1',
    '360' => '#2aad00'
];

$platformNames = [
    'wechat' => '微信',
    'qq' => 'QQ',
    'alipay' => '支付宝',
    'douyin' => '抖音',
    'kuaishou' => '快手',
    'baidu' => '百度',
    '360' => '360'
];

foreach ($platform['platforms'] as $key => $value) {
    if ($value['active']) {
        $data = [];
        $income = [];
        for ($i = 0; $i < 7; $i++) {
            $data[] = rand(10, 100);
            $income[] = rand(100, 1000) / 10;
        }
        $platformsData[$key] = $data;
        $platformsIncome[$key] = $income;
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($platform['name']); ?> - 小程序详情 - 情侣头像匹配系统</title>
    <link rel="stylesheet" href="/assets/vendor/bootstrap/bootstrap.min.css">
    <link rel="stylesheet" href="/assets/vendor/bootstrap-icons/bootstrap-icons.css">
    <link rel="stylesheet" href="/assets/css/admin-style.css">
    <link rel="stylesheet" href="/assets/css/toast-unified.css">
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/theme.css">
    <link rel="stylesheet" href="/assets/css/miniprogram-theme-switcher.css">
    <!-- 主题初始化脚本 - 在页面加载前立即执行 -->
    <script src="/assets/js/miniprogram-theme-init.js"></script>
    <script src="/assets/js/chart.min.js"></script>
    <!-- 使用本地ECharts库 -->
    <script src="/assets/vendor/echarts/echarts.min.js"></script>
    <!-- 确保 Chart.js 和 ECharts 加载成功 -->
    <script>
        // 检查 Chart.js 和 ECharts 是否加载成功
        window.addEventListener('load', function() {
            // 检查 Chart.js
            if (typeof Chart === 'undefined') {
                console.error('Chart.js 未能正确加载，尝试重新加载');
                var script = document.createElement('script');
                script.src = '/assets/js/chart.min.js';
                script.onload = function() {
                    console.log('Chart.js 重新加载成功');
                    if (typeof initCharts === 'function') {
                        initCharts();
                    }
                };
                script.onerror = function() {
                    console.error('Chart.js 重新加载失败');
                };
                document.head.appendChild(script);
            } else {
                console.log('Chart.js 已成功加载');
            }

            // 检查 ECharts
            if (typeof echarts === 'undefined') {
                console.error('ECharts 未能正确加载');
                // 显示错误信息
                var incomeChartContainer = document.getElementById('incomeChart');
                if (incomeChartContainer) {
                    incomeChartContainer.innerHTML = '<div style="padding: 20px; text-align: center; color: #dc3545;">' +
                        '<i class="bi bi-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i><br>' +
                        'ECharts 图表库未加载，请刷新页面重试</div>';
                }
            } else {
                console.log('ECharts 已成功加载');
            }
        });
    </script>
    <style>
        /* 小程序详情页面样式 */
        :root {
            --sidebar-width: 250px;
            --header-height: 60px;
            --primary-color: #ff6b95;
            --secondary-color: #6c757d;
            --success-color: #28a745;
            --info-color: #17a2b8;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --white-color: #ffffff;
            --gray-color: #6c757d;
            --border-radius: 8px;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --transition-speed: 0.3s;
        }

        body {
            background-color: #f5f5f5;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            color: var(--dark-color);
        }

        .miniprogram-container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .miniprogram-sidebar {
            width: var(--sidebar-width);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
            color: var(--white-color);
            box-shadow: 2px 0 10px var(--shadow-color);
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 1000;
            transition: all var(--transition-speed);
            overflow-y: auto;
            animation: slideInLeft 0.5s ease-out;
        }

        /* 侧边栏收起状态 */
        .sidebar-collapsed .miniprogram-sidebar {
            width: 60px;
            overflow: visible;
        }

        .sidebar-collapsed .sidebar-header h3,
        .sidebar-collapsed .sidebar-menu-item span {
            display: none;
        }

        .sidebar-collapsed .sidebar-menu-item {
            justify-content: center;
            padding: 12px 0;
        }

        .sidebar-collapsed .sidebar-menu-item i {
            margin-right: 0;
        }

        @keyframes slideInLeft {
            from {
                transform: translateX(-100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .sidebar-header {
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            height: var(--header-height);
            margin-bottom: 15px;
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--white-color);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .sidebar-toggle {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: var(--white-color);
            cursor: pointer;
            font-size: 1.2rem;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            transition: all var(--transition-speed);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }

        .sidebar-toggle:hover {
            background-color: rgba(255, 255, 255, 0.3);
            transform: rotate(180deg);
        }

        .sidebar-menu {
            padding: 10px 0;
        }

        .sidebar-menu-item {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            color: var(--white-color);
            text-decoration: none;
            transition: all var(--transition-speed);
            border-left: 4px solid transparent;
            margin-bottom: 5px;
            position: relative;
            overflow: hidden;
        }

        .sidebar-menu-item:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0);
            transition: all var(--transition-speed);
            z-index: -1;
        }

        .sidebar-menu-item:hover {
            background-color: rgba(255, 255, 255, 0.2);
            border-left-color: var(--white-color);
        }

        .sidebar-menu-item:hover:before {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .sidebar-menu-item.active {
            background-color: rgba(255, 255, 255, 0.2);
            border-left-color: var(--white-color);
            font-weight: 500;
        }

        .sidebar-menu-item i {
            margin-right: 12px;
            font-size: 1.2rem;
            width: 24px;
            height: 24px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-speed);
        }

        .sidebar-menu-item:hover i {
            transform: scale(1.2);
        }

        /* 主内容区域样式 */
        .miniprogram-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            padding: 20px;
            transition: all var(--transition-speed);
            width: calc(100% - var(--sidebar-width));
        }

        /* 侧边栏收起时的主内容区域 */
        .sidebar-collapsed .miniprogram-content {
            margin-left: 60px;
            width: calc(100% - 60px);
        }

        /* 响应式布局 */
        @media (max-width: 992px) {
            .miniprogram-content {
                width: 100%;
            }

            .stats-row {
                flex-direction: column;
            }

            .platform-meta {
                flex-wrap: wrap;
            }

            .platform-meta-item {
                flex: 1 1 calc(50% - 20px);
                min-width: 120px;
            }
        }

        @media (max-width: 768px) {
            .miniprogram-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .platform-actions {
                margin-top: 15px;
                width: 100%;
                justify-content: space-between;
            }

            .chart-container {
                overflow-x: auto;
            }
        }

        /* 头部导航样式 */
        .miniprogram-header {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: 0 4px 15px var(--shadow-color);
            padding: 20px 25px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            overflow: hidden;
            animation: slideInDown 0.5s;
        }

        @keyframes slideInDown {
            from {
                transform: translateY(-20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .miniprogram-header:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
        }

        .platform-info h1 {
            font-size: 1.8rem;
            margin: 0 0 10px 0;
            color: var(--dark-color);
            font-weight: 700;
            position: relative;
            display: inline-block;
        }

        .platform-info h1:after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 40px;
            height: 3px;
            background-color: var(--primary-color);
            border-radius: 3px;
            transition: all var(--transition-speed);
        }

        .platform-info:hover h1:after {
            width: 100%;
        }

        .platform-meta {
            display: flex;
            gap: 20px;
            font-size: 0.95rem;
            color: var(--gray-color);
            margin-top: 15px;
        }

        .platform-meta-item {
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            padding: 10px 15px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            border-left: 3px solid var(--primary-color);
            min-width: 150px;
        }

        .platform-meta-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-left-width: 5px;
        }

        .meta-label {
            font-size: 0.8rem;
            color: #888;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
        }

        .meta-value {
            font-weight: 600;
            color: #333;
            font-size: 0.95rem;
        }

        .platform-meta i {
            margin-right: 8px;
            font-size: 1.1rem;
            color: var(--primary-color);
        }

        .platform-actions {
            display: flex;
            gap: 15px;
        }

        .platform-actions button {
            padding: 10px 20px;
            border-radius: 50px;
            font-weight: 500;
            transition: all 0.3s;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            border: none;
        }

        .platform-actions button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(0,0,0,0.15);
        }

        .platform-actions button i {
            margin-right: 8px;
            animation: spin 5s linear infinite;
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        /* 统计卡片样式 */
        .stats-card {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: 0 4px 15px var(--shadow-color);
            padding: 25px;
            height: 100%;
            transition: all var(--transition-speed);
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
            z-index: 1;
            border-top: 4px solid var(--primary-color);
            min-height: 180px;
            flex: 1;
        }

        .stats-row {
            display: flex;
            gap: 20px;
            margin-bottom: 25px;
            flex-wrap: wrap;
        }

        .stats-col {
            flex: 1;
            min-width: 250px;
        }

        @media (max-width: 1200px) {
            .stats-col {
                min-width: 200px;
            }
        }

        @media (max-width: 576px) {
            .stats-col {
                flex: 1 1 100%;
            }
        }

        .stats-card:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(255, 107, 149, 0.05) 0%, rgba(255, 107, 149, 0) 100%);
            z-index: -1;
        }

        .stats-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 25px var(--shadow-color);
        }

        .stats-card:hover .stats-card-value {
            color: var(--primary-color);
        }

        .stats-card-title {
            font-size: 1.1rem;
            color: var(--gray-color);
            margin-bottom: 15px;
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .stats-card-title:after {
            content: '';
            flex: 1;
            height: 1px;
            background: linear-gradient(to right, rgba(0,0,0,0.1), rgba(0,0,0,0.02));
            margin-left: 10px;
        }

        .stats-card-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 8px;
            transition: all var(--transition-speed);
            animation: fadeInUp 0.8s;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stats-card-subtitle {
            font-size: 1rem;
            color: var(--gray-color);
            margin-bottom: 15px;
            position: relative;
            padding-left: 12px;
        }

        .stats-card-subtitle:before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 16px;
            background-color: var(--primary-color);
            border-radius: 2px;
        }

        .stats-card-footer {
            margin-top: auto;
            font-size: 0.95rem;
            color: var(--success-color);
            display: flex;
            align-items: center;
            gap: 8px;
            padding-top: 15px;
            border-top: 1px dashed rgba(0,0,0,0.05);
        }

        .stats-card-footer i {
            font-size: 1.1rem;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-5px);
            }
            60% {
                transform: translateY(-3px);
            }
        }

        /* 标签页导航样式 */
        .nav-tabs {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
        }

        .nav-tabs .nav-link {
            color: var(--gray-color);
            border: none;
            border-bottom: 3px solid transparent;
            padding: 10px 15px;
            font-weight: 500;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .nav-tabs .nav-link i {
            font-size: 1.1rem;
        }

        .nav-tabs .nav-link:hover {
            color: var(--primary-color);
            border-bottom-color: rgba(255, 107, 149, 0.3);
            background-color: rgba(255, 107, 149, 0.05);
        }

        .nav-tabs .nav-link.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
            background-color: rgba(255, 107, 149, 0.1);
        }

        .tab-content {
            padding: 10px 0;
        }

        .tab-pane {
            animation: fadeIn 0.5s;
        }

        /* 图表容器样式 */
        .chart-container {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: 0 4px 15px var(--shadow-color);
            padding: 25px;
            margin-bottom: 25px;
            position: relative;
            overflow: hidden;
            transition: all var(--transition-speed);
            border-left: 4px solid var(--primary-color);
            animation: fadeIn 1s;
            width: 100%;
            height: auto;
            min-height: 300px;
            display: flex;
            flex-direction: column;
        }

        .chart-wrapper {
            flex: 1;
            width: 100%;
            height: 100%;
            min-height: 250px;
            position: relative;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        .chart-container:hover {
            box-shadow: 0 8px 20px var(--shadow-color);
            transform: translateY(-5px);
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 25px;
            color: var(--dark-color);
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            position: relative;
            display: flex;
            align-items: center;
        }

        .chart-title:before {
            content: '';
            width: 30px;
            height: 3px;
            background-color: var(--primary-color);
            position: absolute;
            bottom: -2px;
            left: 0;
            border-radius: 3px;
        }

        .chart-container canvas {
            transition: all var(--transition-speed);
            animation: scaleIn 0.8s;
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* 响应式布局 */
        @media (max-width: 768px) {
            .miniprogram-sidebar {
                transform: translateX(-100%);
            }

            .miniprogram-sidebar.show {
                transform: translateX(0);
            }

            .miniprogram-content {
                margin-left: 0;
            }
        }

        /* 侧边栏折叠状态 */
        .sidebar-collapsed .miniprogram-sidebar {
            width: 70px;
        }

        .sidebar-collapsed .sidebar-header h3 {
            opacity: 0;
            width: 0;
            transform: translateX(-20px);
        }

        .sidebar-collapsed .sidebar-menu-item span {
            opacity: 0;
            width: 0;
            transform: translateX(-10px);
            display: none;
        }

        .sidebar-collapsed .miniprogram-content {
            margin-left: 70px;
        }

        .sidebar-collapsed .sidebar-menu-item {
            justify-content: center;
            padding: 15px 10px;
            border-left-width: 0;
            border-right: 4px solid transparent;
        }

        .sidebar-collapsed .sidebar-menu-item.active {
            border-right-color: var(--white-color);
        }

        .sidebar-collapsed .sidebar-menu-item i {
            margin-right: 0;
            font-size: 1.4rem;
            animation: pulse 2s infinite;
        }

        .sidebar-collapsed .sidebar-menu-item:hover i {
            animation: none;
            transform: scale(1.3);
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }

        /* 内容区域淡入动画 */
        .content-section {
            transition: all 0.5s ease;
        }

        .content-section.fade-in {
            animation: fadeInAnimation 0.5s ease-in-out;
        }

        @keyframes fadeInAnimation {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 主题颜色选择器 */
        /* 日期范围选择器样式 */
        .date-range-selector {
            min-width: 120px;
        }

        /* 快捷操作下拉菜单样式 */
        .dropdown-menu {
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
            border: none;
            padding: 10px 0;
            min-width: 220px;
        }

        .dropdown-header {
            color: var(--primary-color);
            font-weight: 600;
            padding: 8px 16px;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .dropdown-item {
            padding: 8px 16px;
            color: var(--dark-color);
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s;
        }

        .dropdown-item i {
            font-size: 1rem;
            color: var(--gray-color);
            transition: all 0.2s;
        }

        .dropdown-item:hover {
            background-color: rgba(255, 107, 149, 0.1);
            color: var(--primary-color);
        }

        .dropdown-item:hover i {
            color: var(--primary-color);
        }

        .dropdown-divider {
            margin: 5px 0;
            border-top-color: rgba(0, 0, 0, 0.05);
        }

        /* 通知中心样式 */
        .notification-dropdown {
            width: 320px;
            padding: 0;
            overflow: hidden;
        }

        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #eee;
        }

        .notification-body {
            max-height: 350px;
            overflow-y: auto;
        }

        .notification-item {
            display: flex;
            padding: 12px 15px;
            border-bottom: 1px solid #f1f1f1;
            text-decoration: none;
            color: var(--dark-color);
            transition: all 0.2s;
        }

        .notification-item:hover {
            background-color: rgba(0, 0, 0, 0.02);
        }

        .notification-item.unread {
            background-color: rgba(255, 107, 149, 0.05);
        }

        .notification-item.unread::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 70%;
            background-color: var(--primary-color);
            border-radius: 0 2px 2px 0;
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .notification-icon i {
            color: white;
            font-size: 1.2rem;
        }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 3px;
        }

        .notification-text {
            font-size: 0.8rem;
            color: var(--gray-color);
            margin-bottom: 3px;
        }

        .notification-time {
            font-size: 0.75rem;
            color: #aaa;
        }

        .notification-footer {
            padding: 10px;
            border-top: 1px solid #eee;
            background-color: #f8f9fa;
        }

        .custom-date-picker {
            position: fixed;
            top: 80px;
            right: 20px;
            width: 300px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
            z-index: 1050;
            overflow: hidden;
            display: none;
            animation: slideInRight 0.3s ease-out;
        }

        .custom-date-header {
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #eee;
            background-color: #f8f9fa;
        }

        .custom-date-header h5 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--dark-color);
        }

        .custom-date-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            transition: all 0.3s;
        }

        .custom-date-close:hover {
            background-color: rgba(0, 0, 0, 0.05);
            color: #333;
        }

        .custom-date-body {
            padding: 20px;
        }

        /* 主题颜色选择器样式 */
        .theme-color-picker {
            position: fixed;
            top: 80px;
            right: 20px;
            width: 250px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
            z-index: 1050;
            overflow: hidden;
            display: none;
            animation: slideInRight 0.3s ease-out;
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateX(20px);
        }

        .theme-color-picker.show {
            display: block;
            opacity: 1;
            transform: translateX(0);
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .theme-color-header {
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #eee;
        }

        .theme-color-header h5 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }

        .theme-color-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            transition: all 0.3s;
        }

        .theme-color-close:hover {
            background-color: rgba(0, 0, 0, 0.05);
            color: #333;
        }

        .theme-color-body {
            padding: 15px;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
        }

        .theme-color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 创建一个更大的点击区域 */
        .theme-color-option:before {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            border-radius: 50%;
            cursor: pointer;
        }

        .theme-color-option:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        .theme-color-option.active {
            border: 3px solid #fff;
            box-shadow: 0 0 0 2px var(--primary-color);
        }

        .theme-color-option.active:after {
            content: '✓';
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        /* 平台管理下拉菜单 */
        .platform-manage-dropdown {
            position: relative;
        }

        .platform-manage-dropdown:hover .platform-manage-menu {
            display: block;
            opacity: 1;
            transform: translateY(0);
            visibility: visible;
        }

        .platform-manage-menu {
            position: absolute;
            top: 100%;
            right: 0;
            z-index: 9999;
            width: 160px;
            padding: 0.5rem 0;
            margin: 0.125rem 0 0;
            font-size: 0.9rem;
            color: #212529;
            text-align: left;
            list-style: none;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid rgba(0, 0, 0, 0.15);
            border-radius: 0.25rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
            display: none;
            opacity: 0;
            transform: translateY(10px);
            transition: opacity 0.2s, transform 0.2s;
            visibility: hidden;
        }

        .platform-manage-menu .dropdown-item {
            display: block;
            width: 100%;
            padding: 0.4rem 1rem;
            clear: both;
            font-weight: 400;
            color: #212529;
            text-align: inherit;
            white-space: nowrap;
            background-color: transparent;
            border: 0;
            text-decoration: none;
            transition: background-color 0.15s ease-in-out;
        }

        .platform-manage-menu .dropdown-item:hover,
        .platform-manage-menu .dropdown-item:focus {
            color: #16181b;
            text-decoration: none;
            background-color: #f8f9fa;
        }

        .platform-manage-menu .dropdown-divider {
            height: 0;
            margin: 0.5rem 0;
            overflow: hidden;
            border-top: 1px solid #e9ecef;
        }

        /* 面包屑导航样式 */
        .platform-breadcrumb {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: 0 2px 10px var(--shadow-color);
            padding: 12px 20px;
            margin-bottom: 20px;
            animation: fadeInDown 0.5s;
        }

        .breadcrumb {
            margin-bottom: 0;
            padding: 0;
            background-color: transparent;
        }

        .breadcrumb-item a {
            color: var(--primary-color);
            text-decoration: none;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
        }

        .breadcrumb-item a:hover {
            color: var(--secondary-color);
            text-decoration: underline;
        }

        .breadcrumb-item a i {
            margin-right: 5px;
            font-size: 0.9rem;
        }

        .breadcrumb-item.active {
            color: var(--dark-color);
            font-weight: 500;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "›";
            color: var(--gray-color);
            font-size: 1.2rem;
            line-height: 1;
            padding: 0 8px;
        }

        /* 加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s, visibility 0.3s;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 107, 149, 0.3);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="loading-overlay" id="loading-overlay">
        <div class="spinner"></div>
    </div>

    <div class="miniprogram-container" id="miniprogram-container">
        <!-- 侧边栏 -->
        <div class="miniprogram-sidebar" id="miniprogram-sidebar">
            <div class="sidebar-header">
                <h3>小程序管理</h3>
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <i class="bi bi-list"></i>
                </button>
            </div>
            <div class="sidebar-menu">
                <a href="#dashboard" class="sidebar-menu-item active" data-section="dashboard">
                    <i class="bi bi-speedometer2"></i>
                    <span>仪表盘</span>
                </a>
                <a href="#match-records" class="sidebar-menu-item" data-section="match-records">
                    <i class="bi bi-images"></i>
                    <span>匹配记录</span>
                </a>
                <a href="#orders" class="sidebar-menu-item" data-section="orders">
                    <i class="bi bi-cart"></i>
                    <span>订单管理</span>
                </a>
                <a href="#manual-orders" class="sidebar-menu-item" data-section="manual-orders">
                    <i class="bi bi-pencil-square"></i>
                    <span>人工订单</span>
                </a>
                <a href="#users" class="sidebar-menu-item" data-section="users">
                    <i class="bi bi-people"></i>
                    <span>用户管理</span>
                </a>
                <a href="#faq" class="sidebar-menu-item" data-section="faq">
                    <i class="bi bi-question-circle"></i>
                    <span>常见问题</span>
                </a>
                <a href="#platform-settings" class="sidebar-menu-item" data-section="platform-settings">
                    <i class="bi bi-gear"></i>
                    <span>平台设置</span>
                </a>
                <a href="#miniprogram-settings" class="sidebar-menu-item" data-section="miniprogram-settings">
                    <i class="bi bi-phone"></i>
                    <span>小程序设置</span>
                </a>
                <a href="#plugins" class="sidebar-menu-item" data-section="plugins">
                    <i class="bi bi-puzzle"></i>
                    <span>插件列表</span>
                </a>
                <a href="/dashboard/platforms" class="sidebar-menu-item" id="return-to-platforms">
                    <i class="bi bi-arrow-left-circle"></i>
                    <span>返回平台列表</span>
                </a>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="miniprogram-content" id="miniprogram-content">
            <!-- 面包屑导航 -->
            <nav aria-label="breadcrumb" class="platform-breadcrumb mb-3">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/dashboard"><i class="bi bi-house-door"></i> 仪表盘/</a></li>
                    <li class="breadcrumb-item active" aria-current="page" id="breadcrumb-current-page">概览</li>
                </ol>
            </nav>

            <!-- 头部信息 -->
            <div class="miniprogram-header py-2">
                <div class="platform-info">
                    <h2 class="mb-2"><?php echo htmlspecialchars($platform['name']); ?></h2>
                    <div class="platform-meta">
                        <div class="platform-meta-item py-1">
                            <div class="meta-label small"><i class="bi bi-calendar"></i> 创建时间</div>
                            <div class="meta-value small"><?php echo htmlspecialchars($platform['created_at']); ?></div>
                        </div>
                        <div class="platform-meta-item py-1">
                            <div class="meta-label small"><i class="bi bi-key"></i> AppID</div>
                            <div class="meta-value small"><?php echo htmlspecialchars($platform['appid']); ?></div>
                        </div>
                        <div class="platform-meta-item py-1">
                            <div class="meta-label small"><i class="bi bi-person"></i> 账户</div>
                            <div class="meta-value small"><?php echo htmlspecialchars($platform['account_name']); ?></div>
                        </div>
                    </div>
                </div>
                <div class="platform-actions">
                    <div class="date-range-selector me-2">
                        <select class="form-select" id="date-range">
                            <option value="today" selected>今天</option>
                            <option value="yesterday">昨天</option>
                            <option value="week">本周</option>
                            <option value="month">本月</option>
                            <option value="custom">自定义</option>
                        </select>
                    </div>
                    <button class="btn btn-outline-primary me-2" id="refresh-btn">
                        <i class="bi bi-arrow-clockwise"></i> 刷新数据
                    </button>
                    <!-- 主题颜色按钮 -->
                    <button class="btn btn-primary me-2" id="theme-color-btn" onclick="toggleThemeSettings()" style="cursor: pointer; pointer-events: auto;">
                        <i class="bi bi-palette"></i> 主题颜色
                    </button>

                    <!-- 通知中心按钮 -->
                    <div class="dropdown d-inline-block me-2">
                        <button class="btn btn-outline-secondary position-relative dropdown-toggle" type="button" id="notificationsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-bell"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                3
                                <span class="visually-hidden">未读通知</span>
                            </span>
                        </button>
                        <div class="dropdown-menu dropdown-menu-end notification-dropdown" aria-labelledby="notificationsDropdown" style="max-height: 400px; overflow-y: auto; z-index: 9999;">
                            <div class="notification-header">
                                <h6 class="m-0">通知中心</h6>
                                <button class="btn btn-sm btn-link text-decoration-none">全部标为已读</button>
                            </div>
                            <div class="notification-body">
                                <a href="#" class="notification-item unread">
                                    <div class="notification-icon bg-primary">
                                        <i class="bi bi-person"></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-title">新用户注册</div>
                                        <div class="notification-text">今日有 15 名新用户注册</div>
                                        <div class="notification-time">10 分钟前</div>
                                    </div>
                                </a>
                                <a href="#" class="notification-item unread">
                                    <div class="notification-icon bg-success">
                                        <i class="bi bi-cart"></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-title">新订单</div>
                                        <div class="notification-text">收到 5 个新订单，总金额 ¥1,350</div>
                                        <div class="notification-time">30 分钟前</div>
                                    </div>
                                </a>
                                <a href="#" class="notification-item unread">
                                    <div class="notification-icon bg-warning">
                                        <i class="bi bi-exclamation-triangle"></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-title">系统警告</div>
                                        <div class="notification-text">服务器负载过高，请检查</div>
                                        <div class="notification-time">1 小时前</div>
                                    </div>
                                </a>
                                <a href="#" class="notification-item">
                                    <div class="notification-icon bg-info">
                                        <i class="bi bi-graph-up"></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-title">数据报告</div>
                                        <div class="notification-text">每周数据报告已生成</div>
                                        <div class="notification-time">昨天</div>
                                    </div>
                                </a>
                            </div>
                            <div class="notification-footer">
                                <a href="#" class="btn btn-sm btn-link w-100 text-center">查看全部通知</a>
                            </div>
                        </div>
                    </div>

                    <a href="/dashboard/platforms" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left-circle"></i> 返回平台
                    </a>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content-section active" id="dashboard">
                <!-- 标签页导航 -->
                <ul class="nav nav-tabs mb-4" id="dashboardTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab" aria-controls="overview" aria-selected="true">
                            <i class="bi bi-grid"></i> 概览
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="users-tab" data-bs-toggle="tab" data-bs-target="#users-data" type="button" role="tab" aria-controls="users-data" aria-selected="false">
                            <i class="bi bi-people"></i> 用户分析
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="income-tab" data-bs-toggle="tab" data-bs-target="#income-data" type="button" role="tab" aria-controls="income-data" aria-selected="false">
                            <i class="bi bi-cash-coin"></i> 收入分析
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="trends-tab" data-bs-toggle="tab" data-bs-target="#trends-data" type="button" role="tab" aria-controls="trends-data" aria-selected="false">
                            <i class="bi bi-graph-up"></i> 趋势预测
                        </button>
                    </li>
                    <li class="nav-item dropdown">
                        <button class="nav-link dropdown-toggle" data-bs-toggle="dropdown" href="#" role="button" aria-expanded="false">
                            <i class="bi bi-three-dots"></i> 更多
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" id="export-tab" data-bs-toggle="tab" data-bs-target="#export-data" role="tab" aria-controls="export-data" aria-selected="false">
                                <i class="bi bi-download"></i> 导出数据
                            </a></li>
                            <li><a class="dropdown-item" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings-data" role="tab" aria-controls="settings-data" aria-selected="false">
                                <i class="bi bi-gear"></i> 图表设置
                            </a></li>
                        </ul>
                    </li>
                </ul>

                <!-- 标签页内容 -->
                <div class="tab-content" id="dashboardTabContent">
                    <!-- 概览标签页 -->
                    <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                        <!-- 统计卡片 -->
                        <div class="row mb-3">
                            <div class="col-md-3 mb-3">
                                <div class="stats-card py-2">
                                    <div class="stats-card-title small">用户数据</div>
                                    <div class="stats-card-value h4"><?php echo number_format($platform['stats']['today_users']); ?></div>
                                    <div class="stats-card-subtitle small">今日新增</div>
                                    <div class="stats-card-footer small">
                                        <i class="bi bi-people"></i> 累计: <?php echo number_format($platform['stats']['total_users']); ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="stats-card py-2">
                                    <div class="stats-card-title small">匹配数据</div>
                                    <div class="stats-card-value h4"><?php echo number_format($platform['stats']['today_matches']); ?></div>
                                    <div class="stats-card-subtitle small">今日匹配</div>
                                    <div class="stats-card-footer small">
                                        <i class="bi bi-images"></i> 累计: <?php echo number_format($platform['stats']['total_matches']); ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="stats-card py-2">
                                    <div class="stats-card-title small">订单数据</div>
                                    <div class="stats-card-value h4"><?php echo number_format($platform['stats']['today_orders']); ?></div>
                                    <div class="stats-card-subtitle small">今日订单</div>
                                    <div class="stats-card-footer small">
                                        <i class="bi bi-cart"></i> 累计: <?php echo number_format($platform['stats']['total_orders']); ?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="stats-card py-2">
                                    <div class="stats-card-title small">收入数据</div>
                                    <div class="stats-card-value h4">¥<?php echo number_format($platform['stats']['today_income'], 2); ?></div>
                                    <div class="stats-card-subtitle small">今日收入</div>
                                    <div class="stats-card-footer small">
                                        <i class="bi bi-currency-yen"></i> 累计: ¥<?php echo number_format($platform['stats']['total_income'], 2); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 图表区域 -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="chart-container">
                                    <div class="chart-title small">匹配统计</div>
                                    <div style="height: 300px; width: 100%;">
                                        <canvas id="matchesChart" style="width: 100%; height: 100%;"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="chart-container">
                                    <div class="chart-title small">收入统计</div>
                                    <div id="incomeChart" style="width: 100%; height: 300px; position: relative;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 用户分析标签页 -->
                    <div class="tab-pane fade" id="users-data" role="tabpanel" aria-labelledby="users-tab">
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="chart-container">
                                    <div class="chart-title">用户增长趋势</div>
                                    <div style="height: 350px; width: 100%;">
                                        <div class="d-flex justify-content-center align-items-center h-100">
                                            <div class="text-center text-muted">
                                                <i class="bi bi-bar-chart-line fs-1"></i>
                                                <p class="mt-3">用户增长趋势图表将在这里显示</p>
                                                <button class="btn btn-sm btn-outline-primary">加载数据</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="chart-container">
                                    <div class="chart-title">用户活跃度</div>
                                    <div style="height: 350px; width: 100%;">
                                        <div class="d-flex justify-content-center align-items-center h-100">
                                            <div class="text-center text-muted">
                                                <i class="bi bi-activity fs-1"></i>
                                                <p class="mt-3">用户活跃度图表将在这里显示</p>
                                                <button class="btn btn-sm btn-outline-primary">加载数据</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="chart-container">
                                    <div class="chart-title">用户留存率</div>
                                    <div style="height: 350px; width: 100%;">
                                        <div class="d-flex justify-content-center align-items-center h-100">
                                            <div class="text-center text-muted">
                                                <i class="bi bi-people fs-1"></i>
                                                <p class="mt-3">用户留存率图表将在这里显示</p>
                                                <button class="btn btn-sm btn-outline-primary">加载数据</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 收入分析标签页 -->
                    <div class="tab-pane fade" id="income-data" role="tabpanel" aria-labelledby="income-tab">
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="chart-container">
                                    <div class="chart-title">收入趋势分析</div>
                                    <div style="height: 350px; width: 100%;">
                                        <div class="d-flex justify-content-center align-items-center h-100">
                                            <div class="text-center text-muted">
                                                <i class="bi bi-graph-up-arrow fs-1"></i>
                                                <p class="mt-3">收入趋势分析图表将在这里显示</p>
                                                <button class="btn btn-sm btn-outline-primary">加载数据</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="chart-container">
                                    <div class="chart-title">收入来源分布</div>
                                    <div style="height: 350px; width: 100%;">
                                        <div class="d-flex justify-content-center align-items-center h-100">
                                            <div class="text-center text-muted">
                                                <i class="bi bi-pie-chart fs-1"></i>
                                                <p class="mt-3">收入来源分布图表将在这里显示</p>
                                                <button class="btn btn-sm btn-outline-primary">加载数据</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="chart-container">
                                    <div class="chart-title">ARPU值分析</div>
                                    <div style="height: 350px; width: 100%;">
                                        <div class="d-flex justify-content-center align-items-center h-100">
                                            <div class="text-center text-muted">
                                                <i class="bi bi-currency-yen fs-1"></i>
                                                <p class="mt-3">ARPU值分析图表将在这里显示</p>
                                                <button class="btn btn-sm btn-outline-primary">加载数据</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 趋势预测标签页 -->
                    <div class="tab-pane fade" id="trends-data" role="tabpanel" aria-labelledby="trends-tab">
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="chart-container">
                                    <div class="chart-title">未来30天趋势预测</div>
                                    <div style="height: 350px; width: 100%;">
                                        <div class="d-flex justify-content-center align-items-center h-100">
                                            <div class="text-center text-muted">
                                                <i class="bi bi-graph-up fs-1"></i>
                                                <p class="mt-3">趋势预测图表将在这里显示</p>
                                                <button class="btn btn-sm btn-outline-primary">生成预测</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 导出数据标签页 -->
                    <div class="tab-pane fade" id="export-data" role="tabpanel" aria-labelledby="export-tab">
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="chart-container">
                                    <div class="chart-title">导出数据</div>
                                    <div class="p-4">
                                        <form>
                                            <div class="mb-3">
                                                <label for="export-date-range" class="form-label">选择日期范围</label>
                                                <select class="form-select" id="export-date-range">
                                                    <option value="today">今天</option>
                                                    <option value="yesterday">昨天</option>
                                                    <option value="week" selected>本周</option>
                                                    <option value="month">本月</option>
                                                    <option value="custom">自定义</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="export-data-type" class="form-label">选择数据类型</label>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" value="users" id="export-users" checked>
                                                    <label class="form-check-label" for="export-users">用户数据</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" value="matches" id="export-matches" checked>
                                                    <label class="form-check-label" for="export-matches">匹配数据</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" value="orders" id="export-orders" checked>
                                                    <label class="form-check-label" for="export-orders">订单数据</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" value="income" id="export-income" checked>
                                                    <label class="form-check-label" for="export-income">收入数据</label>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="export-format" class="form-label">选择导出格式</label>
                                                <select class="form-select" id="export-format">
                                                    <option value="csv">CSV</option>
                                                    <option value="excel" selected>Excel</option>
                                                    <option value="pdf">PDF</option>
                                                    <option value="json">JSON</option>
                                                </select>
                                            </div>
                                            <button type="button" class="btn btn-primary">导出数据</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 图表设置标签页 -->
                    <div class="tab-pane fade" id="settings-data" role="tabpanel" aria-labelledby="settings-tab">
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <div class="chart-container">
                                    <div class="chart-title">图表设置</div>
                                    <div class="p-4">
                                        <form>
                                            <div class="mb-3">
                                                <label class="form-label">显示的图表</label>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" value="matches" id="show-matches" checked>
                                                    <label class="form-check-label" for="show-matches">匹配统计</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" value="income" id="show-income" checked>
                                                    <label class="form-check-label" for="show-income">收入统计</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" value="users" id="show-users">
                                                    <label class="form-check-label" for="show-users">用户增长</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" value="retention" id="show-retention">
                                                    <label class="form-check-label" for="show-retention">用户留存</label>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="chart-refresh" class="form-label">自动刷新间隔</label>
                                                <select class="form-select" id="chart-refresh">
                                                    <option value="0">不自动刷新</option>
                                                    <option value="30">30秒</option>
                                                    <option value="60" selected>1分钟</option>
                                                    <option value="300">5分钟</option>
                                                    <option value="600">10分钟</option>
                                                </select>
                                            </div>
                                            <button type="button" class="btn btn-primary">应用设置</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 其他内容区域（隐藏） -->
            <div class="content-section" id="match-records" style="display: none;">
                <div class="chart-container">
                    <div class="chart-title">匹配记录</div>
                    <p>此部分将显示匹配记录内容。</p>
                </div>
            </div>

            <div class="content-section" id="orders" style="display: none;">
                <div class="chart-container">
                    <div class="chart-title">订单管理</div>
                    <p>此部分将显示订单管理内容。</p>
                </div>
            </div>

            <div class="content-section" id="manual-orders" style="display: none;">
                <div class="chart-container">
                    <div class="chart-title">人工订单</div>
                    <p>此部分将显示人工订单内容。</p>
                </div>
            </div>

            <div class="content-section" id="users" style="display: none;">
                <div class="chart-container">
                    <div class="chart-title">用户管理</div>
                    <p>此部分将显示用户管理内容。</p>
                </div>
            </div>

            <div class="content-section" id="faq" style="display: none;">
                <div class="chart-container">
                    <div class="chart-title">常见问题</div>
                    <p>此部分将显示常见问题内容。</p>
                </div>
            </div>

            <div class="content-section" id="platform-settings" style="display: none;">
                <div class="chart-container">
                    <div class="chart-title">平台设置</div>
                    <p>此部分将显示平台设置内容。</p>
                </div>
            </div>

            <div class="content-section" id="miniprogram-settings" style="display: none;">
                <div class="chart-container">
                    <div class="chart-title">小程序设置</div>
                    <p>此部分将显示小程序设置内容。</p>
                </div>
            </div>

            <div class="content-section" id="plugins" style="display: none;">
                <div class="chart-container">
                    <div class="chart-title">插件列表</div>
                    <p>此部分将显示插件列表内容。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div class="toast-container position-fixed top-0 end-0 p-3" id="toastContainer"></div>

    <!-- 网站底部 -->
    <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/miniprogram-footer.php'); ?>

    <!-- 主题设置面板 -->
    <div class="theme-settings" id="theme-settings-panel">
        <div class="theme-settings-header">
            <h3>主题设置</h3>
            <button class="theme-settings-close" id="theme-settings-close">
                <i class="bi bi-x-lg"></i>
            </button>
        </div>
        <div class="theme-settings-body">
            <div class="theme-option">
                <div class="theme-option-title">主题颜色</div>
                <div class="color-picker">
                    <div class="color-option" style="background-color: #ff6b95;" data-color="#ff6b95"></div>
                    <div class="color-option" style="background-color: #4e73df;" data-color="#4e73df"></div>
                    <div class="color-option" style="background-color: #1cc88a;" data-color="#1cc88a"></div>
                    <div class="color-option" style="background-color: #36b9cc;" data-color="#36b9cc"></div>
                    <div class="color-option" style="background-color: #f6c23e;" data-color="#f6c23e"></div>
                    <div class="color-option" style="background-color: #e74a3b;" data-color="#e74a3b"></div>
                    <div class="color-option" style="background-color: #6f42c1;" data-color="#6f42c1"></div>
                    <div class="color-option" style="background-color: #5a5c69;" data-color="#5a5c69"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/assets/vendor/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/jquery-3.6.0.min.js"></script>
    <script src="/assets/js/common.js"></script>
    <script src="/assets/js/miniprogram-theme-switcher.js"></script>
    <script src="/assets/js/theme-color-draggable.js"></script>
    <script>
        // 主题颜色修改按钮点击事件处理函数
        function toggleThemeSettings(event) {
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            console.log('主题颜色按钮被点击');

            const themeSettings = document.getElementById('theme-settings-panel');
            if (themeSettings) {
                // 确保面板可见
                themeSettings.style.display = 'block';
                themeSettings.style.visibility = 'visible';
                themeSettings.style.opacity = '1';
                themeSettings.style.pointerEvents = 'auto';
                themeSettings.style.zIndex = '9999';

                // 切换显示状态
                if (themeSettings.classList.contains('show')) {
                    themeSettings.classList.remove('show');
                    console.log('主题设置面板已隐藏');
                } else {
                    themeSettings.classList.add('show');
                    console.log('主题设置面板已显示');

                    // 添加关闭按钮事件
                    const closeBtn = document.getElementById('theme-settings-close');
                    if (closeBtn) {
                        closeBtn.onclick = function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            themeSettings.classList.remove('show');
                            console.log('关闭按钮被点击');
                        };
                    }

                    // 点击面板外部关闭面板
                    setTimeout(function() {
                        const clickHandler = function(e) {
                            if (!themeSettings.contains(e.target) && e.target.id !== 'theme-color-btn') {
                                themeSettings.classList.remove('show');
                                document.removeEventListener('click', clickHandler);
                                console.log('点击外部区域，面板已关闭');
                            }
                        };
                        document.addEventListener('click', clickHandler);
                    }, 100);
                }
            } else {
                console.error('未找到主题设置面板');
            }

            return false;
        }

        // DOM元素
        const sidebarToggle = document.getElementById('sidebar-toggle');
        const miniprogramContainer = document.getElementById('miniprogram-container');
        const miniprogramSidebar = document.getElementById('miniprogram-sidebar');
        const refreshBtn = document.getElementById('refresh-btn');
        const loadingOverlay = document.getElementById('loading-overlay');
        const sidebarMenuItems = document.querySelectorAll('.sidebar-menu-item[data-section]');
        const contentSections = document.querySelectorAll('.content-section');
        const clearCacheBtn = document.getElementById('clear-cache-btn');
        const platformManageDropdown = document.getElementById('platformManageDropdown');

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM 加载完成');

            // 初始化主题设置
            initThemeSettings();

            // 显示加载动画
            showLoading();
            console.log('显示加载动画');

            try {
                // 侧边栏折叠切换
                if (sidebarToggle) {
                    sidebarToggle.addEventListener('click', function() {
                        miniprogramContainer.classList.toggle('sidebar-collapsed');
                    });
                }

                // 刷新按钮
                if (refreshBtn) {
                    // 移除旧的事件监听器
                    const newRefreshBtn = refreshBtn.cloneNode(true);
                    refreshBtn.parentNode.replaceChild(newRefreshBtn, refreshBtn);

                    // 确保按钮可点击
                    newRefreshBtn.style.cursor = 'pointer';
                    newRefreshBtn.style.pointerEvents = 'auto';

                    // 添加新的点击事件
                    newRefreshBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('刷新按钮被点击');

                        // 添加旋转动画
                        this.querySelector('i').classList.add('rotating');

                        // 显示加载动画
                        showLoading();

                        // 重新初始化图表
                        try {
                            initCharts();
                            console.log('图表已重新初始化');
                        } catch (error) {
                            console.error('重新初始化图表失败:', error);
                        }

                        setTimeout(function() {
                            // 移除旋转动画
                            newRefreshBtn.querySelector('i').classList.remove('rotating');
                            hideLoading();
                            showToast('success', '数据已刷新');
                        }, 800);
                    });
                }

                // 主题颜色相关功能已移至 miniprogram-theme-switcher.js

                // 清理缓存按钮
                if (clearCacheBtn) {
                    clearCacheBtn.addEventListener('click', function(e) {
                        e.preventDefault();

                        showLoading();

                        // 模拟清理缓存
                        setTimeout(function() {
                            hideLoading();
                            showToast('success', '缓存已清理');
                        }, 1000);
                    });
                }

                // 返回平台按钮
                const returnToPlatformsBtn = document.getElementById('return-to-platforms');
                if (returnToPlatformsBtn) {
                    returnToPlatformsBtn.addEventListener('click', function(e) {
                        e.preventDefault();
                        showLoading();
                        window.location.href = '/dashboard/platforms';
                    });
                }

                // 平台设置下拉菜单项
                const platformSettingsLinks = document.querySelectorAll('.dropdown-item[data-section]');
                if (platformSettingsLinks && platformSettingsLinks.length > 0) {
                    platformSettingsLinks.forEach(link => {
                        link.addEventListener('click', function(e) {
                            e.preventDefault();

                            const targetId = this.getAttribute('data-section');
                            if (!targetId) return;

                            // 显示加载动画
                            showLoading();

                            // 切换活动菜单项
                            sidebarMenuItems.forEach(menuItem => {
                                menuItem.classList.remove('active');
                                if (menuItem.getAttribute('data-section') === targetId) {
                                    menuItem.classList.add('active');
                                }
                            });

                            // 切换内容区域
                            contentSections.forEach(section => {
                                if (section) {
                                    section.style.display = 'none';
                                    section.classList.remove('fade-in');
                                }
                            });

                            const targetSection = document.getElementById(targetId);
                            if (targetSection) {
                                setTimeout(() => {
                                    targetSection.style.display = 'block';
                                    targetSection.classList.add('fade-in');
                                    hideLoading();
                                }, 500);
                            } else {
                                hideLoading();
                            }
                        });
                    });
                }

                // 侧边栏菜单项点击事件
                if (sidebarMenuItems && sidebarMenuItems.length > 0) {
                    sidebarMenuItems.forEach(item => {
                        item.addEventListener('click', function(e) {
                            // 阻止默认行为
                            e.preventDefault();

                            // 获取目标内容区域ID
                            const targetId = this.getAttribute('data-section');
                            if (!targetId) return;

                            // 显示加载动画
                            showLoading();

                            // 检查是否点击当前活动项
                            const isCurrentActive = this.classList.contains('active');

                            // 切换活动菜单项
                            sidebarMenuItems.forEach(menuItem => {
                                menuItem.classList.remove('active');
                            });
                            this.classList.add('active');

                            // 更新面包屑导航
                            const menuText = this.querySelector('span').textContent;
                            const breadcrumbCurrentPage = document.getElementById('breadcrumb-current-page');
                            if (breadcrumbCurrentPage) {
                                if (targetId === 'dashboard') {
                                    breadcrumbCurrentPage.textContent = '概览';
                                } else if (targetId === 'match-records') {
                                    breadcrumbCurrentPage.textContent = '匹配记录/列表';
                                } else {
                                    breadcrumbCurrentPage.textContent = menuText;
                                }
                            }

                            // 切换内容区域
                            contentSections.forEach(section => {
                                if (section) {
                                    section.style.display = 'none';
                                    // 添加淡出动画
                                    section.classList.remove('fade-in');
                                }
                            });

                            const targetSection = document.getElementById(targetId);
                            if (targetSection) {
                                // 模拟数据加载延迟
                                setTimeout(() => {
                                    // 显示目标区域
                                    targetSection.style.display = 'block';
                                    // 添加淡入动画
                                    targetSection.classList.add('fade-in');

                                    // 如果是首页或者点击了当前活动项，重新初始化图表
                                    if (targetId === 'dashboard' || isCurrentActive) {
                                        try {
                                            // 重新初始化图表
                                            initCharts();
                                            console.log('图表已重新初始化');

                                            // 如果点击的是当前活动项，显示刷新提示
                                            if (isCurrentActive) {
                                                showToast('success', '页面已刷新');
                                            }
                                        } catch (error) {
                                            console.error('重新初始化图表失败:', error);
                                        }
                                    }

                                    // 隐藏加载动画
                                    hideLoading();
                                }, 500);
                            } else {
                                hideLoading();
                            }
                        });
                    });
                }

                // 初始化图表
                try {
                    console.log('初始化图表');
                    initCharts();
                } catch (chartError) {
                    console.error('图表初始化错误:', chartError);
                }

                console.log('初始化完成');
            } catch (error) {
                console.error('初始化错误:', error);
            }

            // 隐藏加载动画
            console.log('准备隐藏加载动画');
            setTimeout(function() {
                hideLoading();
                console.log('加载动画已隐藏');
            }, 1000);
        });

        // 显示加载动画
        function showLoading() {
            if (loadingOverlay) {
                loadingOverlay.classList.add('show');
                console.log('显示加载动画');
            } else {
                console.error('加载动画元素不存在');
            }
        }

        // 隐藏加载动画
        function hideLoading() {
            if (loadingOverlay) {
                loadingOverlay.classList.remove('show');
                console.log('隐藏加载动画');
            } else {
                console.error('加载动画元素不存在');
            }

            // 确保加载动画不会永久显示
            setTimeout(function() {
                const overlay = document.getElementById('loading-overlay');
                if (overlay && overlay.classList.contains('show')) {
                    overlay.classList.remove('show');
                    console.warn('强制隐藏加载动画');
                }
            }, 5000);
        }

        // 主题颜色相关函数已移至 miniprogram-theme-init.js 和 miniprogram-theme-switcher.js

        // 颜色转换函数已移至 miniprogram-theme-init.js 和 miniprogram-theme-switcher.js



        // 显示消息提示
        function showToast(type, message) {
            const toastContainer = document.getElementById('toastContainer');

            // 创建toast元素
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : type === 'info' ? 'info' : 'primary'} border-0`;
            toast.setAttribute('role', 'alert');
            toast.setAttribute('aria-live', 'assertive');
            toast.setAttribute('aria-atomic', 'true');

            // 设置toast内容
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="bi ${type === 'success' ? 'bi-check-circle' : type === 'error' ? 'bi-x-circle' : 'bi-info-circle'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-progress"></div>
            `;

            // 添加到容器
            toastContainer.appendChild(toast);

            // 添加进度条动画
            const progress = toast.querySelector('.toast-progress');
            progress.style.width = '100%';
            progress.style.height = '3px';
            progress.style.background = 'rgba(255, 255, 255, 0.7)';
            progress.style.position = 'absolute';
            progress.style.bottom = '0';
            progress.style.left = '0';
            progress.style.transition = 'width 3s linear';

            // 开始进度条动画
            setTimeout(() => {
                progress.style.width = '0%';
            }, 100);

            // 初始化toast
            const bsToast = new bootstrap.Toast(toast, {
                autohide: true,
                delay: 3000
            });

            // 显示toast
            bsToast.show();

            // 监听隐藏事件，移除DOM元素
            toast.addEventListener('hidden.bs.toast', function() {
                toast.remove();
            });
        }

        // 显示图表数据的函数 - 全局定义
        window.showChartData = function(chart) {
            // 创建模态对话框
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.id = 'chartDataModal';
            modal.tabIndex = '-1';
            modal.setAttribute('aria-labelledby', 'chartDataModalLabel');
            modal.setAttribute('aria-hidden', 'true');

            // 模态对话框内容
            modal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="chartDataModalLabel">图表数据</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>日期</th>
                                            ${chart.data.datasets.map(ds => `<th>${ds.label}</th>`).join('')}
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${chart.data.labels.map((label, i) => `
                                            <tr>
                                                <td>${label}</td>
                                                ${chart.data.datasets.map(ds => `<td>${ds.data[i] || '-'}</td>`).join('')}
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                            <button type="button" class="btn btn-primary" id="exportTableBtn">导出数据</button>
                        </div>
                    </div>
                </div>
            `;

            // 添加到文档
            document.body.appendChild(modal);

            // 初始化模态对话框 - 设置为不可通过点击外部区域关闭
            const modalInstance = new bootstrap.Modal(modal, {
                backdrop: 'static',  // 静态背景，点击不会关闭
                keyboard: false      // 按ESC键不会关闭
            });
            modalInstance.show();

            // 导出数据按钮点击事件
            modal.querySelector('#exportTableBtn').addEventListener('click', function() {
                // 创建CSV内容
                let csv = 'data:text/csv;charset=utf-8,\uFEFF';

                // 添加表头
                csv += '日期,' + chart.data.datasets.map(ds => ds.label).join(',') + '\n';

                // 添加数据行
                chart.data.labels.forEach((label, i) => {
                    csv += label + ',' + chart.data.datasets.map(ds => ds.data[i] || '').join(',') + '\n';
                });

                // 创建下载链接
                const encodedUri = encodeURI(csv);
                const link = document.createElement('a');
                link.setAttribute('href', encodedUri);
                link.setAttribute('download', '图表数据.csv');
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            });

            // 模态对话框关闭时移除
            modal.addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(modal);
            });
        };

        // 初始化图表
        function initCharts() {
            console.log('初始化图表');
            try {
                // 检查 Chart 是否存在
                if (typeof Chart === 'undefined') {
                    console.error('Chart 库未加载');
                    // 显示错误信息
                    const matchesCanvas = document.getElementById('matchesChart');
                    const incomeCanvas = document.getElementById('incomeChart');

                    if (matchesCanvas) {
                        const ctx = matchesCanvas.getContext('2d');
                        ctx.font = '14px Arial';
                        ctx.fillStyle = '#dc3545';
                        ctx.textAlign = 'center';
                        ctx.fillText('图表库加载失败，请刷新页面重试', matchesCanvas.width / 2, matchesCanvas.height / 2);
                    }

                    if (incomeCanvas) {
                        const ctx = incomeCanvas.getContext('2d');
                        ctx.font = '14px Arial';
                        ctx.fillStyle = '#dc3545';
                        ctx.textAlign = 'center';
                        ctx.fillText('图表库加载失败，请刷新页面重试', incomeCanvas.width / 2, incomeCanvas.height / 2);
                    }

                    return;
                }

                // 检查 ECharts 是否存在
                let echartsLoaded = true;
                if (typeof echarts === 'undefined') {
                    console.error('ECharts 库未加载');
                    echartsLoaded = false;
                    // 显示错误信息
                    const incomeChartContainer = document.getElementById('incomeChart');
                    if (incomeChartContainer) {
                        incomeChartContainer.innerHTML = '<div style="padding: 20px; text-align: center; color: #dc3545;">' +
                            '<i class="bi bi-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i><br>' +
                            'ECharts 图表库加载失败，请刷新页面重试</div>';
                    }

                    // 继续初始化其他图表
                    console.log('将继续初始化其他图表');
                }

                // 制作统计图表 - 参考上传图片样式
                const matchesCanvas = document.getElementById('matchesChart');
                if (matchesCanvas) {
                    const matchesCtx = matchesCanvas.getContext('2d');

                    // 创建增强的图表实例
                    const matchesChart = new Chart(matchesCtx, {
                        type: 'line',
                        data: {
                            labels: <?php echo json_encode($dates); ?>,
                            datasets: [
                                // 原始数据集
                                <?php
                                $i = 0;
                                foreach ($platformsData as $key => $data) {
                                    echo $i > 0 ? ',' : '';
                                    echo '{
                                        label: "' . $platformNames[$key] . '",
                                        data: ' . json_encode($data) . ',
                                        borderColor: "' . $platformColors[$key] . '",
                                        backgroundColor: "transparent",
                                        borderWidth: 2,
                                        pointRadius: 4,
                                        pointHoverRadius: 6,
                                        pointBackgroundColor: "' . $platformColors[$key] . '",
                                        pointBorderColor: "' . $platformColors[$key] . '",
                                        pointBorderWidth: 1,
                                        tension: 0.4,
                                        fill: false
                                    }';
                                    $i++;
                                }
                                ?>
                                ,
                                // 添加成功率数据集 - 使用右侧Y轴
                                {
                                    label: '成功率',
                                    data: [75, 78, 82, 80, 85, 83, 88],
                                    borderColor: 'rgba(52, 152, 219, 1)',
                                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                                    borderWidth: 2,
                                    borderDash: [5, 5],
                                    pointRadius: 4,
                                    pointHoverRadius: 6,
                                    pointBackgroundColor: 'rgba(52, 152, 219, 1)',
                                    pointBorderColor: '#fff',
                                    pointBorderWidth: 1,
                                    tension: 0.4,
                                    fill: false,
                                    yAxisID: 'y1',
                                    hidden: true
                                },
                                // 添加趋势预测数据集
                                {
                                    label: '趋势预测',
                                    data: function() {
                                        // 获取第一个数据集的数据
                                        const originalData = <?php echo json_encode(reset($platformsData)); ?>;
                                        // 基于原始数据创建预测数据
                                        const lastValue = originalData[originalData.length - 1];
                                        const growth = 1.15; // 15% 增长率
                                        return [...originalData, Math.round(lastValue * growth), Math.round(lastValue * growth * growth)];
                                    }(),
                                    borderColor: 'rgba(231, 76, 60, 0.7)',
                                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                                    borderWidth: 2,
                                    borderDash: [3, 3],
                                    pointRadius: 0,
                                    pointHoverRadius: 6,
                                    pointBackgroundColor: 'rgba(231, 76, 60, 1)',
                                    pointBorderColor: '#fff',
                                    pointStyle: 'star',
                                    pointBorderWidth: 1,
                                    tension: 0.4,
                                    fill: false,
                                    hidden: true
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            animation: {
                                duration: 1000
                            },
                            interaction: {
                                mode: 'index',
                                intersect: false
                            },
                            plugins: {
                                title: {
                                    display: true,
                                    text: '制作统计',
                                    position: 'top',
                                    align: 'start',
                                    font: {
                                        size: 16,
                                        weight: 'normal'
                                    },
                                    padding: {
                                        top: 10,
                                        bottom: 30
                                    },
                                    color: '#333'
                                },
                                subtitle: {
                                    display: true,
                                    text: '与上周相比增长 12.5%',
                                    position: 'top',
                                    align: 'start',
                                    font: {
                                        size: 12,
                                        style: 'italic'
                                    },
                                    padding: {
                                        bottom: 10
                                    },
                                    color: '#2ecc71'
                                },
                                legend: {
                                    position: 'top',
                                    align: 'start',
                                    labels: {
                                        boxWidth: 12,
                                        usePointStyle: true,
                                        pointStyle: 'circle',
                                        padding: 20,
                                        color: '#333',
                                        font: {
                                            size: 12
                                        }
                                    },
                                    onClick: function(e, legendItem, legend) {
                                        // 默认图例点击行为
                                        Chart.defaults.plugins.legend.onClick.call(this, e, legendItem, legend);

                                        // 如果是成功率，需要显示/隐藏右侧Y轴
                                        if (legendItem.text === '成功率') {
                                            const chart = legend.chart;
                                            chart.options.scales.y1.display = !legendItem.hidden;
                                            chart.update();
                                        }

                                        // 如果是趋势预测，需要调整X轴范围
                                        if (legendItem.text === '趋势预测') {
                                            const chart = legend.chart;
                                            if (legendItem.hidden) {
                                                // 预测被隐藏，恢复原始X轴范围
                                                chart.options.scales.x.max = 7;
                                            } else {
                                                // 预测显示，扩展X轴范围
                                                chart.options.scales.x.max = 9;
                                            }
                                            chart.update();
                                        }
                                    }
                                },
                                tooltip: {
                                    enabled: true,
                                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                    titleColor: '#333',
                                    bodyColor: '#666',
                                    borderColor: 'rgba(0, 0, 0, 0.1)',
                                    borderWidth: 1,
                                    cornerRadius: 8,
                                    displayColors: true,
                                    boxWidth: 8,
                                    boxHeight: 8,
                                    usePointStyle: true,
                                    padding: 12,
                                    callbacks: {
                                        title: function(tooltipItems) {
                                            return tooltipItems[0].label;
                                        },
                                        label: function(context) {
                                            let label = context.dataset.label || '';
                                            if (label) {
                                                label += ': ';
                                            }

                                            // 根据数据集类型显示不同的单位
                                            if (context.dataset.label === '成功率') {
                                                label += context.parsed.y + '%';
                                            } else if (context.dataset.label === '趋势预测' && context.dataIndex >= 7) {
                                                label += context.parsed.y + ' (预测)';
                                            } else {
                                                label += context.parsed.y;
                                            }

                                            return label;
                                        },
                                        afterBody: function(tooltipItems) {
                                            // 添加同比/环比数据
                                            const index = tooltipItems[0].dataIndex;
                                            if (index > 0) {
                                                const currentValue = tooltipItems[0].parsed.y;
                                                const previousValue = tooltipItems[0].dataset.data[index - 1];

                                                if (currentValue && previousValue) {
                                                    const change = ((currentValue - previousValue) / previousValue * 100).toFixed(1);
                                                    const changeText = change > 0 ? '+' + change + '%' : change + '%';
                                                    const color = change > 0 ? '#2ecc71' : '#e74c3c';

                                                    return ['', '环比变化: ' + changeText];
                                                }
                                            }
                                            return [];
                                        },
                                        footer: function(tooltipItems) {
                                            // 如果是预测数据，添加说明
                                            const item = tooltipItems[0];
                                            if (item.dataset.label === '趋势预测' && item.dataIndex >= 7) {
                                                return ['', '* 基于历史数据和15%增长率的预测'];
                                            }
                                            return [];
                                        }
                                    }
                                },
                                // 添加注释插件
                                annotation: {
                                    annotations: {
                                        line1: {
                                            type: 'line',
                                            yMin: 80,
                                            yMax: 80,
                                            borderColor: 'rgba(255, 99, 132, 0.5)',
                                            borderWidth: 2,
                                            borderDash: [6, 6],
                                            label: {
                                                display: true,
                                                content: '目标线 (80)',
                                                position: 'end',
                                                backgroundColor: 'rgba(255, 99, 132, 0.8)',
                                                font: {
                                                    size: 11
                                                }
                                            }
                                        }
                                    }
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    border: {
                                        display: false
                                    },
                                    grid: {
                                        color: 'rgba(0, 0, 0, 0.05)',
                                        drawTicks: false,
                                        borderDash: [5, 5]
                                    },
                                    ticks: {
                                        padding: 10,
                                        color: '#999',
                                        font: {
                                            size: 11
                                        },
                                        callback: function(value) {
                                            return value;
                                        }
                                    },
                                    title: {
                                        display: true,
                                        text: '匹配次数',
                                        color: '#666',
                                        font: {
                                            size: 12
                                        }
                                    },
                                    max: 1000
                                },
                                // 添加右侧Y轴 - 用于成功率
                                y1: {
                                    type: 'linear',
                                    position: 'right',
                                    beginAtZero: true,
                                    min: 0,
                                    max: 100,
                                    border: {
                                        display: false
                                    },
                                    grid: {
                                        drawOnChartArea: false
                                    },
                                    ticks: {
                                        padding: 10,
                                        color: 'rgba(52, 152, 219, 1)',
                                        font: {
                                            size: 11
                                        },
                                        callback: function(value) {
                                            return value + '%';
                                        }
                                    },
                                    title: {
                                        display: true,
                                        text: '成功率 (%)',
                                        color: 'rgba(52, 152, 219, 1)',
                                        font: {
                                            size: 12
                                        }
                                    },
                                    display: false
                                },
                                x: {
                                    border: {
                                        display: false
                                    },
                                    grid: {
                                        display: false
                                    },
                                    ticks: {
                                        padding: 10,
                                        color: '#999',
                                        font: {
                                            size: 11
                                        }
                                    },
                                    max: 7 // 默认显示7天数据，预测数据显示时会扩展到9
                                }
                            },
                            layout: {
                                padding: {
                                    left: 10,
                                    right: 10,
                                    top: 0,
                                    bottom: 10
                                }
                            },
                            elements: {
                                line: {
                                    tension: 0.4
                                },
                                point: {
                                    radius: 3,
                                    hoverRadius: 5
                                }
                            }
                        },
                        plugins: [
                            {
                                id: 'customTooltip',
                                afterDraw: (chart) => {
                                    const activeElements = chart.getActiveElements();
                                    if (activeElements.length > 0) {
                                        const activePoint = activeElements[0];
                                        const ctx = chart.ctx;
                                        const x = activePoint.element.x;
                                        const yAxis = chart.scales.y;
                                        const topY = yAxis.top;
                                        const bottomY = yAxis.bottom;

                                        // 绘制垂直线
                                        ctx.save();
                                        ctx.beginPath();
                                        ctx.moveTo(x, topY);
                                        ctx.lineTo(x, bottomY);
                                        ctx.lineWidth = 1;
                                        ctx.strokeStyle = 'rgba(0, 0, 0, 0.1)';
                                        ctx.setLineDash([4, 4]);
                                        ctx.stroke();
                                        ctx.restore();

                                        // 绘制数据点的值
                                        activeElements.forEach(activeElement => {
                                            const datasetIndex = activeElement.datasetIndex;
                                            const index = activeElement.index;
                                            const value = chart.data.datasets[datasetIndex].data[index];
                                            const label = chart.data.datasets[datasetIndex].label;
                                            const color = chart.data.datasets[datasetIndex].borderColor;

                                            // 绘制背景框
                                            const textWidth = ctx.measureText(value).width + 10;
                                            const textHeight = 20;
                                            ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
                                            ctx.fillRect(x - textWidth / 2, activeElement.element.y - 25, textWidth, textHeight);

                                            // 绘制文本
                                            ctx.fillStyle = '#fff';
                                            ctx.textAlign = 'center';
                                            ctx.textBaseline = 'middle';
                                            ctx.fillText(value, x, activeElement.element.y - 15);
                                        });
                                    }
                                }
                            },
                            {
                                id: 'customToolbar',
                                beforeDraw: (chart) => {
                                    const ctx = chart.ctx;
                                    const width = chart.width;
                                    const height = chart.height;

                                    // 如果工具栏已经存在，不再重复创建
                                    if (chart.toolbar) return;

                                    // 创建工具栏
                                    const toolbar = document.createElement('div');
                                    toolbar.className = 'chart-toolbar';
                                    toolbar.style.position = 'absolute';
                                    toolbar.style.top = '10px';
                                    toolbar.style.right = '10px';
                                    toolbar.style.display = 'flex';
                                    toolbar.style.gap = '5px';
                                    toolbar.style.zIndex = '10';

                                    // 添加工具按钮
                                    const tools = [
                                        { icon: 'bi-download', title: '下载图表', action: 'download' },
                                        { icon: 'bi-table', title: '查看数据', action: 'data' },
                                        { icon: 'bi-arrow-repeat', title: '刷新数据', action: 'refresh' },
                                        { icon: 'bi-gear', title: '图表设置', action: 'settings' },
                                        { icon: 'bi-zoom-in', title: '放大查看', action: 'zoom' }
                                    ];

                                    tools.forEach(tool => {
                                        const button = document.createElement('button');
                                        button.className = 'btn btn-sm btn-light';
                                        button.title = tool.title;
                                        button.innerHTML = `<i class="bi ${tool.icon}"></i>`;
                                        button.style.padding = '4px 8px';
                                        button.style.fontSize = '12px';
                                        button.style.borderRadius = '4px';
                                        button.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';

                                        button.addEventListener('click', () => {
                                            switch (tool.action) {
                                                case 'download':
                                                    // 下载图表为图片
                                                    const link = document.createElement('a');
                                                    link.download = '匹配统计图表.png';
                                                    link.href = chart.toBase64Image();
                                                    link.click();
                                                    break;
                                                case 'data':
                                                    // 显示数据表格
                                                    showChartData(chart);
                                                    break;
                                                case 'refresh':
                                                    // 刷新数据
                                                    showLoading();
                                                    setTimeout(() => {
                                                        // 模拟数据刷新
                                                        chart.data.datasets.forEach(dataset => {
                                                            if (dataset.label !== '趋势预测' && dataset.label !== '成功率') {
                                                                dataset.data = dataset.data.map(() => Math.floor(Math.random() * 100) + 50);
                                                            }
                                                        });
                                                        chart.update();
                                                        hideLoading();
                                                        showToast('success', '数据已刷新');
                                                    }, 1000);
                                                    break;
                                                case 'settings':
                                                    // 切换到图表设置标签页
                                                    const settingsTab = document.querySelector('#settings-tab');
                                                    if (settingsTab) {
                                                        settingsTab.click();
                                                    }
                                                    break;
                                                case 'zoom':
                                                    // 创建全屏查看模态框
                                                    const modal = document.createElement('div');
                                                    modal.className = 'modal fade';
                                                    modal.id = 'chartZoomModal';
                                                    modal.tabIndex = '-1';
                                                    modal.setAttribute('aria-labelledby', 'chartZoomModalLabel');
                                                    modal.setAttribute('aria-hidden', 'true');

                                                    // 模态框内容
                                                    modal.innerHTML = `
                                                        <div class="modal-dialog modal-xl">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title" id="chartZoomModalLabel">匹配统计详情</h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    <div class="chart-container" style="position: relative; height: 70vh;">
                                                                        <canvas id="zoomChart"></canvas>
                                                                    </div>
                                                                </div>
                                                                <div class="modal-footer">
                                                                    <div class="btn-group me-auto">
                                                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="toggleDataType">
                                                                            <i class="bi bi-bar-chart"></i> 切换图表类型
                                                                        </button>
                                                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="toggleDatasets">
                                                                            <i class="bi bi-layers"></i> 显示/隐藏数据集
                                                                        </button>
                                                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="showAllData">
                                                                            <i class="bi bi-eye"></i> 显示所有数据
                                                                        </button>
                                                                    </div>
                                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                                                    <button type="button" class="btn btn-primary" id="downloadZoomChart">
                                                                        <i class="bi bi-download"></i> 下载图表
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    `;

                                                    // 添加到文档
                                                    document.body.appendChild(modal);

                                                    // 初始化模态框 - 设置为不可通过点击外部区域关闭
                                                    const modalInstance = new bootstrap.Modal(modal, {
                                                        backdrop: 'static',  // 静态背景，点击不会关闭
                                                        keyboard: false      // 按ESC键不会关闭
                                                    });
                                                    modalInstance.show();

                                                    // 创建放大版图表
                                                    modal.addEventListener('shown.bs.modal', function() {
                                                        const zoomCtx = document.getElementById('zoomChart').getContext('2d');

                                                        // 复制原图表配置
                                                        const zoomConfig = JSON.parse(JSON.stringify(chart.config));

                                                        // 调整配置以适应大屏幕
                                                        zoomConfig.options.responsive = true;
                                                        zoomConfig.options.maintainAspectRatio = false;
                                                        zoomConfig.options.plugins.title.font.size = 20;
                                                        zoomConfig.options.plugins.subtitle.font.size = 16;

                                                        // 创建新图表
                                                        const zoomChart = new Chart(zoomCtx, zoomConfig);

                                                        // 下载按钮事件
                                                        document.getElementById('downloadZoomChart').addEventListener('click', function() {
                                                            const link = document.createElement('a');
                                                            link.download = '匹配统计图表_大图.png';
                                                            link.href = zoomChart.toBase64Image();
                                                            link.click();
                                                        });

                                                        // 切换图表类型
                                                        document.getElementById('toggleDataType').addEventListener('click', function() {
                                                            const types = ['line', 'bar', 'radar'];
                                                            const currentType = zoomChart.config.type;
                                                            const newTypeIndex = (types.indexOf(currentType) + 1) % types.length;
                                                            const newType = types[newTypeIndex];

                                                            zoomChart.config.type = newType;
                                                            zoomChart.update();
                                                            showToast('info', `图表类型已切换为: ${newType}`);
                                                        });

                                                        // 显示/隐藏数据集
                                                        document.getElementById('toggleDatasets').addEventListener('click', function() {
                                                            // 创建数据集选择对话框
                                                            const datasetsModal = document.createElement('div');
                                                            datasetsModal.className = 'modal fade';
                                                            datasetsModal.id = 'datasetsModal';
                                                            datasetsModal.tabIndex = '-1';

                                                            // 构建数据集选择内容
                                                            let checkboxesHTML = '';
                                                            zoomChart.data.datasets.forEach((dataset, index) => {
                                                                const isChecked = !dataset.hidden;
                                                                const color = dataset.borderColor;
                                                                checkboxesHTML += `
                                                                    <div class="form-check mb-2">
                                                                        <input class="form-check-input" type="checkbox" id="dataset-${index}"
                                                                            data-index="${index}" ${isChecked ? 'checked' : ''}>
                                                                        <label class="form-check-label" for="dataset-${index}">
                                                                            <span class="color-dot" style="background-color: ${color}"></span>
                                                                            ${dataset.label}
                                                                        </label>
                                                                    </div>
                                                                `;
                                                            });

                                                            datasetsModal.innerHTML = `
                                                                <div class="modal-dialog modal-dialog-centered">
                                                                    <div class="modal-content">
                                                                        <div class="modal-header">
                                                                            <h5 class="modal-title">选择要显示的数据集</h5>
                                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                        </div>
                                                                        <div class="modal-body">
                                                                            <style>
                                                                                .color-dot {
                                                                                    display: inline-block;
                                                                                    width: 12px;
                                                                                    height: 12px;
                                                                                    border-radius: 50%;
                                                                                    margin-right: 8px;
                                                                                }
                                                                            </style>
                                                                            ${checkboxesHTML}
                                                                        </div>
                                                                        <div class="modal-footer">
                                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                                            <button type="button" class="btn btn-primary" id="applyDatasets">应用</button>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            `;

                                                            document.body.appendChild(datasetsModal);
                                                            const dsModalInstance = new bootstrap.Modal(datasetsModal, {
                                                                backdrop: 'static',  // 静态背景，点击不会关闭
                                                                keyboard: false      // 按ESC键不会关闭
                                                            });
                                                            dsModalInstance.show();

                                                            // 应用数据集选择
                                                            document.getElementById('applyDatasets').addEventListener('click', function() {
                                                                const checkboxes = datasetsModal.querySelectorAll('input[type="checkbox"]');
                                                                checkboxes.forEach(checkbox => {
                                                                    const index = parseInt(checkbox.getAttribute('data-index'));
                                                                    zoomChart.data.datasets[index].hidden = !checkbox.checked;
                                                                });

                                                                zoomChart.update();
                                                                dsModalInstance.hide();

                                                                // 移除模态框
                                                                datasetsModal.addEventListener('hidden.bs.modal', function() {
                                                                    document.body.removeChild(datasetsModal);
                                                                });
                                                            });
                                                        });

                                                        // 显示所有数据
                                                        document.getElementById('showAllData').addEventListener('click', function() {
                                                            zoomChart.data.datasets.forEach(dataset => {
                                                                dataset.hidden = false;
                                                            });
                                                            zoomChart.update();
                                                            showToast('success', '已显示所有数据集');
                                                        });
                                                    });

                                                    // 模态框关闭时移除
                                                    modal.addEventListener('hidden.bs.modal', function() {
                                                        document.body.removeChild(modal);
                                                    });
                                                    break;
                                            }
                                        });

                                        toolbar.appendChild(button);
                                    });

                                    // 将工具栏添加到图表容器
                                    const container = chart.canvas.parentNode;
                                    container.style.position = 'relative';
                                    container.appendChild(toolbar);

                                    // 保存工具栏引用
                                    chart.toolbar = toolbar;
                                }
                            }
                        ]
                    });

                    // 创建图表工具栏和数据查看功能
                    matchesChart.update();
                } else {
                    console.error('未找到制作统计图表元素');
                }

                // 收入统计图表 - 使用ECharts实现堆叠条形图
                const incomeChartContainer = document.getElementById('incomeChart');
                if (incomeChartContainer) {
                    try {
                        // 检查 ECharts 是否存在
                        if (typeof echarts === 'undefined') {
                            console.error('初始化图表时出错: echarts is not defined');
                            incomeChartContainer.innerHTML = '<div style="padding: 20px; text-align: center; color: #dc3545;">' +
                                '<i class="bi bi-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i><br>' +
                                'ECharts 图表库未加载，请刷新页面重试</div>';

                            // 尝试加载本地ECharts库
                            var script = document.createElement('script');
                            script.src = '/assets/vendor/echarts/echarts.min.js';
                            script.onload = function() {
                                console.log('本地ECharts库加载成功，重新初始化图表');
                                // 清空错误信息
                                incomeChartContainer.innerHTML = '';
                                // 重新初始化图表
                                try {
                                    const incomeChart = echarts.init(incomeChartContainer);
                                    incomeChart.setOption(option);
                                } catch (e) {
                                    console.error('重新初始化图表失败:', e);
                                }
                            };
                            document.head.appendChild(script);
                            return;
                        }

                        // 初始化ECharts实例
                        console.log('初始化ECharts实例');
                        const incomeChart = echarts.init(incomeChartContainer);

                    // 定义平台颜色 - 与参考图片一致
                    const platformColors = {
                        '微信': '#2ecc71',
                        '支付宝': '#3498db',
                        '头条': '#e74c3c',
                        '百度': '#9b59b6',
                        'QQ': '#00bcd4',
                        '快手': '#ff5722'
                    };

                    <?php
                    // 模拟数据 - 确保有足够的数据展示，与参考图片一致
                    $mockDates = ['05.17', '05.16', '05.15', '05.14', '05.13', '05.12', '05.11'];
                    $mockPlatforms = [
                        '微信' => ['color' => '#2ecc71', 'data' => [45, 55, 70, 55, 50, 65, 60]],
                        '支付宝' => ['color' => '#3498db', 'data' => [15, 20, 10, 25, 15, 10, 15]],
                        '头条' => ['color' => '#e74c3c', 'data' => [5, 8, 12, 10, 7, 9, 11]],
                        '百度' => ['color' => '#9b59b6', 'data' => [3, 5, 7, 4, 6, 8, 5]],
                        'QQ' => ['color' => '#00bcd4', 'data' => [2, 4, 6, 3, 5, 7, 4]],
                        '快手' => ['color' => '#ff5722', 'data' => [1, 3, 5, 2, 4, 6, 3]]
                    ];

                    // 确保至少有一些数据显示
                    if (empty($platformsIncome)) {
                        $platformsIncome = $mockPlatforms;
                    }

                    // 准备ECharts的数据格式
                    echo 'const chartDates = ' . json_encode($mockDates) . ';';
                    echo 'const platformNames = ' . json_encode(array_keys($mockPlatforms)) . ';';
                    echo 'const platformData = {';
                    foreach ($mockPlatforms as $platform => $info) {
                        echo '"' . $platform . '": ' . json_encode($info['data']) . ',';
                    }
                    echo '};';
                    echo 'const colorMap = ' . json_encode($platformColors) . ';';
                    ?>

                    // 准备系列数据
                    const series = platformNames.map(platform => {
                        return {
                            name: platform,
                            type: 'bar',
                            stack: 'total',
                            barWidth: '60%',
                            emphasis: {
                                focus: 'series'
                            },
                            data: platformData[platform],
                            itemStyle: {
                                color: colorMap[platform]
                            }
                        };
                    });

                    // 配置项
                    const option = {
                        title: {
                            text: '收入统计',
                            left: 'left',
                            top: 10,
                            textStyle: {
                                fontSize: 16,
                                fontWeight: 'normal',
                                color: '#333'
                            },
                            subtext: '与上周同比增长 15.2%',
                            subtextStyle: {
                                color: '#2ecc71',
                                fontSize: 12
                            }
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            },
                            formatter: function(params) {
                                let result = params[0].axisValue + '<br/>';
                                let total = 0;

                                // 先计算总和
                                params.forEach(param => {
                                    if (param.value > 0) {
                                        total += param.value;
                                    }
                                });

                                // 添加各平台数据
                                params.forEach(param => {
                                    if (param.value > 0) {
                                        const percentage = ((param.value / total) * 100).toFixed(1);
                                        result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' + param.color + '"></span>';
                                        result += param.seriesName + ': ' + param.value + ' (' + percentage + '%)<br/>';
                                    }
                                });

                                // 添加总计
                                result += '<div style="margin-top:5px;border-top:1px solid #eee;padding-top:5px;">';
                                result += '总计: ' + total;

                                // 添加同比/环比数据
                                const compareData = {
                                    '同比': '+15.2%',
                                    '环比': '+5.8%'
                                };

                                for (const [label, value] of Object.entries(compareData)) {
                                    const color = value.startsWith('+') ? '#2ecc71' : '#e74c3c';
                                    result += '<br/><span style="color:' + color + '">' + label + ': ' + value + '</span>';
                                }

                                result += '</div>';
                                return result;
                            }
                        },
                        legend: {
                            data: platformNames,
                            top: 40,
                            left: 'center',
                            itemWidth: 15,
                            itemHeight: 10,
                            textStyle: {
                                color: '#333'
                            },
                            icon: 'rect',
                            selectedMode: true,
                            orient: 'horizontal',
                            padding: [5, 10],
                            itemGap: 20,
                            formatter: function(name) {
                                // 确保图例文字不会太长
                                return name.length > 4 ? name.substring(0, 4) + '...' : name;
                            }
                        },
                        toolbox: {
                            feature: {
                                saveAsImage: { show: true, title: '保存图片' },
                                dataView: { show: true, title: '数据视图', readOnly: true },
                                magicType: { show: true, type: ['line', 'bar', 'stack'], title: { line: '折线图', bar: '柱状图', stack: '堆叠' } },
                                restore: { show: true, title: '还原' }
                            },
                            right: 20,
                            top: 20
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            top: '80px',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'value',
                            max: 120, // 增加最大值以适应更多数据
                            axisLine: {
                                show: false
                            },
                            axisTick: {
                                show: false
                            },
                            splitLine: {
                                lineStyle: {
                                    type: 'dashed',
                                    color: 'rgba(0, 0, 0, 0.05)'
                                }
                            }
                        },
                        yAxis: {
                            type: 'category',
                            data: chartDates,
                            axisLine: {
                                show: false
                            },
                            axisTick: {
                                show: false
                            },
                            splitLine: {
                                show: false
                            }
                        },
                        series: series
                    };

                    // 使用配置项设置图表
                    incomeChart.setOption(option);

                    // 响应窗口大小变化
                    window.addEventListener('resize', function() {
                        incomeChart.resize();
                    });
                    } catch (error) {
                        console.error('ECharts初始化错误:', error);
                        incomeChartContainer.innerHTML = '<div style="padding: 20px; text-align: center; color: #dc3545;">' +
                            '<i class="bi bi-exclamation-triangle" style="font-size: 24px; margin-bottom: 10px;"></i><br>' +
                            'ECharts 图表初始化失败: ' + error.message + '</div>';
                    }
                } else {
                    console.error('未找到收入统计图表容器');
                }

                // 初始化完成
                console.log('图表初始化完成');

            } catch (error) {
                console.error('初始化图表时出错:', error);
                // 显示错误信息
                const canvases = document.querySelectorAll('canvas');
                canvases.forEach(canvas => {
                    const ctx = canvas.getContext('2d');
                    ctx.font = '14px Arial';
                    ctx.fillStyle = '#dc3545';
                    ctx.textAlign = 'center';
                    ctx.fillText('图表初始化失败: ' + error.message, canvas.width / 2, canvas.height / 2);
                });
            }
        }
    </script>

    <!-- 日期范围选择器、标签页和主题切换脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化Bootstrap标签页
            const triggerTabList = document.querySelectorAll('#dashboardTabs button, #dashboardTabs a')
            triggerTabList.forEach(triggerEl => {
                triggerEl.addEventListener('click', function(event) {
                    event.preventDefault();
                    const tabTrigger = new bootstrap.Tab(triggerEl);
                    tabTrigger.show();
                });
            });

            // 数据导出功能
            const exportButton = document.querySelector('#export-data button.btn-primary');
            if (exportButton) {
                exportButton.addEventListener('click', function() {
                    const dateRange = document.getElementById('export-date-range').value;
                    const format = document.getElementById('export-format').value;

                    // 获取选中的数据类型
                    const dataTypes = [];
                    document.querySelectorAll('#export-data input[type="checkbox"]:checked').forEach(checkbox => {
                        dataTypes.push(checkbox.value);
                    });

                    if (dataTypes.length === 0) {
                        showToast('error', '请至少选择一种数据类型');
                        return;
                    }

                    // 显示加载动画
                    showLoading();

                    // 模拟导出过程
                    setTimeout(function() {
                        hideLoading();

                        // 创建一个模拟的下载链接
                        const link = document.createElement('a');
                        link.href = 'javascript:void(0)';
                        link.download = `platform_data_${dateRange}.${format}`;
                        link.style.display = 'none';

                        // 显示成功消息
                        showToast('success', `数据已导出为 ${format.toUpperCase()} 格式`);

                        // 移除链接
                        setTimeout(() => {
                            if (document.body.contains(link)) {
                                document.body.removeChild(link);
                            }
                        }, 100);
                    }, 1500);
                });
            }
            // 日期范围选择器
            const dateRangeSelect = document.getElementById('date-range');
            const customDatePicker = document.getElementById('custom-date-picker');
            const startDateInput = document.getElementById('start-date');
            const endDateInput = document.getElementById('end-date');

            // 设置默认日期
            const today = new Date();
            const oneWeekAgo = new Date(today);
            oneWeekAgo.setDate(today.getDate() - 7);

            startDateInput.valueAsDate = oneWeekAgo;
            endDateInput.valueAsDate = today;

            // 日期范围选择事件
            dateRangeSelect.addEventListener('change', function() {
                const selectedValue = this.value;

                if (selectedValue === 'custom') {
                    // 显示自定义日期选择器
                    customDatePicker.style.display = 'block';
                    // 隐藏主题颜色选择器
                    document.getElementById('theme-color-picker').style.display = 'none';
                } else {
                    // 根据选择的日期范围更新数据
                    updateDateRange(selectedValue);
                }
            });

            // 关闭自定义日期选择器
            document.getElementById('custom-date-close').addEventListener('click', function() {
                customDatePicker.style.display = 'none';
                // 如果没有应用自定义日期，恢复之前的选择
                if (dateRangeSelect.value === 'custom' && !customDateApplied) {
                    dateRangeSelect.value = 'week';
                }
            });

            // 应用自定义日期范围
            let customDateApplied = false;
            document.getElementById('apply-date-range').addEventListener('click', function() {
                const startDate = startDateInput.value;
                const endDate = endDateInput.value;

                if (startDate && endDate) {
                    // 应用自定义日期范围
                    customDateApplied = true;
                    updateDateRange('custom', startDate, endDate);
                    customDatePicker.style.display = 'none';
                }
            });

            // 更新日期范围函数
            function updateDateRange(rangeType, customStart, customEnd) {
                showLoading();

                // 计算日期范围
                const today = new Date();
                let startDate, endDate;

                switch (rangeType) {
                    case 'today':
                        startDate = today;
                        endDate = today;
                        break;
                    case 'yesterday':
                        startDate = new Date(today);
                        startDate.setDate(today.getDate() - 1);
                        endDate = startDate;
                        break;
                    case 'week':
                        startDate = new Date(today);
                        startDate.setDate(today.getDate() - 7);
                        endDate = today;
                        break;
                    case 'month':
                        startDate = new Date(today);
                        startDate.setMonth(today.getMonth() - 1);
                        endDate = today;
                        break;
                    case 'custom':
                        startDate = new Date(customStart);
                        endDate = new Date(customEnd);
                        break;
                }

                // 格式化日期为 YYYY-MM-DD
                const formatDate = (date) => {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                };

                console.log(`更新日期范围: ${formatDate(startDate)} 至 ${formatDate(endDate)}`);

                // 这里应该发送请求获取新的数据
                // 模拟数据加载
                setTimeout(function() {
                    // 重新初始化图表
                    initCharts();
                    hideLoading();
                }, 500);
            }

            // 主题颜色选择器
            document.getElementById('theme-color-btn').addEventListener('click', function() {
                document.getElementById('theme-color-picker').style.display = 'block';
                // 隐藏日期选择器
                document.getElementById('custom-date-picker').style.display = 'none';
            });

            document.getElementById('theme-color-close').addEventListener('click', function() {
                document.getElementById('theme-color-picker').style.display = 'none';
            });

            // 主题颜色选项点击事件
            const colorOptions = document.querySelectorAll('.theme-color-option');
            colorOptions.forEach(function(option) {
                option.addEventListener('click', function() {
                    const color = this.getAttribute('data-color');
                    document.documentElement.style.setProperty('--primary-color', color);

                    // 更新活跃状态
                    colorOptions.forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 通知中心功能
            const notificationItems = document.querySelectorAll('.notification-item');
            const markAllReadButton = document.querySelector('.notification-header button');
            const notificationBadge = document.querySelector('#notificationsDropdown .badge');

            // 标记单个通知为已读
            notificationItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    if (this.classList.contains('unread')) {
                        this.classList.remove('unread');

                        // 更新未读通知数量
                        const unreadCount = document.querySelectorAll('.notification-item.unread').length;
                        if (unreadCount > 0) {
                            notificationBadge.textContent = unreadCount;
                        } else {
                            notificationBadge.style.display = 'none';
                        }
                    }
                });
            });

            // 标记所有通知为已读
            if (markAllReadButton) {
                markAllReadButton.addEventListener('click', function() {
                    notificationItems.forEach(item => {
                        item.classList.remove('unread');
                    });

                    // 隐藏未读通知徽章
                    notificationBadge.style.display = 'none';
                });
            }

            // 图表设置功能
            const chartSettingsButton = document.querySelector('#settings-data button.btn-primary');
            if (chartSettingsButton) {
                chartSettingsButton.addEventListener('click', function() {
                    // 获取自动刷新间隔
                    const refreshInterval = parseInt(document.getElementById('chart-refresh').value);

                    // 获取显示的图表
                    const visibleCharts = [];
                    document.querySelectorAll('#settings-data input[type="checkbox"]:checked').forEach(checkbox => {
                        visibleCharts.push(checkbox.value);
                    });

                    // 显示加载动画
                    showLoading();

                    // 应用设置
                    setTimeout(function() {
                        // 设置图表可见性
                        const allCharts = ['matches', 'income', 'users', 'retention'];
                        allCharts.forEach(chartType => {
                            const chartElement = document.getElementById(chartType + 'Chart');
                            if (chartElement) {
                                const chartContainer = chartElement.closest('.chart-container');
                                if (chartContainer) {
                                    if (visibleCharts.includes(chartType)) {
                                        chartContainer.style.display = 'block';
                                    } else {
                                        chartContainer.style.display = 'none';
                                    }
                                }
                            }
                        });

                        // 设置自动刷新
                        if (window.chartRefreshInterval) {
                            clearInterval(window.chartRefreshInterval);
                        }

                        if (refreshInterval > 0) {
                            window.chartRefreshInterval = setInterval(function() {
                                console.log('自动刷新图表数据...');
                                initCharts();
                            }, refreshInterval * 1000);
                        }

                        hideLoading();
                        showToast('success', '图表设置已应用');
                    }, 1000);
                });
            }
        });
    </script>

    <!-- 自定义日期范围弹出层 -->
    <div class="custom-date-picker" id="custom-date-picker">
        <div class="custom-date-header">
            <h5>选择日期范围</h5>
            <button class="custom-date-close" id="custom-date-close">×</button>
        </div>
        <div class="custom-date-body">
            <div class="mb-3">
                <label for="start-date" class="form-label">开始日期</label>
                <input type="date" class="form-control" id="start-date">
            </div>
            <div class="mb-3">
                <label for="end-date" class="form-label">结束日期</label>
                <input type="date" class="form-control" id="end-date">
            </div>
            <button class="btn btn-primary w-100" id="apply-date-range">应用</button>
        </div>
    </div>

    <!-- 主题颜色选择器 -->
    <div class="theme-color-picker" id="theme-color-picker">
        <div class="theme-color-header">
            <h5>选择主题颜色</h5>
            <button class="theme-color-close" id="theme-color-close">×</button>
        </div>
        <div class="theme-color-body">
            <div class="theme-color-option active" style="background-color: #ff6b95;" data-color="#ff6b95"></div>
            <div class="theme-color-option" style="background-color: #3498db;" data-color="#3498db"></div>
            <div class="theme-color-option" style="background-color: #2ecc71;" data-color="#2ecc71"></div>
            <div class="theme-color-option" style="background-color: #9b59b6;" data-color="#9b59b6"></div>
            <div class="theme-color-option" style="background-color: #e74c3c;" data-color="#e74c3c"></div>
            <div class="theme-color-option" style="background-color: #f39c12;" data-color="#f39c12"></div>
            <div class="theme-color-option" style="background-color: #1abc9c;" data-color="#1abc9c"></div>
            <div class="theme-color-option" style="background-color: #34495e;" data-color="#34495e"></div>
        </div>
    </div>

    <!-- 主题颜色修改图标拖动功能 -->
    <script src="/assets/js/theme-color-drag.js"></script>

    <!-- 主题颜色修改样式 -->
    <link rel="stylesheet" href="/assets/css/theme-switcher.css">

    <!-- 主题颜色修改按钮点击事件 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取主题颜色按钮
            const themeColorBtn = document.getElementById('theme-color-btn');

            // 如果按钮存在，添加点击事件
            if (themeColorBtn) {
                console.log('找到主题颜色按钮');

                // 确保按钮可点击
                themeColorBtn.style.pointerEvents = 'auto';
                themeColorBtn.style.cursor = 'pointer';

                // 移除旧的事件监听器
                const newThemeColorBtn = themeColorBtn.cloneNode(true);
                themeColorBtn.parentNode.replaceChild(newThemeColorBtn, themeColorBtn);

                // 添加点击事件
                newThemeColorBtn.onclick = function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('主题颜色按钮被点击');

                    // 显示主题设置面板
                    const themeSettings = document.querySelector('.theme-settings');
                    if (themeSettings) {
                        themeSettings.classList.toggle('show');
                    } else {
                        console.error('未找到主题设置面板');
                    }

                    return false;
                };
            } else {
                console.error('未找到主题颜色按钮');
            }
        });
    </script>

    <style>
        /* 主题设置面板样式 */
        .theme-settings {
            position: fixed;
            right: -300px;
            top: 70px;
            width: 300px;
            height: auto;
            background-color: #fff;
            border-radius: 10px 0 0 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
            padding: 20px;
            z-index: 1000;
            transition: right 0.3s ease;
        }

        .theme-settings.show {
            right: 0;
        }

        .theme-settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .theme-settings-header h3 {
            margin: 0;
            font-size: 18px;
            color: var(--primary-color);
        }

        .theme-settings-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #666;
        }

        .theme-option-title {
            font-weight: 600;
            margin-bottom: 10px;
        }

        .color-picker {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .color-option {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.2s;
        }

        .color-option:hover {
            transform: scale(1.1);
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
        }

        .color-option.active {
            border: 3px solid #fff;
            box-shadow: 0 0 0 2px var(--primary-color);
        }

        @keyframes slideIn {
            from { right: -300px; }
            to { right: 0; }
        }

        @keyframes slideOut {
            from { right: 0; }
            to { right: -300px; }
        }

        /* 旋转动画 */
        @keyframes rotating {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .rotating {
            animation: rotating 1s linear infinite;
            display: inline-block;
        }
    </style>

    <!-- 侧边栏收起展开功能 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.miniprogram-container');
            const sidebarToggle = document.getElementById('sidebar-toggle');

            // 检查本地存储中的侧边栏状态
            const sidebarCollapsed = localStorage.getItem('miniprogram_sidebar_collapsed') === 'true';

            // 应用初始状态
            if (sidebarCollapsed) {
                container.classList.add('sidebar-collapsed');
            }

            // 侧边栏切换事件
            if (sidebarToggle) {
                console.log('找到侧边栏切换按钮');

                // 确保按钮可点击
                sidebarToggle.style.pointerEvents = 'auto';
                sidebarToggle.style.cursor = 'pointer';

                // 移除旧的事件监听器
                const newSidebarToggle = sidebarToggle.cloneNode(true);
                sidebarToggle.parentNode.replaceChild(newSidebarToggle, sidebarToggle);

                // 添加点击事件
                newSidebarToggle.onclick = function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('侧边栏切换按钮被点击');

                    // 切换侧边栏状态
                    container.classList.toggle('sidebar-collapsed');

                    // 保存状态到本地存储
                    localStorage.setItem(
                        'miniprogram_sidebar_collapsed',
                        container.classList.contains('sidebar-collapsed')
                    );

                    // 触发窗口调整事件，以便图表重新调整大小
                    setTimeout(function() {
                        window.dispatchEvent(new Event('resize'));
                    }, 300);

                    return false;
                };

                // 添加鼠标悬停效果
                newSidebarToggle.addEventListener('mouseover', function() {
                    this.style.backgroundColor = 'rgba(255, 255, 255, 0.3)';
                });

                newSidebarToggle.addEventListener('mouseout', function() {
                    this.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
                });
            } else {
                console.error('未找到侧边栏切换按钮');
            }

            // 监听窗口大小变化，自动调整布局
            window.addEventListener('resize', function() {
                // 获取所有图表容器
                const chartContainers = document.querySelectorAll('.chart-container');
                chartContainers.forEach(function(container) {
                    // 查找容器中的canvas元素
                    const canvas = container.querySelector('canvas');
                    if (canvas && canvas.chart) {
                        // 如果canvas有关联的Chart.js图表，调整其大小
                        canvas.chart.resize();
                    }
                });

                // 调整ECharts图表大小
                const echartsInstances = document.querySelectorAll('[_echarts_instance_]');
                echartsInstances.forEach(function(instance) {
                    const echartsInstance = echarts.getInstanceByDom(instance);
                    if (echartsInstance) {
                        echartsInstance.resize();
                    }
                });
            });
        });
    </script>

    <!-- 完全禁用天气和人生哲理组件 -->
    <style>
        /* 主题设置面板样式 */
        .theme-settings {
            position: fixed;
            right: -300px;
            top: 70px;
            width: 300px;
            height: auto;
            background-color: #fff;
            border-radius: 10px 0 0 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
            padding: 20px;
            z-index: 1000;
            transition: right 0.3s ease;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            pointer-events: auto !important;
        }

        .theme-settings.show {
            right: 0;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            pointer-events: auto !important;
        }

        .theme-settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .theme-settings-header h3 {
            margin: 0;
            font-size: 18px;
            color: var(--primary-color);
        }

        .theme-settings-close {
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #666;
        }

        .theme-option-title {
            font-weight: 600;
            margin-bottom: 10px;
        }

        .color-picker {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .color-option {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.2s;
        }

        .color-option:hover {
            transform: scale(1.1);
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
        }

        .color-option.active {
            border: 3px solid #fff;
            box-shadow: 0 0 0 2px var(--primary-color);
        }

        /* 完全隐藏天气和人生哲理组件及其所有元素 */
        #header-info-content,
        .header-info-content,
        .wisdom-quote,
        .weather-info,
        .info-actions,
        #wisdom-text,
        #weather-text,
        #refresh-info,
        #toggle-info-type,
        #settings-info,
        span[id="wisdom-text"],
        span[id="weather-text"],
        div[class*="wisdom"],
        div[class*="weather"],
        [id*="wisdom"],
        [id*="weather"],
        [class*="wisdom"],
        [class*="weather"],
        [data-type*="wisdom"],
        [data-type*="weather"],
        #weather-settings-modal,
        .modal[id*="weather"],
        .modal[id*="wisdom"] {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            width: 0 !important;
            height: 0 !important;
            position: absolute !important;
            overflow: hidden !important;
            clip: rect(0, 0, 0, 0) !important;
            margin: 0 !important;
            padding: 0 !important;
            border: 0 !important;
            pointer-events: none !important;
            z-index: -9999 !important;
            max-height: 0 !important;
            max-width: 0 !important;
            transform: scale(0) !important;
        }
    </style>

    <script>
        // 立即执行函数，确保在页面加载前就开始阻止天气和人生哲理组件
        (function() {
            // 阻止加载 wisdom-weather.js 脚本
            const originalCreateElement = document.createElement;
            document.createElement = function(tagName) {
                const element = originalCreateElement.call(document, tagName);

                if (tagName.toLowerCase() === 'script') {
                    const originalSetAttribute = element.setAttribute;
                    element.setAttribute = function(name, value) {
                        if (name === 'src' && value && (
                            value.includes('wisdom') ||
                            value.includes('weather') ||
                            value.includes('quote') ||
                            value.includes('forecast')
                        )) {
                            console.log('阻止加载天气和人生哲理相关脚本: ' + value);
                            // 完全阻止脚本加载
                            element.setAttribute = function() { return; };
                            return;
                        }
                        return originalSetAttribute.call(this, name, value);
                    };
                }

                // 阻止创建相关元素
                if (element.id && (
                    element.id.includes('wisdom') ||
                    element.id.includes('weather') ||
                    element.id === 'refresh-info' ||
                    element.id === 'toggle-info-type' ||
                    element.id === 'settings-info' ||
                    element.id === 'header-info-content'
                )) {
                    // 使元素不可见
                    element.style.display = 'none';
                    element.style.visibility = 'hidden';
                    element.style.opacity = '0';
                    element.style.width = '0';
                    element.style.height = '0';
                    element.style.position = 'absolute';
                    element.style.overflow = 'hidden';
                    element.style.clip = 'rect(0, 0, 0, 0)';
                    element.style.margin = '0';
                    element.style.padding = '0';
                    element.style.border = '0';
                    element.style.pointerEvents = 'none';
                    element.style.zIndex = '-9999';
                }

                return element;
            };

            // 阻止wisdom-weather.js相关的localStorage操作
            const originalSetItem = localStorage.setItem;
            localStorage.setItem = function(key, value) {
                if (key === 'infoType' ||
                    key.includes('wisdom') ||
                    key.includes('weather') ||
                    key.includes('quote') ||
                    key.includes('forecast') ||
                    key === 'weatherSettings') {
                    return;
                }
                return originalSetItem.call(this, key, value);
            };

            // 清除已存在的相关localStorage项
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (
                    key.includes('wisdom') ||
                    key.includes('weather') ||
                    key === 'infoType' ||
                    key.includes('quote') ||
                    key.includes('forecast') ||
                    key === 'weatherSettings'
                )) {
                    localStorage.removeItem(key);
                }
            }

            // 阻止相关事件监听器
            const originalAddEventListener = EventTarget.prototype.addEventListener;
            EventTarget.prototype.addEventListener = function(type, listener, options) {
                if (this.id && (
                    this.id.includes('wisdom') ||
                    this.id.includes('weather') ||
                    this.id === 'refresh-info' ||
                    this.id === 'toggle-info-type' ||
                    this.id === 'settings-info' ||
                    this.id === 'header-info-content'
                )) {
                    return;
                }

                // 阻止可能的事件委托
                if (type === 'DOMContentLoaded' || type === 'load') {
                    const originalListener = listener;
                    listener = function(event) {
                        // 先执行原始监听器
                        const result = originalListener.apply(this, arguments);

                        // 然后移除所有相关元素
                        setTimeout(function() {
                            removeAllWisdomWeatherElements();
                        }, 0);

                        return result;
                    };
                }

                return originalAddEventListener.call(this, type, listener, options);
            };

            // 阻止定时器可能创建的元素
            const originalSetInterval = window.setInterval;
            window.setInterval = function(callback, delay) {
                const originalCallback = callback;
                if (typeof callback === 'function') {
                    callback = function() {
                        const result = originalCallback.apply(this, arguments);
                        setTimeout(removeAllWisdomWeatherElements, 0);
                        return result;
                    };
                }
                return originalSetInterval.call(window, callback, delay);
            };

            const originalSetTimeout = window.setTimeout;
            window.setTimeout = function(callback, delay) {
                const originalCallback = callback;
                if (typeof callback === 'function') {
                    callback = function() {
                        const result = originalCallback.apply(this, arguments);
                        setTimeout(removeAllWisdomWeatherElements, 0);
                        return result;
                    };
                }
                return originalSetTimeout.call(window, callback, delay);
            };

            // 定义一个全局函数来移除所有相关元素
            window.removeAllWisdomWeatherElements = function() {
                // 移除已存在的天气和人生哲理组件
                const elementsToRemove = [
                    document.getElementById('header-info-content'),
                    document.querySelector('.header-info-content'),
                    document.querySelector('.wisdom-quote'),
                    document.querySelector('.weather-info'),
                    document.querySelector('.info-actions'),
                    document.getElementById('wisdom-text'),
                    document.getElementById('weather-text'),
                    document.getElementById('refresh-info'),
                    document.getElementById('toggle-info-type'),
                    document.getElementById('settings-info'),
                    document.getElementById('weather-settings-modal')
                ];

                elementsToRemove.forEach(function(element) {
                    if (element) {
                        element.remove();
                    }
                });

                // 查找并移除所有带有wisdom或weather相关ID的元素
                document.querySelectorAll('[id*="wisdom"], [id*="weather"], [id="refresh-info"], [id="toggle-info-type"], [id="settings-info"], [id="header-info-content"], [id*="weather-settings"]').forEach(function(element) {
                    element.remove();
                });

                // 查找并移除所有带有wisdom或weather相关class的元素
                document.querySelectorAll('[class*="wisdom"], [class*="weather"], .header-info-content, .info-actions, .modal[id*="weather"], .modal[id*="wisdom"]').forEach(function(element) {
                    element.remove();
                });

                // 移除所有带有特定动画样式的元素
                document.querySelectorAll('[style*="animation"]').forEach(function(element) {
                    if (element.className && (
                        element.className.includes('wisdom') ||
                        element.className.includes('weather') ||
                        element.className.includes('info')
                    )) {
                        element.remove();
                    }
                });

                // 移除所有可能的脚本标签
                document.querySelectorAll('script').forEach(function(element) {
                    const src = element.getAttribute('src');
                    if (src && (
                        src.includes('wisdom') ||
                        src.includes('weather') ||
                        src.includes('quote') ||
                        src.includes('forecast')
                    )) {
                        element.remove();
                    }

                    // 检查内联脚本
                    const content = element.textContent;
                    if (content && (
                        content.includes('wisdom') ||
                        content.includes('weather') ||
                        content.includes('quote') ||
                        content.includes('forecast') ||
                        content.includes('header-info-content') ||
                        content.includes('weatherSettings')
                    )) {
                        element.textContent = '';
                    }
                });
            };
        })();

        // 在DOM加载后执行更彻底的清理
        document.addEventListener('DOMContentLoaded', function() {
            // 立即执行一次
            window.removeAllWisdomWeatherElements();

            // 创建一个MutationObserver来监视DOM变化
            const observer = new MutationObserver(function(mutations) {
                window.removeAllWisdomWeatherElements();
            });

            // 开始观察文档变化
            observer.observe(document.body, { childList: true, subtree: true, attributes: true, characterData: true });

            // 每50毫秒检查一次，确保组件被移除
            setInterval(window.removeAllWisdomWeatherElements, 50);

            // 阻止wisdom-weather.js脚本的加载
            document.querySelectorAll('script').forEach(function(script) {
                const src = script.getAttribute('src');
                if (src && (
                    src.includes('wisdom') ||
                    src.includes('weather') ||
                    src.includes('quote') ||
                    src.includes('forecast')
                )) {
                    script.remove();
                }
            });

            // 阻止动态加载的脚本
            const originalAppendChild = Node.prototype.appendChild;
            Node.prototype.appendChild = function(node) {
                if (node.nodeName === 'SCRIPT') {
                    const src = node.getAttribute('src');
                    if (src && (
                        src.includes('wisdom') ||
                        src.includes('weather') ||
                        src.includes('quote') ||
                        src.includes('forecast')
                    )) {
                        console.log('阻止动态加载脚本: ' + src);
                        return document.createComment('Blocked script: ' + src);
                    }
                }

                // 阻止添加天气和人生哲理相关元素
                if (node.id && (
                    node.id.includes('wisdom') ||
                    node.id.includes('weather') ||
                    node.id === 'refresh-info' ||
                    node.id === 'toggle-info-type' ||
                    node.id === 'settings-info' ||
                    node.id === 'header-info-content' ||
                    node.id === 'weather-settings-modal'
                )) {
                    return document.createComment('Blocked element: ' + node.id);
                }

                if (node.className && (
                    node.className.includes('wisdom') ||
                    node.className.includes('weather') ||
                    node.className.includes('header-info-content') ||
                    node.className.includes('info-actions')
                )) {
                    return document.createComment('Blocked element by class: ' + node.className);
                }

                return originalAppendChild.call(this, node);
            };

            // 阻止 wisdom-weather.js 脚本的执行
            window.createHeaderInfoContent = function() { return; };
            window.initContent = function() { return; };
            window.updateContent = function() { return; };
            window.updateWisdom = function() { return; };
            window.updateWeather = function() { return; };
            window.toggleInfoType = function() { return; };
            window.updateDisplayState = function() { return; };
            window.getRandomWisdom = function() { return; };
            window.getSimulatedWeather = function() { return; };
        });
    </script>
</body>
</html>