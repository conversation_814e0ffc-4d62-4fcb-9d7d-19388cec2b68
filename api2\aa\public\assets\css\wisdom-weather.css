/**
 * 天气和人生哲理组件样式
 * 统一所有页面的天气和人生哲理组件样式
 */

/* 头部信息内容容器 */
.header-info-content {
    display: flex;
    align-items: center;
    margin: 0 auto;
    flex: 1;
    padding: 0 20px;
    overflow: hidden;
    color: var(--text-color, #333);
    font-size: 14px;
    max-width: 600px;
    justify-content: center;
}

/* 智慧语录和天气信息 */
.wisdom-quote, .weather-info {
    display: flex;
    align-items: center;
    margin-right: 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    animation: fadeIn 0.5s;
    max-width: 250px;
}

/* 图标样式 */
.wisdom-quote i, .weather-info i {
    margin-right: 8px;
    font-size: 16px;
    color: var(--primary-color);
    flex-shrink: 0;
}

/* 操作按钮容器 */
.info-actions {
    display: flex;
    gap: 10px;
    margin-left: 10px;
}

/* 按钮样式 */
.info-actions button {
    background: none;
    border: none;
    color: var(--text-color, #333);
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
}

/* 按钮悬停效果 */
.info-actions button:hover {
    background-color: rgba(var(--primary-color-rgb, 255, 107, 149), 0.1);
    color: var(--primary-color);
}

/* 按钮图标 */
.info-actions button i {
    font-size: 14px;
}

/* 淡入动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 旋转动画 */
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 旋转类 */
.rotating {
    animation: rotate 1s linear;
}

/* 天气设置下拉菜单样式 */
.weather-settings-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 300px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    z-index: 1050;
    display: none;
    animation: fadeInDown 0.3s;
    overflow: hidden;
}

.weather-settings-dropdown.show {
    display: block;
}

.dropdown-content {
    width: 100%;
}

.dropdown-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    background-color: rgba(var(--primary-color-rgb, 255, 107, 149), 0.1);
}

.dropdown-header h3 {
    margin: 0;
    font-size: 1rem;
    color: var(--primary-color);
}

.dropdown-body {
    padding: 15px;
}

.city-search-container {
    margin-bottom: 15px;
    position: relative;
}

.city-search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s;
}

.city-search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb, 255, 107, 149), 0.2);
    outline: none;
}

.city-search-results {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    background-color: #fff;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 4px 4px;
    z-index: 1;
    display: none;
}

.city-search-results.show {
    display: block;
}

.search-result-item {
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s;
}

.search-result-item:hover {
    background-color: rgba(var(--primary-color-rgb, 255, 107, 149), 0.1);
}

.selected-city-container {
    margin-bottom: 15px;
    padding: 10px;
    background-color: rgba(var(--primary-color-rgb, 255, 107, 149), 0.05);
    border-radius: 4px;
    text-align: center;
}

.selected-city-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.selected-city {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
}

.common-cities {
    margin-top: 15px;
}

.common-cities-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 10px;
}

.common-cities-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
}

.city-item {
    padding: 6px 0;
    text-align: center;
    background-color: rgba(var(--primary-color-rgb, 255, 107, 149), 0.05);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 13px;
}

.city-item:hover {
    background-color: rgba(var(--primary-color-rgb, 255, 107, 149), 0.2);
    color: var(--primary-color);
}

/* 下拉动画 */
@keyframes fadeInDown {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 响应式样式 */
@media (max-width: 768px) {
    .wisdom-quote {
        display: none !important;
    }

    .weather-info {
        margin-right: 0;
        max-width: 150px;
    }

    .header-info-content {
        padding: 0 10px;
    }
}
