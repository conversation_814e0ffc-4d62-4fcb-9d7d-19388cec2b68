<!-- 主题初始化脚本，必须放在最前面 -->
<script src="/assets/js/theme-init.js"></script>

<link href="/assets/vendor/bootstrap/bootstrap.min.css" rel="stylesheet">
<link href="/assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
<link href="/assets/vendor/animate/animate.min.css" rel="stylesheet">
<link rel="stylesheet" href="/assets/css/theme.css">
<link rel="stylesheet" href="/assets/css/animations.css">
<link rel="stylesheet" href="/assets/css/enhanced-animations.css">
<link rel="stylesheet" href="/assets/css/common.css">
<link rel="stylesheet" href="/assets/css/theme-switcher.css">
<link rel="stylesheet" href="/assets/css/ui-enhancements.css">
<link rel="stylesheet" href="/assets/css/responsive-layout.css">
<link rel="stylesheet" href="/assets/css/sidebar-fix.css">
<link rel="stylesheet" href="/assets/css/stat-item-fix.css">
<link rel="stylesheet" href="/assets/css/toast-fix.css">
<link rel="stylesheet" href="/assets/css/toast-enhanced.css">
<link rel="stylesheet" href="/assets/css/theme-position-fix.css">
<link rel="stylesheet" href="/assets/css/modal-style-fix.css">
<link rel="stylesheet" href="/assets/css/header-dropdown-fix.css">
