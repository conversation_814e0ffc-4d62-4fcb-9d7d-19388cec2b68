// 头部信息功能
function initHeaderInfo() {
    const refreshBtn = document.getElementById('refresh-info');
    const toggleBtn = document.getElementById('toggle-info-type');
    const settingsBtn = document.getElementById('settings-info');
    const wisdomText = document.getElementById('wisdom-text');
    const weatherText = document.getElementById('weather-text');

    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            updateWisdom();
            updateWeather();
        });
    }

    if (toggleBtn) {
        toggleBtn.addEventListener('click', function() {
            const currentType = localStorage.getItem('headerInfoType') || 'both';
            let newType;
            switch (currentType) {
                case 'both':
                    newType = 'wisdom';
                    break;
                case 'wisdom':
                    newType = 'weather';
                    break;
                case 'weather':
                    newType = 'both';
                    break;
                default:
                    newType = 'both';
            }
            localStorage.setItem('headerInfoType', newType);
            updateDisplayState(newType);
        });
    }

    if (settingsBtn) {
        settingsBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const modal = document.getElementById('weatherSettingsModal');
            if (modal) {
                // 使用Bootstrap模态框API或直接显示
                if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                    const bsModal = new bootstrap.Modal(modal);
                    bsModal.show();
                } else {
                    // 手动显示模态框
                    modal.style.display = 'block';
                    modal.classList.add('show');
                    document.body.classList.add('modal-open');

                    // 创建背景遮罩
                    const backdrop = document.createElement('div');
                    backdrop.className = 'modal-backdrop fade show';
                    backdrop.id = 'weather-modal-backdrop';
                    document.body.appendChild(backdrop);

                    // 点击背景关闭模态框
                    backdrop.addEventListener('click', function() {
                        closeWeatherModal();
                    });

                    // ESC键关闭模态框
                    document.addEventListener('keydown', function(e) {
                        if (e.key === 'Escape') {
                            closeWeatherModal();
                        }
                    });
                }
            }
        });
    }

    // 初始化显示状态
    const savedType = localStorage.getItem('headerInfoType') || 'both';
    updateDisplayState(savedType);

    // 初始加载
    updateWisdom();
    updateWeather();
}

function updateDisplayState(type) {
    const wisdomQuote = document.querySelector('.wisdom-quote');
    const weatherInfo = document.querySelector('.weather-info');

    if (!wisdomQuote || !weatherInfo) return;

    switch (type) {
        case 'wisdom':
            wisdomQuote.style.display = 'flex';
            weatherInfo.style.display = 'none';
            break;
        case 'weather':
            wisdomQuote.style.display = 'none';
            weatherInfo.style.display = 'flex';
            break;
        case 'both':
        default:
            wisdomQuote.style.display = 'flex';
            weatherInfo.style.display = 'flex';
            break;
    }
}

function updateWisdom() {
    const wisdomText = document.getElementById('wisdom-text');
    if (!wisdomText) return;

    const wisdoms = [
        "成功不是终点，失败不是末日，继续前进的勇气才最可贵。",
        "每一个不曾起舞的日子，都是对生命的辜负。",
        "你的时间有限，不要浪费在重复别人的生活上。",
        "今天的努力，是为了明天的选择权。",
        "不要等待机会，而要创造机会。"
    ];

    const randomWisdom = wisdoms[Math.floor(Math.random() * wisdoms.length)];
    wisdomText.textContent = randomWisdom;
}

function updateWeather() {
    const weatherText = document.getElementById('weather-text');
    if (!weatherText) return;

    const settings = {
        city: '北京'
    };

    // 强制请求真实天气数据（不使用缓存）
    const timestamp = new Date().getTime();
    fetch(`../api/weather.php?city=${encodeURIComponent(settings.city)}&t=${timestamp}`)
        .then(response => response.json())
        .then(data => {
            let weatherDisplay = '';

            if (data.success) {
                // 使用真实天气数据
                const weather = data.data;
                weatherDisplay = `${data.city} ${weather.condition} ${weather.temperature}°C`;
                console.log('天气刷新成功:', data);
            } else {
                // API失败时显示错误
                weatherDisplay = `${data.city} 获取失败`;
                console.warn('天气API错误:', data.error);
            }

            // 设置文本
            weatherText.textContent = weatherDisplay;

            // 添加淡入动画
            weatherText.style.animation = 'none';
            setTimeout(() => {
                weatherText.style.animation = 'fadeIn 0.5s';
            }, 10);
        })
        .catch(error => {
            console.error('天气请求失败:', error);
            weatherText.textContent = `${settings.city} 网络错误`;
        });
}

function closeWeatherModal() {
    const modal = document.getElementById('weatherSettingsModal');
    const backdrop = document.getElementById('weather-modal-backdrop');

    if (modal) {
        modal.style.display = 'none';
        modal.classList.remove('show');
        document.body.classList.remove('modal-open');
    }

    if (backdrop) {
        backdrop.remove();
    }
}

function saveWeatherSettings() {
    // 天气设置保存功能
    const form = document.getElementById('weatherSettingsForm');
    if (!form) {
        console.error('天气设置表单不存在');
        return;
    }

    const formData = new FormData(form);
    const settings = Object.fromEntries(formData);

    console.log('保存天气设置:', settings);

    // 发送到后端保存
    fetch('/api/weather/settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('天气设置保存成功');
            // 显示成功提示
            if (typeof showToast === 'function') {
                showToast('天气设置保存成功', 'success');
            }
        } else {
            console.error('天气设置保存失败:', data.message);
            if (typeof showToast === 'function') {
                showToast('天气设置保存失败: ' + (data.message || '未知错误'), 'error');
            }
        }
    })
    .catch(error => {
        console.error('天气设置保存出错:', error);
        if (typeof showToast === 'function') {
            showToast('天气设置保存出错', 'error');
        }
    });

    // 关闭模态框
    closeWeatherModal();

    // 刷新天气
    setTimeout(() => {
        updateWeather();
    }, 1000);
}

function updateApiProviderInfo() {
    // 更新API提供商信息
    const provider = document.getElementById('weatherApiProvider').value;
    const infoDiv = document.getElementById('apiProviderInfo');
    
    if (provider === 'openweather') {
        infoDiv.innerHTML = `
            <small class="text-muted">
                <i class="bi bi-info-circle"></i>
                OpenWeatherMap 提供准确的全球天气数据，需要免费注册获取API密钥。
                <a href="https://openweathermap.org/api" target="_blank" class="text-primary">获取API密钥</a>
            </small>
        `;
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', function() {
    initHeaderInfo();
});
