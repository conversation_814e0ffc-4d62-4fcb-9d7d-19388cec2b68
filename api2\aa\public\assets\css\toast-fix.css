/**
 * Toast提示修复样式
 * 修复Toast提示透明的问题
 */

/* 消息提示样式 */
.toast {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    width: 300px !important;
    background-color: var(--white-color) !important;
    border-radius: var(--border-radius) !important;
    box-shadow: 0 5px 15px var(--shadow-color) !important;
    overflow: hidden !important;
    z-index: 9999 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateX(30px) !important;
    transition: all 0.3s !important;
}

.toast.show {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateX(0) !important;
}

.toast-content {
    display: flex !important;
    align-items: center !important;
    padding: 15px !important;
    background-color: var(--white-color) !important;
}

.toast-icon {
    font-size: 1.5rem !important;
    margin-right: 15px !important;
    display: none !important;
}

.toast-icon.success {
    color: var(--success-color) !important;
    display: inline-block !important;
}

.toast-icon.error {
    color: var(--danger-color) !important;
    display: inline-block !important;
}

.toast-icon.info {
    color: var(--info-color) !important;
    display: inline-block !important;
}

.toast-message {
    flex: 1 !important;
    font-weight: 500 !important;
    color: var(--dark-color) !important;
}

.toast-progress {
    height: 3px !important;
    background-color: var(--primary-color) !important;
    width: 100% !important;
    animation: progress 3s linear !important;
}

@keyframes progress {
    from { width: 100%; }
    to { width: 0%; }
}
