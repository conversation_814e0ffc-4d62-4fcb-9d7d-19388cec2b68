/**
 * 控制面板页面完全修复样式
 * 彻底修复控制面板页面的样式问题
 */

/* 统计项基础样式 */
.stat-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding: 12px 15px;
    margin-bottom: 10px;
    background-color: rgba(var(--primary-color-rgb), 0.03);
    border-radius: 6px;
    transition: all 0.3s ease;
    overflow: hidden;
    text-align: center;
    min-width: 0;
    height: 120px; /* 固定高度 */
}

/* 统计项标签样式 */
.stat-label {
    font-weight: 500;
    color: var(--text-muted);
    margin-bottom: 8px;
    line-height: 1.4;
    font-size: 0.9em;
    border-bottom: 1px dashed rgba(var(--primary-color-rgb), 0.2);
    padding-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    text-align: center;
    display: block;
}

/* 统计项值样式 */
.stat-value {
    font-weight: 600;
    color: var(--text-color);
    line-height: 1.4;
    font-size: 1.1em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    text-align: center;
    display: block;
}

/* 带按钮的统计项值样式 */
.stat-value-with-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    flex-grow: 1;
}

/* 带按钮的统计项值中的值样式 */
.stat-value-with-btn .stat-value {
    margin-bottom: 8px;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
}

/* 带按钮的统计项值中的按钮样式 */
.stat-value-with-btn .btn {
    white-space: nowrap;
    font-size: 0.9em;
    padding: 4px 12px;
    margin-top: 2px;
}

/* 多行值样式 */
.stat-value-multiline {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    flex-grow: 1;
}

/* 多行值中的值样式 */
.stat-value-multiline .stat-value {
    margin-bottom: 4px;
    width: 100%;
    white-space: normal;
    word-break: break-word;
    line-height: 1.3;
    display: block;
}

/* 多行值中的最后一个值样式 */
.stat-value-multiline .stat-value:last-child {
    margin-bottom: 0;
}

/* 时间显示样式 */
#current-time {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: var(--primary-color);
    margin-top: 0;
    display: block;
}

/* 统计网格样式 */
.stat-grid {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 15px;
    width: 100%;
}

/* 邮箱样式 */
.stat-item:has(.stat-label i.bi-envelope) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
}

.stat-item:has(.stat-label i.bi-envelope) .stat-value-multiline {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    flex-grow: 1;
}

/* 运行环境样式 */
.stat-item:has(.stat-label i.bi-hdd-stack) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
}

.stat-item:has(.stat-label i.bi-hdd-stack) .stat-value-multiline {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    flex-grow: 1;
}

/* 授权时长样式 */
.stat-item:has(.stat-label i.bi-calendar-check) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
}

.stat-item:has(.stat-label i.bi-calendar-check) .stat-value-with-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    flex-grow: 1;
}

/* 当前版本样式 */
.stat-item:has(.stat-label i.bi-code-square) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
}

.stat-item:has(.stat-label i.bi-code-square) .stat-value-with-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    flex-grow: 1;
}

/* 服务期限样式 */
.stat-item:has(.stat-label i.bi-hourglass-split) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
}

.stat-item:has(.stat-label i.bi-hourglass-split) .stat-value-with-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    flex-grow: 1;
}

/* 系统时间样式 */
.stat-item:has(.stat-label i.bi-calendar-date) {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
}

.stat-item:has(.stat-label i.bi-calendar-date) .stat-value-with-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    flex-grow: 1;
}

/* 状态样式 */
.status-active {
    color: var(--success-color);
    font-weight: bold;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .stat-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-item {
        height: auto;
        min-height: 120px;
    }
    
    .stat-label {
        font-size: 0.85em;
        margin-bottom: 6px;
    }
    
    .stat-value {
        font-size: 1em;
    }
    
    .stat-value-with-btn .btn {
        padding: 3px 10px;
        font-size: 0.8em;
        margin-top: 5px;
    }
}
