/**
 * 模态框函数修复
 * 修复历史版本、续费按钮以及查看全部的模态框不能正确弹出的问题
 */

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('模态框函数修复脚本已加载');

    // 修复历史版本按钮
    fixVersionHistoryButton();

    // 修复续费按钮
    fixRenewButton();

    // 修复查看全部按钮
    fixViewAllButton();

    // 修复更新版本按钮
    fixUpdateVersionButton();

    // 添加查看全部公告模态框
    addAllAnnouncementsModal();
});

// 修复历史版本按钮
function fixVersionHistoryButton() {
    // 重新定义showVersionHistory函数
    window.showVersionHistory = function() {
        console.log('显示历史版本');
        openModal('version-history-modal');
    };
}

// 修复续费按钮
function fixRenewButton() {
    // 重新定义openRenewModal函数
    window.openRenewModal = function() {
        console.log('打开续费模态框');
        openModal('renew-service-modal');
    };

    // 重新定义confirmRenew函数
    window.confirmRenew = function() {
        console.log('确认续费');
        const servicePlan = document.getElementById('service-plan').value;

        if (!servicePlan) {
            showToast('请选择服务套餐', 'error');
            return;
        }

        showLoading();
        // 模拟API请求
        setTimeout(() => {
            hideLoading();
            closeModal('renew-service-modal');

            // 直接调用自动刷新通知函数
            console.log('💰 直接调用showAutoRefreshToast');
            if (typeof window.showAutoRefreshToast === 'function') {
                window.showAutoRefreshToast('服务续费成功！', {
                    refreshCallback: function() {
                        console.log('续费完成，刷新页面');
                        location.reload();
                    }
                });
            } else {
                console.error('❌ showAutoRefreshToast函数不存在');
                // 备用方案
                alert('服务续费成功！');
                setTimeout(() => location.reload(), 1600);
            }
        }, 800);
    };
}

// 修复查看全部按钮
function fixViewAllButton() {
    // 重新定义showAllAnnouncements函数
    window.showAllAnnouncements = function() {
        console.log('显示所有公告');
        openModal('all-announcements-modal');
    };

    // 查找查看全部按钮并添加点击事件
    const viewAllButtons = document.querySelectorAll('.view-all-btn');
    viewAllButtons.forEach(button => {
        button.onclick = function() {
            showAllAnnouncements();
        };
    });
}

// 修复更新版本按钮
function fixUpdateVersionButton() {
    // 重新定义showUpdateVersion函数
    window.showUpdateVersion = function() {
        console.log('显示更新版本');
        openModal('update-version-modal');
    };

    // 确认更新版本
    window.confirmUpdate = function() {
        console.log('确认更新版本');
        showLoading();
        // 模拟更新过程
        setTimeout(() => {
            hideLoading();
            closeModal('update-version-modal');

            // 更新版本号显示
            const versionElement = document.querySelector('.stat-item:nth-child(2) .stat-value');
            if (versionElement) {
                versionElement.textContent = 'v1.0.3';
            }

            // 直接调用自动刷新通知函数
            console.log('🚀 直接调用showAutoRefreshToast');
            if (typeof window.showAutoRefreshToast === 'function') {
                window.showAutoRefreshToast('系统已成功更新到最新版本！', {
                    refreshCallback: function() {
                        console.log('更新版本完成，刷新页面');
                        location.reload();
                    }
                });
            } else {
                console.error('❌ showAutoRefreshToast函数不存在');
                // 备用方案
                alert('系统已成功更新到最新版本！');
                setTimeout(() => location.reload(), 1600);
            }
        }, 2000);
    };
}

// 添加查看全部公告模态框
function addAllAnnouncementsModal() {
    // 检查是否已存在模态框
    if (document.getElementById('all-announcements-modal')) {
        return;
    }

    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'all-announcements-modal';

    // 设置模态框内容
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>所有公告</h3>
                <button class="close-btn" onclick="closeModal('all-announcements-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <ul class="announcement-list">
                    <li class="announcement-item">
                        <div class="announcement-header">
                            <span class="announcement-title">系统更新通知</span>
                            <span class="announcement-date">2023-06-15</span>
                        </div>
                        <p class="announcement-content">尊敬的用户，我们将于2023年6月20日凌晨2:00-4:00进行系统升级维护，届时系统将暂停服务。给您带来的不便，敬请谅解。</p>
                    </li>
                    <li class="announcement-item">
                        <div class="announcement-header">
                            <span class="announcement-title">新功能上线通知</span>
                            <span class="announcement-date">2023-06-10</span>
                        </div>
                        <p class="announcement-content">我们很高兴地通知您，新的数据分析功能已经上线，您可以在控制面板中查看更详细的数据统计和分析报告。</p>
                    </li>
                    <li class="announcement-item">
                        <div class="announcement-header">
                            <span class="announcement-title">安全更新提醒</span>
                            <span class="announcement-date">2023-06-05</span>
                        </div>
                        <p class="announcement-content">为了保障您的账户安全，我们建议您定期修改密码，并开启两步验证功能。如有任何安全问题，请及时联系客服。</p>
                    </li>
                    <li class="announcement-item">
                        <div class="announcement-header">
                            <span class="announcement-title">服务条款更新</span>
                            <span class="announcement-date">2023-06-01</span>
                        </div>
                        <p class="announcement-content">我们已更新服务条款和隐私政策，新的条款将于2023年7月1日生效。请您仔细阅读并了解相关内容的变更。</p>
                    </li>
                    <li class="announcement-item">
                        <div class="announcement-header">
                            <span class="announcement-title">端午节放假通知</span>
                            <span class="announcement-date">2023-05-20</span>
                        </div>
                        <p class="announcement-content">尊敬的用户，我们将于2023年6月22日至6月24日放假，期间客服响应可能会有延迟。祝您端午节快乐！</p>
                    </li>
                </ul>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('all-announcements-modal')">关闭</button>
            </div>
        </div>
    `;

    // 添加到body
    document.body.appendChild(modal);
}
