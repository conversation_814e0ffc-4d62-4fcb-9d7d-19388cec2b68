/**
 * 个人资料页面重置按钮修复
 * 修改重置按钮的行为，使用自定义模态框代替系统默认确认框
 */

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('个人资料页面重置按钮修复脚本已加载');

    // 获取重置按钮
    const resetButtons = document.querySelectorAll('button[onclick^="resetForm"]');

    // 如果找到重置按钮，添加点击事件
    if (resetButtons.length > 0) {
        console.log('找到重置按钮:', resetButtons.length);

        resetButtons.forEach(button => {
            // 获取表单ID
            const onclickAttr = button.getAttribute('onclick');
            const formId = onclickAttr.match(/resetForm\(['"](.+?)['"]\)/)[1];
            
            // 移除默认的重置行为
            button.removeAttribute('onclick');

            // 添加点击事件
            button.addEventListener('click', function(e) {
                // 阻止默认行为
                e.preventDefault();
                console.log('重置按钮被点击, 表单ID:', formId);

                // 显示自定义确认模态框
                showResetConfirmModal(formId);
            });
        });
    } else {
        console.error('未找到重置按钮');
    }

    // 创建重置确认模态框
    createResetConfirmModal();
});

/**
 * 创建重置确认模态框
 */
function createResetConfirmModal() {
    // 检查模态框是否已存在
    if (document.getElementById('reset-confirm-modal')) {
        return;
    }

    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'reset-confirm-modal';

    // 设置模态框内容
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>确认重置</h3>
                <button class="close-btn" onclick="closeResetConfirmModal()"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <div class="confirm-icon">
                    <i class="bi bi-exclamation-triangle"></i>
                </div>
                <p class="confirm-message">确定要重置表单吗？此操作将清空所有已输入的内容，且不可撤销。</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeResetConfirmModal()">取消</button>
                <button class="btn btn-danger" onclick="confirmReset()">确认重置</button>
            </div>
        </div>
    `;

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        #reset-confirm-modal .modal-content {
            max-width: 450px;
            border-radius: 10px;
            overflow: hidden;
            animation: modalFadeIn 0.3s ease-out;
        }

        #reset-confirm-modal .modal-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        #reset-confirm-modal .modal-header h3 {
            margin: 0;
            font-size: 1.2rem;
            color: #343a40;
        }

        #reset-confirm-modal .close-btn {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            color: #6c757d;
            transition: color 0.2s;
        }

        #reset-confirm-modal .close-btn:hover {
            color: #343a40;
        }

        #reset-confirm-modal .modal-body {
            padding: 25px 20px;
            text-align: center;
        }

        #reset-confirm-modal .confirm-icon {
            font-size: 3rem;
            color: #ffc107;
            margin-bottom: 15px;
        }

        #reset-confirm-modal .confirm-message {
            color: #495057;
            font-size: 1rem;
            line-height: 1.5;
            margin: 0;
        }

        #reset-confirm-modal .modal-footer {
            padding: 15px 20px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        #reset-confirm-modal .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        #reset-confirm-modal .btn-danger:hover {
            background-color: #c82333;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }

        @keyframes modalFadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    `;

    // 将模态框和样式添加到文档
    document.body.appendChild(style);
    document.body.appendChild(modal);

    console.log('重置确认模态框已创建');
}

// 当前要重置的表单ID
let currentFormId = '';

/**
 * 显示重置确认模态框
 */
function showResetConfirmModal(formId) {
    currentFormId = formId;
    const modal = document.getElementById('reset-confirm-modal');
    if (modal) {
        modal.style.display = 'flex';
        console.log('显示重置确认模态框, 表单ID:', formId);
    }
}

/**
 * 关闭重置确认模态框
 */
function closeResetConfirmModal() {
    const modal = document.getElementById('reset-confirm-modal');
    if (modal) {
        modal.style.display = 'none';
        console.log('关闭重置确认模态框');
    }
}

/**
 * 确认重置
 */
function confirmReset() {
    console.log('确认重置表单:', currentFormId);

    // 关闭模态框
    closeResetConfirmModal();

    // 显示加载动画
    if (typeof showLoading === 'function') {
        showLoading();
    }

    // 重置表单
    const form = document.getElementById(currentFormId);
    if (form) {
        form.reset();
        
        // 如果是密码表单，重置密码强度指示器
        if (currentFormId === 'change-password-form') {
            const strengthBar = document.getElementById('password-strength-bar');
            const strengthText = document.getElementById('password-strength-text');
            const matchText = document.getElementById('password-match-text');

            if (strengthBar) strengthBar.style.width = '0';
            if (strengthBar) strengthBar.style.backgroundColor = '#eee';
            if (strengthText) strengthText.textContent = '密码强度: 未输入';
            if (matchText) matchText.textContent = '';
        }
    }

    // 延迟隐藏加载动画并显示提示
    setTimeout(() => {
        // 隐藏加载动画
        if (typeof hideLoading === 'function') {
            hideLoading();
        }

        // 显示成功提示
        if (typeof showToast === 'function') {
            showToast('表单已重置', 'info');
        }
    }, 500);
}
