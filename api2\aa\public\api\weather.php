<?php
// 真实天气API

// 关闭错误显示，避免HTML错误输出
error_reporting(0);
ini_set('display_errors', 0);

// 开启会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 设置响应头
header('Content-Type: application/json');

// 检查用户是否已登录
if (!isset($_SESSION['username'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => '未授权访问']);
    exit;
}

try {
    // 包含数据库配置
    $configPath = __DIR__ . '/../../config/config.php';
    $config = require_once($configPath);
    
    $dbConfig = $config['database'];
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $prefix = $dbConfig['prefix'];
    
    // 获取天气设置
    $stmt = $pdo->prepare("SELECT * FROM {$prefix}weather_settings WHERE id = 1");
    $stmt->execute();
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$settings) {
        // 如果没有设置记录，创建默认设置
        $stmt = $pdo->prepare("INSERT INTO {$prefix}weather_settings (api_provider, api_key, default_city, enabled, cache_duration) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute(['openweather', '', '北京', 1, 30]);

        // 重新获取设置
        $stmt = $pdo->prepare("SELECT * FROM {$prefix}weather_settings WHERE id = 1");
        $stmt->execute();
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);
    }

    if (!$settings['enabled']) {
        throw new Exception('天气功能未启用');
    }
    
    $city = $_GET['city'] ?? $settings['default_city'] ?? '北京';
    $provider = $settings['api_provider'] ?? 'openweather';
    $apiKey = $settings['api_key'] ?? '';

    // 调试信息
    error_log("Weather API Debug - Provider: $provider, City: $city, API Key Length: " . strlen($apiKey));

    if (empty($apiKey)) {
        throw new Exception('API密钥未设置，当前长度: ' . strlen($apiKey));
    }

    // 检查OpenWeatherMap API密钥长度
    if ($provider === 'openweather' && strlen($apiKey) < 30) {
        throw new Exception('OpenWeatherMap API密钥长度不正确，当前长度: ' . strlen($apiKey));
    }
    
    // 根据服务商获取天气
    $weatherData = getWeatherData($provider, $city, $apiKey);
    
    echo json_encode([
        'success' => true,
        'data' => $weatherData,
        'city' => $city,
        'provider' => $provider
    ]);
    
} catch (Exception $e) {
    // 确保返回JSON格式
    http_response_code(200);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'city' => $_GET['city'] ?? '北京'
    ]);
    exit;
}

/**
 * 获取天气数据
 */
function getWeatherData($provider, $city, $apiKey) {
    switch ($provider) {
        case 'openweather':
            return getOpenWeatherData($city, $apiKey);
        default:
            throw new Exception('不支持的天气服务商: ' . $provider);
    }
}

/**
 * OpenWeatherMap API
 * 文档: https://openweathermap.org/current
 */
function getOpenWeatherData($city, $apiKey) {
    // 中文城市名转换为英文
    $cityMap = [
        '北京' => 'Beijing',
        '上海' => 'Shanghai',
        '广州' => 'Guangzhou',
        '深圳' => 'Shenzhen',
        '杭州' => 'Hangzhou',
        '南京' => 'Nanjing',
        '苏州' => 'Suzhou',
        '成都' => 'Chengdu',
        '重庆' => 'Chongqing',
        '武汉' => 'Wuhan',
        '西安' => 'Xian',
        '天津' => 'Tianjin',
        '青岛' => 'Qingdao',
        '大连' => 'Dalian',
        '厦门' => 'Xiamen',
        '宁波' => 'Ningbo',
        '无锡' => 'Wuxi',
        '佛山' => 'Foshan',
        '温州' => 'Wenzhou',
        '烟台' => 'Yantai',
        '长沙' => 'Changsha',
        '石家庄' => 'Shijiazhuang',
        '济南' => 'Jinan',
        '东莞' => 'Dongguan',
        '南通' => 'Nantong',
        '郑州' => 'Zhengzhou',
        '沈阳' => 'Shenyang',
        '长春' => 'Changchun',
        '哈尔滨' => 'Harbin',
        '昆明' => 'Kunming',
        '福州' => 'Fuzhou',
        '合肥' => 'Hefei',
        '南昌' => 'Nanchang',
        '贵阳' => 'Guiyang',
        '太原' => 'Taiyuan',
        '兰州' => 'Lanzhou'
    ];

    $englishCity = $cityMap[$city] ?? $city;
    $url = "https://api.openweathermap.org/data/2.5/weather?q=" . urlencode($englishCity) . "&appid=" . $apiKey . "&units=metric&lang=zh_cn";

    $context = stream_context_create([
        'http' => [
            'timeout' => 15,
            'user_agent' => 'Weather App/1.0',
            'method' => 'GET'
        ],
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false
        ]
    ]);

    $response = @file_get_contents($url, false, $context);

    if ($response === false) {
        // 尝试cURL方法
        return getOpenWeatherDataWithCurl($city, $apiKey);
    }

    $data = json_decode($response, true);

    if (!$data || isset($data['cod']) && $data['cod'] != 200) {
        throw new Exception($data['message'] ?? 'OpenWeatherMap API错误: ' . ($data['cod'] ?? 'unknown'));
    }

    return [
        'temperature' => round($data['main']['temp']),
        'condition' => $data['weather'][0]['description'] ?? '未知',
        'humidity' => $data['main']['humidity'] ?? 0,
        'wind_speed' => $data['wind']['speed'] ?? 0
    ];
}

/**
 * OpenWeatherMap API备用方法（使用cURL）
 */
function getOpenWeatherDataWithCurl($city, $apiKey) {
    $url = "https://api.openweathermap.org/data/2.5/weather?q=" . urlencode($city) . "&appid=" . $apiKey . "&units=metric&lang=zh_cn";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Weather App/1.0');

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($response === false) {
        $curlError = curl_error($ch);
        throw new Exception('cURL请求OpenWeatherMap API失败: ' . $curlError);
    }

    if ($httpCode !== 200) {
        throw new Exception('OpenWeatherMap API返回HTTP错误: ' . $httpCode . ', 响应: ' . substr($response, 0, 200));
    }

    $data = json_decode($response, true);

    if (!$data || isset($data['cod']) && $data['cod'] != 200) {
        throw new Exception($data['message'] ?? 'OpenWeatherMap API错误: ' . ($data['cod'] ?? 'unknown'));
    }

    return [
        'temperature' => round($data['main']['temp']),
        'condition' => $data['weather'][0]['description'] ?? '未知',
        'humidity' => $data['main']['humidity'] ?? 0,
        'wind_speed' => $data['wind']['speed'] ?? 0
    ];
}





?>
