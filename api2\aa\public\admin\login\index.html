﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 情侣头像匹配系统</title>
    <style>
        body {
            font-family: "Microsoft YaHei", sans-serif;
            background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
            background-size: 400% 400%;
            animation: gradientBG 15s ease infinite;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        
        @keyframes gradientBG {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .login-container {
            max-width: 400px;
            width: 100%;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            animation: fadeInUp 0.5s;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .card-header {
            background: linear-gradient(135deg, #ff6b95 0%, #ffa5c0 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            margin-bottom: 15px;
            animation: pulse 3s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .card-body {
            padding: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }
        
        .input-group {
            position: relative;
            display: flex;
        }
        
        .input-group-text {
            background-color: #ffa5c0;
            color: white;
            border: none;
            border-radius: 5px 0 0 5px;
            padding: 10px;
            display: flex;
            align-items: center;
        }
        
        .form-control {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 0 5px 5px 0;
            font-size: 16px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #ff6b95;
            box-shadow: 0 0 0 3px rgba(255, 107, 149, 0.25);
        }
        
        .form-check {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .form-check-input {
            margin-right: 10px;
        }
        
        .btn {
            display: block;
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #ff6b95 0%, #ffa5c0 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(255, 107, 149, 0.4);
        }
        
        h3, p {
            margin: 0;
        }
        
        .mb-0 {
            margin-bottom: 0;
        }
        
        .floating-hearts {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: -1;
        }
        
        .heart {
            position: absolute;
            width: 20px;
            height: 20px;
            background: url("data:image/svg+xml;utf8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'%23ffffff\'%3E%3Cpath d=\'M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\'/%3E%3C/svg%3E") no-repeat center center;
            opacity: 0;
            animation: floatUp 4s linear infinite;
        }
        
        @keyframes floatUp {
            0% {
                transform: translateY(100vh) scale(0);
                opacity: 0;
            }
            20% {
                opacity: 0.8;
            }
            80% {
                opacity: 0.8;
            }
            100% {
                transform: translateY(-20vh) scale(1.5);
                opacity: 0;
            }
        }
        
        .error-message {
            color: #dc3545;
            font-size: 0.9rem;
            margin-top: 5px;
            display: none;
            animation: shake 0.5s;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
        
        .alert {
            padding: 10px 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
            /* 提示信息样式 */
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 5px;
        }
        
        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        
        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .alert-warning {
            color: #856404;
            background-color: #fff3cd;
            border-color: #ffeeba;
        }
        
        .alert-info {
            color: #0c5460;
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
    </style>
    <script src="/admin/login/handleSubmit.js"></script>
</head>
<body>
    <div class="floating-hearts"></div>
    
    <div class="login-container">
        <div class="card-header">
            <img src="/assets/images/logo.png" alt="Logo" class="logo" onerror="this.src='data:image/svg+xml;utf8,%3Csvg xmlns=\\\'http://www.w3.org/2000/svg\\\' viewBox=\\\'0 0 24 24\\\' fill=\\\'%23ffffff\\\'%3E%3Cpath d=\\\'M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\\\'/%3E%3C/svg%3E'">
            <h3 class="mb-0">管理员登录</h3>
            <p>情侣头像匹配系统</p>
        </div>
        <div class="card-body">
            <div id="alert-container" style="display: none;"></div>
            <div id="alert-container"></div>
            <form id="login-form" action="login.php" method="post" onsubmit="return handleSubmit(event);">
                <div class="form-group">
                    <label for="username" class="form-label">用户名</label>
                    <div class="input-group">
                        <span class="input-group-text">👤</span>
                        <input type="text" class="form-control" id="username" name="username" placeholder="请输入用户名" required>
                    </div>
                    <div class="error-message" id="username-error">请输入有效的用户名</div>
                </div>
                <div class="form-group">
                    <label for="password" class="form-label">密码</label>
                    <div class="input-group">
                        <span class="input-group-text">🔒</span>
                        <input type="password" class="form-control" id="password" name="password" placeholder="请输入密码" required>
                    </div>
                    <div class="error-message" id="password-error">请输入有效的密码</div>
                </div>
                <div class="form-group">
                    <label for="captcha" class="form-label">验证码</label>
                    <div class="input-group">
                        <span class="input-group-text">🔐</span>
                        <input type="text" class="form-control" id="captcha" name="captcha" placeholder="请输入验证码" required style="width: 60%;">
                        <div id="captcha-text" style="height: 38px; padding: 0 10px; display: flex; align-items: center; justify-content: center; background-color: #f0f0f0; font-family: monospace; font-size: 18px; font-weight: bold; letter-spacing: 3px; cursor: pointer; border-radius: 0 5px 5px 0; color: #333;" title="点击刷新验证码">加载中...</div>
                    </div>
                    <div class="error-message" id="captcha-error">请输入有效的验证码</div>
                </div>
                <div class="form-check">
                    <input type="checkbox" class="form-check-input" id="remember" name="remember">
                    <label class="form-check-label" for="remember">记住我</label>
                </div>
                <button type="submit" class="btn">登录</button>
            </form>
        </div>
    </div>

    <script>
        // 创建漂浮的心形元素
        function createFloatingHearts() {
            const container = document.querySelector(".floating-hearts");
            const heartCount = 15;
            
            for (let i = 0; i < heartCount; i++) {
                const heart = document.createElement("div");
                heart.classList.add("heart");
                heart.style.left = Math.random() * 100 + "vw";
                heart.style.animationDelay = Math.random() * 5 + "s";
                heart.style.opacity = Math.random() * 0.5 + 0.5;
                heart.style.transform = `scale(${Math.random() * 0.5 + 0.5})`;
                container.appendChild(heart);
            }
        }
        
        // 显示提示信息
        function showAlert(message, type = 'danger') {
            const alertContainer = document.getElementById('alert-container');
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            // 清除之前的提示
            alertContainer.innerHTML = '';
            alertContainer.appendChild(alertDiv);
            
            // 5秒后自动消失
            setTimeout(() => {
                alertDiv.style.opacity = '0';
                alertDiv.style.transition = 'opacity 0.5s';
                setTimeout(() => {
                    alertContainer.removeChild(alertDiv);
                }, 500);
            }, 5000);
        }
        
        // 引入表单处理函数
        
        // 页面加载完成后执行
        document.addEventListener("DOMContentLoaded", function() {
            // 创建漂浮的心形
            createFloatingHearts();
            
            // 验证码刷新功能
            document.getElementById("captcha-text").addEventListener("click", function() {
                refreshCaptcha();
            });
            
            // 刷新验证码
            function refreshCaptcha() {
                fetch("/captcha_text.php?" + new Date().getTime())
                    .then(response => response.text())
                    .then(code => {
                        document.getElementById("captcha-text").textContent = code;
                    })
                    .catch(error => {
                        console.error("验证码加载失败:", error);
                        document.getElementById("captcha-text").textContent = "加载失败";
                    });
            }
            
            // 初始加载验证码
            refreshCaptcha();
            
            // 检查URL参数是否有错误信息
            const urlParams = new URLSearchParams(window.location.search);
            const errorMsg = urlParams.get('error');
            if (errorMsg) {
                showAlert(decodeURIComponent(errorMsg));
            }
        });
    </script>
</body>
</html>

