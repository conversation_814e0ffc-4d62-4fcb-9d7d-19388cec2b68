﻿            </div>
            <!-- 主要内容区域结束 -->
        </div>
        <!-- 页面内容结束 -->
    </div>

    <!-- 底部栏 -->
    <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/footer-content.php'); ?>

    <!-- 回到顶部按钮 -->
    <button id="scrollTopButton" class="btn btn-primary rounded-circle position-fixed bottom-0 end-0 m-4 d-none d-lg-block btn-hover" style="display: none;">
        <i class="bi bi-arrow-up"></i>
    </button>

    <!-- 统一的加载动画 -->
    <div class="loading-overlay" id="loading-overlay" style="display: none;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">正在加载...</span>
        </div>
        <p class="mt-2" id="loading-message">正在处理，请稍候...</p>
    </div>

    <!-- 统一的消息提示容器 -->
    <div class="toast-container position-fixed top-0 end-0 p-3" id="toastContainer"></div>

    <!-- Bootstrap JS -->
    <script src="/assets/vendor/bootstrap/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="/assets/js/jquery-3.6.0.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // 侧边栏切换
            document.getElementById('sidebarCollapse').addEventListener('click', function () {
                document.getElementById('sidebar').classList.toggle('active');
                document.getElementById('content').classList.toggle('active');
                this.classList.toggle('active');
            });

            // 初始化所有工具提示
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            });

            // 创建全局加载器
            window.loader = {
                show: function(message = null) {
                    const loadingOverlay = document.getElementById('loading-overlay');
                    if (message) {
                        document.getElementById('loading-message').textContent = message;
                    }
                    loadingOverlay.style.display = 'flex';
                },
                hide: function() {
                    const loadingOverlay = document.getElementById('loading-overlay');
                    loadingOverlay.style.display = 'none';
                },
                setMessage: function(message) {
                    document.getElementById('loading-message').textContent = message;
                }
            };

            // 添加卡片动画效果
            document.querySelectorAll('.card').forEach(function(card) {
                card.classList.add('card-hover');
            });

            // 添加按钮动画效果
            document.querySelectorAll('.btn').forEach(function(btn) {
                btn.classList.add('btn-hover');
            });

            // 添加徽章动画效果
            document.querySelectorAll('.badge.bg-success, .badge.bg-danger').forEach(function(badge) {
                badge.classList.add('pulse');
            });

            // 添加页面元素进入动画
            document.querySelectorAll('.card').forEach(function(card, index) {
                card.classList.add('scale-in');
                card.style.animationDelay = (index * 0.1) + 's';
            });

            // 回到顶部按钮
            const scrollTopButton = document.getElementById('scrollTopButton');
            if (scrollTopButton) {
                window.addEventListener('scroll', function() {
                    if (window.scrollY > 300) {
                        scrollTopButton.style.display = 'block';
                    } else {
                        scrollTopButton.style.display = 'none';
                    }
                });

                scrollTopButton.addEventListener('click', function() {
                    window.scrollTo({
                        top: 0,
                        behavior: 'smooth'
                    });
                });
            }

            // 全局消息提示函数
            if (typeof showToast !== 'function') {
                window.showToast = function(type, title, message, delay = 3000) {
                    const toastContainer = document.getElementById('toastContainer');

                    // 创建toast元素
                    const toast = document.createElement('div');
                    toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : type === 'error' ? 'danger' : type === 'warning' ? 'warning' : 'info'} border-0`;
                    toast.setAttribute('role', 'alert');
                    toast.setAttribute('aria-live', 'assertive');
                    toast.setAttribute('aria-atomic', 'true');

                    // 设置toast内容
                    toast.innerHTML = `
                        <div class="d-flex">
                            <div class="toast-body">
                                <div class="d-flex align-items-center">
                                    <i class="bi ${type === 'success' ? 'bi-check-circle' : type === 'error' ? 'bi-x-circle' : type === 'warning' ? 'bi-exclamation-triangle' : 'bi-info-circle'} me-2"></i>
                                    <strong>${title}</strong>
                                </div>
                                <div class="mt-1">${message}</div>
                            </div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                    `;

                    // 添加到容器
                    toastContainer.appendChild(toast);

                    // 初始化toast
                    const bsToast = new bootstrap.Toast(toast, {
                        autohide: true,
                        delay: delay
                    });

                    // 显示toast
                    bsToast.show();

                    // 添加进入动画
                    toast.classList.add('scale-in');

                    // 监听隐藏事件，移除DOM元素
                    toast.addEventListener('hidden.bs.toast', function() {
                        toast.remove();
                    });

                    // 返回toast元素，以便调用者可以进一步控制
                    return toast;
                };
            }
        });
    </script>
</body>
</html>
