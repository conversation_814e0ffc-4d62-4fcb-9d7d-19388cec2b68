/**
 * 头部下拉菜单修复脚本
 * 使用JavaScript处理下拉菜单的显示和隐藏
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('头部下拉菜单修复脚本已加载');

    // 获取用户信息区域和下拉菜单
    const userInfo = document.querySelector('.header .user-info');
    const dropdownContent = document.querySelector('.header .dropdown-content');

    if (!userInfo || !dropdownContent) {
        console.error('未找到用户信息区域或下拉菜单');
        return;
    }

    // 确保头像文字颜色为白色
    const userAvatar = document.querySelector('.header .user-avatar');
    if (userAvatar) {
        userAvatar.style.color = '#ffffff';
    }

    // 移除现有的hover事件
    userInfo.classList.remove('dropdown');

    // 使用鼠标悬停事件
    let hoverTimeout;

    // 鼠标进入用户信息区域时显示下拉菜单
    userInfo.addEventListener('mouseenter', function() {
        clearTimeout(hoverTimeout);
        dropdownContent.style.display = 'block';

        // 确保下拉菜单在视口内
        const rect = dropdownContent.getBoundingClientRect();
        if (rect.right > window.innerWidth) {
            dropdownContent.style.right = '0';
            dropdownContent.style.left = 'auto';
        }

        // 确保下拉菜单在其他元素之上
        dropdownContent.style.zIndex = '100000';
    });

    // 鼠标离开用户信息区域时隐藏下拉菜单（延迟执行，给用户时间移动到下拉菜单）
    userInfo.addEventListener('mouseleave', function() {
        hoverTimeout = setTimeout(function() {
            if (!isMouseOverDropdown) {
                dropdownContent.style.display = 'none';
            }
        }, 100);
    });

    // 跟踪鼠标是否在下拉菜单上
    let isMouseOverDropdown = false;

    // 鼠标进入下拉菜单时
    dropdownContent.addEventListener('mouseenter', function() {
        clearTimeout(hoverTimeout);
        isMouseOverDropdown = true;
    });

    // 鼠标离开下拉菜单时
    dropdownContent.addEventListener('mouseleave', function() {
        isMouseOverDropdown = false;
        dropdownContent.style.display = 'none';
    });

    // 确保下拉菜单项可点击
    const dropdownLinks = dropdownContent.querySelectorAll('a');
    dropdownLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href) {
                window.location.href = href;
            }
        });
    });

    // 为移动设备添加点击事件
    if ('ontouchstart' in window) {
        let isDropdownOpen = false;

        userInfo.addEventListener('click', function(e) {
            e.stopPropagation();

            if (isDropdownOpen) {
                dropdownContent.style.display = 'none';
                isDropdownOpen = false;
            } else {
                dropdownContent.style.display = 'block';
                isDropdownOpen = true;
            }
        });

        document.addEventListener('click', function() {
            if (isDropdownOpen) {
                dropdownContent.style.display = 'none';
                isDropdownOpen = false;
            }
        });

        dropdownContent.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    }
});
