﻿<?php
// 开启会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 启用错误报告
ini_set('display_errors', 1);
error_reporting(E_ALL);

// 记录请求信息
$logFile = __DIR__ . '/../../storage/logs/tenant.log';
$logDir = dirname($logFile);
if (!is_dir($logDir)) {
    mkdir($logDir, 0777, true);
}

file_put_contents($logFile, date('Y-m-d H:i:s') . " - 租户控制面板请求开始\n", FILE_APPEND);
file_put_contents($logFile, "SESSION 数据: " . print_r($_SESSION, true) . "\n", FILE_APPEND);

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    // 未登录，重定向到登录页面
    header('Location: /login');
    exit;
}

// 获取用户角色
$isTenant = isset($_SESSION['role']) && $_SESSION['role'] === 'tenant';
$username = $_SESSION['username'] ?? '租户用户';
$userInitial = mb_substr($username, 0, 1, 'UTF-8');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>租户控制面板 - 情侣头像匹配系统</title>
    <link href="/assets/vendor/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="/assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="/assets/vendor/animate/animate.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/theme.css">
    <link rel="stylesheet" href="/assets/css/animations.css">
    <link rel="stylesheet" href="/assets/css/enhanced-animations.css">
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/theme-switcher.css">
    <link rel="stylesheet" href="/assets/css/ui-enhancements.css">
    <link rel="stylesheet" href="/assets/css/stat-item-fix.css">
    <link rel="stylesheet" href="/assets/css/dashboard-value-fix.css">
    <link rel="stylesheet" href="/assets/css/service-info-fix.css">
    <link rel="stylesheet" href="/assets/css/dashboard-position-fix.css">
    <link rel="stylesheet" href="/assets/css/dashboard-alignment-fix.css">
    <style>
        /* 基础样式 */
        :root {
            --primary-color: #ff6b95;
            --secondary-color: #ffa5c0;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --gray-color: #6c757d;
            --white-color: #ffffff;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --transition-speed: 0.3s;
            --border-radius: 8px;
            --animate-delay: 0.1s;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }

        body {
            background-color: #f5f7fa;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: hidden;
        }

        /* 加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 107, 149, 0.3);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 布局样式 */
        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--white-color);
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 2px 0 10px var(--shadow-color);
            transition: all var(--transition-speed);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            margin-bottom: 20px;
        }

        .sidebar-logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 10px;
            object-fit: cover;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .sidebar-title {
            color: var(--white-color);
            font-size: 1.5rem;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .sidebar-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .sidebar.collapsed .sidebar-title,
        .sidebar.collapsed .sidebar-subtitle {
            display: none;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu li a {
            color: var(--white-color);
            text-decoration: none;
            display: block;
            padding: 12px 20px;
            transition: all var(--transition-speed);
            border-left: 4px solid transparent;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.2);
            border-left-color: var(--white-color);
        }

        .sidebar-menu li a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }

        .sidebar.collapsed .sidebar-menu li a span {
            display: none;
        }

        .sidebar.collapsed .sidebar-menu li a i {
            margin-right: 0;
            font-size: 1.3rem;
        }

        /* 主内容区域样式 */
        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 20px;
            transition: all var(--transition-speed);
        }

        .main-content.expanded {
            margin-left: 70px;
        }

        /* 头部样式 */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .menu-toggle {
            background: none;
            border: none;
            color: var(--dark-color);
            font-size: 1.5rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            transition: all var(--transition-speed);
        }

        .menu-toggle:hover {
            background-color: var(--light-color);
        }

        .user-info {
            display: flex;
            align-items: center;
            position: relative;
            cursor: pointer;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: var(--white-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }

        .user-name {
            font-weight: 500;
        }

        .dropdown-content {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: var(--white-color);
            min-width: 160px;
            box-shadow: 0 8px 16px 0 var(--shadow-color);
            border-radius: var(--border-radius);
            padding: 10px 0;
            z-index: 1;
            display: none;
            animation: fadeIn 0.3s;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .user-info:hover .dropdown-content {
            display: block;
        }

        .dropdown-content a {
            color: var(--dark-color);
            padding: 10px 20px;
            text-decoration: none;
            display: block;
            transition: all var(--transition-speed);
        }

        .dropdown-content a:hover {
            background-color: var(--light-color);
        }

        .dropdown-content a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        /* 仪表盘特定样式 */
        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: 0 5px 15px var(--shadow-color);
            padding: 20px;
            display: flex;
            align-items: flex-start;
            gap: 15px;
            transition: all var(--transition-speed);
            animation: fadeInUp 0.5s;
            border-top: 3px solid var(--primary-color);
        }

        .stat-card:hover {
            box-shadow: 0 8px 25px var(--shadow-color);
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--white-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            box-shadow: 0 5px 15px rgba(255, 107, 149, 0.3);
        }

        .stat-content {
            flex: 1;
        }

        .stat-content h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 15px;
            position: relative;
            padding-bottom: 10px;
            text-align: center;
        }

        .stat-content h3::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 3px;
            background-color: var(--primary-color);
            border-radius: 3px;
        }

        .stat-details {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            gap: 5px;
            text-align: center;
        }

        .stat-label {
            color: var(--gray-color);
            font-weight: 500;
            text-align: right;
            width: 45%;
        }

        .stat-value {
            color: var(--dark-color);
            font-weight: 600;
            text-align: left;
            width: 45%;
        }

        .status-active {
            color: var(--success-color);
        }

        .version-history {
            margin-left: 10px;
            font-size: 0.8rem;
            color: var(--primary-color);
            text-decoration: none;
        }

        .version-history:hover {
            text-decoration: underline;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
            margin-left: 10px;
        }

        .btn-xs {
            padding: 0.1rem 0.3rem;
            font-size: 0.75rem;
            line-height: 1.2;
            border-radius: 0.2rem;
        }

        .dashboard-sections {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 20px;
        }

        .section-row {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .section {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: 0 5px 15px var(--shadow-color);
            overflow: hidden;
            animation: fadeInUp 0.5s;
            height: 100%;
        }

        .section-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }

        .section-header h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark-color);
            display: flex;
            align-items: center;
        }

        .section-header h3 i {
            margin-right: 8px;
            color: var(--primary-color);
        }

        .view-all {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all var(--transition-speed);
        }

        .view-all:hover {
            color: #ff4f7e;
            text-decoration: underline;
        }

        .section-content {
            padding: 20px;
        }

        .announcement-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .announcement-item {
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
            transition: all var(--transition-speed);
        }

        .announcement-item:hover {
            transform: translateX(5px);
        }

        .announcement-item:last-child {
            padding-bottom: 0;
            border-bottom: none;
        }

        .announcement-title {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 5px;
        }

        .announcement-time {
            font-size: 0.8rem;
            color: var(--gray-color);
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .announcement-time::before {
            content: "\\F282";
            font-family: "bootstrap-icons";
            margin-right: 5px;
            font-size: 0.9rem;
        }

        .shortcut-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 15px;
        }

        .shortcut-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: var(--dark-color);
            padding: 20px 15px;
            border-radius: var(--border-radius);
            transition: all var(--transition-speed);
            background-color: #f8f9fa;
            box-shadow: 0 2px 5px var(--shadow-color);
        }

        .shortcut-item:hover {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--white-color);
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(255, 107, 149, 0.3);
        }

        .shortcut-item i {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .shortcut-item span {
            font-weight: 500;
        }

        /* 支付方式样式 */
        .payment-methods {
            display: flex;
            gap: 15px;
            margin-top: 10px;
        }

        .payment-option {
            flex: 1;
            border: 1px solid #eee;
            border-radius: var(--border-radius);
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-speed);
            position: relative;
        }

        .payment-option:hover {
            border-color: var(--primary-color);
            background-color: rgba(255, 107, 149, 0.05);
        }

        .payment-option input {
            position: absolute;
            top: 10px;
            left: 10px;
            margin: 0;
        }

        .payment-option input:checked + .payment-icon {
            color: var(--primary-color);
        }

        .payment-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: var(--gray-color);
        }

        .payment-name {
            font-weight: 500;
        }

        /* 动画 */
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 信息卡片样式 */
        .info-card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: 0 4px 10px var(--shadow-color);
            transition: all var(--transition-speed);
            overflow: hidden;
        }

        .info-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px var(--shadow-color);
        }

        .card-body {
            display: flex;
            align-items: center;
            padding: 20px;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: var(--white-color);
            font-size: 1.5rem;
        }

        .bg-primary {
            background-color: var(--primary-color);
        }

        .bg-success {
            background-color: var(--success-color);
        }

        .bg-warning {
            background-color: var(--warning-color);
        }

        .bg-info {
            background-color: var(--info-color);
        }

        .card-info {
            flex: 1;
        }

        .card-title {
            font-size: 0.9rem;
            color: var(--gray-color);
            margin-bottom: 5px;
        }

        .card-value {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
        }

        /* 公告样式 */
        .announcement-item {
            padding: 15px 0;
            border-bottom: 1px solid var(--light-color);
        }

        .announcement-item:last-child {
            border-bottom: none;
        }

        .announcement-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .announcement-title {
            font-weight: 600;
            margin: 0;
        }

        .announcement-date {
            font-size: 0.8rem;
            color: var(--gray-color);
        }

        .announcement-content {
            color: var(--dark-color);
            line-height: 1.6;
        }

        /* 动画效果 */
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s;
        }

        .animate-on-scroll.animated {
            opacity: 1;
            transform: translateY(0);
        }

        /* 卡片样式 */
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: 0 4px 10px var(--shadow-color);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            background-color: var(--white-color);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 15px 20px;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1001;
            overflow-y: auto;
            padding: 20px;
        }

        .modal-content {
            background-color: var(--white-color);
            margin: 5% auto;
            width: 600px;
            max-width: 90%;
            border-radius: var(--border-radius);
            box-shadow: 0 10px 30px var(--shadow-color);
            animation: slideDown 0.3s;
            overflow: hidden;
        }

        @keyframes slideDown {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark-color);
        }

        .close-btn {
            background: none;
            border: none;
            color: var(--gray-color);
            font-size: 1.2rem;
            cursor: pointer;
            transition: all var(--transition-speed);
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .close-btn:hover {
            color: var(--danger-color);
            background-color: rgba(220, 53, 69, 0.1);
        }

        .modal-body {
            padding: 20px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            background-color: #f8f9fa;
        }

        .version-item {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--light-color);
        }

        .version-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .version-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .version-number {
            font-weight: 600;
        }

        .version-date {
            font-size: 0.8rem;
            color: var(--gray-color);
        }

        .version-changes {
            list-style-type: disc;
            padding-left: 20px;
            margin: 0;
        }

        .version-changes li {
            margin-bottom: 5px;
        }

        /* 消息提示样式 */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 300px;
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: 0 5px 15px var(--shadow-color);
            overflow: hidden;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transform: translateX(30px);
            transition: all 0.3s;
        }

        .toast.show {
            opacity: 1;
            visibility: visible;
            transform: translateX(0);
        }

        .toast-content {
            display: flex;
            align-items: center;
            padding: 15px;
        }

        .toast-icon {
            font-size: 1.5rem;
            margin-right: 15px;
            display: none;
        }

        .toast-icon.success {
            color: var(--success-color);
        }

        .toast-icon.error {
            color: var(--danger-color);
        }

        .toast-icon.info {
            color: var(--info-color);
        }

        .toast-message {
            flex: 1;
            font-weight: 500;
        }

        .toast-progress {
            height: 3px;
            background-color: var(--primary-color);
            width: 100%;
            animation: progress 3s linear;
        }

        @keyframes progress {
            from { width: 100%; }
            to { width: 0%; }
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .sidebar {
                width: 0;
                padding: 0;
            }

            .sidebar.collapsed {
                width: 70px;
            }

            .main-content {
                margin-left: 0;
            }

            .main-content.expanded {
                margin-left: 70px;
            }

            .dashboard-welcome {
                flex-direction: column;
            }

            .welcome-illustration {
                display: none;
            }
        }

        .stat-value-with-btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.stat-value {
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
}
</style>
</head>
<body>
    <div class="loading-overlay" id="loading-overlay">
        <div class="spinner"></div>
    </div>

    <div class="dashboard-container">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <img src="/assets/images/logo.png" alt="Logo" class="sidebar-logo" onerror="this.src='data:image/svg+xml;utf8,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 24 24%22 fill=%22%23ffffff%22%3E%3Cpath d=%22M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z%22/%3E%3C/svg%3E'">
                <h3 class="sidebar-title">情侣头像匹配</h3>
                <p class="sidebar-subtitle">管理系统</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="/dashboard" class="active"><i class="bi bi-speedometer2"></i> <span>控制面板</span></a></li>
                <li><a href="/dashboard/platforms"><i class="bi bi-phone"></i> <span>小程序平台</span></a></li>
                <li><a href="/dashboard/orders"><i class="bi bi-cart3"></i> <span>订单搜索</span></a></li>
                <li><a href="/dashboard/recycle"><i class="bi bi-trash"></i> <span>应用回收站</span></a></li>
                <li><a href="/logout.php"><i class="bi bi-box-arrow-right"></i> <span>退出登录</span></a></li>
            </ul>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content" id="main-content">
            <div class="header">
                <button class="menu-toggle" id="menu-toggle"><i class="bi bi-list"></i></button>
                <div class="user-info dropdown">
                    <div class="user-avatar"><?php echo htmlspecialchars($userInitial); ?></div>
                    <span class="user-name"><?php echo htmlspecialchars($username); ?></span>
                    <div class="dropdown-content">
                        <a href="/profile"><i class="bi bi-person"></i> 个人资料</a>
                        <a href="/logout.php"><i class="bi bi-box-arrow-right"></i> 退出登录</a>
                    </div>
                </div>
            </div>

            <!-- 账户概述 -->
            <div class="dashboard-stats">
                <div class="stat-card floating-card hover-card">
                    <div class="stat-icon"><i class="bi bi-person-check rotate-icon"></i></div>
                    <div class="stat-content">
                        <h3>账户信息</h3>
                        <div class="stat-details">
                            <div class="stat-grid">
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-person"></i> 用户名</span>
                                    <span class="stat-value"><?php echo htmlspecialchars($username); ?></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-phone"></i> 手机号</span>
                                    <span class="stat-value">138****8888</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-envelope"></i> 邮箱</span>
                                    <div class="stat-value-multiline">
                                        <span class="stat-value"><EMAIL></span>
                                    </div>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-check-circle"></i> 账户状态</span>
                                    <span class="stat-value status-active">正常</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-plus-circle"></i> 可创建小程序</span>
                                    <span class="stat-value">5</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-app"></i> 已创建小程序</span>
                                    <span class="stat-value">3</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="stat-card floating-card hover-card">
                    <div class="stat-icon"><i class="bi bi-shield-check rotate-icon"></i></div>
                    <div class="stat-content">
                        <h3>系统概况</h3>
                        <div class="stat-details">
                            <div class="stat-grid">
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-shield-check"></i> 授权状态</span>
                                    <span class="stat-value status-active">已授权</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-award"></i> 授权类型</span>
                                    <span class="stat-value">正版坑位</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-person-badge"></i> 账户权限</span>
                                    <span class="stat-value">租户</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-server"></i> 部署方式</span>
                                    <span class="stat-value">账号开户</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-clock-history"></i> 系统运行时间</span>
                                    <span class="stat-value">128天</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-hdd-stack"></i> 运行环境</span>
                                    <div class="stat-value-multiline">
                                        <span class="stat-value">PHP 7.4</span>
                                        <span class="stat-value">MySQL 5.7</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="stat-card floating-card hover-card">
    <div class="stat-icon"><i class="bi bi-calendar-check rotate-icon"></i></div>
    <div class="stat-content">
        <h3>服务信息</h3>
        <div class="stat-details">
            <div class="stat-grid">
                <div class="stat-item">
                    <span class="stat-label"><i class="bi bi-calendar-check"></i> 授权时长</span>
                    <span class="stat-value">永久</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label"><i class="bi bi-code-square"></i> 当前版本</span>
                    <span class="stat-value">v1.0.2</span>
                    <a href="javascript:void(0)" class="btn btn-sm btn-outline-primary btn-xs" style="margin-top: 8px;" onclick="showVersionHistory()">历史版本</a>
                </div>
                <div class="stat-item">
                    <span class="stat-label"><i class="bi bi-hourglass-split"></i> 服务期限</span>
                    <span class="stat-value">永久</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label"><i class="bi bi-calendar-date"></i> 系统时间</span>
                    <span class="stat-value" id="current-date">23年5月15日</span>
                    <span class="stat-value" id="current-time" style="margin-top: 5px; color: var(--primary-color); font-family: 'Courier New', monospace; font-weight: bold;">12:00:00</span>
                </div>
            </div>
        </div>
    </div>
</div>

                <style>
                    .stat-grid {
                        display: grid;
                        grid-template-columns: repeat(2, minmax(0, 1fr));
                        gap: 15px;
                        width: 100%;
                    }

                    .stat-item {
                        position: relative;
                        display: flex;
                        flex-direction: column;
                        padding: 12px 15px;
                        margin-bottom: 10px;
                        background-color: rgba(var(--primary-color-rgb), 0.03);
                        border-radius: 6px;
                        transition: all 0.3s ease;
                        overflow: hidden;
                        text-align: center;
                        min-width: 0; /* 确保弹性项目可以收缩 */
                    }

                    .stat-label {
                        font-weight: 500;
                        color: var(--text-muted);
                        margin-bottom: 8px;
                        line-height: 1.4;
                        font-size: 0.9em;
                        border-bottom: 1px dashed rgba(var(--primary-color-rgb), 0.2);
                        padding-bottom: 5px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        width: 100%;
                        text-align: center;
                    }

                    .stat-value {
                        font-weight: 600;
                        color: var(--text-color);
                        line-height: 1.4;
                        font-size: 1.1em;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        width: 100%;
                        text-align: center;
                    }

                    .stat-value-with-btn {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        width: 100%;
                    }

                    .stat-value-with-btn .stat-value {
                        margin-bottom: 8px;
                        width: 100%;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    /* 时间显示样式 */
                    #current-time {
                        font-family: 'Courier New', monospace;
                        font-weight: bold;
                        color: var(--primary-color);
                        margin-top: 0;
                    }

                    /* 多行值样式 */
                    .stat-value-multiline {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        width: 100%;
                    }

                    .stat-value-multiline .stat-value {
                        margin-bottom: 4px;
                        width: 100%;
                        white-space: normal;
                        word-break: break-word;
                        line-height: 1.3;
                    }

                    .stat-value-multiline .stat-value:last-child {
                        margin-bottom: 0;
                    }

                    .stat-value-with-btn .btn {
                        white-space: nowrap;
                        font-size: 0.9em;
                        padding: 4px 12px;
                        margin-top: 2px;
                    }

                    .stat-item:hover {
                        background-color: rgba(var(--primary-color-rgb), 0.08);
                    }

                    .status-active {
                        color: var(--success-color);
                        font-weight: bold;
                    }

                    .version-history {
                        font-size: 0.85em;
                        margin-left: 8px;
                        color: var(--primary-color);
                        text-decoration: none;
                    }

                    .version-history:hover {
                        text-decoration: underline;
                    }

                    @media (max-width: 768px) {
                        .stat-grid {
                            grid-template-columns: 1fr;
                        }

                        .stat-item {
                            padding: 10px;
                            width: 100%;
                        }

                        .stat-label {
                            font-size: 0.85em;
                            margin-bottom: 6px;
                            width: 100%;
                            max-width: 100%;
                        }

                        .stat-value {
                            font-size: 1em;
                            width: 100%;
                            max-width: 100%;
                        }

                        .stat-value-with-btn .btn {
                            padding: 3px 10px;
                            font-size: 0.8em;
                            margin-top: 5px;
                        }
                    }
                </style>
            </div>

            <!-- 服务状态和公告 -->
            <div class="dashboard-sections">
                <div class="section-row">
                    <div class="section">
                        <div class="section-header">
                            <h3><i class="bi bi-megaphone"></i> 最新公告</h3>
                            <a href="javascript:void(0)" class="view-all" onclick="showAllAnnouncements()">查看全部</a>
                        </div>
                        <div class="section-content">
                            <div class="announcement-list">
                                <div class="announcement-item scroll-anim">
                                    <div class="announcement-title gradient-text">系统更新通知</div>
                                    <div class="announcement-time">2023-05-10</div>
                                    <div class="announcement-content">
                                        系统将于2023年5月15日进行版本更新，更新内容包括界面优化、功能增强和安全性提升。更新期间系统将暂停服务约30分钟，请提前做好准备。
                                    </div>
                                </div>
                                <div class="announcement-item scroll-anim scroll-anim-delay-1">
                                    <div class="announcement-title gradient-text">新功能上线</div>
                                    <div class="announcement-time">2023-05-05</div>
                                    <div class="announcement-content">
                                        我们新增了批量处理功能，现在您可以一次性处理多个订单，提高工作效率。详情请查看帮助文档。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-header">
                            <h3><i class="bi bi-lightning-charge"></i> 快捷入口</h3>
                        </div>
                        <div class="section-content">
                            <div class="shortcut-grid">

                                <a href="/dashboard/platforms" class="shortcut-item bounce-btn pulse">
                                    <i class="bi bi-phone"></i>
                                    <span>小程序平台</span>
                                </a>
                                <a href="/dashboard/orders" class="shortcut-item bounce-btn">
                                    <i class="bi bi-cart3"></i>
                                    <span>订单搜索</span>
                                </a>
                                <a href="/profile" class="shortcut-item bounce-btn">
                                    <i class="bi bi-person"></i>
                                    <span>个人资料</span>
                                </a>
                                <a href="/dashboard/recycle" class="shortcut-item bounce-btn">
                                    <i class="bi bi-trash"></i>
                                    <span>应用回收站</span>
                                </a>

                            </div>
                        </div>
                    </div>
                </div>

                <div class="section-row">
                    <div class="section">
                        <div class="section-header">
                            <h3><i class="bi bi-bar-chart"></i> 订单统计</h3>
                        </div>
                        <div class="section-content">
                            <canvas id="orderChart" width="400" height="200"></canvas>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-header">
                            <h3><i class="bi bi-pie-chart"></i> 平台使用情况</h3>
                        </div>
                        <div class="section-content">
                            <canvas id="usageChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 版本历史模态框 -->
    <div class="modal" id="version-history-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>版本历史</h3>
                <button class="close-btn" onclick="closeModal('version-history-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <div class="version-list">
                    <div class="version-item">
                        <div class="version-header">
                            <span class="version-number">v1.0.2</span>
                            <span class="version-date">2023-05-10</span>
                        </div>
                        <div class="version-content">
                            <ul>
                                <li>优化了用户界面，提升用户体验</li>
                                <li>修复了订单搜索功能的已知问题</li>
                                <li>增强了系统安全性</li>
                                <li>改进了小程序平台的管理功能</li>
                            </ul>
                        </div>
                    </div>
                    <div class="version-item">
                        <div class="version-header">
                            <span class="version-number">v1.0.1</span>
                            <span class="version-date">2023-04-15</span>
                        </div>
                        <div class="version-content">
                            <ul>
                                <li>新增了批量处理功能</li>
                                <li>修复了数据统计的显示问题</li>
                                <li>优化了系统性能</li>
                            </ul>
                        </div>
                    </div>
                    <div class="version-item">
                        <div class="version-header">
                            <span class="version-number">v1.0.0</span>
                            <span class="version-date">2023-03-01</span>
                        </div>
                        <div class="version-content">
                            <ul>
                                <li>系统正式发布</li>
                                <li>支持多租户管理</li>
                                <li>支持小程序平台管理</li>
                                <li>支持订单管理和搜索</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('version-history-modal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 续费服务模态框 -->
    <div class="modal" id="renew-service-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>续费服务</h3>
                <button class="close-btn" onclick="closeModal('renew-service-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <form id="renew-service-form">
                    <div class="form-group">
                        <label for="service-plan">服务套餐</label>
                        <select id="service-plan" class="form-control" required>
                            <option value="">选择套餐</option>
                            <option value="1">基础版 - 1年 - ¥999</option>
                            <option value="2">标准版 - 1年 - ¥1999</option>
                            <option value="3">高级版 - 1年 - ¥2999</option>
                            <option value="4">基础版 - 3年 - ¥2499</option>
                            <option value="5">标准版 - 3年 - ¥4999</option>
                            <option value="6">高级版 - 3年 - ¥7499</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>支付方式</label>
                        <div class="payment-methods">
                            <label class="payment-option">
                                <input type="radio" name="payment-method" value="wechat" checked>
                                <div class="payment-icon"><i class="bi bi-wechat"></i></div>
                                <div class="payment-name">微信支付</div>
                            </label>
                            <label class="payment-option">
                                <input type="radio" name="payment-method" value="alipay">
                                <div class="payment-icon"><i class="bi bi-credit-card"></i></div>
                                <div class="payment-name">支付宝</div>
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('renew-service-modal')">取消</button>
                <button class="btn btn-primary" onclick="confirmRenew()">确认支付</button>
            </div>
        </div>
    </div>

    <!-- 全部公告模态框 -->
    <div class="modal" id="all-announcements-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>全部公告</h3>
                <button class="close-btn" onclick="closeModal('all-announcements-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <div class="announcement-list">
                    <div class="announcement-item">
                        <div class="announcement-title gradient-text">系统更新通知</div>
                        <div class="announcement-time">2023-05-10</div>
                        <div class="announcement-content">
                            系统将于2023年5月15日进行版本更新，更新内容包括界面优化、功能增强和安全性提升。更新期间系统将暂停服务约30分钟，请提前做好准备。
                        </div>
                    </div>
                    <div class="announcement-item">
                        <div class="announcement-title gradient-text">新功能上线</div>
                        <div class="announcement-time">2023-05-05</div>
                        <div class="announcement-content">
                            我们新增了批量处理功能，现在您可以一次性处理多个订单，提高工作效率。详情请查看帮助文档。
                        </div>
                    </div>
                    <div class="announcement-item">
                        <div class="announcement-title gradient-text">系统维护通知</div>
                        <div class="announcement-time">2023-04-20</div>
                        <div class="announcement-content">
                            系统将于2023年4月25日凌晨2:00-4:00进行例行维护，期间系统将暂停服务。给您带来的不便，敬请谅解。
                        </div>
                    </div>
                    <div class="announcement-item">
                        <div class="announcement-title gradient-text">安全更新</div>
                        <div class="announcement-time">2023-04-10</div>
                        <div class="announcement-content">
                            我们发布了一项重要的安全更新，建议所有用户及时更新系统，以确保您的数据安全。
                        </div>
                    </div>
                    <div class="announcement-item">
                        <div class="announcement-title gradient-text">价格调整通知</div>
                        <div class="announcement-time">2023-03-15</div>
                        <div class="announcement-content">
                            由于运营成本上升，我们将从2023年4月1日起对部分服务价格进行调整。现有用户在当前合同期内不受影响。
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('all-announcements-modal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 更新版本模态框 -->
    <div class="modal" id="update-version-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>更新版本</h3>
                <button class="close-btn" onclick="closeModal('update-version-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <div class="update-info">
                    <div class="update-header">
                        <h4>发现新版本：v1.0.3</h4>
                        <span class="update-date">发布日期：2023-06-20</span>
                    </div>
                    <div class="update-content">
                        <h5>更新内容：</h5>
                        <ul>
                            <li>优化了用户界面，提升用户体验</li>
                            <li>新增了数据分析功能，支持更多维度的数据统计</li>
                            <li>修复了已知的安全漏洞</li>
                            <li>提高了系统性能，减少了页面加载时间</li>
                            <li>改进了移动端适配，提供更好的移动端体验</li>
                        </ul>
                        <div class="update-note">
                            <p><strong>注意：</strong>更新过程中系统将暂停服务约5分钟，请在业务低峰期进行更新。</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('update-version-modal')">取消</button>
                <button class="btn btn-primary" onclick="confirmUpdate()">立即更新</button>
            </div>
        </div>
    </div>

    <!-- 引入Bootstrap和其他JS库 -->
    <script src="/assets/vendor/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="/assets/vendor/chart/chart.min.js"></script>
    <script src="/assets/js/common.js"></script>
    <script src="/assets/js/theme-switcher.js"></script>
    <script>
        // DOM元素
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');
        const menuToggle = document.getElementById('menu-toggle');
        const loadingOverlay = document.getElementById('loading-overlay');
        const currentDateElement = document.getElementById('current-date');
        const versionHistoryModal = document.getElementById('version-history-modal');
        const renewServiceModal = document.getElementById('renew-service-modal');

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 显示加载动画
            showLoading();

            // 检查本地存储中的侧边栏状态
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (sidebarCollapsed) {
                sidebar.classList.add('collapsed');
                mainContent.classList.add('expanded');
            }

            // 设置当前日期
            const now = new Date();
            const options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
            currentDateElement.textContent = now.toLocaleDateString('zh-CN', options);

            // 初始化图表
            initCharts();

            // 添加卡片动画
            addCardAnimations();

            // 隐藏加载动画
            setTimeout(hideLoading, 500);

            // 显示欢迎提示
            setTimeout(() => {
                showToast(`欢迎回来，${document.querySelector('.user-name').textContent}！`, 'success');
            }, 1000);
        });

        // 侧边栏切换
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        });

        // 显示加载动画
        function showLoading() {
            loadingOverlay.classList.add('show');
        }

        // 隐藏加载动画
        function hideLoading() {
            loadingOverlay.classList.remove('show');
        }

        // 显示版本历史
        function showVersionHistory() {
            openModal('version-history-modal');
        }

        // 打开续费模态框
        function openRenewModal() {
            openModal('renew-service-modal');
        }

        // 显示全部公告
        function showAllAnnouncements() {
            openModal('all-announcements-modal');
        }

        // 打开模态框
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        };

        // 添加卡片动画
        function addCardAnimations() {
            // 为统计卡片添加动画
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });

            // 为部分添加动画
            const sections = document.querySelectorAll('.section');
            sections.forEach((section, index) => {
                section.style.animationDelay = `${0.3 + index * 0.1}s`;
            });

            // 为快捷方式添加动画
            const shortcuts = document.querySelectorAll('.shortcut-item');
            shortcuts.forEach((shortcut, index) => {
                shortcut.style.animationDelay = `${0.5 + index * 0.05}s`;
            });
        }

        // 初始化图表
        function initCharts() {
            // 直接初始化租户图表
                // 租户统计图表
                const tenantCtx = document.getElementById('tenantChart').getContext('2d');
                new Chart(tenantCtx, {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月'],
                        datasets: [{
                            label: '租户数量',
                            data: [5, 8, 12, 15, 20],
                            backgroundColor: 'rgba(255, 107, 149, 0.2)',
                            borderColor: 'rgba(255, 107, 149, 1)',
                            borderWidth: 2,
                            tension: 0.3
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            title: {
                                display: true,
                                text: '租户增长趋势'
                            }
                        }
                    }
                });

                // 平台统计图表
                const platformCtx = document.getElementById('platformChart').getContext('2d');
                new Chart(platformCtx, {
                    type: 'bar',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月'],
                        datasets: [{
                            label: '平台数量',
                            data: [10, 15, 25, 35, 50],
                            backgroundColor: 'rgba(138, 111, 214, 0.2)',
                            borderColor: 'rgba(138, 111, 214, 1)',
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            title: {
                                display: true,
                                text: '平台增长趋势'
                            }
                        }
                    }
                });
                // 平台使用情况图表
                const usageCtx = document.getElementById('usageChart').getContext('2d');
                new Chart(usageCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['已使用', '剩余'],
                        datasets: [{
                            data: [3, 2],
                            backgroundColor: [
                                'rgba(255, 107, 149, 0.7)',
                                'rgba(138, 111, 214, 0.7)'
                            ],
                            borderColor: [
                                'rgba(255, 107, 149, 1)',
                                'rgba(138, 111, 214, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            title: {
                                display: true,
                                text: '小程序平台使用情况'
                            }
                        }
                    }
                });

                // 订单统计图表
                const orderCtx = document.getElementById('orderChart').getContext('2d');
                new Chart(orderCtx, {
                    type: 'bar',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月'],
                        datasets: [{
                            label: '订单数量',
                            data: [30, 45, 60, 75, 90],
                            backgroundColor: 'rgba(40, 199, 111, 0.2)',
                            borderColor: 'rgba(40, 199, 111, 1)',
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            title: {
                                display: true,
                                text: '订单趋势'
                            }
                        }
                    }
                });
        }

        // 续费服务
        function renewService() {
            const servicePlan = document.getElementById('service-plan').value;
            const paymentMethod = document.querySelector('input[name="payment-method"]:checked').value;

            if (!servicePlan) {
                showToast('请选择服务套餐', 'error');
                return;
            }

            // 显示加载动画
            showLoading();

            // 模拟支付过程
            setTimeout(() => {
                // 隐藏加载动画
                hideLoading();

                // 关闭模态框
                closeModal('renew-service-modal');

                // 显示成功提示
                showToast('服务续费成功！', 'success');
            }, 1500);
        }

        // 显示消息提示
        function showToast(message, type = 'success') {
            // 移除现有的提示
            const existingToasts = document.querySelectorAll('.toast');
            existingToasts.forEach(toast => {
                document.body.removeChild(toast);
            });

            // 创建新提示
            const toast = document.createElement('div');
            toast.className = 'toast';

            const toastContent = document.createElement('div');
            toastContent.className = 'toast-content';

            const icon = document.createElement('i');
            if (type === 'success') {
                icon.className = 'bi bi-check-circle-fill toast-icon success';
                icon.style.display = 'block';
                icon.style.color = 'var(--success-color)';
            } else if (type === 'error') {
                icon.className = 'bi bi-x-circle-fill toast-icon error';
                icon.style.display = 'block';
                icon.style.color = 'var(--danger-color)';
            } else {
                icon.className = 'bi bi-info-circle-fill toast-icon info';
                icon.style.display = 'block';
                icon.style.color = 'var(--info-color)';
            }

            const toastMessage = document.createElement('div');
            toastMessage.className = 'toast-message';
            toastMessage.textContent = message;

            const toastProgress = document.createElement('div');
            toastProgress.className = 'toast-progress';

            toastContent.appendChild(icon);
            toastContent.appendChild(toastMessage);
            toast.appendChild(toastContent);
            toast.appendChild(toastProgress);

            document.body.appendChild(toast);

            // 显示提示
            setTimeout(() => {
                toast.classList.add('show');
            }, 10);

            // 自动隐藏提示
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 更新日期和时间
        function updateDateTime() {
            const now = new Date();

            // 更新日期
            const year = now.getFullYear();
            const month = now.getMonth() + 1;
            const day = now.getDate();
            const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
            const weekday = weekdays[now.getDay()];

            const dateElement = document.getElementById('current-date');
            if (dateElement) {
                dateElement.textContent = `${year.toString().slice(-2)}年${month}月${day}日 星期${weekday}`;
            }

            // 更新时间
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');

            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = `${hours}:${minutes}:${seconds}`;
            }

            // 每秒更新一次
            setTimeout(updateDateTime, 1000);
        }

        // 页面加载完成后启动时钟
        document.addEventListener('DOMContentLoaded', function() {
            updateDateTime();
        });

        // 显示历史版本
        function showVersionHistory() {
            openModal('version-history-modal');
        }

        // 打开续费模态框
        function openRenewModal() {
            openModal('renew-service-modal');
        }

        // 确认续费函数已在modal-functions-fix.js中定义

        // 显示所有公告
        function showAllAnnouncements() {
            openModal('all-announcements-modal');
        }

        // 显示更新版本模态框
        function showUpdateVersion() {
            openModal('update-version-modal');
        }

        // 确认更新版本函数已在modal-functions-fix.js中定义

    </script>

    <!-- 历史版本模态框 -->
    <div class="modal" id="version-history-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>历史版本</h3>
                <button class="close-btn" onclick="closeModal('version-history-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <ul class="version-list">
                    <li class="version-item">
                        <div class="version-header">
                            <span class="version-number">v1.0.2</span>
                            <span class="version-date">2023-06-15</span>
                        </div>
                        <ul class="version-changes">
                            <li>修复了用户登录时的验证问题</li>
                            <li>优化了头像匹配算法，提高匹配准确率</li>
                            <li>改进了UI界面，提升用户体验</li>
                            <li>新增了数据统计功能</li>
                        </ul>
                    </li>
                    <li class="version-item">
                        <div class="version-header">
                            <span class="version-number">v1.0.1</span>
                            <span class="version-date">2023-05-20</span>
                        </div>
                        <ul class="version-changes">
                            <li>修复了多个已知bug</li>
                            <li>优化了系统性能</li>
                            <li>改进了移动端适配</li>
                        </ul>
                    </li>
                    <li class="version-item">
                        <div class="version-header">
                            <span class="version-number">v1.0.0</span>
                            <span class="version-date">2023-05-01</span>
                        </div>
                        <ul class="version-changes">
                            <li>首次发布</li>
                            <li>基础功能实现</li>
                            <li>支持头像上传和匹配</li>
                            <li>用户管理系统</li>
                            <li>基础数据统计</li>
                        </ul>
                    </li>
                </ul>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('version-history-modal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 查看全部公告模态框 -->
    <div class="modal" id="all-announcements-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>所有公告</h3>
                <button class="close-btn" onclick="closeModal('all-announcements-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <ul class="announcement-list">
                    <li class="announcement-item">
                        <div class="announcement-header">
                            <span class="announcement-title">系统更新通知</span>
                            <span class="announcement-date">2023-06-15</span>
                        </div>
                        <p class="announcement-content">尊敬的用户，我们将于2023年6月20日凌晨2:00-4:00进行系统升级维护，届时系统将暂停服务。给您带来的不便，敬请谅解。</p>
                    </li>
                    <li class="announcement-item">
                        <div class="announcement-header">
                            <span class="announcement-title">新功能上线通知</span>
                            <span class="announcement-date">2023-06-10</span>
                        </div>
                        <p class="announcement-content">我们很高兴地通知您，新的数据分析功能已经上线，您可以在控制面板中查看更详细的数据统计和分析报告。</p>
                    </li>
                    <li class="announcement-item">
                        <div class="announcement-header">
                            <span class="announcement-title">安全更新提醒</span>
                            <span class="announcement-date">2023-06-05</span>
                        </div>
                        <p class="announcement-content">为了保障您的账户安全，我们建议您定期修改密码，并开启两步验证功能。如有任何安全问题，请及时联系客服。</p>
                    </li>
                    <li class="announcement-item">
                        <div class="announcement-header">
                            <span class="announcement-title">服务条款更新</span>
                            <span class="announcement-date">2023-06-01</span>
                        </div>
                        <p class="announcement-content">我们已更新服务条款和隐私政策，新的条款将于2023年7月1日生效。请您仔细阅读并了解相关内容的变更。</p>
                    </li>
                    <li class="announcement-item">
                        <div class="announcement-header">
                            <span class="announcement-title">端午节放假通知</span>
                            <span class="announcement-date">2023-05-20</span>
                        </div>
                        <p class="announcement-content">尊敬的用户，我们将于2023年6月22日至6月24日放假，期间客服响应可能会有延迟。祝您端午节快乐！</p>
                    </li>
                </ul>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('all-announcements-modal')">关闭</button>
            </div>
        </div>
    </div>
<script>
    // 控制面板页面专用侧边栏修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('控制面板页面侧边栏修复脚本已加载');

        // 获取DOM元素
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');
        const menuToggle = document.getElementById('menu-toggle');

        if (sidebar && mainContent && menuToggle) {
            console.log('找到控制面板侧边栏元素');

            // 移除所有现有的点击事件
            const oldMenuToggle = menuToggle.cloneNode(true);
            menuToggle.parentNode.replaceChild(oldMenuToggle, menuToggle);

            // 添加新的点击事件
            oldMenuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('控制面板侧边栏切换按钮被点击');

                // 切换侧边栏状态
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');

                // 保存状态到本地存储
                localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
                console.log('控制面板侧边栏状态已保存:', sidebar.classList.contains('collapsed'));
            });

            // 从本地存储中恢复状态
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            console.log('控制面板从本地存储中恢复侧边栏状态:', sidebarCollapsed);

            if (sidebarCollapsed) {
                sidebar.classList.add('collapsed');
                mainContent.classList.add('expanded');
            } else {
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('expanded');
            }

            console.log('控制面板侧边栏切换已修复');
        } else {
            console.error('未找到控制面板侧边栏元素');
        }
    });
</script>

<!-- 网站底部 -->
<?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/site-footer.php'); ?>

<!-- 确保这些脚本在最后加载 -->
<script src="/assets/js/sidebar-toggle-fix.js"></script>
<script src="/assets/js/responsive-layout.js"></script>
<script src="/assets/js/modal-functions-fix.js"></script>
<script src="/assets/js/modal-behavior-fix.js"></script>
<script src="/assets/js/admin-sidebar-fix.js"></script>
<script src="/assets/js/avatar-theme-fix.js"></script>
</body>
</html>