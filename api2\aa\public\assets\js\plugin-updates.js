/**
 * 插件更新通知功能
 * 提供插件更新检查、通知和自动更新功能
 */

// 在DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('插件更新通知功能已加载');
    
    // 初始化插件更新检查
    initPluginUpdates();
    
    // 初始化插件使用统计
    initPluginUsageStats();
});

/**
 * 初始化插件更新检查
 */
function initPluginUpdates() {
    // 检查是否存在插件列表
    const pluginTable = document.querySelector('.table');
    if (!pluginTable) return;
    
    // 添加更新检查按钮
    addUpdateCheckButton();
    
    // 检查插件更新
    checkPluginUpdates();
    
    // 添加自动更新设置
    setupAutoUpdateSettings();
    
    console.log('插件更新检查已初始化');
}

/**
 * 添加更新检查按钮
 */
function addUpdateCheckButton() {
    // 检查是否存在操作栏
    const actionBar = document.querySelector('.action-bar');
    if (!actionBar) return;
    
    // 创建更新检查按钮
    const checkUpdateBtn = document.createElement('button');
    checkUpdateBtn.className = 'btn btn-outline-primary';
    checkUpdateBtn.innerHTML = '<i class="bi bi-arrow-repeat"></i> 检查更新';
    checkUpdateBtn.addEventListener('click', manualCheckUpdates);
    
    // 添加到操作栏
    actionBar.appendChild(checkUpdateBtn);
    
    console.log('更新检查按钮已添加');
}

/**
 * 手动检查插件更新
 */
function manualCheckUpdates() {
    console.log('手动检查插件更新');
    
    // 显示加载动画
    showLoading();
    
    // 模拟API请求
    setTimeout(() => {
        // 检查插件更新
        const updates = checkPluginUpdates(true);
        
        // 隐藏加载动画
        hideLoading();
        
        // 显示结果
        if (updates.length > 0) {
            showToast(`发现 ${updates.length} 个插件可更新`, 'info');
        } else {
            showToast('所有插件已是最新版本', 'success');
        }
    }, 1000);
}

/**
 * 检查插件更新
 * @param {boolean} [showAll=false] - 是否显示所有更新
 * @returns {Array} 可更新的插件列表
 */
function checkPluginUpdates(showAll = false) {
    // 获取所有插件行
    const pluginRows = document.querySelectorAll('tbody tr');
    
    // 可更新的插件列表
    const updatablePlugins = [];
    
    // 检查每个插件
    pluginRows.forEach(row => {
        // 获取插件ID
        const pluginId = row.getAttribute('data-id');
        if (!pluginId) return;
        
        // 获取插件名称
        const nameCell = row.querySelector('td:nth-child(2)');
        if (!nameCell) return;
        
        // 获取插件版本
        const versionCell = row.querySelector('td:nth-child(3)');
        if (!versionCell) return;
        
        // 获取插件操作单元格
        const actionsCell = row.querySelector('td:last-child');
        if (!actionsCell) return;
        
        // 模拟检查更新
        const hasUpdate = checkPluginHasUpdate(pluginId);
        
        if (hasUpdate) {
            // 添加到可更新列表
            updatablePlugins.push({
                id: pluginId,
                name: nameCell.textContent.trim(),
                currentVersion: versionCell.textContent.trim(),
                newVersion: getNewVersion(versionCell.textContent.trim())
            });
            
            // 添加更新标记
            if (!row.querySelector('.update-badge')) {
                // 添加更新标记
                const updateBadge = document.createElement('span');
                updateBadge.className = 'update-badge';
                updateBadge.innerHTML = '<i class="bi bi-arrow-up-circle-fill"></i> 可更新';
                versionCell.appendChild(updateBadge);
                
                // 添加更新按钮
                const updateButton = document.createElement('button');
                updateButton.className = 'btn btn-sm btn-outline-primary update-btn';
                updateButton.innerHTML = '<i class="bi bi-arrow-up-circle"></i> 更新';
                updateButton.setAttribute('data-plugin-id', pluginId);
                updateButton.addEventListener('click', function() {
                    updatePlugin(pluginId);
                });
                
                actionsCell.appendChild(updateButton);
            }
        } else if (showAll) {
            // 显示已是最新版本
            if (!row.querySelector('.up-to-date-badge')) {
                const upToDateBadge = document.createElement('span');
                upToDateBadge.className = 'up-to-date-badge';
                upToDateBadge.innerHTML = '<i class="bi bi-check-circle-fill"></i> 已是最新';
                versionCell.appendChild(upToDateBadge);
                
                // 3秒后移除
                setTimeout(() => {
                    upToDateBadge.remove();
                }, 3000);
            }
        }
    });
    
    // 如果有可更新的插件，显示更新通知
    if (updatablePlugins.length > 0 && !showAll) {
        showUpdateNotification(updatablePlugins);
    }
    
    return updatablePlugins;
}

/**
 * 检查插件是否有更新
 * @param {string} pluginId - 插件ID
 * @returns {boolean} 是否有更新
 */
function checkPluginHasUpdate(pluginId) {
    // 模拟检查更新，根据插件ID决定是否有更新
    return parseInt(pluginId) % 3 === 0;
}

/**
 * 获取新版本号
 * @param {string} currentVersion - 当前版本号
 * @returns {string} 新版本号
 */
function getNewVersion(currentVersion) {
    // 解析版本号
    const versionParts = currentVersion.split('.');
    
    // 增加次版本号
    versionParts[1] = parseInt(versionParts[1]) + 1;
    
    // 返回新版本号
    return versionParts.join('.');
}

/**
 * 显示更新通知
 * @param {Array} plugins - 可更新的插件列表
 */
function showUpdateNotification(plugins) {
    // 检查是否已存在通知
    if (document.getElementById('update-notification')) return;
    
    // 创建通知元素
    const notification = document.createElement('div');
    notification.id = 'update-notification';
    notification.className = 'update-notification';
    
    // 创建通知内容
    let content = `
        <div class="update-notification-header">
            <i class="bi bi-arrow-up-circle-fill"></i>
            <span>插件更新可用</span>
            <button class="close-btn" onclick="document.getElementById('update-notification').remove()">
                <i class="bi bi-x"></i>
            </button>
        </div>
        <div class="update-notification-body">
            <p>发现 ${plugins.length} 个插件有可用更新：</p>
            <ul>
    `;
    
    // 添加插件列表
    plugins.forEach(plugin => {
        content += `
            <li>
                <span class="plugin-name">${plugin.name}</span>
                <span class="version-info">${plugin.currentVersion} → ${plugin.newVersion}</span>
            </li>
        `;
    });
    
    // 添加操作按钮
    content += `
            </ul>
        </div>
        <div class="update-notification-footer">
            <button class="btn btn-sm btn-outline-secondary" onclick="document.getElementById('update-notification').remove()">稍后提醒</button>
            <button class="btn btn-sm btn-primary" onclick="updateAllPlugins()">全部更新</button>
        </div>
    `;
    
    // 设置通知内容
    notification.innerHTML = content;
    
    // 添加到文档
    document.body.appendChild(notification);
    
    // 显示通知
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    console.log('更新通知已显示');
}

/**
 * 更新插件
 * @param {string} pluginId - 插件ID
 */
function updatePlugin(pluginId) {
    console.log(`更新插件: ${pluginId}`);
    
    // 显示加载动画
    showLoading();
    
    // 模拟更新过程
    setTimeout(() => {
        // 获取插件行
        const pluginRow = document.querySelector(`tr[data-id="${pluginId}"]`);
        if (!pluginRow) {
            hideLoading();
            return;
        }
        
        // 获取版本单元格
        const versionCell = pluginRow.querySelector('td:nth-child(3)');
        if (!versionCell) {
            hideLoading();
            return;
        }
        
        // 获取当前版本
        const currentVersion = versionCell.textContent.trim().split(' ')[0];
        
        // 获取新版本
        const newVersion = getNewVersion(currentVersion);
        
        // 更新版本号
        versionCell.textContent = newVersion;
        
        // 移除更新标记
        const updateBadge = pluginRow.querySelector('.update-badge');
        if (updateBadge) {
            updateBadge.remove();
        }
        
        // 移除更新按钮
        const updateButton = pluginRow.querySelector('.update-btn');
        if (updateButton) {
            updateButton.remove();
        }
        
        // 添加已更新标记
        const updatedBadge = document.createElement('span');
        updatedBadge.className = 'updated-badge';
        updatedBadge.innerHTML = '<i class="bi bi-check-circle-fill"></i> 已更新';
        versionCell.appendChild(updatedBadge);
        
        // 3秒后移除已更新标记
        setTimeout(() => {
            updatedBadge.remove();
        }, 3000);
        
        // 隐藏加载动画
        hideLoading();
        
        // 显示成功消息
        showToast(`插件已更新至 ${newVersion}`, 'success');
    }, 1500);
}

/**
 * 更新所有插件
 */
function updateAllPlugins() {
    console.log('更新所有插件');
    
    // 关闭通知
    const notification = document.getElementById('update-notification');
    if (notification) {
        notification.remove();
    }
    
    // 显示加载动画
    showLoading();
    
    // 获取所有可更新的插件
    const updateButtons = document.querySelectorAll('.update-btn');
    const totalUpdates = updateButtons.length;
    let completedUpdates = 0;
    
    // 如果没有可更新的插件
    if (totalUpdates === 0) {
        hideLoading();
        showToast('没有可更新的插件', 'info');
        return;
    }
    
    // 更新每个插件
    updateButtons.forEach((button, index) => {
        const pluginId = button.getAttribute('data-plugin-id');
        
        // 延迟更新，避免同时更新
        setTimeout(() => {
            // 获取插件行
            const pluginRow = document.querySelector(`tr[data-id="${pluginId}"]`);
            if (!pluginRow) return;
            
            // 获取版本单元格
            const versionCell = pluginRow.querySelector('td:nth-child(3)');
            if (!versionCell) return;
            
            // 获取当前版本
            const currentVersion = versionCell.textContent.trim().split(' ')[0];
            
            // 获取新版本
            const newVersion = getNewVersion(currentVersion);
            
            // 更新版本号
            versionCell.textContent = newVersion;
            
            // 移除更新标记
            const updateBadge = pluginRow.querySelector('.update-badge');
            if (updateBadge) {
                updateBadge.remove();
            }
            
            // 移除更新按钮
            button.remove();
            
            // 添加已更新标记
            const updatedBadge = document.createElement('span');
            updatedBadge.className = 'updated-badge';
            updatedBadge.innerHTML = '<i class="bi bi-check-circle-fill"></i> 已更新';
            versionCell.appendChild(updatedBadge);
            
            // 3秒后移除已更新标记
            setTimeout(() => {
                updatedBadge.remove();
            }, 3000);
            
            // 更新完成计数
            completedUpdates++;
            
            // 如果所有更新都完成了
            if (completedUpdates === totalUpdates) {
                // 隐藏加载动画
                hideLoading();
                
                // 显示成功消息
                showToast(`已成功更新 ${totalUpdates} 个插件`, 'success');
            }
        }, index * 500); // 每个插件延迟500毫秒
    });
}

/**
 * 设置自动更新设置
 */
function setupAutoUpdateSettings() {
    // 检查是否存在设置按钮
    const settingsButton = document.querySelector('.settings-btn');
    if (!settingsButton) {
        // 创建设置按钮
        const actionBar = document.querySelector('.action-bar');
        if (!actionBar) return;
        
        const settingsBtn = document.createElement('button');
        settingsBtn.className = 'btn btn-outline-secondary settings-btn';
        settingsBtn.innerHTML = '<i class="bi bi-gear"></i> 设置';
        settingsBtn.addEventListener('click', openUpdateSettings);
        
        actionBar.appendChild(settingsBtn);
    } else {
        // 添加点击事件
        settingsButton.addEventListener('click', openUpdateSettings);
    }
    
    console.log('自动更新设置已初始化');
}

/**
 * 打开更新设置对话框
 */
function openUpdateSettings() {
    console.log('打开更新设置对话框');
    
    // 检查是否存在设置对话框
    let settingsModal = document.getElementById('update-settings-modal');
    
    // 如果不存在，创建一个
    if (!settingsModal) {
        settingsModal = document.createElement('div');
        settingsModal.className = 'modal';
        settingsModal.id = 'update-settings-modal';
        settingsModal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>插件更新设置</h3>
                    <button class="close-btn" onclick="closeModal('update-settings-modal')"><i class="bi bi-x-lg"></i></button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label class="switch-label">
                            <span>自动检查更新</span>
                            <label class="switch">
                                <input type="checkbox" id="auto-check-updates" checked>
                                <span class="slider round"></span>
                            </label>
                        </label>
                        <p class="form-text">每天自动检查插件更新</p>
                    </div>
                    <div class="form-group">
                        <label class="switch-label">
                            <span>自动更新插件</span>
                            <label class="switch">
                                <input type="checkbox" id="auto-update-plugins">
                                <span class="slider round"></span>
                            </label>
                        </label>
                        <p class="form-text">发现更新时自动安装</p>
                    </div>
                    <div class="form-group">
                        <label for="update-frequency">检查频率</label>
                        <select id="update-frequency" class="form-control">
                            <option value="daily">每天</option>
                            <option value="weekly">每周</option>
                            <option value="monthly">每月</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal('update-settings-modal')">取消</button>
                    <button class="btn btn-primary" onclick="saveUpdateSettings()">保存</button>
                </div>
            </div>
        `;
        document.body.appendChild(settingsModal);
    }
    
    // 显示设置对话框
    openModal('update-settings-modal');
}

/**
 * 保存更新设置
 */
function saveUpdateSettings() {
    // 获取设置值
    const autoCheck = document.getElementById('auto-check-updates').checked;
    const autoUpdate = document.getElementById('auto-update-plugins').checked;
    const frequency = document.getElementById('update-frequency').value;
    
    console.log(`保存更新设置: 自动检查=${autoCheck}, 自动更新=${autoUpdate}, 频率=${frequency}`);
    
    // 保存设置
    localStorage.setItem('plugin-auto-check', autoCheck);
    localStorage.setItem('plugin-auto-update', autoUpdate);
    localStorage.setItem('plugin-update-frequency', frequency);
    
    // 关闭设置对话框
    closeModal('update-settings-modal');
    
    // 显示成功消息
    showToast('更新设置已保存', 'success');
}
