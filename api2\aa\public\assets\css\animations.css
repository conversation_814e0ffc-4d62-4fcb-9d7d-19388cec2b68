/**
 * 情侣头像匹配系统 - 高级动效样式
 */

/* 悬停下划线滑动效果 */
.hover-underline-animation {
  position: relative;
  display: inline-block;
}

.hover-underline-animation::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: var(--primary-color);
  transition: width 0.3s ease-in-out;
}

.hover-underline-animation:hover::after {
  width: 100%;
}

/* 子菜单滑动展开效果 */
.submenu {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s cubic-bezier(0, 1, 0, 1);
}

.submenu.open {
  max-height: 1000px;
  transition: max-height 0.5s cubic-bezier(0.9, 0, 0.3, 0.9);
}

/* 卡片悬停3D效果 */
.card-3d {
  transition: transform 0.3s ease-out, box-shadow 0.3s ease-out;
  transform-style: preserve-3d;
  perspective: 1000px;
}

.card-3d:hover {
  transform: translateY(-10px) rotateX(5deg) rotateY(5deg);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.05);
}

/* 按钮波纹效果 */
.btn-ripple {
  position: relative;
  overflow: hidden;
}

.btn-ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.btn-ripple:focus:not(:active)::after {
  animation: ripple 1s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0) translate(-50%, -50%);
    opacity: 0.5;
  }
  100% {
    transform: scale(100, 100) translate(-50%, -50%);
    opacity: 0;
  }
}

/* 页面切换淡入效果 */
.page-transition {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 脉冲动画 */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 107, 149, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(255, 107, 149, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 107, 149, 0);
  }
}

/* 闪光效果 */
.shine {
  position: relative;
  overflow: hidden;
}

.shine::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 100%
  );
  transform: skewX(-25deg);
}

.shine:hover::before {
  animation: shine 0.75s;
}

@keyframes shine {
  100% {
    left: 125%;
  }
}

/* 渐变边框效果 */
.gradient-border {
  position: relative;
  border-radius: var(--border-radius);
  padding: 0.25rem;
}

.gradient-border::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: var(--border-radius);
  padding: 2px;
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  -webkit-mask: 
    linear-gradient(#fff 0 0) content-box, 
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
}

/* 打字机效果 */
.typewriter {
  overflow: hidden;
  border-right: 0.15em solid var(--primary-color);
  white-space: nowrap;
  margin: 0 auto;
  letter-spacing: 0.15em;
  animation: 
    typing 3.5s steps(40, end),
    blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

@keyframes blink-caret {
  from, to { border-color: transparent }
  50% { border-color: var(--primary-color) }
}

/* 图片悬停缩放效果 */
.img-hover-zoom {
  overflow: hidden;
  border-radius: var(--border-radius);
}

.img-hover-zoom img {
  transition: transform 0.5s ease;
}

.img-hover-zoom:hover img {
  transform: scale(1.1);
}

/* 滚动渐入效果 */
.fade-in-section {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in-section.is-visible {
  opacity: 1;
  transform: translateY(0);
}

/* 3D翻转卡片 */
.flip-card {
  perspective: 1000px;
  height: 200px;
}

.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.flip-card:hover .flip-card-inner {
  transform: rotateY(180deg);
}

.flip-card-front, .flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
}

.flip-card-front {
  background-color: var(--neutral-100);
  color: var(--neutral-800);
}

.flip-card-back {
  background-color: var(--primary-color);
  color: white;
  transform: rotateY(180deg);
}

/* 浮动标签效果 */
.float-label {
  position: relative;
}

.float-label input, .float-label textarea {
  padding: 1.5rem 1rem 0.5rem;
}

.float-label label {
  position: absolute;
  top: 0;
  left: 1rem;
  font-size: 0.75rem;
  opacity: 0;
  transform: translateY(0);
  transition: all 0.2s ease-out;
}

.float-label input:focus + label,
.float-label input:not(:placeholder-shown) + label,
.float-label textarea:focus + label,
.float-label textarea:not(:placeholder-shown) + label {
  opacity: 1;
  transform: translateY(0.5rem);
}

/* 滑动开关 */
.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--neutral-400);
  transition: .4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--primary-color);
}

input:focus + .slider {
  box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* 菜单展开动画 */
.menu-item {
  position: relative;
  overflow: hidden;
}

.menu-item-content {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

.menu-item-icon {
  margin-right: 0.75rem;
  transition: transform 0.3s ease;
}

.menu-item:hover .menu-item-icon {
  transform: scale(1.2);
}

.menu-item-arrow {
  margin-left: auto;
  transition: transform 0.3s ease;
}

.menu-item.open .menu-item-arrow {
  transform: rotate(90deg);
}

/* 通知徽章动画 */
.badge-animated {
  position: relative;
  display: inline-block;
}

.badge-animated::after {
  content: attr(data-count);
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: var(--danger-color);
  color: white;
  font-size: 0.75rem;
  min-width: 18px;
  height: 18px;
  line-height: 18px;
  text-align: center;
  border-radius: 9px;
  animation: badge-pulse 2s infinite;
}

@keyframes badge-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(234, 84, 85, 0.7);
  }
  70% {
    transform: scale(1.1);
    box-shadow: 0 0 0 5px rgba(234, 84, 85, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(234, 84, 85, 0);
  }
}
