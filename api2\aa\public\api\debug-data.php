<?php
// 调试数据库数据

// 开启会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 设置响应头
header('Content-Type: application/json');

// 检查用户是否已登录
if (!isset($_SESSION['username'])) {
    http_response_code(401);
    echo json_encode(['error' => '未授权访问']);
    exit;
}

try {
    // 包含数据库配置
    $configPath = __DIR__ . '/../../config/config.php';
    $config = require_once($configPath);
    
    $dbConfig = $config['database'];
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $prefix = $dbConfig['prefix'];
    
    // 检查各表的数据
    $result = [];
    
    // 1. 检查yh_record表
    $stmt = $pdo->prepare("SELECT COUNT(*) as total, MIN(intime) as min_time, MAX(intime) as max_time FROM {$prefix}record");
    $stmt->execute();
    $recordData = $stmt->fetch(PDO::FETCH_ASSOC);
    $result['record'] = [
        'total' => $recordData['total'],
        'min_time' => $recordData['min_time'],
        'max_time' => $recordData['max_time'],
        'min_date' => date('Y-m-d H:i:s', $recordData['min_time']),
        'max_date' => date('Y-m-d H:i:s', $recordData['max_time'])
    ];
    
    // 2. 检查yh_business表
    $stmt = $pdo->prepare("SELECT type, COUNT(*) as count FROM {$prefix}business GROUP BY type");
    $stmt->execute();
    $businessData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $result['business'] = $businessData;
    
    // 3. 检查yh_order表
    $stmt = $pdo->prepare("SELECT COUNT(*) as total, MIN(intime) as min_time, MAX(intime) as max_time FROM {$prefix}order");
    $stmt->execute();
    $orderData = $stmt->fetch(PDO::FETCH_ASSOC);
    $result['order'] = [
        'total' => $orderData['total'],
        'min_time' => $orderData['min_time'],
        'max_time' => $orderData['max_time'],
        'min_date' => date('Y-m-d H:i:s', $orderData['min_time']),
        'max_date' => date('Y-m-d H:i:s', $orderData['max_time'])
    ];
    
    // 4. 检查今天的数据
    $today = date('Y-m-d');
    $startTime = strtotime("$today 00:00:00");
    $endTime = strtotime("$today 23:59:59");
    
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM {$prefix}record WHERE intime >= ? AND intime <= ?");
    $stmt->execute([$startTime, $endTime]);
    $todayRecord = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM {$prefix}order WHERE intime >= ? AND intime <= ?");
    $stmt->execute([$startTime, $endTime]);
    $todayOrder = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $result['today'] = [
        'date' => $today,
        'start_time' => $startTime,
        'end_time' => $endTime,
        'record_count' => $todayRecord['count'],
        'order_count' => $todayOrder['count']
    ];
    
    echo json_encode($result, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
