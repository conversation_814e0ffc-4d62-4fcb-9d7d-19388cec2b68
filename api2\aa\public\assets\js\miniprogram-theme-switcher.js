/**
 * 情侣头像匹配系统 - 主题切换器
 */

class ThemeSwitcher {
    constructor() {
        this.init();
    }

    init() {
        // 初始化主题设置
        this.loadSavedTheme();

        // 创建主题设置面板
        this.createThemeSettingsPanel();

        // 绑定事件
        this.bindEvents();

        console.log('主题切换器已初始化');
    }

    loadSavedTheme() {
        // 加载保存的主题设置
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            document.documentElement.setAttribute('data-theme', savedTheme);
        }

        // 加载保存的颜色方案
        const savedColor = localStorage.getItem('primary-color');
        if (savedColor) {
            this.setColorScheme(savedColor);

            // 标记当前选中的颜色
            setTimeout(() => {
                const colorOptions = document.querySelectorAll('.color-option');
                colorOptions.forEach(option => {
                    if (option.getAttribute('data-color') === savedColor) {
                        option.classList.add('active');
                    }
                });
            }, 100);
        }
    }

    createThemeSettingsPanel() {
        // 创建主题设置面板
        const themeSettings = document.createElement('div');
        themeSettings.className = 'theme-settings';
        themeSettings.innerHTML = `
            <div class="theme-settings-header">
                <h3>主题设置</h3>
                <button class="theme-settings-close">
                    <i class="bi bi-x"></i>
                </button>
            </div>
            <div class="theme-option">
                <div class="theme-option-title">主题颜色</div>
                <div class="color-picker">
                    <div class="color-option" data-color="#ff6b95" style="background-color: #ff6b95;"></div>
                    <div class="color-option" data-color="#8a6fd6" style="background-color: #8a6fd6;"></div>
                    <div class="color-option" data-color="#28c76f" style="background-color: #28c76f;"></div>
                    <div class="color-option" data-color="#00cfe8" style="background-color: #00cfe8;"></div>
                    <div class="color-option" data-color="#ff9f43" style="background-color: #ff9f43;"></div>
                    <div class="color-option" data-color="#ea5455" style="background-color: #ea5455;"></div>
                    <div class="color-option" data-color="#4b4b4b" style="background-color: #4b4b4b;"></div>
                    <div class="color-option" data-color="#3b82f6" style="background-color: #3b82f6;"></div>
                </div>
            </div>
        `;

        // 添加到文档
        document.body.appendChild(themeSettings);
    }

    bindEvents() {
        // 颜色选项点击事件
        const colorOptions = document.querySelectorAll('.color-option');
        colorOptions.forEach(option => {
            option.addEventListener('click', () => {
                // 移除所有颜色选项的激活状态
                colorOptions.forEach(opt => opt.classList.remove('active'));

                // 激活当前选中的颜色选项
                option.classList.add('active');

                // 设置颜色方案
                const color = option.getAttribute('data-color');
                this.setColorScheme(color);

                // 保存颜色方案到本地存储
                localStorage.setItem('primary-color', color);

                // 触发自定义事件
                const event = new CustomEvent('themeChanged', { detail: { color: color } });
                document.dispatchEvent(event);

                // 显示提示
                if (typeof showToast === 'function') {
                    showToast('success', '主题颜色已更新');
                }
            });
        });

        // 主题设置面板切换按钮点击事件
        const themeSettingsToggles = document.querySelectorAll('.theme-settings-toggle');
        const themeSettings = document.querySelector('.theme-settings');

        // 关闭按钮点击事件
        const themeSettingsClose = document.querySelector('.theme-settings-close');
        if (themeSettingsClose) {
            themeSettingsClose.addEventListener('click', (e) => {
                e.stopPropagation(); // 阻止事件冒泡
                themeSettings.classList.remove('show');
            });
        }

        themeSettingsToggles.forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                e.stopPropagation(); // 阻止事件冒泡
                themeSettings.classList.toggle('show');

                // 如果是显示状态，添加动画效果
                if (themeSettings.classList.contains('show')) {
                    themeSettings.style.animation = 'slideIn 0.3s forwards';
                } else {
                    themeSettings.style.animation = 'slideOut 0.3s forwards';
                }
            });
        });

        // 点击页面任意处关闭配色面板
        document.addEventListener('click', (e) => {
            // 如果点击的不是主题设置面板内的元素，也不是切换按钮
            if (themeSettings.classList.contains('show') &&
                !themeSettings.contains(e.target) &&
                !Array.from(themeSettingsToggles).some(toggle => toggle === e.target || toggle.contains(e.target))) {

                themeSettings.classList.remove('show');
                themeSettings.style.animation = 'slideOut 0.3s forwards';
            }
        });

        // 阻止主题设置面板内的点击事件冒泡
        themeSettings.addEventListener('click', (e) => {
            e.stopPropagation();
        });
    }

    setColorScheme(color) {
        console.log('设置主题颜色:', color);

        // 设置主色调
        document.documentElement.style.setProperty('--primary-color', color);

        // 设置主色调的RGB值（用于透明度）
        const rgbColor = this.hexToRgb(color);
        document.documentElement.style.setProperty('--primary-color-rgb', rgbColor);

        // 根据主色调生成辅助色
        const secondaryColor = this.adjustColor(color, 30);
        document.documentElement.style.setProperty('--secondary-color', secondaryColor);

        // 生成暗色版本（用于悬停效果）
        const darkColor = this.adjustColor(color, -30);
        document.documentElement.style.setProperty('--primary-dark', darkColor);

        // 创建或获取主题样式元素
        let themeStyle = document.getElementById('theme-dynamic-style');
        if (!themeStyle) {
            themeStyle = document.createElement('style');
            themeStyle.id = 'theme-dynamic-style';
            document.head.appendChild(themeStyle);
        }

        // 设置动态样式
        themeStyle.textContent = `
            /* 按钮样式 */
            .btn-primary {
                color: #ffffff !important;
                background: linear-gradient(135deg, ${color} 0%, ${secondaryColor} 100%) !important;
                border-color: ${color} !important;
            }

            .btn-primary:hover {
                color: #ffffff !important;
                background: linear-gradient(135deg, ${darkColor} 0%, ${color} 100%) !important;
                border-color: ${darkColor} !important;
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(${rgbColor}, 0.3);
            }

            .btn-outline-primary {
                color: ${color} !important;
                border-color: ${color} !important;
                background-color: transparent !important;
            }

            .btn-outline-primary:hover {
                color: #ffffff !important;
                background-color: ${color} !important;
                border-color: ${color} !important;
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(${rgbColor}, 0.3);
            }

            /* 侧边栏样式 */
            .miniprogram-sidebar {
                background: linear-gradient(135deg, ${color} 0%, ${secondaryColor} 100%) !important;
            }

            .sidebar-menu-item.active {
                background-color: rgba(255, 255, 255, 0.2) !important;
                border-left-color: var(--white-color) !important;
            }

            /* 卡片样式 */
            .stats-card {
                border-top-color: ${color} !important;
            }

            .stats-card:hover .stats-card-value {
                color: ${color} !important;
            }

            .stats-card-subtitle:before {
                background-color: ${color} !important;
            }

            .chart-container {
                border-left-color: ${color} !important;
            }

            .chart-title:before {
                background-color: ${color} !important;
            }

            /* 其他元素 */
            .breadcrumb-item a {
                color: ${color} !important;
            }

            .spinner {
                border-top-color: ${color} !important;
            }

            .platform-info h2:after {
                background-color: ${color} !important;
            }

            /* 导航和标签页 */
            .nav-tabs .nav-link.active {
                color: ${color} !important;
                border-color: ${color} !important;
            }

            .nav-link:hover {
                color: ${color} !important;
            }

            /* 底部链接 */
            .beian-link:hover {
                color: ${color} !important;
            }

            /* 主题颜色选择器 */
            .color-option.active {
                border: 3px solid #fff !important;
                box-shadow: 0 0 0 2px ${color} !important;
            }

            /* 主题设置面板切换按钮 */
            .theme-settings-toggle {
                background-color: ${color} !important;
                border-color: ${color} !important;
            }
        `;

        // 直接修改侧边栏样式
        const sidebar = document.querySelector('.miniprogram-sidebar');
        if (sidebar) {
            console.log('直接修改侧边栏样式:', color);
            sidebar.style.background = `linear-gradient(135deg, ${color} 0%, ${secondaryColor} 100%)`;

            // 强制重绘
            sidebar.style.display = 'none';
            setTimeout(() => {
                sidebar.style.display = '';
            }, 10);
        }
    }

    hexToRgb(hex) {
        // 移除#号
        hex = hex.replace('#', '');

        // 解析RGB值
        const r = parseInt(hex.substring(0, 2), 16);
        const g = parseInt(hex.substring(2, 4), 16);
        const b = parseInt(hex.substring(4, 6), 16);

        return `${r}, ${g}, ${b}`;
    }

    adjustColor(color, amount) {
        // 移除#号
        color = color.replace('#', '');

        // 将十六进制颜色转换为RGB
        let r = parseInt(color.substring(0, 2), 16);
        let g = parseInt(color.substring(2, 4), 16);
        let b = parseInt(color.substring(4, 6), 16);

        // 调整RGB值
        r = Math.max(0, Math.min(255, r + amount));
        g = Math.max(0, Math.min(255, g + amount));
        b = Math.max(0, Math.min(255, b + amount));

        // 将RGB转换回十六进制
        return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    }
}

// 页面加载完成后初始化主题切换器
document.addEventListener('DOMContentLoaded', () => {
    new ThemeSwitcher();
});
