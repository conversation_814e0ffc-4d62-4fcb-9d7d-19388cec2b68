/**
 * 自动刷新通知系统
 * 专门处理成功通知消息的自动刷新功能
 */

// 创建全新的自动刷新通知函数
window.showAutoRefreshToast = function(message, options = {}) {
    console.log('🚀 showAutoRefreshToast被调用:', { message, options });

    // 默认类型为success
    const type = options.type || 'success';
    
    // 移除现有的提示
    const existingToasts = document.querySelectorAll('.auto-refresh-toast');
    existingToasts.forEach(toast => {
        if (document.body.contains(toast)) {
            document.body.removeChild(toast);
        }
    });

    // 创建新提示
    const toast = document.createElement('div');
    toast.className = 'auto-refresh-toast';

    // 直接设置样式属性，确保样式生效
    toast.style.position = 'fixed';
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.width = '350px';
    toast.style.backgroundColor = '#fff';
    toast.style.borderRadius = '8px';
    toast.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
    toast.style.zIndex = '9999';
    toast.style.opacity = '0';
    toast.style.visibility = 'hidden';
    toast.style.transform = 'translateX(30px)';
    toast.style.transition = 'all 0.3s ease';
    toast.style.border = '2px solid #28a745'; // 添加绿色边框便于识别

    const toastContent = document.createElement('div');
    toastContent.style.display = 'flex';
    toastContent.style.alignItems = 'center';
    toastContent.style.padding = '15px';

    const icon = document.createElement('i');
    icon.style.fontSize = '1.5rem';
    icon.style.marginRight = '15px';
    icon.style.display = 'inline-block';
    icon.style.lineHeight = '1';

    // 根据类型设置图标和颜色
    if (type === 'success') {
        icon.className = 'bi bi-check-circle-fill';
        icon.style.color = '#28a745';
        toast.style.border = '2px solid #28a745';
    } else if (type === 'error') {
        icon.className = 'bi bi-x-circle-fill';
        icon.style.color = '#dc3545';
        toast.style.border = '2px solid #dc3545';
    } else if (type === 'warning') {
        icon.className = 'bi bi-exclamation-triangle-fill';
        icon.style.color = '#ffc107';
        toast.style.border = '2px solid #ffc107';
    } else {
        icon.className = 'bi bi-info-circle-fill';
        icon.style.color = '#17a2b8';
        toast.style.border = '2px solid #17a2b8';
    }

    const toastMessage = document.createElement('div');
    toastMessage.style.flex = '1';
    toastMessage.style.fontWeight = '500';
    toastMessage.style.color = '#333';
    toastMessage.textContent = message;

    const toastProgress = document.createElement('div');
    toastProgress.style.height = '3px';
    toastProgress.style.width = '100%';
    toastProgress.style.animation = 'autoRefreshProgress 1s linear';
    toastProgress.style.transformOrigin = 'left';

    // 根据类型设置进度条颜色
    if (type === 'success') {
        toastProgress.style.backgroundColor = '#28a745';
    } else if (type === 'error') {
        toastProgress.style.backgroundColor = '#dc3545';
    } else if (type === 'warning') {
        toastProgress.style.backgroundColor = '#ffc107';
    } else {
        toastProgress.style.backgroundColor = '#17a2b8';
    }

    // 添加CSS动画
    if (!document.getElementById('auto-refresh-toast-styles')) {
        const style = document.createElement('style');
        style.id = 'auto-refresh-toast-styles';
        style.textContent = `
            @keyframes autoRefreshProgress {
                0% { width: 100%; }
                80% { width: 20%; }
                100% { width: 0%; }
            }
            .auto-refresh-toast.show {
                opacity: 1;
                visibility: visible;
                transform: translateX(0);
            }
        `;
        document.head.appendChild(style);
    }

    toastContent.appendChild(icon);
    toastContent.appendChild(toastMessage);
    toast.appendChild(toastContent);
    toast.appendChild(toastProgress);

    document.body.appendChild(toast);

    // 显示提示
    setTimeout(() => {
        toast.style.opacity = '1';
        toast.style.visibility = 'visible';
        toast.style.transform = 'translateX(0)';
        console.log('🎨 Toast样式已更新为可见');
    }, 10);

    console.log('✅ Toast已创建并显示');

    // 检查是否需要自动刷新
    if (options.autoRefresh || options.refreshCallback) {
        console.log('🔄 启用自动刷新逻辑');
        // 在80%时刷新页面（800ms）
        setTimeout(() => {
            console.log('🔄 开始刷新页面');
            if (options.refreshCallback && typeof options.refreshCallback === 'function') {
                options.refreshCallback();
            } else {
                location.reload();
            }
        }, 800);
    } else {
        console.log('📋 普通显示，3秒后自动消失');
        // 普通显示，3秒后自动消失
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.visibility = 'hidden';
            toast.style.transform = 'translateX(30px)';
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }
};

// 创建统一样式的普通通知函数（不自动刷新）
function showUnifiedToast(message, type = 'success', options = {}) {
    console.log('📋 showUnifiedToast被调用:', { message, type, options });

    // 移除现有的提示
    const existingToasts = document.querySelectorAll('.auto-refresh-toast');
    existingToasts.forEach(toast => {
        if (document.body.contains(toast)) {
            document.body.removeChild(toast);
        }
    });

    // 创建新提示 - 使用与自动刷新通知相同的样式
    const toast = document.createElement('div');
    toast.className = 'auto-refresh-toast';

    // 统一样式设置
    toast.style.position = 'fixed';
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.width = '350px';
    toast.style.backgroundColor = '#fff';
    toast.style.borderRadius = '8px';
    toast.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
    toast.style.zIndex = '9999';
    toast.style.opacity = '0';
    toast.style.visibility = 'hidden';
    toast.style.transform = 'translateX(30px)';
    toast.style.transition = 'all 0.3s ease';

    const toastContent = document.createElement('div');
    toastContent.style.display = 'flex';
    toastContent.style.alignItems = 'center';
    toastContent.style.padding = '15px';

    const icon = document.createElement('i');
    icon.style.fontSize = '1.5rem';
    icon.style.marginRight = '15px';
    icon.style.display = 'inline-block';
    icon.style.lineHeight = '1';

    // 根据类型设置图标和颜色
    if (type === 'success') {
        icon.className = 'bi bi-check-circle-fill';
        icon.style.color = '#28a745';
        toast.style.border = '2px solid #28a745';
    } else if (type === 'error') {
        icon.className = 'bi bi-x-circle-fill';
        icon.style.color = '#dc3545';
        toast.style.border = '2px solid #dc3545';
    } else if (type === 'warning') {
        icon.className = 'bi bi-exclamation-triangle-fill';
        icon.style.color = '#ffc107';
        toast.style.border = '2px solid #ffc107';
    } else {
        icon.className = 'bi bi-info-circle-fill';
        icon.style.color = '#17a2b8';
        toast.style.border = '2px solid #17a2b8';
    }

    const toastMessage = document.createElement('div');
    toastMessage.style.flex = '1';
    toastMessage.style.fontWeight = '500';
    toastMessage.style.color = '#333';
    toastMessage.textContent = message;

    // 添加进度条（与自动刷新通知一致）
    const toastProgress = document.createElement('div');
    toastProgress.style.height = '3px';
    toastProgress.style.width = '100%';
    toastProgress.style.transformOrigin = 'left';
    toastProgress.style.animation = 'unifiedToastProgress 3s linear';

    // 根据类型设置进度条颜色
    if (type === 'success') {
        toastProgress.style.backgroundColor = '#28a745';
    } else if (type === 'error') {
        toastProgress.style.backgroundColor = '#dc3545';
    } else if (type === 'warning') {
        toastProgress.style.backgroundColor = '#ffc107';
    } else {
        toastProgress.style.backgroundColor = '#17a2b8';
    }

    // 添加普通通知的CSS动画
    if (!document.getElementById('unified-toast-styles')) {
        const style = document.createElement('style');
        style.id = 'unified-toast-styles';
        style.textContent = `
            @keyframes unifiedToastProgress {
                0% { width: 100%; }
                100% { width: 0%; }
            }
        `;
        document.head.appendChild(style);
    }

    toastContent.appendChild(icon);
    toastContent.appendChild(toastMessage);
    toast.appendChild(toastContent);
    toast.appendChild(toastProgress);

    document.body.appendChild(toast);

    // 显示提示
    setTimeout(() => {
        toast.style.opacity = '1';
        toast.style.visibility = 'visible';
        toast.style.transform = 'translateX(0)';
        console.log('🎨 普通Toast样式已更新为可见');
    }, 10);

    // 设置显示时间（默认3秒）
    const duration = options.duration || 3000;

    // 自动隐藏提示
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.visibility = 'hidden';
        toast.style.transform = 'translateX(30px)';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, duration);
}

// 覆盖原有的showToast函数，专门处理成功消息的自动刷新
function enhanceShowToast() {
    // 保存原始的showToast函数
    const originalShowToast = window.showToast;
    
    // 重写showToast函数 - 统一使用自动刷新通知系统
    window.showToast = function(message, type = 'success', options = {}) {
        console.log('🎯 增强版showToast被调用:', { message, type, options });

        // 兼容旧的API（第三个参数是boolean）
        if (typeof options === 'boolean') {
            options = { autoRefresh: options };
        }

        // 统一使用showAutoRefreshToast，但根据是否需要自动刷新决定行为
        if (options.autoRefresh) {
            console.log('🚀 使用自动刷新通知');
            showAutoRefreshToast(message, { ...options, type: type });
        } else {
            console.log('📋 使用普通通知（统一样式）');
            // 使用showAutoRefreshToast但不自动刷新，只是为了统一样式
            showUnifiedToast(message, type, options);
        }
    };
    
    console.log('🔧 showToast函数已增强，支持自动刷新');
}

// 当文档加载完成后，增强showToast函数
document.addEventListener('DOMContentLoaded', function() {
    console.log('📋 自动刷新通知系统正在初始化...');
    
    // 延迟一点时间，确保其他脚本都加载完成
    setTimeout(() => {
        enhanceShowToast();
        console.log('✅ 自动刷新通知系统初始化完成');
    }, 100);
});

// 也在页面加载完成后再次尝试
window.addEventListener('load', function() {
    setTimeout(() => {
        enhanceShowToast();
        console.log('🔄 页面加载完成，重新增强showToast函数');
    }, 200);
});
