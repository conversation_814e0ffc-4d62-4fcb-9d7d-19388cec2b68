<?php

/**
 * Migration for creating the users table
 * This table stores user information with tenant isolation
 */
class CreateUsersTable
{
    /**
     * Run the migration
     */
    public function up()
    {
        $sql = "CREATE TABLE IF NOT EXISTS `users` (
            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            `tenant_id` bigint(20) UNSIGNED NOT NULL COMMENT 'Reference to tenants table',
            `openid` varchar(64) DEFAULT NULL COMMENT 'WeChat/Alipay OpenID',
            `unionid` varchar(64) DEFAULT NULL COMMENT 'WeChat UnionID',
            `phone` varchar(20) DEFAULT NULL COMMENT 'User phone number',
            `email` varchar(100) DEFAULT NULL COMMENT 'User email',
            `nickname` varchar(32) DEFAULT NULL COMMENT 'User nickname',
            `avatar` varchar(255) DEFAULT NULL COMMENT 'User avatar URL',
            `is_vip` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=not VIP, 1=VIP',
            `vip_expire_time` datetime DEFAULT NULL COMMENT 'VIP expiration time',
            `match_count` int(11) NOT NULL DEFAULT 5 COMMENT 'Remaining match count',
            `download_count` int(11) NOT NULL DEFAULT 3 COMMENT 'Remaining download count',
            `platform` varchar(20) DEFAULT NULL COMMENT 'wechat/alipay/h5',
            `last_login_time` datetime DEFAULT NULL COMMENT 'Last login time',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `tenant_id` (`tenant_id`),
            KEY `openid_platform` (`openid`, `platform`),
            KEY `phone` (`phone`),
            KEY `email` (`email`),
            CONSTRAINT `users_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        
        return $sql;
    }

    /**
     * Reverse the migration
     */
    public function down()
    {
        return "DROP TABLE IF EXISTS `users`;";
    }
}
