/**
 * 情侣头像匹配系统 - 主题切换器样式
 */

/* 主题设置面板 */
.theme-settings {
    position: fixed;
    top: 80px;
    right: -300px;
    width: 300px;
    background-color: var(--white-color, #ffffff);
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    z-index: 9999;
    padding: 0;
    transition: all 0.3s ease;
    overflow: hidden;
}

/* 主题设置切换按钮 */
.theme-settings-toggle {
    position: fixed;
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: move;
    color: white;
    font-size: 20px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    z-index: 10000;
    right: 20px;
    bottom: 20px;
    transition: all 0.3s ease;
}

.theme-settings-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
}

/* 拖动时的样式 */
.theme-settings-toggle.dragging {
    opacity: 0.8;
    box-shadow: 0 0 15px var(--primary-color);
    transition: none;
}

.theme-settings.show {
    right: 20px;
}

.theme-settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--light-color, #f0f0f0);
    background-color: var(--primary-color);
    color: white;
}

.theme-settings-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.theme-settings-close {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    transition: all 0.2s;
}

.theme-settings-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.theme-option {
    padding: 20px;
}

.theme-option-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 15px;
    color: var(--dark-color, #333);
}

/* 颜色选择器 */
.color-picker {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
}

.color-option {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 创建一个更大的点击区域 */
.color-option:before {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    border-radius: 50%;
    cursor: pointer;
}

.color-option:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.color-option.active {
    border: 3px solid #fff;
    box-shadow: 0 0 0 2px var(--primary-color);
}

.color-option.active:after {
    content: '✓';
    color: white;
    font-size: 18px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 动画 */
@keyframes slideIn {
    from { right: -300px; }
    to { right: 20px; }
}

@keyframes slideOut {
    from { right: 20px; }
    to { right: -300px; }
}
