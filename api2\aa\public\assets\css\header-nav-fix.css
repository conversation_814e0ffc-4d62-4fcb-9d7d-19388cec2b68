/**
 * 头部导航修复样式
 * 修复所有页面的头像和下拉菜单样式问题
 */

/* 重置所有页面的头像和下拉菜单样式 */
.header .user-info,
.header .user-avatar,
.header .user-name,
.header .dropdown-content,
.header .dropdown-content a,
.header .dropdown-content a i {
    all: initial !important;
    box-sizing: border-box !important;
    font-family: "Microsoft YaHei", sans-serif !important;
}

/* 确保用户头像样式一致 */
.header .user-info {
    display: flex !important;
    align-items: center !important;
    position: relative !important;
    cursor: pointer !important;
    z-index: 1000 !important; /* 确保下拉菜单在其他元素之上 */
}

.header .user-avatar {
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    background-color: var(--primary-color) !important;
    color: var(--white-color) !important; /* 确保文字颜色为白色 */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-weight: bold !important;
    margin-right: 10px !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
    font-size: 16px !important;
}

.header .user-name {
    font-weight: 500 !important;
    color: var(--dark-color) !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
}

/* 确保下拉菜单样式一致 */
.header .dropdown-content {
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
    left: auto !important;
    background-color: var(--white-color) !important;
    min-width: 160px !important;
    box-shadow: 0 8px 16px 0 var(--shadow-color) !important;
    border-radius: var(--border-radius) !important;
    padding: 10px 0 !important;
    z-index: 1001 !important; /* 确保下拉菜单在其他元素之上 */
    display: none !important;
    animation: fadeIn 0.3s !important;
    margin-top: 10px !important;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.header .user-info:hover .dropdown-content {
    display: block !important;
}

.header .dropdown-content a {
    color: var(--dark-color) !important;
    padding: 10px 20px !important;
    text-decoration: none !important;
    display: block !important;
    transition: all var(--transition-speed) !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
}

.header .dropdown-content a:hover {
    background-color: var(--light-color) !important;
}

.header .dropdown-content a i {
    margin-right: 10px !important;
    width: 20px !important;
    text-align: center !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
}

/* 确保头部样式一致 */
.header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 20px !important;
    padding-bottom: 20px !important;
    border-bottom: 1px solid #eee !important;
    position: relative !important; /* 确保相对定位 */
    z-index: 1000 !important;
    padding-top: 10px !important;
    padding-right: 20px !important;
    padding-left: 20px !important;
}

/* 确保操作栏不会覆盖下拉菜单 */
.action-bar {
    position: relative !important;
    z-index: 999 !important; /* 确保比下拉菜单的z-index低 */
}

/* 修复可能的样式冲突 */
.main-content > * {
    position: relative !important;
    z-index: auto !important;
}

/* 确保模态框在最上层 */
.modal {
    z-index: 1050 !important;
}

/* 确保toast在最上层 */
.toast {
    z-index: 9999 !important;
}

/* 强制覆盖任何可能的冲突样式 */
.header .user-info {
    position: relative !important;
    right: auto !important;
    top: auto !important;
    margin-left: auto !important; /* 将用户信息推到右侧 */
}
