<?php
/**
 * 测试配置文件路径
 */

echo "<h2>配置文件路径测试</h2>";

// 当前目录
echo "<p><strong>当前目录：</strong>" . __DIR__ . "</p>";

// 尝试不同的路径
$paths = [
    dirname(__DIR__) . '/config/config.php',
    dirname(__DIR__, 2) . '/config/config.php',
    dirname(__DIR__, 3) . '/config/config.php',
    '../../config/config.php',
    '../config/config.php',
];

foreach ($paths as $index => $path) {
    $realPath = realpath($path);
    $exists = file_exists($path);
    
    echo "<p><strong>路径 " . ($index + 1) . "：</strong></p>";
    echo "<ul>";
    echo "<li>相对路径: " . htmlspecialchars($path) . "</li>";
    echo "<li>绝对路径: " . htmlspecialchars($realPath ?: '无法解析') . "</li>";
    echo "<li>文件存在: " . ($exists ? '✅ 是' : '❌ 否') . "</li>";
    echo "</ul>";
    
    if ($exists) {
        echo "<p style='color: green;'>✅ 找到配置文件！</p>";
        try {
            $config = require_once($path);
            echo "<p><strong>配置内容：</strong></p>";
            echo "<pre>" . htmlspecialchars(print_r($config, true)) . "</pre>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ 配置文件加载失败: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        break;
    }
}

echo "<p><a href='index.php'>返回登录页</a></p>";
?>
