/**
 * 插件图表功能
 * 提供热门插件排行和分类分布图表
 */

// 在DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('插件图表功能已加载');
    
    // 创建热门插件排行图表
    createTopPluginsChart(document.getElementById('top-plugins-chart'));
    
    // 创建插件分类分布图表
    createCategoryDistributionChart(document.getElementById('category-distribution-chart'));
});

/**
 * 创建热门插件排行图表
 * @param {HTMLElement} container - 图表容器
 */
function createTopPluginsChart(container) {
    if (!container) return;
    
    // 清除现有图表
    const existingChart = Chart.getChart(container);
    if (existingChart) {
        existingChart.destroy();
    }
    
    // 创建canvas元素
    const canvas = document.createElement('canvas');
    canvas.id = 'topPluginsChart';
    container.appendChild(canvas);
    
    // 获取上下文
    const ctx = canvas.getContext('2d');
    
    // 模拟数据
    const plugins = [
        { name: 'AI头像优化', downloads: 1250 },
        { name: '数据分析工具', downloads: 980 },
        { name: '社交分享增强', downloads: 850 },
        { name: '支付通道整合', downloads: 720 },
        { name: '用户行为跟踪', downloads: 650 },
        { name: '安全防护工具', downloads: 580 },
        { name: '多语言支持', downloads: 520 },
        { name: 'SEO优化助手', downloads: 480 }
    ];
    
    // 排序插件
    plugins.sort((a, b) => b.downloads - a.downloads);
    
    // 提取数据
    const labels = plugins.map(plugin => plugin.name);
    const data = plugins.map(plugin => plugin.downloads);
    
    // 创建图表
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: '安装次数',
                data: data,
                backgroundColor: [
                    'rgba(255, 107, 149, 0.8)',
                    'rgba(255, 107, 149, 0.7)',
                    'rgba(255, 107, 149, 0.6)',
                    'rgba(255, 107, 149, 0.5)',
                    'rgba(255, 107, 149, 0.4)',
                    'rgba(255, 107, 149, 0.3)',
                    'rgba(255, 107, 149, 0.2)',
                    'rgba(255, 107, 149, 0.1)'
                ],
                borderColor: [
                    'rgba(255, 107, 149, 1)',
                    'rgba(255, 107, 149, 0.9)',
                    'rgba(255, 107, 149, 0.8)',
                    'rgba(255, 107, 149, 0.7)',
                    'rgba(255, 107, 149, 0.6)',
                    'rgba(255, 107, 149, 0.5)',
                    'rgba(255, 107, 149, 0.4)',
                    'rgba(255, 107, 149, 0.3)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `安装次数: ${context.raw}`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '安装次数'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: '插件名称'
                    }
                }
            }
        }
    });
    
    console.log('热门插件排行图表已创建');
}

/**
 * 创建插件分类分布图表
 * @param {HTMLElement} container - 图表容器
 */
function createCategoryDistributionChart(container) {
    if (!container) return;
    
    // 清除现有图表
    const existingChart = Chart.getChart(container);
    if (existingChart) {
        existingChart.destroy();
    }
    
    // 创建canvas元素
    const canvas = document.createElement('canvas');
    canvas.id = 'categoryDistributionChart';
    container.appendChild(canvas);
    
    // 获取上下文
    const ctx = canvas.getContext('2d');
    
    // 模拟数据
    const categories = [
        { name: '头像处理', count: 12 },
        { name: '社交分享', count: 8 },
        { name: '数据分析', count: 10 },
        { name: '支付工具', count: 6 },
        { name: '安全防护', count: 4 },
        { name: '其他', count: 2 }
    ];
    
    // 提取数据
    const labels = categories.map(category => category.name);
    const data = categories.map(category => category.count);
    
    // 创建图表
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: [
                    'rgba(255, 107, 149, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 206, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(153, 102, 255, 0.8)',
                    'rgba(255, 159, 64, 0.8)'
                ],
                borderColor: [
                    'rgba(255, 107, 149, 1)',
                    'rgba(54, 162, 235, 1)',
                    'rgba(255, 206, 86, 1)',
                    'rgba(75, 192, 192, 1)',
                    'rgba(153, 102, 255, 1)',
                    'rgba(255, 159, 64, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
    
    console.log('插件分类分布图表已创建');
}
