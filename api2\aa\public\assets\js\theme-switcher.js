/**
 * 情侣头像匹配系统 - 主题切换脚本
 */

// 主题切换功能
class ThemeSwitcher {
    constructor() {
        this.init();
    }

    init() {
        // 创建主题设置面板
        this.createThemeSettingsPanel();

        // 初始化主题
        this.initTheme();

        // 初始化颜色方案
        this.initColorScheme();

        // 绑定事件
        this.bindEvents();
    }

    createThemeSettingsPanel() {
        // 创建主题设置面板
        const themeSettings = document.createElement('div');
        themeSettings.className = 'theme-settings';
        themeSettings.innerHTML = `
            <div class="theme-settings-toggle" id="theme-color-icon">
                <i class="bi bi-palette"></i>
            </div>
            <h3>主题设置</h3>
            <div class="theme-option">
                <div class="theme-option-title">主题颜色</div>
                <div class="color-picker">
                    <div class="color-option" data-color="#ff6b95" style="background-color: #ff6b95;"></div>
                    <div class="color-option" data-color="#8a6fd6" style="background-color: #8a6fd6;"></div>
                    <div class="color-option" data-color="#28c76f" style="background-color: #28c76f;"></div>
                    <div class="color-option" data-color="#00cfe8" style="background-color: #00cfe8;"></div>
                    <div class="color-option" data-color="#ff9f43" style="background-color: #ff9f43;"></div>
                    <div class="color-option" data-color="#ea5455" style="background-color: #ea5455;"></div>
                </div>
            </div>
        `;

        document.body.appendChild(themeSettings);
    }

    initTheme() {
        // 从本地存储中获取主题设置
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            document.documentElement.setAttribute('data-theme', savedTheme);
        }
    }

    initColorScheme() {
        // 从本地存储中获取颜色方案
        const savedColor = localStorage.getItem('theme-color');
        if (savedColor) {
            this.setColorScheme(savedColor);

            // 设置颜色选项的激活状态
            const colorOptions = document.querySelectorAll('.color-option');
            colorOptions.forEach(option => {
                if (option.getAttribute('data-color') === savedColor) {
                    option.classList.add('active');
                }
            });
        } else {
            // 默认激活第一个颜色选项
            const firstColorOption = document.querySelector('.color-option');
            if (firstColorOption) {
                firstColorOption.classList.add('active');
            }
        }
    }

    bindEvents() {
        // 颜色选项点击事件
        const colorOptions = document.querySelectorAll('.color-option');
        colorOptions.forEach(option => {
            option.addEventListener('click', () => {
                // 移除所有颜色选项的激活状态
                colorOptions.forEach(opt => opt.classList.remove('active'));

                // 激活当前选中的颜色选项
                option.classList.add('active');

                // 设置颜色方案
                const color = option.getAttribute('data-color');
                this.setColorScheme(color);

                // 保存颜色方案到本地存储
                localStorage.setItem('primary-color', color);
            });
        });

        // 主题设置面板切换按钮点击事件
        const themeSettingsToggle = document.querySelector('.theme-settings-toggle');
        const themeSettings = document.querySelector('.theme-settings');

        themeSettingsToggle.addEventListener('click', (e) => {
            e.stopPropagation(); // 阻止事件冒泡
            themeSettings.classList.toggle('show');
        });

        // 点击页面任意处关闭配色面板
        document.addEventListener('click', (e) => {
            // 如果点击的不是主题设置面板内的元素，则关闭面板
            if (themeSettings.classList.contains('show') &&
                !themeSettings.contains(e.target) &&
                e.target !== themeSettingsToggle) {
                themeSettings.classList.remove('show');
            }
        });

        // 阻止主题设置面板内的点击事件冒泡
        themeSettings.addEventListener('click', (e) => {
            e.stopPropagation();
        });
    }

    setColorScheme(color) {
        // 设置主色调
        document.documentElement.style.setProperty('--primary-color', color);

        // 设置主色调的RGB值（用于透明度）
        const rgbColor = this.hexToRgb(color);
        document.documentElement.style.setProperty('--primary-color-rgb', rgbColor);

        // 根据主色调生成辅助色
        const secondaryColor = this.adjustColor(color, -30);
        document.documentElement.style.setProperty('--secondary-color', secondaryColor);

        // 保存到localStorage
        localStorage.setItem('theme-color', color);

        // 立即应用到所有相关元素
        this.applyColorToElements(color);
    }

    applyColorToElements(color) {
        // 强制重新渲染所有使用主色调的元素
        const elementsToUpdate = [
            '.btn-primary',
            '.card-header',
            '.sidebar .menu-item.active',
            '.stat-icon',
            '.progress-bar',
            '.badge-primary',
            '.text-primary',
            '.border-primary',
            '.bg-primary'
        ];

        elementsToUpdate.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                // 触发重新渲染
                element.style.display = 'none';
                element.offsetHeight; // 强制重排
                element.style.display = '';
            });
        });

        // 触发自定义事件，通知其他组件颜色已更改
        document.dispatchEvent(new CustomEvent('themeColorChanged', {
            detail: { color: color }
        }));
    }

    hexToRgb(hex) {
        // 移除#号
        hex = hex.replace('#', '');

        // 解析RGB值
        const r = parseInt(hex.substring(0, 2), 16);
        const g = parseInt(hex.substring(2, 4), 16);
        const b = parseInt(hex.substring(4, 6), 16);

        // 返回RGB格式
        return `${r}, ${g}, ${b}`;
    }

    adjustColor(color, amount) {
        // 将十六进制颜色转换为RGB
        let r = parseInt(color.substring(1, 3), 16);
        let g = parseInt(color.substring(3, 5), 16);
        let b = parseInt(color.substring(5, 7), 16);

        // 调整RGB值
        r = Math.max(0, Math.min(255, r + amount));
        g = Math.max(0, Math.min(255, g + amount));
        b = Math.max(0, Math.min(255, b + amount));

        // 将RGB转换回十六进制
        return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    }
}

// 页面加载完成后初始化主题切换功能
document.addEventListener('DOMContentLoaded', () => {
    new ThemeSwitcher();

    // 添加页面过渡效果
    addPageTransitionEffects();

    // 添加滚动动画
    addScrollAnimations();

    // 添加微交互反馈
    addMicroInteractions();
});

// 添加页面过渡效果
function addPageTransitionEffects() {
    // 为所有链接添加过渡效果
    document.querySelectorAll('a:not([target="_blank"])').forEach(link => {
        link.addEventListener('click', function(e) {
            // 排除特定链接
            if (this.getAttribute('href').startsWith('#') ||
                this.getAttribute('href').startsWith('javascript:') ||
                this.getAttribute('href') === '') {
                return;
            }

            e.preventDefault();

            // 创建过渡动画元素
            const transition = document.createElement('div');
            transition.className = 'page-transition';
            document.body.appendChild(transition);

            // 触发过渡动画
            setTimeout(() => {
                transition.classList.add('active');
            }, 10);

            // 动画结束后跳转
            setTimeout(() => {
                window.location.href = this.getAttribute('href');
            }, 500);
        });
    });
}

// 添加滚动动画
function addScrollAnimations() {
    // 检测元素是否在视口中
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.bottom >= 0
        );
    }

    // 为可滚动元素添加动画
    const scrollAnimElements = document.querySelectorAll('.scroll-anim');

    function checkScrollAnimations() {
        scrollAnimElements.forEach(element => {
            if (isInViewport(element) && !element.classList.contains('animated')) {
                element.classList.add('animated');
            }
        });
    }

    // 初始检查
    checkScrollAnimations();

    // 滚动时检查
    window.addEventListener('scroll', checkScrollAnimations);
}

// 添加微交互反馈
function addMicroInteractions() {
    // 为按钮添加点击波纹效果
    document.querySelectorAll('button, .btn').forEach(button => {
        button.addEventListener('click', function(e) {
            // 创建波纹元素
            const ripple = document.createElement('span');
            ripple.className = 'ripple';
            this.appendChild(ripple);

            // 设置波纹位置
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            ripple.style.width = ripple.style.height = `${size}px`;
            ripple.style.left = `${e.clientX - rect.left - size / 2}px`;
            ripple.style.top = `${e.clientY - rect.top - size / 2}px`;

            // 动画结束后移除波纹元素
            ripple.addEventListener('animationend', () => {
                ripple.remove();
            });
        });
    });
}
