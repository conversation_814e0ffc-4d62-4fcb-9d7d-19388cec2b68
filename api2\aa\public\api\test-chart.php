<?php
// 测试图表API

// 开启会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 设置响应头
header('Content-Type: application/json');

// 检查用户是否已登录
if (!isset($_SESSION['username'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => '未授权访问']);
    exit;
}

try {
    // 包含数据库配置
    $configPath = __DIR__ . '/../../config/config.php';
    $config = require_once($configPath);
    
    if (!$config || !is_array($config)) {
        throw new Exception('配置文件加载失败');
    }
    
    $dbConfig = $config['database'];
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 返回测试数据
    $testData = [
        'labels' => ['1月', '2月', '3月', '4月', '5月', '6月'],
        'datasets' => [
            [
                'data' => [10, 20, 30, 40, 50, 60]
            ]
        ]
    ];
    
    echo json_encode($testData);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
