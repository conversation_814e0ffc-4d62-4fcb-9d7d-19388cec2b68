<?php
/**
 * 安全头部设置
 * 实现内容安全策略和其他安全头部
 */

// 设置内容安全策略
$csp = [
    "default-src" => "'self'",
    "script-src" => "'self' 'unsafe-inline'", // 允许内联脚本，生产环境应移除
    "style-src" => "'self' 'unsafe-inline'", // 允许内联样式，生产环境应移除
    "img-src" => "'self' data:", // 允许data URI图片
    "font-src" => "'self'",
    "connect-src" => "'self'",
    "media-src" => "'self'",
    "object-src" => "'none'",
    "frame-src" => "'self'",
    "base-uri" => "'self'",
    "form-action" => "'self'",
    "frame-ancestors" => "'self'"
];

// 构建CSP头部
$cspHeader = '';
foreach ($csp as $directive => $value) {
    $cspHeader .= $directive . ' ' . $value . '; ';
}

// 设置CSP头部
header("Content-Security-Policy: " . $cspHeader);

// 设置X-Content-Type-Options头部，防止MIME类型嗅探
header("X-Content-Type-Options: nosniff");

// 设置X-Frame-Options头部，防止点击劫持
header("X-Frame-Options: SAMEORIGIN");

// 设置X-XSS-Protection头部，启用XSS过滤
header("X-XSS-Protection: 1; mode=block");

// 设置Referrer-Policy头部，控制Referrer信息
header("Referrer-Policy: strict-origin-when-cross-origin");

// 设置Permissions-Policy头部，限制功能
header("Permissions-Policy: geolocation=(), microphone=(), camera=()");

// 设置Strict-Transport-Security头部，强制HTTPS（如果使用HTTPS）
if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
    header("Strict-Transport-Security: max-age=31536000; includeSubDomains; preload");
}

// 生成CSRF令牌
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// 验证CSRF令牌
function validateCSRFToken($token) {
    if (!isset($_SESSION['csrf_token']) || $token !== $_SESSION['csrf_token']) {
        // 令牌无效，记录安全事件
        logSecurityEvent('CSRF令牌验证失败', [
            'provided_token' => $token,
            'expected_token' => $_SESSION['csrf_token'] ?? 'not_set',
            'ip' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT']
        ]);
        return false;
    }
    return true;
}

// 记录安全事件
function logSecurityEvent($event, $data = []) {
    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'event' => $event,
        'ip' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'],
        'data' => $data
    ];
    
    // 将安全事件写入日志文件
    $logFile = __DIR__ . '/../logs/security.log';
    $logDir = dirname($logFile);
    
    // 确保日志目录存在
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    // 写入日志
    file_put_contents(
        $logFile,
        json_encode($logEntry) . PHP_EOL,
        FILE_APPEND
    );
}

// 净化输入
function sanitizeInput($input) {
    if (is_array($input)) {
        foreach ($input as $key => $value) {
            $input[$key] = sanitizeInput($value);
        }
    } else {
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    }
    return $input;
}

// 验证输入
function validateInput($input, $type) {
    switch ($type) {
        case 'email':
            return filter_var($input, FILTER_VALIDATE_EMAIL) !== false;
        case 'url':
            return filter_var($input, FILTER_VALIDATE_URL) !== false;
        case 'int':
            return filter_var($input, FILTER_VALIDATE_INT) !== false;
        case 'float':
            return filter_var($input, FILTER_VALIDATE_FLOAT) !== false;
        case 'ip':
            return filter_var($input, FILTER_VALIDATE_IP) !== false;
        case 'phone':
            // 中国手机号格式
            return preg_match('/^1[3-9]\d{9}$/', $input) === 1;
        case 'username':
            // 字母、数字、下划线，长度3-20
            return preg_match('/^[a-zA-Z0-9_]{3,20}$/', $input) === 1;
        case 'password':
            // 至少8个字符，包含大小写字母和数字
            return preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/', $input) === 1;
        default:
            return true;
    }
}

// 检查是否存在安全漏洞
function checkSecurityVulnerabilities() {
    // 检查是否存在SQL注入尝试
    $sqlInjectionPatterns = [
        '/(\%27)|(\')|(\-\-)|(\%23)|(#)/',
        '/((\%3D)|(=))[^\n]*((\%27)|(\')|(\-\-)|(\%3B)|(\;))/',
        '/\w*((\%27)|(\'))((\%6F)|o|(\%4F))((\%72)|r|(\%52))/',
        '/((\%27)|(\'))union/',
        '/((\%27)|(\'))select/',
        '/((\%27)|(\'))insert/',
        '/((\%27)|(\'))update/',
        '/((\%27)|(\'))delete/',
        '/((\%27)|(\'))drop/'
    ];
    
    // 检查所有GET和POST参数
    $params = array_merge($_GET, $_POST);
    foreach ($params as $param => $value) {
        foreach ($sqlInjectionPatterns as $pattern) {
            if (preg_match($pattern, $value)) {
                // 检测到SQL注入尝试，记录安全事件
                logSecurityEvent('SQL注入尝试', [
                    'parameter' => $param,
                    'value' => $value,
                    'pattern' => $pattern
                ]);
                
                // 终止请求
                http_response_code(403);
                die('安全警告：检测到可疑请求');
            }
        }
    }
    
    // 检查是否存在XSS尝试
    $xssPatterns = [
        '/<script\b[^>]*>(.*?)<\/script>/is',
        '/on\w+\s*=\s*"[^"]*"/is',
        '/on\w+\s*=\s*\'[^\']*\'/is',
        '/<\s*iframe/is',
        '/<\s*object/is',
        '/<\s*embed/is',
        '/<\s*applet/is',
        '/<\s*meta/is',
        '/<\s*base/is'
    ];
    
    foreach ($params as $param => $value) {
        foreach ($xssPatterns as $pattern) {
            if (preg_match($pattern, $value)) {
                // 检测到XSS尝试，记录安全事件
                logSecurityEvent('XSS尝试', [
                    'parameter' => $param,
                    'value' => $value,
                    'pattern' => $pattern
                ]);
                
                // 终止请求
                http_response_code(403);
                die('安全警告：检测到可疑请求');
            }
        }
    }
}

// 执行安全检查
checkSecurityVulnerabilities();

// 生成CSRF令牌
$csrf_token = generateCSRFToken();
