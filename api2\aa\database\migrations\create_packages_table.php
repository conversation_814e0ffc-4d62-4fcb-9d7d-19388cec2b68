<?php

/**
 * Migration for creating the packages table
 * This table stores package information with tenant isolation
 */
class CreatePackagesTable
{
    /**
     * Run the migration
     */
    public function up()
    {
        $sql = "CREATE TABLE IF NOT EXISTS `packages` (
            `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
            `tenant_id` bigint(20) UNSIGNED NOT NULL COMMENT 'Reference to tenants table',
            `name` varchar(64) NOT NULL COMMENT 'Package name',
            `description` varchar(255) DEFAULT NULL COMMENT 'Package description',
            `type` varchar(20) NOT NULL COMMENT 'vip/match/download',
            `price` decimal(10,2) NOT NULL COMMENT 'Package price',
            `value` int(11) NOT NULL COMMENT 'Package value (days for VIP, count for match/download)',
            `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '0=inactive, 1=active',
            `sort` int(11) NOT NULL DEFAULT 0 COMMENT 'Sort order',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `tenant_id` (`tenant_id`),
            KEY `type` (`type`),
            CONSTRAINT `packages_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        
        return $sql;
    }

    /**
     * Reverse the migration
     */
    public function down()
    {
        return "DROP TABLE IF EXISTS `packages`;";
    }
}
