﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账户管理 - 情侣头像匹配系统</title>
    <style>
        body {
            font-family: "Microsoft YaHei", sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            color: #333;
        }
        
        .loading-spinner {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            animation: fadeIn 0.5s;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .spinner {
            width: 80px;
            height: 80px;
            border: 5px solid rgba(255, 107, 149, 0.3);
            border-radius: 50%;
            border-top-color: #ff6b95;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        h4 {
            margin: 0 0 10px 0;
            color: #ff6b95;
        }
        
        p {
            margin: 0;
            color: #666;
        }
        
        .confetti-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden;
        }
        
        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background-color: #ff6b95;
            opacity: 0.7;
            animation: fall 5s linear infinite;
        }
        
        @keyframes fall {
            0% {
                top: -10%;
                transform: translateX(0) rotate(0deg);
            }
            100% {
                top: 100%;
                transform: translateX(100px) rotate(360deg);
            }
        }
    </style>
</head>
<body>
    <div class="confetti-container"></div>
    
    <div class="loading-spinner">
        <div class="spinner"></div>
        <h4>正在加载账户管理...</h4>
        <p>请稍候，系统正在准备您的账户管理界面</p>
    </div>

    <script>
        // 创建彩色纸屑效果
        function createConfetti() {
            const container = document.querySelector(".confetti-container");
            const confettiCount = 50;
            const colors = ["#ff6b95", "#ffa5c0", "#6b95ff", "#a5c0ff", "#ffcc00", "#66cc99"];
            
            for (let i = 0; i < confettiCount; i++) {
                const confetti = document.createElement("div");
                confetti.classList.add("confetti");
                confetti.style.left = Math.random() * 100 + "vw";
                confetti.style.width = Math.random() * 15 + 5 + "px";
                confetti.style.height = confetti.style.width;
                confetti.style.background = colors[Math.floor(Math.random() * colors.length)];
                confetti.style.animationDuration = Math.random() * 3 + 2 + "s";
                confetti.style.animationDelay = Math.random() * 5 + "s";
                
                // 随机形状
                const shapeType = Math.floor(Math.random() * 3);
                if (shapeType === 0) {
                    // 圆形
                    confetti.style.borderRadius = "50%";
                } else if (shapeType === 1) {
                    // 正方形
                    confetti.style.borderRadius = "0";
                } else {
                    // 三角形
                    confetti.style.width = "0";
                    confetti.style.height = "0";
                    confetti.style.borderLeft = confetti.style.width + " solid transparent";
                    confetti.style.borderRight = confetti.style.width + " solid transparent";
                    confetti.style.borderBottom = confetti.style.height + " solid " + confetti.style.background;
                    confetti.style.background = "transparent";
                }
                
                container.appendChild(confetti);
            }
        }
        
        // 页面加载完成后自动跳转
        document.addEventListener("DOMContentLoaded", function() {
            // 创建彩色纸屑效果
            createConfetti();
            
            // 添加加载音效
            try {
                const audio = new Audio("data:audio/mpeg;base64,SUQzBAAAAAABEVRYWFgAAAAtAAADY29tbWVudABCaWdTb3VuZEJhbmsuY29tIC8gTGFzb25pY1N0dWRpb3MuY29tAFRYWFgAAAAhAAADZW5naW5lZXIAQmlnU291bmRCYW5rLmNvbQBUWFhYAAAAGwAAA3NvZnR3YXJlAExhdmY1OC43Ni4xMDAAAFRDT04AAAAbAAADZW5jb2RlZCBieQBMYXZmNTguNzYuMTAwAAAAAAAAAAAAAAD/+1AAAAA8YXVkaW8vbXBlZwAAAAAAAAAAAABUSVQyAAAAGQAAAzIwMjMtMDUtMDZUMTc6MjM6MTgAAAAAAAAAAAAA//tQxAADwAABpAAAACAAADSAAAAETEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//tQxBYDwAABpAAAACAAADSAAAAEVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//tQxBcAAAANIAAAAAA");
                audio.volume = 0.3;
                audio.play();
            } catch (e) {
                console.log("音频播放失败", e);
            }
            
            // 模拟加载过程
            setTimeout(() => {
                // 检查是否已登录
                window.location.href = "/admin/accounts";
            }, 2000);
        });
    </script>
</body>
</html>
