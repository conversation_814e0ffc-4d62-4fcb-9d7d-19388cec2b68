/**
 * 小程序平台统计样式
 * 为平台统计图表和数据分析提供样式支持
 */

/* 统计卡片容器 */
.statistics-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 统计卡片 */
.stat-card {
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 10px var(--shadow-color);
    padding: 20px;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px var(--shadow-color);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.stat-card-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.stat-card-title i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.stat-card-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.stat-card-subtitle {
    font-size: 0.9rem;
    color: var(--gray-color);
    margin-bottom: 15px;
}

.stat-card-trend {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
    margin-top: auto;
}

.stat-card-trend.up {
    color: var(--success-color);
}

.stat-card-trend.down {
    color: var(--danger-color);
}

.stat-card-trend.neutral {
    color: var(--info-color);
}

/* 图表容器 */
.chart-section {
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 10px var(--shadow-color);
    padding: 20px;
    margin-bottom: 30px;
}

.chart-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.chart-section-title i {
    color: var(--primary-color);
    font-size: 1.3rem;
}

.chart-container {
    height: 350px;
    position: relative;
}

/* 筛选器样式 */
.statistics-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0;
    white-space: nowrap;
}

.filter-select {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: var(--white-color);
    font-size: 0.9rem;
    color: var(--dark-color);
    transition: all 0.3s ease;
    min-width: 120px;
}

.filter-select:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

/* 导出按钮样式 */
.statistics-export {
    margin-left: auto;
}

.statistics-export .dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    font-size: 0.9rem;
}

.statistics-export .dropdown-menu {
    min-width: 150px;
    padding: 5px 0;
    font-size: 0.9rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.statistics-export .dropdown-item {
    padding: 8px 15px;
    transition: all 0.2s ease;
}

.statistics-export .dropdown-item:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1);
}

/* 平台比较表格 */
.platform-comparison {
    margin-top: 30px;
    overflow-x: auto;
}

.comparison-table {
    width: 100%;
    border-collapse: collapse;
}

.comparison-table th,
.comparison-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.comparison-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: var(--dark-color);
}

.comparison-table tr:hover {
    background-color: rgba(var(--primary-color-rgb), 0.05);
}

.comparison-table .platform-name {
    font-weight: 500;
    color: var(--primary-color);
}

.comparison-table .trend-up {
    color: var(--success-color);
}

.comparison-table .trend-down {
    color: var(--danger-color);
}

/* 平台批量操作工具栏 */
.batch-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.batch-actions .btn {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 15px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.batch-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.batch-actions .btn i {
    font-size: 1rem;
}

/* 平台分类标签 */
.platform-category {
    display: inline-block;
    padding: 3px 10px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    margin-right: 5px;
}

.platform-category.entertainment {
    background-color: rgba(255, 99, 132, 0.1);
    color: rgb(255, 99, 132);
}

.platform-category.utility {
    background-color: rgba(54, 162, 235, 0.1);
    color: rgb(54, 162, 235);
}

.platform-category.social {
    background-color: rgba(255, 206, 86, 0.1);
    color: rgb(255, 206, 86);
}

.platform-category.education {
    background-color: rgba(75, 192, 192, 0.1);
    color: rgb(75, 192, 192);
}

/* 平台快速预览 */
.platform-preview {
    position: absolute;
    top: 100%;
    left: 0;
    width: 300px;
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 15px;
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.platform-preview.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.preview-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.preview-logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    overflow: hidden;
}

.preview-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

.preview-info {
    margin-bottom: 15px;
}

.preview-info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.preview-info-label {
    color: var(--gray-color);
}

.preview-info-value {
    font-weight: 500;
    color: var(--dark-color);
}

.preview-actions {
    display: flex;
    gap: 10px;
}

.preview-actions .btn {
    flex: 1;
    font-size: 0.85rem;
    padding: 5px 10px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .statistics-container {
        grid-template-columns: 1fr;
    }
    
    .statistics-filters {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .filter-group {
        width: 100%;
    }
    
    .statistics-export {
        margin-left: 0;
        margin-top: 10px;
    }
    
    .chart-container {
        height: 300px;
    }
}
