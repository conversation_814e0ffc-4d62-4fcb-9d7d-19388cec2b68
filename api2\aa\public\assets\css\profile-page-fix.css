/**
 * 个人资料页面修复样式
 * 修复个人资料页面的头像和下拉菜单问题
 */

/* 确保右上角头像正确显示，并移除侧收缩按钮下面的头像 */
.header .user-info {
    display: flex !important;
    align-items: center !important;
    position: absolute !important; /* 使用绝对定位 */
    top: 10px !important; /* 距离顶部的距离 */
    right: 20px !important; /* 距离右侧的距离 */
    cursor: pointer !important;
    z-index: 99999 !important;
}

.header .user-avatar {
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    background-color: var(--primary-color) !important;
    color: #ffffff !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-weight: bold !important;
    margin-right: 10px !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
    font-size: 16px !important;
    text-shadow: none !important;
    border: none !important;
    transition: background-color 0.3s ease !important;
}

/* 确保侧收缩按钮正确显示 */
.header .menu-toggle {
    position: relative !important;
    z-index: 99998 !important;
}

/* 确保个人资料页面的大头像不影响右上角的头像 */
.profile-header .profile-avatar {
    width: 100px !important;
    height: 100px !important;
    border-radius: 50% !important;
    background-color: var(--primary-color) !important;
    color: var(--white-color) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 2.5rem !important;
    font-weight: 600 !important;
    margin-right: 20px !important;
    box-shadow: 0 5px 15px rgba(255, 107, 149, 0.3) !important;
}

/* 确保下拉菜单正确显示 */
.header .dropdown-content {
    position: absolute !important;
    top: calc(100% - 5px) !important;
    right: 0 !important;
    left: auto !important;
    width: 160px !important;
    background-color: #ffffff !important;
    box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.1) !important;
    border-radius: 8px !important;
    padding: 10px 0 !important;
    z-index: 100000 !important;
    display: none !important;
    margin-top: 0 !important;
    border: 1px solid #eee !important;
}

/* 添加一个连接区域，确保鼠标移动时不会触发菜单消失 */
.header .user-info::after {
    content: '' !important;
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    height: 15px !important;
    background-color: transparent !important;
    z-index: 99999 !important;
}

/* 显示下拉菜单 */
.header .user-info:hover .dropdown-content {
    display: block !important;
}

/* 下拉菜单项样式 */
.header .dropdown-content a {
    color: #333333 !important;
    padding: 10px 20px !important;
    text-decoration: none !important;
    display: block !important;
    transition: background-color 0.3s !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    font-weight: normal !important;
}

.header .dropdown-content a:hover {
    background-color: #f8f9fa !important;
}

.header .dropdown-content a i {
    margin-right: 10px !important;
    width: 20px !important;
    text-align: center !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
}

/* 确保头部区域不会遮挡下拉菜单 */
.header {
    overflow: visible !important;
    position: relative !important;
    z-index: 99998 !important;
    height: 60px !important; /* 设置固定高度 */
    padding-top: 10px !important;
    padding-bottom: 10px !important;
}

/* 修复侧收缩按钮样式 */
.menu-toggle {
    background: none !important;
    border: none !important;
    color: var(--dark-color) !important;
    font-size: 1.5rem !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    transition: all var(--transition-speed) !important;
}

.menu-toggle:hover {
    background-color: var(--light-color) !important;
}

