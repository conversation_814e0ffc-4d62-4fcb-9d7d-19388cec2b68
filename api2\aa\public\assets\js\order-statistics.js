/**
 * 订单统计图表功能
 * 提供订单数量、金额等统计分析功能
 */

// 在DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('订单统计功能已加载');
    
    // 初始化订单统计图表
    initOrderStatistics();
    
    // 初始化订单数据筛选器
    setupOrderFilters();
    
    // 添加图表导出按钮
    setupOrderExport();
});

/**
 * 初始化订单统计图表
 */
function initOrderStatistics() {
    // 检查是否存在统计容器
    const dailyOrdersContainer = document.getElementById('daily-orders-chart');
    const orderAmountContainer = document.getElementById('order-amount-chart');
    const orderStatusContainer = document.getElementById('order-status-chart');
    const orderTrendContainer = document.getElementById('order-trend-chart');
    
    if (dailyOrdersContainer) {
        createDailyOrdersChart(dailyOrdersContainer);
    }
    
    if (orderAmountContainer) {
        createOrderAmountChart(orderAmountContainer);
    }
    
    if (orderStatusContainer) {
        createOrderStatusChart(orderStatusContainer);
    }
    
    if (orderTrendContainer) {
        createOrderTrendChart(orderTrendContainer);
    }
}

/**
 * 创建每日订单数量图表
 * @param {HTMLElement} container - 图表容器
 */
function createDailyOrdersChart(container) {
    // 创建canvas元素
    const canvas = document.createElement('canvas');
    canvas.id = 'dailyOrdersChart';
    container.appendChild(canvas);
    
    // 获取上下文
    const ctx = canvas.getContext('2d');
    
    // 获取最近30天的日期
    const dates = getLast30Days();
    
    // 模拟数据
    const orderCounts = generateRandomData(30, 20, 100);
    
    // 创建图表
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: dates,
            datasets: [
                {
                    label: '每日订单数',
                    data: orderCounts,
                    backgroundColor: 'rgba(255, 107, 149, 0.2)',
                    borderColor: 'rgba(255, 107, 149, 1)',
                    borderWidth: 2
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '每日订单数量',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '订单数量'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: '日期'
                    }
                }
            }
        }
    });
    
    console.log('每日订单数量图表已创建');
}

/**
 * 创建订单金额图表
 * @param {HTMLElement} container - 图表容器
 */
function createOrderAmountChart(container) {
    // 创建canvas元素
    const canvas = document.createElement('canvas');
    canvas.id = 'orderAmountChart';
    container.appendChild(canvas);
    
    // 获取上下文
    const ctx = canvas.getContext('2d');
    
    // 获取最近30天的日期
    const dates = getLast30Days();
    
    // 模拟数据
    const orderAmounts = generateRandomData(30, 1000, 5000);
    
    // 创建图表
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [
                {
                    label: '每日订单金额',
                    data: orderAmounts,
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    tension: 0.4,
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '每日订单金额',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                label += '¥' + context.parsed.y.toFixed(2);
                            }
                            return label;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '订单金额 (元)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: '日期'
                    }
                }
            }
        }
    });
    
    console.log('订单金额图表已创建');
}

/**
 * 创建订单状态分布图表
 * @param {HTMLElement} container - 图表容器
 */
function createOrderStatusChart(container) {
    // 创建canvas元素
    const canvas = document.createElement('canvas');
    canvas.id = 'orderStatusChart';
    container.appendChild(canvas);
    
    // 获取上下文
    const ctx = canvas.getContext('2d');
    
    // 模拟数据
    const statusData = [
        { status: '已完成', count: 350, color: 'rgba(40, 167, 69, 0.8)' },
        { status: '处理中', count: 120, color: 'rgba(255, 193, 7, 0.8)' },
        { status: '待付款', count: 80, color: 'rgba(23, 162, 184, 0.8)' },
        { status: '已取消', count: 50, color: 'rgba(220, 53, 69, 0.8)' },
        { status: '已退款', count: 30, color: 'rgba(108, 117, 125, 0.8)' }
    ];
    
    // 创建图表
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: statusData.map(item => item.status),
            datasets: [
                {
                    data: statusData.map(item => item.count),
                    backgroundColor: statusData.map(item => item.color),
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '订单状态分布',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    position: 'right'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.raw || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
    
    console.log('订单状态分布图表已创建');
}

/**
 * 创建订单趋势图表
 * @param {HTMLElement} container - 图表容器
 */
function createOrderTrendChart(container) {
    // 创建canvas元素
    const canvas = document.createElement('canvas');
    canvas.id = 'orderTrendChart';
    container.appendChild(canvas);
    
    // 获取上下文
    const ctx = canvas.getContext('2d');
    
    // 模拟数据
    const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
    const orderCounts = [320, 350, 380, 420, 450, 480, 520, 550, 580, 620, 650, 700];
    const orderAmounts = orderCounts.map(count => count * 50);
    
    // 创建图表
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: months,
            datasets: [
                {
                    label: '订单数量',
                    data: orderCounts,
                    backgroundColor: 'rgba(255, 107, 149, 0.2)',
                    borderColor: 'rgba(255, 107, 149, 1)',
                    borderWidth: 2,
                    yAxisID: 'y'
                },
                {
                    label: '订单金额 (元)',
                    data: orderAmounts,
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    type: 'line',
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '订单趋势分析',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                legend: {
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '订单数量'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false
                    },
                    title: {
                        display: true,
                        text: '订单金额 (元)'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: '月份'
                    }
                }
            }
        }
    });
    
    console.log('订单趋势图表已创建');
}

/**
 * 获取最近30天的日期
 * @returns {Array} 日期数组
 */
function getLast30Days() {
    const dates = [];
    const today = new Date();
    
    for (let i = 29; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(today.getDate() - i);
        dates.push(formatDate(date));
    }
    
    return dates;
}

/**
 * 格式化日期
 * @param {Date} date - 日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date) {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}/${day}`;
}

/**
 * 生成随机数据
 * @param {number} count - 数据点数量
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {Array} 随机数据数组
 */
function generateRandomData(count, min, max) {
    return Array.from({length: count}, () => Math.floor(Math.random() * (max - min + 1)) + min);
}

/**
 * 设置订单数据筛选器
 */
function setupOrderFilters() {
    // 检查是否存在筛选器容器
    const filterContainer = document.getElementById('order-filters');
    if (!filterContainer) return;
    
    // 创建时间范围筛选器
    const timeRangeFilter = document.createElement('div');
    timeRangeFilter.className = 'filter-group';
    timeRangeFilter.innerHTML = `
        <label for="time-range">时间范围:</label>
        <select id="time-range" class="filter-select">
            <option value="7days">最近7天</option>
            <option value="30days" selected>最近30天</option>
            <option value="90days">最近90天</option>
            <option value="year">今年</option>
            <option value="all">全部</option>
        </select>
    `;
    
    // 创建订单状态筛选器
    const statusFilter = document.createElement('div');
    statusFilter.className = 'filter-group';
    statusFilter.innerHTML = `
        <label for="status-filter">订单状态:</label>
        <select id="status-filter" class="filter-select">
            <option value="all" selected>全部状态</option>
            <option value="completed">已完成</option>
            <option value="processing">处理中</option>
            <option value="pending">待付款</option>
            <option value="cancelled">已取消</option>
            <option value="refunded">已退款</option>
        </select>
    `;
    
    // 创建应用按钮
    const applyButton = document.createElement('button');
    applyButton.className = 'btn btn-primary';
    applyButton.textContent = '应用筛选';
    applyButton.addEventListener('click', applyOrderFilters);
    
    // 添加到筛选器容器
    filterContainer.appendChild(timeRangeFilter);
    filterContainer.appendChild(statusFilter);
    filterContainer.appendChild(applyButton);
    
    console.log('订单数据筛选器已设置');
}

/**
 * 应用订单数据筛选
 */
function applyOrderFilters() {
    const timeRange = document.getElementById('time-range').value;
    const status = document.getElementById('status-filter').value;
    
    console.log(`应用筛选: 时间范围=${timeRange}, 状态=${status}`);
    
    // 显示加载动画
    showLoading();
    
    // 模拟API请求
    setTimeout(() => {
        // 更新图表数据
        updateOrderChartsData(timeRange, status);
        
        // 隐藏加载动画
        hideLoading();
        
        // 显示成功消息
        showToast('订单统计数据已更新', 'success');
    }, 800);
}

/**
 * 更新订单图表数据
 * @param {string} timeRange - 时间范围
 * @param {string} status - 订单状态
 */
function updateOrderChartsData(timeRange, status) {
    // 获取图表实例
    const dailyOrdersChart = Chart.getChart('dailyOrdersChart');
    const orderAmountChart = Chart.getChart('orderAmountChart');
    const orderStatusChart = Chart.getChart('orderStatusChart');
    const orderTrendChart = Chart.getChart('orderTrendChart');
    
    // 根据筛选条件生成新数据
    let dates, orderCounts, orderAmounts;
    
    switch (timeRange) {
        case '7days':
            dates = getLast30Days().slice(-7);
            orderCounts = generateRandomData(7, 20, 100);
            orderAmounts = generateRandomData(7, 1000, 5000);
            break;
        case '30days':
            dates = getLast30Days();
            orderCounts = generateRandomData(30, 20, 100);
            orderAmounts = generateRandomData(30, 1000, 5000);
            break;
        case '90days':
            dates = ['1月', '2月', '3月'];
            orderCounts = generateRandomData(3, 500, 1000);
            orderAmounts = generateRandomData(3, 25000, 50000);
            break;
        case 'year':
            dates = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
            orderCounts = generateRandomData(12, 300, 700);
            orderAmounts = generateRandomData(12, 15000, 35000);
            break;
        default:
            dates = getLast30Days();
            orderCounts = generateRandomData(30, 20, 100);
            orderAmounts = generateRandomData(30, 1000, 5000);
    }
    
    // 更新每日订单数量图表
    if (dailyOrdersChart) {
        dailyOrdersChart.data.labels = dates;
        dailyOrdersChart.data.datasets[0].data = orderCounts;
        dailyOrdersChart.update();
    }
    
    // 更新订单金额图表
    if (orderAmountChart) {
        orderAmountChart.data.labels = dates;
        orderAmountChart.data.datasets[0].data = orderAmounts;
        orderAmountChart.update();
    }
    
    // 更新订单状态分布图表
    if (orderStatusChart) {
        // 根据状态筛选调整数据
        let statusData;
        
        if (status === 'all') {
            statusData = [
                { status: '已完成', count: 350, color: 'rgba(40, 167, 69, 0.8)' },
                { status: '处理中', count: 120, color: 'rgba(255, 193, 7, 0.8)' },
                { status: '待付款', count: 80, color: 'rgba(23, 162, 184, 0.8)' },
                { status: '已取消', count: 50, color: 'rgba(220, 53, 69, 0.8)' },
                { status: '已退款', count: 30, color: 'rgba(108, 117, 125, 0.8)' }
            ];
        } else {
            // 模拟根据状态筛选的数据
            statusData = [
                { status: '已完成', count: status === 'completed' ? 350 : Math.floor(Math.random() * 100), color: 'rgba(40, 167, 69, 0.8)' },
                { status: '处理中', count: status === 'processing' ? 120 : Math.floor(Math.random() * 50), color: 'rgba(255, 193, 7, 0.8)' },
                { status: '待付款', count: status === 'pending' ? 80 : Math.floor(Math.random() * 30), color: 'rgba(23, 162, 184, 0.8)' },
                { status: '已取消', count: status === 'cancelled' ? 50 : Math.floor(Math.random() * 20), color: 'rgba(220, 53, 69, 0.8)' },
                { status: '已退款', count: status === 'refunded' ? 30 : Math.floor(Math.random() * 10), color: 'rgba(108, 117, 125, 0.8)' }
            ];
        }
        
        orderStatusChart.data.labels = statusData.map(item => item.status);
        orderStatusChart.data.datasets[0].data = statusData.map(item => item.count);
        orderStatusChart.data.datasets[0].backgroundColor = statusData.map(item => item.color);
        orderStatusChart.update();
    }
    
    // 更新订单趋势图表
    if (orderTrendChart) {
        // 只在年度视图时更新
        if (timeRange === 'year') {
            const yearlyOrderCounts = generateRandomData(12, 300, 700);
            const yearlyOrderAmounts = yearlyOrderCounts.map(count => count * 50);
            
            orderTrendChart.data.datasets[0].data = yearlyOrderCounts;
            orderTrendChart.data.datasets[1].data = yearlyOrderAmounts;
            orderTrendChart.update();
        }
    }
    
    console.log('订单图表数据已更新');
}

/**
 * 设置订单数据导出功能
 */
function setupOrderExport() {
    // 检查是否存在导出按钮容器
    const exportContainer = document.getElementById('order-export');
    if (!exportContainer) return;
    
    // 创建导出按钮
    exportContainer.innerHTML = `
        <div class="dropdown">
            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="bi bi-download"></i> 导出数据
            </button>
            <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                <li><a class="dropdown-item" href="#" data-format="excel">Excel</a></li>
                <li><a class="dropdown-item" href="#" data-format="csv">CSV</a></li>
                <li><a class="dropdown-item" href="#" data-format="pdf">PDF</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="#" data-format="image">图表图片</a></li>
            </ul>
        </div>
    `;
    
    // 添加导出按钮事件
    exportContainer.querySelectorAll('.dropdown-item').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const format = this.getAttribute('data-format');
            exportOrderData(format);
        });
    });
    
    console.log('订单数据导出功能已设置');
}

/**
 * 导出订单数据
 * @param {string} format - 导出格式
 */
function exportOrderData(format) {
    console.log(`导出订单数据: 格式=${format}`);
    
    // 显示加载动画
    showLoading();
    
    // 模拟导出过程
    setTimeout(() => {
        // 隐藏加载动画
        hideLoading();
        
        // 显示成功消息
        showToast(`订单数据已导出为${format.toUpperCase()}格式`, 'success');
    }, 800);
}
