﻿<?php
// 小程序平台页面
?>
<?php
// 包含身份验证检查
include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/auth-check.php');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小程序平台 - 情侣头像匹配系统</title>
    <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/header-scripts.php'); ?>
<style>
    /* 基础样式 */
    :root {
        --primary-color: #ff6b95;
        --secondary-color: #ffa5c0;
        --success-color: #28a745;
        --danger-color: #dc3545;
        --warning-color: #ffc107;
        --info-color: #17a2b8;
        --light-color: #f8f9fa;
        --dark-color: #343a40;
        --gray-color: #6c757d;
        --white-color: #ffffff;
        --shadow-color: rgba(0, 0, 0, 0.1);
        --transition-speed: 0.3s;
        --border-radius: 8px;
        --animate-delay: 0.1s;
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: "Microsoft YaHei", sans-serif;
    }

    body {
        background-color: #f5f7fa;
        color: #333;
        font-size: 14px;
        line-height: 1.5;
        overflow-x: hidden;
    }

    /* 加载动画 */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s;
    }

    .loading-overlay.show {
        opacity: 1;
        visibility: visible;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 107, 149, 0.3);
        border-top: 4px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* 布局样式 */
    .dashboard-container {
        display: flex;
        min-height: 100vh;
    }

    /* 侧边栏样式 */
    .sidebar {
        width: 250px;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: var(--white-color);
        padding: 20px 0;
        position: fixed;
        height: 100vh;
        overflow-y: auto;
        z-index: 1000;
        box-shadow: 2px 0 10px var(--shadow-color);
        transition: all var(--transition-speed);
    }

    .sidebar.collapsed {
        width: 70px;
    }

    .sidebar-header {
        padding: 0 20px 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        text-align: center;
        margin-bottom: 20px;
    }

    .sidebar-logo {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        margin-bottom: 10px;
        object-fit: cover;
        background-color: rgba(255, 255, 255, 0.2);
    }

    .sidebar-title {
        color: var(--white-color);
        font-size: 1.5rem;
        margin-bottom: 5px;
        font-weight: 600;
    }

    .sidebar-subtitle {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
    }

    .sidebar.collapsed .sidebar-title,
    .sidebar.collapsed .sidebar-subtitle {
        display: none;
    }

    .sidebar-menu {
        list-style: none;
        padding: 0;
    }

    .sidebar-menu li {
        margin-bottom: 5px;
    }

    .sidebar-menu li a {
        color: var(--white-color);
        text-decoration: none;
        display: block;
        padding: 12px 20px;
        transition: all var(--transition-speed);
        border-left: 4px solid transparent;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .sidebar-menu li a:hover,
    .sidebar-menu li a.active {
        background-color: rgba(255, 255, 255, 0.2);
        border-left-color: var(--white-color);
    }

    .sidebar-menu li a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
        font-size: 1.1rem;
    }

    .sidebar.collapsed .sidebar-menu li a span {
        display: none;
    }

    .sidebar.collapsed .sidebar-menu li a i {
        margin-right: 0;
        font-size: 1.3rem;
    }

    /* 主内容区域样式 */
    .main-content {
        flex: 1;
        margin-left: 250px;
        padding: 20px;
        transition: all var(--transition-speed);
    }

    .main-content.expanded {
        margin-left: 70px;
    }

    /* 头部样式 */
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }

    .menu-toggle {
        background: none;
        border: none;
        color: var(--dark-color);
        font-size: 1.5rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        transition: all var(--transition-speed);
    }

    .menu-toggle:hover {
        background-color: var(--light-color);
    }

    .user-info {
        display: flex;
        align-items: center;
        position: relative;
        cursor: pointer;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: var(--white-color);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 10px;
    }

    .user-name {
        font-weight: 500;
    }

    .dropdown-content {
        position: absolute;
        top: 100%;
        right: 0;
        background-color: var(--white-color);
        min-width: 160px;
        box-shadow: 0 8px 16px 0 var(--shadow-color);
        border-radius: var(--border-radius);
        padding: 10px 0;
        z-index: 1;
        display: none;
        animation: fadeIn 0.3s;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .user-info:hover .dropdown-content {
        display: block;
    }

    .dropdown-content a {
        color: var(--dark-color);
        padding: 10px 20px;
        text-decoration: none;
        display: block;
        transition: all var(--transition-speed);
    }

    .dropdown-content a:hover {
        background-color: var(--light-color);
    }

    .dropdown-content a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }

    /* 操作栏样式 */
    .action-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    /* 操作按钮容器 */
    .action-buttons {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    /* 批量操作按钮 */
    .batch-operations {
        display: flex;
        gap: 10px;
    }

    .batch-operations button {
        padding: 5px 10px;
        font-size: 0.85rem;
    }

    /* 复选框样式 */
    input[type="checkbox"] {
        width: 16px;
        height: 16px;
        cursor: pointer;
        accent-color: var(--primary-color);
    }

    .search-container {
        display: flex;
        align-items: center;
        background-color: var(--white-color);
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: 0 2px 5px var(--shadow-color);
        width: 300px;
    }

    .search-input {
        flex: 1;
        border: none;
        padding: 10px 15px;
        outline: none;
        font-size: 14px;
    }

    .search-button {
        background-color: var(--primary-color);
        color: var(--white-color);
        border: none;
        padding: 10px 15px;
        cursor: pointer;
        transition: all var(--transition-speed);
    }

    .search-button:hover {
        background-color: #ff4f7e;
    }

    /* 按钮样式 */
    .btn {
        display: inline-block;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        user-select: none;
        border: 1px solid transparent;
        padding: 0.5rem 1rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: var(--border-radius);
        transition: all var(--transition-speed);
        cursor: pointer;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .btn:active {
        transform: translateY(0);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .btn-primary {
        color: var(--white-color);
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        border-color: var(--primary-color);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #ff4f7e 0%, #ffa5c0 100%);
        border-color: #ff4f7e;
    }

    .btn-secondary {
        color: var(--white-color);
        background-color: var(--gray-color);
        border-color: var(--gray-color);
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #545b62;
    }

    .btn-danger {
        color: var(--white-color);
        background-color: var(--danger-color);
        border-color: var(--danger-color);
    }

    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }

    .btn-outline-primary {
        color: var(--primary-color);
        background-color: transparent;
        border-color: var(--primary-color);
    }

    .btn-outline-primary:hover {
        color: var(--white-color);
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }

    /* 表格样式 */
    .table-container {
        background-color: var(--white-color);
        border-radius: var(--border-radius);
        box-shadow: 0 2px 5px var(--shadow-color);
        overflow: hidden;
        margin-bottom: 20px;
        animation: fadeInUp 0.5s;
    }

    @keyframes fadeInUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .table {
        width: 100%;
        border-collapse: collapse;
    }

    .table th,
    .table td {
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid #eee;
    }

    .table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
    }

    .table tr:last-child td {
        border-bottom: none;
    }

    .table tr:hover td {
        background-color: #f8f9fa;
    }

    .status {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
    }

    .status.active {
        background-color: rgba(40, 167, 69, 0.1);
        color: var(--success-color);
    }

    .status.inactive {
        background-color: rgba(220, 53, 69, 0.1);
        color: var(--danger-color);
    }

    .actions {
        display: flex;
        gap: 5px;
    }

    .actions button {
        width: auto;
        height: 32px;
        border-radius: 16px;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all var(--transition-speed);
        padding: 0 12px;
        margin-right: 5px;
    }

    .actions button i {
        margin-right: 5px;
    }

    .actions .enter {
        background-color: rgba(23, 162, 184, 0.1);
        color: var(--info-color);
    }

    .actions .enter:hover {
        background-color: var(--info-color);
        color: var(--white-color);
    }

    .actions .edit {
        background-color: rgba(255, 193, 7, 0.1);
        color: var(--warning-color);
    }

    .actions .edit:hover {
        background-color: var(--warning-color);
        color: var(--white-color);
    }

    .actions .delete {
        background-color: rgba(220, 53, 69, 0.1);
        color: var(--danger-color);
    }

    .actions .delete:hover {
        background-color: var(--danger-color);
        color: var(--white-color);
    }

    /* 分页样式 */
    .pagination-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;
    }

    .pagination-info {
        color: var(--gray-color);
    }

    .pagination-info span {
        font-weight: 500;
        color: var(--dark-color);
    }

    .pagination-controls {
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .pagination {
        display: flex;
        align-items: center;
    }

    .pagination button {
        width: 32px;
        height: 32px;
        border-radius: 4px;
        border: 1px solid #eee;
        background-color: var(--white-color);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all var(--transition-speed);
        margin: 0 2px;
    }

    .pagination button:hover:not(:disabled) {
        background-color: #f8f9fa;
    }

    .pagination button.active {
        background-color: var(--primary-color);
        color: var(--white-color);
        border-color: var(--primary-color);
    }

    .pagination button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .pagination-jump {
        display: flex;
        align-items: center;
        margin-left: 10px;
    }

    .pagination-jump span {
        margin: 0 5px;
        color: var(--gray-color);
    }

    .pagination-jump input {
        width: 40px;
        height: 32px;
        border-radius: 4px;
        border: 1px solid #eee;
        text-align: center;
        padding: 0 5px;
    }

    .pagination-jump button {
        width: auto;
        padding: 0 10px;
    }

    .pagination-size {
        display: flex;
        align-items: center;
    }

    .pagination-size span {
        margin: 0 5px;
        color: var(--gray-color);
    }

    .pagination-size select {
        height: 32px;
        border-radius: 4px;
        border: 1px solid #eee;
        padding: 0 5px;
    }

    /* 模态框样式 */
    .modal {
        display: none;
        position: fixed;
        z-index: 1050;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0, 0, 0, 0.5);
        animation: fadeIn 0.3s;
    }

    .modal-content {
        background-color: var(--white-color);
        margin: 5% auto;
        width: 600px;
        max-width: 90%;
        border-radius: var(--border-radius);
        box-shadow: 0 5px 15px var(--shadow-color);
        animation: slideDown 0.3s;
    }

    .modal-body {
        padding: 20px;
        max-height: 70vh;
        overflow-y: auto;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: var(--dark-color);
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: var(--border-radius);
        font-size: 14px;
        transition: all var(--transition-speed);
        background-color: #f9f9f9;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(255, 107, 149, 0.25);
        outline: none;
        background-color: var(--white-color);
    }

    .form-group.checkbox {
        display: flex;
        align-items: center;
    }

    .form-group.checkbox input {
        width: auto;
        margin-right: 10px;
    }

    .required {
        color: var(--danger-color);
    }

    /* 消息提示样式 */
    .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 300px;
        background-color: var(--white-color);
        border-radius: var(--border-radius);
        box-shadow: 0 5px 15px var(--shadow-color);
        overflow: hidden;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transform: translateX(30px);
        transition: all 0.3s;
    }

    .toast.show {
        opacity: 1;
        visibility: visible;
        transform: translateX(0);
    }

    .toast-content {
        display: flex;
        align-items: center;
        padding: 15px;
    }

    .toast-icon {
        font-size: 1.5rem;
        margin-right: 15px;
        display: none;
    }

    .toast-icon.success {
        color: var(--success-color);
    }

    .toast-icon.error {
        color: var(--danger-color);
    }

    .toast-icon.info {
        color: var(--info-color);
    }

    .toast-message {
        flex: 1;
        font-weight: 500;
    }

    .toast-progress {
        height: 3px;
        background-color: var(--primary-color);
        width: 100%;
        animation: progress 3s linear;
    }

    @keyframes progress {
        from { width: 100%; }
        to { width: 0%; }
    }

    /* 响应式样式 */
    @media (max-width: 768px) {
        .sidebar {
            width: 70px;
        }

        .sidebar-title,
        .sidebar-subtitle {
            display: none;
        }

        .sidebar-menu li a span {
            display: none;
        }

        .sidebar-menu li a i {
            margin-right: 0;
            font-size: 1.3rem;
        }

        .main-content {
            margin-left: 70px;
        }

        .action-bar {
            flex-direction: column;
            align-items: stretch;
            gap: 10px;
        }

        .search-container {
            width: 100%;
        }

        .action-buttons {
            width: 100%;
            flex-direction: column;
            gap: 10px;
        }

        .batch-operations {
            width: 100%;
            flex-wrap: wrap;
            justify-content: space-between;
        }

        .batch-operations button {
            flex: 1;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            margin-bottom: 5px;
        }

        #add-platform-btn {
            width: 100%;
        }

        .pagination-container {
            flex-direction: column;
            gap: 10px;
        }

        .pagination-controls {
            flex-direction: column;
            gap: 10px;
        }
    }
</style>

</head>
<body>
    <div class="loading-overlay" id="loading-overlay">
        <div class="spinner"></div>
    </div>

    <div class="dashboard-container">
        <!-- 引入通用头部导航 -->
        <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/header-nav.php'); ?>

            <!-- 面包屑导航 -->
            <?php
            // 设置当前页面标题
            $page_title = '平台列表';
            // 设置当前页面路径
            $page_path = [
                ['title' => '小程序平台', 'url' => '/dashboard/platforms']
            ];
            // 包含面包屑导航组件
            include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/admin-breadcrumb.php');
            ?>



            <div class="action-bar">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="搜索平台名称或用户名...">
                    <button class="search-button"><i class="bi bi-search"></i></button>
                </div>
                <div class="action-buttons">
                    <div class="batch-operations">
                        <button class="btn btn-outline-primary btn-sm" id="batch-enable-btn">批量启用</button>
                        <button class="btn btn-outline-primary btn-sm" id="batch-disable-btn">批量禁用</button>
                        <button class="btn btn-outline-danger btn-sm" id="batch-delete-btn">批量删除</button>
                    </div>
                    <button class="btn btn-primary" id="add-platform-btn">新增平台</button>
                </div>
            </div>

            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="select-all" title="全选/取消全选"></th>
                            <th>平台名称</th>
                            <th>账户信息</th>
                            <th>用户数</th>
                            <th>匹配次数</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr data-id="1">
                            <td><input type="checkbox" class="platform-checkbox" data-id="1"></td>
                            <td>情侣头像匹配小程序1</td>
                            <td>test001</td>
                            <td>256</td>
                            <td>1024</td>
                            <td><span class="status active" data-id="1">启用</span></td>
                            <td>2023-01-15</td>
                            <td class="actions">
                                <button class="enter" data-id="1" title="在新标签页打开"><i class="bi bi-box-arrow-in-right"></i> <span class="btn-text">进入</span></button>
                                <a href="/miniprogram/detail/index.php?id=1" class="enter-link" target="_blank" style="display:none;">直接链接</a>
                                <button class="edit" onclick="openEditModal(1)" title="编辑"><i class="bi bi-pencil"></i> <span class="btn-text">编辑</span></button>
                                <button class="delete" onclick="deletePlatform(1)" title="删除"><i class="bi bi-trash"></i> <span class="btn-text">删除</span></button>
                            </td>
                        </tr>
                        <tr data-id="2">
                            <td><input type="checkbox" class="platform-checkbox" data-id="2"></td>
                            <td>情侣头像匹配小程序2</td>
                            <td>test001</td>
                            <td>128</td>
                            <td>512</td>
                            <td><span class="status inactive" data-id="2">禁用</span></td>
                            <td>2023-02-20</td>
                            <td class="actions">
                                <button class="enter" data-id="2" title="在新标签页打开"><i class="bi bi-box-arrow-in-right"></i> <span class="btn-text">进入</span></button>
                                <a href="/miniprogram/detail/index.php?id=2" class="enter-link" target="_blank" style="display:none;">直接链接</a>
                                <button class="edit" onclick="openEditModal(2)" title="编辑"><i class="bi bi-pencil"></i> <span class="btn-text">编辑</span></button>
                                <button class="delete" onclick="deletePlatform(2)" title="删除"><i class="bi bi-trash"></i> <span class="btn-text">删除</span></button>
                            </td>
                        </tr>
                        <tr data-id="3">
                            <td><input type="checkbox" class="platform-checkbox" data-id="3"></td>
                            <td>情侣头像匹配小程序3</td>
                            <td>test002</td>
                            <td>64</td>
                            <td>256</td>
                            <td><span class="status active" data-id="3">启用</span></td>
                            <td>2023-03-10</td>
                            <td class="actions">
                                <button class="enter" data-id="3" title="在新标签页打开"><i class="bi bi-box-arrow-in-right"></i> <span class="btn-text">进入</span></button>
                                <a href="/miniprogram/detail/index.php?id=3" class="enter-link" target="_blank" style="display:none;">直接链接</a>
                                <button class="edit" onclick="openEditModal(3)" title="编辑"><i class="bi bi-pencil"></i> <span class="btn-text">编辑</span></button>
                                <button class="delete" onclick="deletePlatform(3)" title="删除"><i class="bi bi-trash"></i> <span class="btn-text">删除</span></button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="pagination-container">
                <div class="pagination-info">
                    显示 <span>1-10</span> 条，共 <span>25</span> 条
                </div>
                <div class="pagination-controls">
                    <div class="pagination">
                        <button disabled><i class="bi bi-chevron-left"></i></button>
                        <button class="active">1</button>
                        <button>2</button>
                        <button>3</button>
                        <button><i class="bi bi-chevron-right"></i></button>
                        <div class="pagination-jump">
                            <span>跳至</span>
                            <input type="number" min="1" max="3" value="1">
                            <span>页</span>
                            <button>GO</button>
                        </div>
                    </div>
                    <div class="pagination-size">
                        <span>每页显示</span>
                        <select>
                            <option value="10" selected>10</option>
                            <option value="20">20</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span>条</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增平台模态框 -->
    <div class="modal" id="add-platform-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>新增平台</h3>
                <button class="close-btn" onclick="closeModal('add-platform-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <form id="add-platform-form">
                    <div class="form-group">
                        <label for="platform-name">平台名称 <span class="required">*</span></label>
                        <input type="text" id="platform-name" required>
                    </div>
                    <div class="form-group">
                        <label for="platform-desc">平台描述</label>
                        <textarea id="platform-desc" rows="3"></textarea>
                    </div>
                    <div class="form-group checkbox">
                        <input type="checkbox" id="platform-status" checked>
                        <label for="platform-status">启用平台</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('add-platform-modal')">取消</button>
                <button class="btn btn-primary" onclick="addPlatform()">确定</button>
            </div>
        </div>
    </div>

    <!-- 编辑平台模态框 -->
    <div class="modal" id="edit-platform-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑平台</h3>
                <button class="close-btn" onclick="closeModal('edit-platform-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <form id="edit-platform-form">
                    <input type="hidden" id="edit-platform-id">
                    <div class="form-group">
                        <label for="edit-platform-name">平台名称 <span class="required">*</span></label>
                        <input type="text" id="edit-platform-name" required>
                    </div>
                    <div class="form-group">
                        <label for="edit-platform-desc">平台描述</label>
                        <textarea id="edit-platform-desc" rows="3"></textarea>
                    </div>
                    <div class="form-group checkbox">
                        <input type="checkbox" id="edit-platform-status">
                        <label for="edit-platform-status">启用平台</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('edit-platform-modal')">取消</button>
                <button class="btn btn-primary" onclick="updatePlatform()">保存</button>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div class="modal" id="confirm-delete-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>确认删除</h3>
                <button class="close-btn" onclick="closeModal('confirm-delete-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <p>确定要删除该平台吗？此操作不可恢复！</p>
                <input type="hidden" id="delete-platform-id">
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('confirm-delete-modal')">取消</button>
                <button class="btn btn-danger" onclick="confirmDelete()">删除</button>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div class="toast" id="toast">
        <div class="toast-content">
            <i class="bi bi-check-circle-fill toast-icon success"></i>
            <i class="bi bi-x-circle-fill toast-icon error"></i>
            <i class="bi bi-info-circle-fill toast-icon info"></i>
            <div class="toast-message">操作成功</div>
        </div>
        <div class="toast-progress"></div>
    </div>

    <!-- jQuery -->
    <script src="/assets/js/jquery-3.6.0.min.js"></script>

    <!-- 引入通用JS文件 -->
    <script src="/assets/js/common.js"></script>

<script>
    // DOM元素
    const addPlatformBtn = document.getElementById("add-platform-btn");
    const searchButton = document.querySelector(".search-button");

    // 初始化
    document.addEventListener("DOMContentLoaded", function() {
        // 显示加载动画
        showLoading();

        // 添加表格行的动画延迟
        const tableRows = document.querySelectorAll(".table tbody tr");
        tableRows.forEach((row, index) => {
            row.style.animationDelay = `${index * 0.1}s`;
        });

        // 添加状态切换功能
        const statusElements = document.querySelectorAll(".status");
        statusElements.forEach(status => {
            status.addEventListener("click", function() {
                toggleStatus(this);
            });
        });

        // 添加搜索功能
        if (searchButton) {
            searchButton.addEventListener("click", function() {
                searchPlatforms();
            });
        }

        // 添加按钮点击事件
        setupButtonEvents();

        // 添加批量操作按钮事件
        setupBatchOperations();

        // 添加全选/取消全选功能
        setupSelectAll();

        // 隐藏加载动画
        setTimeout(hideLoading, 500);
    });

    // 设置全选/取消全选功能
    function setupSelectAll() {
        const selectAllCheckbox = document.getElementById('select-all');
        const platformCheckboxes = document.querySelectorAll('.platform-checkbox');

        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                platformCheckboxes.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
            });
        }

        // 当单个复选框状态变化时，更新全选复选框状态
        platformCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateSelectAllCheckbox();
            });
        });
    }

    // 更新全选复选框状态
    function updateSelectAllCheckbox() {
        const selectAllCheckbox = document.getElementById('select-all');
        const platformCheckboxes = document.querySelectorAll('.platform-checkbox');

        if (selectAllCheckbox) {
            const checkedCount = document.querySelectorAll('.platform-checkbox:checked').length;
            selectAllCheckbox.checked = checkedCount === platformCheckboxes.length && platformCheckboxes.length > 0;
            selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < platformCheckboxes.length;
        }
    }

    // 获取选中的平台ID
    function getSelectedPlatformIds() {
        const selectedIds = [];
        const checkedBoxes = document.querySelectorAll('.platform-checkbox:checked');

        checkedBoxes.forEach(checkbox => {
            const id = checkbox.getAttribute('data-id');
            if (id) {
                selectedIds.push(id);
            }
        });

        return selectedIds;
    }

    // 新增平台按钮点击事件
    if (addPlatformBtn) {
        addPlatformBtn.addEventListener("click", function() {
            openModal("add-platform-modal");
        });
    }

    // 设置按钮点击事件
    function setupButtonEvents() {
        // 进入按钮 - 移除原有的事件监听器，避免重复触发
        const enterButtons = document.querySelectorAll(".actions .enter");
        enterButtons.forEach(button => {
            // 移除所有现有的点击事件监听器
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);

            // 添加新的点击事件监听器
            newButton.addEventListener("click", function(event) {
                // 阻止默认行为和事件冒泡
                event.preventDefault();
                event.stopPropagation();

                // 防止重复点击
                if (this.getAttribute('data-processing') === 'true') {
                    console.log('按钮正在处理中，忽略重复点击');
                    return;
                }

                // 标记按钮为处理中
                this.setAttribute('data-processing', 'true');

                // 获取平台ID
                const id = this.getAttribute("data-id") || this.closest("tr").getAttribute("data-id");
                console.log("事件监听器触发进入平台，ID:", id);

                if (id) {
                    enterPlatform(id);
                } else {
                    console.error("无法获取平台ID");
                    showToast("无法获取平台ID", "error");
                }

                // 1秒后重置按钮状态
                setTimeout(() => {
                    this.removeAttribute('data-processing');
                }, 1000);
            });
        });

        // 编辑按钮
        const editButtons = document.querySelectorAll(".actions .edit");
        editButtons.forEach(button => {
            button.addEventListener("click", function() {
                const id = this.closest("tr").getAttribute("data-id");
                openEditModal(id);
            });
        });

        // 删除按钮
        const deleteButtons = document.querySelectorAll(".actions .delete");
        deleteButtons.forEach(button => {
            button.addEventListener("click", function() {
                const id = this.closest("tr").getAttribute("data-id");
                deletePlatform(id);
            });
        });
    }

    // 搜索平台
    function searchPlatforms() {
        const searchInput = document.querySelector(".search-input");
        const searchTerm = searchInput.value.trim();

        if (!searchTerm) {
            showToast("请输入搜索关键词", "info");
            return;
        }

        showLoading();

        // 模拟搜索请求
        setTimeout(() => {
            hideLoading();
            showToast(`已搜索: ${searchTerm}`, "success");
        }, 500);
    }

    // 切换状态
    function toggleStatus(element) {
        showLoading();

        // 获取当前状态
        const isActive = element.classList.contains("active");
        const platformId = element.getAttribute("data-id") || element.closest("tr").getAttribute("data-id");

        // 模拟API请求
        setTimeout(() => {
            hideLoading();

            // 切换状态
            if (isActive) {
                element.classList.remove("active");
                element.classList.add("inactive");
                element.textContent = "禁用";
                showToast("平台已禁用", "info");
            } else {
                element.classList.remove("inactive");
                element.classList.add("active");
                element.textContent = "启用";
                showToast("平台已启用", "success");
            }
        }, 500);
    }

    // 进入平台 - 在新标签页打开
    function enterPlatform(id) {
        if (!id || id === 'null' || id === 'undefined') {
            showToast('error', '平台ID无效，请刷新页面重试');
            return;
        }

        showLoading();
        console.log('进入平台ID:', id);

        // 记录日志 - 使用原生 fetch API 替代 jQuery ajax
        try {
            fetch('/api/log.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=enter_platform&platform_id=' + encodeURIComponent(id)
            })
            .then(response => {
                if (response.ok) {
                    console.log('日志记录成功');
                } else {
                    console.error('日志记录失败', response.status);
                }
            })
            .catch(error => {
                console.error('日志记录请求失败', error);
            });
        } catch (error) {
            console.error('日志记录异常', error);
            // 继续执行，不阻止导航
        }

        // 在新标签页打开平台详情页
        setTimeout(() => {
            try {
                // 确保 ID 是有效的数字
                const platformId = parseInt(id);
                if (isNaN(platformId)) {
                    showToast('error', '平台ID无效，请刷新页面重试');
                    hideLoading();
                    return;
                }

                // 使用绝对路径确保链接正确
                const detailUrl = `/miniprogram/detail/index.php?id=${platformId}`;
                console.log('准备在新标签页打开:', detailUrl);

                // 使用单一方法在新标签页打开，避免多次打开
                try {
                    // 首选方法: 使用链接点击，这是最可靠的方法
                    console.log('使用链接点击方式打开新标签页');
                    const enterLink = document.querySelector(`.enter-link[href="${detailUrl}"]`);

                    if (enterLink) {
                        // 确保链接可见，然后点击
                        enterLink.style.display = 'inline-block';
                        enterLink.click();
                        // 点击后隐藏链接
                        setTimeout(() => {
                            enterLink.style.display = 'none';
                        }, 100);
                        hideLoading();
                        return;
                    }

                    // 如果找不到对应的链接，使用 window.open 作为备选方法
                    console.log('找不到对应链接，尝试使用window.open');
                    const newWindow = window.open(detailUrl, '_blank');

                    if (newWindow) {
                        console.log('成功使用window.open打开新标签页');
                        newWindow.focus();
                        hideLoading();
                        return;
                    }

                    // 如果前两种方法都失败，显示错误信息
                    console.error('无法打开新标签页');
                    showToast('error', '无法打开新标签页，请检查浏览器设置');

                } catch (e) {
                    console.error('打开新标签页出错:', e);
                    // 显示错误提示
                    showToast('error', '打开新标签页失败，请检查浏览器是否阻止弹出窗口');
                }

                // 无论成功与否，都隐藏加载动画
                hideLoading();

            } catch (error) {
                console.error('打开新标签页错误:', error);
                hideLoading();
                showToast('error', '打开新标签页出错，请刷新页面重试');
            }
        }, 500);
    }

    // 打开编辑模态框
    function openEditModal(id) {
        showLoading();
        // 模拟API请求获取平台数据
        setTimeout(() => {
            hideLoading();

            // 模拟数据
            const platformData = {
                id: id,
                name: "情侣头像匹配小程序",
                description: "平台描述",
                status: id % 2 === 0 ? 0 : 1
            };

            // 填充表单
            document.getElementById("edit-platform-id").value = platformData.id;
            document.getElementById("edit-platform-name").value = platformData.name;
            document.getElementById("edit-platform-desc").value = platformData.description;
            document.getElementById("edit-platform-status").checked = platformData.status === 1;

            // 打开模态框
            openModal("edit-platform-modal");
        }, 500);
    }

    // 更新平台
    function updatePlatform() {
        const name = document.getElementById("edit-platform-name").value;

        if (!name) {
            showToast("请填写平台名称", "error");
            return;
        }

        showLoading();
        // 模拟API请求
        setTimeout(() => {
            hideLoading();
            closeModal("edit-platform-modal");
            showToast("平台更新成功");

            // 刷新页面或更新UI
            // location.reload();
        }, 800);
    }

    // 删除平台
    function deletePlatform(id) {
        document.getElementById("delete-platform-id").value = id;
        openModal("confirm-delete-modal");
    }

    // 确认删除
    function confirmDelete() {
        const id = document.getElementById("delete-platform-id").value;
        showLoading();
        // 模拟API请求
        setTimeout(() => {
            hideLoading();
            closeModal("confirm-delete-modal");
            showToast("平台已删除");

            // 刷新页面或更新UI
            // location.reload();
        }, 800);
    }

    // 设置批量操作按钮事件
    function setupBatchOperations() {
        // 批量启用按钮
        const batchEnableBtn = document.getElementById('batch-enable-btn');
        if (batchEnableBtn) {
            batchEnableBtn.addEventListener('click', function() {
                const selectedIds = getSelectedPlatformIds();
                if (selectedIds.length === 0) {
                    showToast('请至少选择一个平台', 'info');
                    return;
                }

                showConfirmDialog('批量启用', `确定要启用选中的 ${selectedIds.length} 个平台吗？`, function() {
                    showLoading();
                    setTimeout(() => {
                        hideLoading();
                        showToast(`已启用 ${selectedIds.length} 个平台`, 'success');
                    }, 800);
                });
            });
        }

        // 批量禁用按钮
        const batchDisableBtn = document.getElementById('batch-disable-btn');
        if (batchDisableBtn) {
            batchDisableBtn.addEventListener('click', function() {
                const selectedIds = getSelectedPlatformIds();
                if (selectedIds.length === 0) {
                    showToast('请至少选择一个平台', 'info');
                    return;
                }

                showConfirmDialog('批量禁用', `确定要禁用选中的 ${selectedIds.length} 个平台吗？`, function() {
                    showLoading();
                    setTimeout(() => {
                        hideLoading();
                        showToast(`已禁用 ${selectedIds.length} 个平台`, 'info');
                    }, 800);
                });
            });
        }

        // 批量删除按钮
        const batchDeleteBtn = document.getElementById('batch-delete-btn');
        if (batchDeleteBtn) {
            batchDeleteBtn.addEventListener('click', function() {
                const selectedIds = getSelectedPlatformIds();
                if (selectedIds.length === 0) {
                    showToast('请至少选择一个平台', 'info');
                    return;
                }

                showConfirmDialog('批量删除', `确定要删除选中的 ${selectedIds.length} 个平台吗？此操作不可恢复！`, function() {
                    showLoading();
                    setTimeout(() => {
                        hideLoading();
                        showToast(`已删除 ${selectedIds.length} 个平台`, 'success');
                    }, 800);
                });
            });
        }
    }

    // 添加平台
    function addPlatform() {
        const name = document.getElementById("platform-name").value;

        if (!name) {
            showToast("请填写平台名称", "error");
            return;
        }

        showLoading();
        // 模拟API请求
        setTimeout(() => {
            hideLoading();
            closeModal("add-platform-modal");
            showToast("平台添加成功");

            // 刷新页面或更新UI
            // location.reload();
        }, 800);
    }
</script>

<?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/footer-scripts.php'); ?>

<!-- 网站底部 -->
<?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/site-footer.php'); ?>

<script src="/assets/js/sidebar-toggle-fix.js"></script>
<script src="/assets/js/responsive-layout.js"></script>

<!-- 模态框精确修复脚本 -->
<script src="/assets/js/modal-precise-fix.js"></script>

<!-- 平台管理增强功能 -->
<script src="/assets/js/platform-batch-operations.js"></script>
<script src="/assets/js/platform-quick-preview.js"></script>

<!-- 删除多余的批量操作按钮 -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 删除右上角的批量操作按钮
        const batchActions = document.querySelector('.batch-actions');
        if (batchActions) {
            batchActions.remove();
        }

        // 删除多余的复选框列
        const checkboxColumns = document.querySelectorAll('.checkbox-column');
        if (checkboxColumns.length > 0) {
            checkboxColumns.forEach(col => col.remove());
        }
    });
</script>
</body>
</html>









