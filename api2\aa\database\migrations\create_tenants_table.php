<?php

/**
 * Migration for creating the tenants table
 * This table stores information about each tenant in the SAAS system
 */
class CreateTenantsTable
{
    /**
     * Run the migration
     */
    public function up()
    {
        $sql = "CREATE TABLE IF NOT EXISTS `tenants` (
            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            `domain` varchar(255) NOT NULL COMMENT 'Tenant domain name',
            `name` varchar(100) NOT NULL COMMENT 'Tenant name',
            `admin_email` varchar(100) NOT NULL COMMENT 'Admin email',
            `admin_phone` varchar(20) DEFAULT NULL COMMENT 'Admin phone',
            `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '0=inactive, 1=active',
            `storage_limit` int(11) NOT NULL DEFAULT 1024 COMMENT 'Storage limit in MB',
            `api_limit` int(11) NOT NULL DEFAULT 10000 COMMENT 'API call limit per day',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `domain` (`domain`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        
        return $sql;
    }

    /**
     * Reverse the migration
     */
    public function down()
    {
        return "DROP TABLE IF EXISTS `tenants`;";
    }
}
