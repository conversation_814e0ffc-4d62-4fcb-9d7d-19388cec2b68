/**
 * 账户管理页面样式修复
 * 修复按钮和模态框样式
 */

/* 按钮样式修复 */
.btn-primary {
    color: #ffffff !important;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
    border-color: var(--primary-color) !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #ff4f7e 0%, #ffa5c0 100%) !important;
    border-color: #ff4f7e !important;
}

.btn-secondary {
    color: #ffffff !important;
    background-color: var(--gray-color) !important;
    border-color: var(--gray-color) !important;
}

.btn-secondary:hover {
    background-color: #5a6268 !important;
    border-color: #545b62 !important;
}

.btn-danger {
    color: #ffffff !important;
    background-color: var(--danger-color) !important;
    border-color: var(--danger-color) !important;
}

.btn-danger:hover {
    background-color: #c82333 !important;
    border-color: #bd2130 !important;
}

.btn-outline-primary {
    color: var(--primary-color) !important;
    background-color: transparent !important;
    border-color: var(--primary-color) !important;
}

.btn-outline-primary:hover {
    color: #ffffff !important;
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

/* 模态框样式修复 */
.modal {
    display: none;
    position: fixed;
    z-index: 1050;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5) !important;
}

.modal-content {
    background-color: #ffffff !important;
    margin: 10% auto;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: 80%;
    max-width: 600px;
    position: relative;
    animation: fadeInDown 0.3s;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
    margin-bottom: 15px;
    background-color: #ffffff !important;
}

.modal-header h3 {
    margin: 0;
    color: var(--dark-color);
    font-size: 1.2rem;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--gray-color);
    transition: color 0.3s;
}

.close-btn:hover {
    color: var(--danger-color);
}

.modal-body {
    margin-bottom: 20px;
    background-color: #ffffff !important;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding-top: 15px;
    border-top: 1px solid #eee;
    background-color: #ffffff !important;
}

/* 表单样式修复 */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--dark-color);
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group input[type="tel"],
.form-group input[type="date"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s;
    background-color: #ffffff !important;
    color: var(--dark-color) !important;
}

.form-group input[type="text"]:focus,
.form-group input[type="email"]:focus,
.form-group input[type="password"]:focus,
.form-group input[type="tel"]:focus,
.form-group input[type="date"]:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    outline: none;
}

.form-group.checkbox {
    display: flex;
    align-items: center;
}

.form-group.checkbox input[type="checkbox"] {
    margin-right: 10px;
}

.form-group.checkbox label {
    margin-bottom: 0;
}

.required {
    color: var(--danger-color);
}

/* 操作按钮样式修复 */
.actions button {
    color: inherit !important;
}

.actions button:hover {
    color: #ffffff !important;
}

.actions .edit {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color) !important;
}

.actions .edit:hover {
    background-color: var(--warning-color);
    color: #ffffff !important;
}

.actions .reset-pwd {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color) !important;
}

.actions .reset-pwd:hover {
    background-color: var(--info-color);
    color: #ffffff !important;
}

.actions .delete {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color) !important;
}

.actions .delete:hover {
    background-color: var(--danger-color);
    color: #ffffff !important;
}

/* 确保按钮文本颜色正确 */
.btn-text {
    color: inherit !important;
}

button:hover .btn-text {
    color: #ffffff !important;
}
