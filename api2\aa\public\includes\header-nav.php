<?php
// 获取当前页面路径
$current_page = $_SERVER['REQUEST_URI'];

// 注意：session_start()和登录检查应该在包含此文件的页面中完成，而不是在这里

// 检查用户角色
$isAdmin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';

// 获取用户信息
$username = $_SESSION['username'] ?? '管理员';
$userInitial = mb_substr($username, 0, 1, 'UTF-8');
?>

<!-- 侧边栏 -->
<div class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <img src="/assets/images/logo.png" alt="Logo" class="sidebar-logo">
        <h3 class="sidebar-title">情侣头像匹配</h3>
        <p class="sidebar-subtitle">管理系统</p>
    </div>
    <ul class="sidebar-menu">
        <li><a href="/dashboard" <?php echo strpos($current_page, '/dashboard') !== false && $current_page === '/dashboard' ? 'class="active"' : ''; ?>><i class="bi bi-speedometer2"></i> <span>控制面板</span></a></li>
        <?php if ($isAdmin): ?>
        <li><a href="/dashboard/accounts" <?php echo strpos($current_page, '/dashboard/accounts') !== false ? 'class="active"' : ''; ?>><i class="bi bi-people"></i> <span>账户管理</span></a></li>
        <?php endif; ?>
        <li><a href="/dashboard/platforms" <?php echo strpos($current_page, '/dashboard/platforms') !== false ? 'class="active"' : ''; ?>><i class="bi bi-phone"></i> <span>小程序平台</span></a></li>
        <li><a href="/dashboard/orders" <?php echo strpos($current_page, '/dashboard/orders') !== false ? 'class="active"' : ''; ?>><i class="bi bi-cart3"></i> <span>订单搜索</span></a></li>
        <?php if ($isAdmin): ?>
        <li><a href="/dashboard/announcements" <?php echo strpos($current_page, '/dashboard/announcements') !== false ? 'class="active"' : ''; ?>><i class="bi bi-megaphone"></i> <span>公告管理</span></a></li>
        <li><a href="/dashboard/plugins" <?php echo strpos($current_page, '/dashboard/plugins') !== false ? 'class="active"' : ''; ?>><i class="bi bi-puzzle"></i> <span>插件中心</span></a></li>
        <li><a href="/dashboard/settings" <?php echo strpos($current_page, '/dashboard/settings') !== false ? 'class="active"' : ''; ?>><i class="bi bi-gear"></i> <span>系统设置</span></a></li>
        <?php endif; ?>
        <li><a href="/dashboard/recycle" <?php echo strpos($current_page, '/dashboard/recycle') !== false ? 'class="active"' : ''; ?>><i class="bi bi-trash"></i> <span>应用回收站</span></a></li>
        <li><a href="/logout.php"><i class="bi bi-box-arrow-right"></i> <span>退出登录</span></a></li>
    </ul>
</div>

<!-- 主内容区域 -->
<div class="main-content" id="main-content">
    <div class="header">
        <button class="menu-toggle" id="menu-toggle"><i class="bi bi-list"></i></button>
        <div class="header-info-content" id="header-info-content">
            <div class="wisdom-quote">
                <i class="bi bi-quote"></i>
                <span id="wisdom-text">加载中...</span>
            </div>
            <div class="weather-info">
                <i class="bi bi-cloud-sun"></i>
                <span id="weather-text">加载中...</span>
            </div>
            <div class="info-actions">
                <button id="refresh-info" title="刷新"><i class="bi bi-arrow-clockwise"></i></button>
                <button id="toggle-info-type" title="切换显示内容"><i class="bi bi-shuffle"></i></button>
                <button id="settings-info" title="设置"><i class="bi bi-gear"></i></button>
            </div>
        </div>
        <div class="user-info">
            <div class="user-avatar"><?php echo htmlspecialchars($userInitial); ?></div>
            <span class="user-name"><?php echo htmlspecialchars($username); ?></span>
            <div class="dropdown-content">
                <a href="/profile"><i class="bi bi-person"></i> 个人资料</a>
                <a href="/dashboard/settings"><i class="bi bi-gear"></i> 设置</a>
                <a href="/logout.php"><i class="bi bi-box-arrow-right"></i> 退出登录</a>
            </div>
        </div>
    </div>

<!-- 天气设置下拉菜单 -->
<div class="weather-settings-dropdown" id="weather-settings-dropdown">
    <div class="dropdown-content">
        <div class="dropdown-header">
            <h3>天气设置</h3>
        </div>
        <div class="dropdown-body">
            <div class="city-search-container">
                <input type="text" id="weather-city-search" placeholder="搜索城市..." class="city-search-input">
                <div class="city-search-results" id="city-search-results"></div>
            </div>
            <div class="selected-city-container">
                <div class="selected-city-label">当前选择的城市</div>
                <div class="selected-city" id="selected-city">北京</div>
            </div>
            <div class="common-cities">
                <div class="common-cities-label">热门城市</div>
                <div class="common-cities-grid">
                    <div class="city-item" data-city="北京">北京</div>
                    <div class="city-item" data-city="上海">上海</div>
                    <div class="city-item" data-city="广州">广州</div>
                    <div class="city-item" data-city="深圳">深圳</div>
                    <div class="city-item" data-city="杭州">杭州</div>
                    <div class="city-item" data-city="成都">成都</div>
                    <div class="city-item" data-city="重庆">重庆</div>
                    <div class="city-item" data-city="武汉">武汉</div>
                    <div class="city-item" data-city="西安">西安</div>
                    <div class="city-item" data-city="南京">南京</div>
                    <div class="city-item" data-city="长沙">长沙</div>
                    <div class="city-item" data-city="天津">天津</div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* 天气设置下拉菜单样式 */
    .weather-settings-dropdown {
        position: absolute;
        top: 100%;
        right: 0;
        width: 300px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        z-index: 1050;
        display: none;
        animation: fadeInDown 0.3s;
        overflow: hidden;
    }

    .weather-settings-dropdown.show {
        display: block;
    }

    .dropdown-content {
        width: 100%;
    }

    .dropdown-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 15px;
        border-bottom: 1px solid #eee;
        background-color: rgba(var(--primary-color-rgb, 255, 107, 149), 0.1);
    }

    .dropdown-header h3 {
        margin: 0;
        font-size: 1rem;
        color: var(--primary-color);
    }

    .dropdown-body {
        padding: 15px;
    }

    .city-search-container {
        margin-bottom: 15px;
        position: relative;
    }

    .city-search-input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        transition: all 0.3s;
    }

    .city-search-input:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb, 255, 107, 149), 0.2);
        outline: none;
    }

    .city-search-results {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        max-height: 200px;
        overflow-y: auto;
        background-color: #fff;
        border: 1px solid #ddd;
        border-top: none;
        border-radius: 0 0 4px 4px;
        z-index: 1;
        display: none;
    }

    .city-search-results.show {
        display: block;
    }

    .search-result-item {
        padding: 8px 12px;
        cursor: pointer;
        transition: all 0.2s;
    }

    .search-result-item:hover {
        background-color: rgba(var(--primary-color-rgb, 255, 107, 149), 0.1);
    }

    .selected-city-container {
        margin-bottom: 15px;
        padding: 10px;
        background-color: rgba(var(--primary-color-rgb, 255, 107, 149), 0.05);
        border-radius: 4px;
        text-align: center;
    }

    .selected-city-label {
        font-size: 12px;
        color: #666;
        margin-bottom: 5px;
    }

    .selected-city {
        font-size: 16px;
        font-weight: 600;
        color: var(--primary-color);
    }

    .common-cities {
        margin-top: 15px;
    }

    .common-cities-label {
        font-size: 12px;
        color: #666;
        margin-bottom: 10px;
    }

    .common-cities-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;
    }

    .city-item {
        padding: 6px 0;
        text-align: center;
        background-color: rgba(var(--primary-color-rgb, 255, 107, 149), 0.05);
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 13px;
    }

    .city-item:hover {
        background-color: rgba(var(--primary-color-rgb, 255, 107, 149), 0.2);
        color: var(--primary-color);
    }

    @keyframes fadeInDown {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 获取元素
        const settingsBtn = document.getElementById('settings-info');
        const dropdown = document.getElementById('weather-settings-dropdown');
        const citySearch = document.getElementById('weather-city-search');
        const citySearchResults = document.getElementById('city-search-results');
        const selectedCity = document.getElementById('selected-city');
        const cityItems = document.querySelectorAll('.city-item');

        // 城市数据
        const allCities = [
            "北京", "上海", "广州", "深圳", "杭州", "成都", "重庆", "武汉", "西安", "南京", "长沙", "天津",
            "苏州", "厦门", "青岛", "大连", "沈阳", "哈尔滨", "济南", "郑州", "福州", "宁波", "合肥", "石家庄",
            "昆明", "南宁", "贵阳", "太原", "兰州", "海口", "乌鲁木齐", "拉萨", "银川", "呼和浩特", "南昌", "长春"
        ];

        // 从localStorage加载设置
        function loadSettings() {
            const settings = JSON.parse(localStorage.getItem('weatherSettings')) || {
                city: '北京'
            };

            selectedCity.textContent = settings.city;
        }

        // 保存设置到localStorage
        function saveSettings(city) {
            const settings = {
                city: city || '北京'
            };

            localStorage.setItem('weatherSettings', JSON.stringify(settings));

            // 更新显示
            selectedCity.textContent = settings.city;

            // 更新天气显示
            updateWeatherDisplay();

            // 关闭下拉菜单
            dropdown.classList.remove('show');
        }

        // 更新天气显示
        function updateWeatherDisplay() {
            const settings = JSON.parse(localStorage.getItem('weatherSettings')) || {
                city: '北京'
            };

            // 这里应该调用天气API获取实际数据
            // 这里使用模拟数据
            const weatherText = document.getElementById('weather-text');
            if (weatherText) {
                // 随机天气状况和温度
                const conditions = ['晴', '多云', '阴', '小雨', '中雨', '大雨', '雷阵雨', '小雪', '大雪'];
                const randomCondition = conditions[Math.floor(Math.random() * conditions.length)];
                const randomTemp = Math.floor(Math.random() * 30) + 5; // 5-34度

                weatherText.textContent = `${settings.city} ${randomCondition} ${randomTemp}°C`;
            }
        }

        // 搜索城市
        function searchCity(keyword) {
            if (!keyword) {
                citySearchResults.innerHTML = '';
                citySearchResults.classList.remove('show');
                return;
            }

            const results = allCities.filter(city => city.includes(keyword));

            if (results.length === 0) {
                citySearchResults.innerHTML = '<div class="search-result-item">没有找到匹配的城市</div>';
            } else {
                citySearchResults.innerHTML = results
                    .map(city => `<div class="search-result-item" data-city="${city}">${city}</div>`)
                    .join('');

                // 添加点击事件
                const resultItems = citySearchResults.querySelectorAll('.search-result-item');
                resultItems.forEach(item => {
                    item.addEventListener('click', function() {
                        const city = this.getAttribute('data-city');
                        if (city) {
                            saveSettings(city);
                            citySearch.value = '';
                            citySearchResults.innerHTML = '';
                            citySearchResults.classList.remove('show');
                        }
                    });
                });
            }

            citySearchResults.classList.add('show');
        }

        // 事件监听器
        if (settingsBtn) {
            settingsBtn.addEventListener('click', function(e) {
                e.stopPropagation(); // 阻止事件冒泡
                loadSettings();
                dropdown.classList.toggle('show');

                // 设置下拉菜单位置
                const infoActions = this.closest('.info-actions');
                if (infoActions) {
                    const rect = infoActions.getBoundingClientRect();
                    dropdown.style.top = (rect.bottom + 5) + 'px';
                    dropdown.style.right = (window.innerWidth - rect.right) + 'px';
                }
            });
        }

        // 搜索框事件
        if (citySearch) {
            citySearch.addEventListener('input', function() {
                searchCity(this.value.trim());
            });

            citySearch.addEventListener('focus', function() {
                if (this.value.trim()) {
                    searchCity(this.value.trim());
                }
            });
        }

        // 热门城市点击事件
        cityItems.forEach(item => {
            item.addEventListener('click', function() {
                const city = this.getAttribute('data-city');
                if (city) {
                    saveSettings(city);
                }
            });
        });

        // 点击页面其他地方关闭下拉菜单
        document.addEventListener('click', function(event) {
            if (!dropdown.contains(event.target) && event.target !== settingsBtn && !settingsBtn.contains(event.target)) {
                dropdown.classList.remove('show');
                citySearchResults.classList.remove('show');
            }
        });

        // 初始化
        updateWeatherDisplay();
    });
</script>