﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>情侣头像匹配系统</title>
    <style>
        body {
            font-family: "Microsoft YaHei", sans-serif;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            width: 100%;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            overflow: hidden;
            animation: fadeIn 0.5s ease;
        }
        .header {
            background: linear-gradient(135deg, #ff6b95 0%, #ffa5c0 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 30px;
            text-align: center;
        }
        .loader {
            width: 80px;
            height: 80px;
            border: 5px solid rgba(255, 107, 149, 0.3);
            border-radius: 50%;
            border-top-color: #ff6b95;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        h1 {
            margin: 0;
            font-size: 24px;
        }
        p {
            color: #666;
            margin: 10px 0 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>情侣头像匹配系统</h1>
        </div>
        <div class="content">
            <div class="loader"></div>
            <p>正在检查系统状态，请稍候...</p>
            <p>如果页面没有自动跳转，请<a href="/install">点击这里</a></p>
        </div>
    </div>
    <script>
        setTimeout(function() {
            window.location.href = "/install";
        }, 2000);
    </script>
</body>
</html>

