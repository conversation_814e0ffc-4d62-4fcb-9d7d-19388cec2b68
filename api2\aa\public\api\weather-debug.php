<?php
// 天气API诊断工具

// 关闭错误显示，避免HTML错误输出
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置响应头
header('Content-Type: text/html; charset=utf-8');

echo "<h1>天气API诊断工具</h1>";

try {
    // 包含数据库配置
    $configPath = __DIR__ . '/../../config/config.php';
    if (!file_exists($configPath)) {
        throw new Exception("配置文件不存在: $configPath");
    }
    
    $config = require_once($configPath);
    echo "<p>✅ 配置文件加载成功</p>";
    
    $dbConfig = $config['database'];
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p>✅ 数据库连接成功</p>";
    
    $prefix = $dbConfig['prefix'];
    
    // 获取天气设置
    $stmt = $pdo->prepare("SELECT * FROM {$prefix}weather_settings WHERE id = 1");
    $stmt->execute();
    $settings = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$settings) {
        echo "<p>❌ 天气设置表为空</p>";
        echo "<p>创建默认设置...</p>";
        $stmt = $pdo->prepare("INSERT INTO {$prefix}weather_settings (api_provider, api_key, private_key, default_city, enabled, cache_duration) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(['openweather', '', '', '北京', 1, 30]);
        echo "<p>✅ 默认设置已创建</p>";
        
        // 重新获取设置
        $stmt = $pdo->prepare("SELECT * FROM {$prefix}weather_settings WHERE id = 1");
        $stmt->execute();
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    echo "<h2>当前设置</h2>";
    echo "<ul>";
    echo "<li>API服务商: " . ($settings['api_provider'] ?? 'null') . "</li>";
    echo "<li>API密钥: " . (empty($settings['api_key']) ? '❌ 未设置' : '✅ 已设置 (' . substr($settings['api_key'], 0, 10) . '...)') . "</li>";
    echo "<li>私钥: " . (empty($settings['private_key']) ? '❌ 未设置' : '✅ 已设置 (' . substr($settings['private_key'], 0, 10) . '...)') . "</li>";
    echo "<li>默认城市: " . ($settings['default_city'] ?? 'null') . "</li>";
    echo "<li>启用状态: " . ($settings['enabled'] ? '✅ 启用' : '❌ 禁用') . "</li>";
    echo "</ul>";
    
    if (!$settings['enabled']) {
        echo "<p>❌ 天气功能未启用</p>";
        exit;
    }
    
    $city = $_GET['city'] ?? $settings['default_city'] ?? '北京';
    $provider = $settings['api_provider'] ?? 'openweather';
    $apiKey = $settings['api_key'] ?? '';
    $privateKey = $settings['private_key'] ?? '';
    
    echo "<h2>测试参数</h2>";
    echo "<ul>";
    echo "<li>测试城市: $city</li>";
    echo "<li>服务商: $provider</li>";
    echo "<li>API密钥: " . (empty($apiKey) ? '❌ 空' : '✅ 有值') . "</li>";
    echo "</ul>";
    
    if (empty($apiKey)) {
        echo "<p>❌ API密钥为空，无法测试</p>";
        exit;
    }
    
    echo "<h2>网络连接测试</h2>";
    
    // 测试基本网络连接
    echo "<h3>1. 测试DNS解析</h3>";
    $hosts = [
        'openweathermap' => 'api.openweathermap.org',
        'heweather' => 'devapi.qweather.com',
        'seniverse' => 'api.seniverse.com'
    ];
    
    foreach ($hosts as $name => $host) {
        $ip = gethostbyname($host);
        if ($ip === $host) {
            echo "<p>❌ $name ($host): DNS解析失败</p>";
        } else {
            echo "<p>✅ $name ($host): $ip</p>";
        }
    }
    
    echo "<h3>2. 测试HTTP连接</h3>";
    
    // 测试具体的API调用
    switch ($provider) {
        case 'openweather':
            testOpenWeatherAPI($city, $apiKey);
            break;
        case 'heweather':
            testHeWeatherAPI($city, $apiKey);
            break;
        case 'seniverse':
            testSeniverseAPI($city, $apiKey);
            break;
        default:
            echo "<p>❌ 未知的服务商: $provider</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ 错误: " . $e->getMessage() . "</p>";
}

function testOpenWeatherAPI($city, $apiKey) {
    echo "<h4>OpenWeatherMap API测试</h4>";
    $url = "https://api.openweathermap.org/data/2.5/weather?q=" . urlencode($city) . "&appid=" . $apiKey . "&units=metric&lang=zh_cn";
    echo "<p>请求URL: $url</p>";
    
    testAPICall($url, 'OpenWeatherMap');
}

function testHeWeatherAPI($city, $apiKey) {
    echo "<h4>和风天气API测试</h4>";
    $url = "https://devapi.qweather.com/v7/weather/now?location=" . urlencode($city) . "&key=" . $apiKey;
    echo "<p>请求URL: $url</p>";
    
    testAPICall($url, '和风天气');
}

function testSeniverseAPI($city, $apiKey) {
    echo "<h4>心知天气API测试</h4>";
    $url = "https://api.seniverse.com/v3/weather/now.json?key=" . $apiKey . "&location=" . urlencode($city) . "&language=zh-Hans&unit=c";
    echo "<p>请求URL: $url</p>";
    
    testAPICall($url, '心知天气');
}

function testAPICall($url, $serviceName) {
    echo "<h5>方法1: file_get_contents</h5>";
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 15,
            'user_agent' => 'Weather App/1.0',
            'method' => 'GET'
        ],
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false
        ]
    ]);
    
    $start = microtime(true);
    $response = @file_get_contents($url, false, $context);
    $end = microtime(true);
    
    if ($response === false) {
        echo "<p>❌ file_get_contents 失败</p>";
        $error = error_get_last();
        if ($error) {
            echo "<p>错误信息: " . $error['message'] . "</p>";
        }
    } else {
        echo "<p>✅ file_get_contents 成功 (耗时: " . round(($end - $start) * 1000) . "ms)</p>";
        echo "<p>响应长度: " . strlen($response) . " 字节</p>";
        echo "<details><summary>响应内容</summary><pre>" . htmlspecialchars($response) . "</pre></details>";
    }
    
    echo "<h5>方法2: cURL</h5>";
    
    if (!function_exists('curl_init')) {
        echo "<p>❌ cURL 扩展未安装</p>";
        return;
    }
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Weather App/1.0');
    curl_setopt($ch, CURLOPT_VERBOSE, true);
    
    $start = microtime(true);
    $response = curl_exec($ch);
    $end = microtime(true);
    
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($response === false) {
        echo "<p>❌ cURL 失败</p>";
        echo "<p>错误信息: $error</p>";
    } else {
        echo "<p>✅ cURL 成功 (耗时: " . round(($end - $start) * 1000) . "ms)</p>";
        echo "<p>HTTP状态码: $httpCode</p>";
        echo "<p>响应长度: " . strlen($response) . " 字节</p>";
        echo "<details><summary>响应内容</summary><pre>" . htmlspecialchars($response) . "</pre></details>";
    }
}

echo "<h2>系统信息</h2>";
echo "<ul>";
echo "<li>PHP版本: " . PHP_VERSION . "</li>";
echo "<li>cURL支持: " . (function_exists('curl_init') ? '✅ 是' : '❌ 否') . "</li>";
echo "<li>OpenSSL支持: " . (extension_loaded('openssl') ? '✅ 是' : '❌ 否') . "</li>";
echo "<li>allow_url_fopen: " . (ini_get('allow_url_fopen') ? '✅ 启用' : '❌ 禁用') . "</li>";
echo "<li>user_agent: " . ini_get('user_agent') . "</li>";
echo "</ul>";

?>
<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3, h4, h5 { color: #333; }
p { margin: 5px 0; }
ul { margin: 10px 0; }
details { margin: 10px 0; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
