/**
 * 最小化修复脚本
 * 只解决：1. 头像遮罩问题  2. 模态框误触关闭问题
 */

(function() {
    'use strict';

    console.log('最小化修复脚本已加载');

    // 解决头像遮罩问题的函数
    function fixAvatarMask() {
        const userInfo = document.querySelector('.user-info');
        if (userInfo) {
            // 方法：降低亮度，禁用点击
            userInfo.style.setProperty('filter', 'brightness(0.6)', 'important');
            userInfo.style.setProperty('pointer-events', 'none', 'important');
            console.log('✅ 头像已变暗并禁用点击');
        }
    }

    // 恢复头像正常状态
    function restoreAvatar() {
        const userInfo = document.querySelector('.user-info');
        if (userInfo) {
            userInfo.style.removeProperty('filter');
            userInfo.style.removeProperty('pointer-events');
            console.log('✅ 头像已恢复正常');
        }
    }

    // 强化模态框防误触
    function preventModalClose() {
        const modal = document.getElementById('weatherSettingsModal');
        if (modal) {
            // 使用捕获阶段阻止点击空白处关闭（更强力）
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    e.stopPropagation();
                    e.stopImmediatePropagation();
                    e.preventDefault();
                    console.log('🚫 阻止点击空白处关闭');
                    return false;
                }
            }, true); // 使用捕获阶段

            // 阻止ESC键关闭
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && modal.classList.contains('show')) {
                    e.stopPropagation();
                    e.stopImmediatePropagation();
                    e.preventDefault();
                    console.log('🚫 阻止ESC键关闭');
                    return false;
                }
            }, true); // 使用捕获阶段

            // 额外保护：重新配置模态框实例
            setTimeout(() => {
                const existingModal = bootstrap.Modal.getInstance(modal);
                if (existingModal) {
                    existingModal.dispose();
                }

                // 重新创建为static backdrop
                new bootstrap.Modal(modal, {
                    backdrop: 'static',
                    keyboard: false
                });
                console.log('✅ 模态框已重新配置为static backdrop');
            }, 100);
        }
    }

    // 监听背景遮罩变化，实现完美同步
    function watchBackdropChanges() {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    // 检查是否有modal-backdrop被添加
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1 && node.classList && node.classList.contains('modal-backdrop')) {
                            console.log('🎭 背景遮罩出现，同步应用头像遮罩');
                            setTimeout(() => fixAvatarMask(), 10);
                        }
                    });

                    // 检查是否有modal-backdrop被移除
                    mutation.removedNodes.forEach(function(node) {
                        if (node.nodeType === 1 && node.classList && node.classList.contains('modal-backdrop')) {
                            console.log('🎭 背景遮罩消失，同步恢复头像');
                            restoreAvatar();
                        }
                    });
                }
            });
        });

        observer.observe(document.body, { childList: true });
        console.log('✅ 背景遮罩监听器已启动');
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(() => {
            const weatherModal = document.getElementById('weatherSettingsModal');
            if (weatherModal) {
                // 模态框即将显示时
                weatherModal.addEventListener('show.bs.modal', function() {
                    console.log('🔒 模态框即将显示');
                    // 延迟一点点，与Bootstrap背景遮罩同步
                    setTimeout(() => {
                        fixAvatarMask();
                    }, 50);
                });

                // 模态框完全显示后，确保遮罩已应用
                weatherModal.addEventListener('shown.bs.modal', function() {
                    // 双重保险，确保遮罩已应用
                    fixAvatarMask();
                });

                // 模态框即将隐藏时（与背景遮罩退出同步）
                weatherModal.addEventListener('hide.bs.modal', function() {
                    console.log('🔓 模态框即将隐藏，立即恢复头像');
                    restoreAvatar();
                });

                // 设置防误触
                preventModalClose();
            }

            // 启动背景遮罩监听器，实现完美同步
            watchBackdropChanges();
        }, 500);
    });

})();
