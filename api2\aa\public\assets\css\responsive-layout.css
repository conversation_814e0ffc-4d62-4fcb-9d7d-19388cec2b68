/**
 * 响应式布局CSS
 * 用于实现侧边栏收起后页面自适应的功能
 */

/* 布局样式 */
.dashboard-container {
    display: flex;
    min-height: 100vh;
    position: relative;
}

/* 侧边栏样式 */
.sidebar {
    width: 250px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--white-color);
    padding: 20px 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 2px 0 10px var(--shadow-color);
    transition: all var(--transition-speed);
}

.sidebar.collapsed {
    width: 70px;
}

/* 主内容区域样式 */
.main-content {
    flex: 1;
    margin-left: 250px;
    padding: 20px;
    transition: all var(--transition-speed);
    width: calc(100% - 250px);
}

.main-content.expanded {
    margin-left: 70px;
    width: calc(100% - 70px);
}

/* 响应式布局 */
@media (max-width: 992px) {
    .sidebar {
        width: 70px;
    }
    
    .sidebar.expanded {
        width: 250px;
    }
    
    .main-content {
        margin-left: 70px;
        width: calc(100% - 70px);
    }
    
    .main-content.collapsed {
        margin-left: 250px;
        width: calc(100% - 250px);
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 0;
        opacity: 0;
        visibility: hidden;
    }
    
    .sidebar.expanded {
        width: 250px;
        opacity: 1;
        visibility: visible;
    }
    
    .main-content {
        margin-left: 0;
        width: 100%;
    }
    
    .main-content.collapsed {
        margin-left: 0;
        width: 100%;
    }
}

/* 网格布局自适应 */
.grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.grid-item {
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px var(--shadow-color);
    overflow: hidden;
    transition: all var(--transition-speed);
}

/* 卡片布局自适应 */
.card-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.card {
    flex: 1 1 300px;
    min-width: 0;
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px var(--shadow-color);
    overflow: hidden;
    transition: all var(--transition-speed);
}
