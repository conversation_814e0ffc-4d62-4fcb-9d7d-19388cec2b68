﻿<?php
// 开启会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 包含安全头部设置
require_once($_SERVER['DOCUMENT_ROOT'] . '/includes/security-headers.php');

// 启用错误报告
ini_set('display_errors', 1);
error_reporting(E_ALL);

// 记录请求信息
$logFile = __DIR__ . '/../../storage/logs/admin.log';
$logDir = dirname($logFile);
if (!is_dir($logDir)) {
    mkdir($logDir, 0777, true);
}

file_put_contents($logFile, date('Y-m-d H:i:s') . " - 控制面板请求开始\n", FILE_APPEND);
file_put_contents($logFile, "SESSION 数据: " . print_r($_SESSION, true) . "\n", FILE_APPEND);

// 记录安全日志
logSecurityEvent('页面访问', [
    'page' => 'admin.php',
    'user_id' => $_SESSION['user_id'] ?? 'unknown'
]);

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    // 未登录，重定向到登录页面
    header('Location: /login');
    exit;
}

// 数据库配置
$config = [
    'host' => 'localhost',
    'database' => 'qq',
    'username' => 'qq',
    'password' => '123456',
    'charset' => 'utf8mb4',
    'prefix' => 'yh_'
];

// 连接数据库
try {
    $dsn = "mysql:host={$config['host']};dbname={$config['database']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    // 如果数据库连接失败，使用模拟数据
    $pdo = null;
}

// 获取统计数据
function getStats($pdo, $prefix) {
    if (!$pdo) {
        // 返回模拟数据
        return [
            'total_today' => 0,
            'success_today' => 0,
            'failed_today' => 0,
            'success_rate' => 0,
            'total_yesterday' => 0,
            'total_users' => 29,
            'point_users' => 15,
            'monthly_users' => 14,
            'total_orders' => 9,
            'paid_orders' => 8,
            'total_revenue' => 305.00,
        ];
    }

    $today = date('Y-m-d');
    $yesterday = date('Y-m-d', strtotime('-1 day'));

    try {
        // 今日调用统计
        $stmt = $pdo->prepare("
            SELECT
                COUNT(*) as total_today,
                COUNT(CASE WHEN status = 1 THEN 1 END) as success_today,
                COUNT(CASE WHEN status = 0 THEN 1 END) as failed_today
            FROM {$prefix}record
            WHERE DATE(FROM_UNIXTIME(intime)) = ?
        ");
        $stmt->execute([$today]);
        $todayStats = $stmt->fetch(PDO::FETCH_ASSOC);

        // 昨日调用统计
        $stmt = $pdo->prepare("SELECT COUNT(*) as total_yesterday FROM {$prefix}record WHERE DATE(FROM_UNIXTIME(intime)) = ?");
        $stmt->execute([$yesterday]);
        $yesterdayStats = $stmt->fetch(PDO::FETCH_ASSOC);

        // 用户统计
        $stmt = $pdo->prepare("
            SELECT
                COUNT(*) as total_users,
                COUNT(CASE WHEN type = 1 THEN 1 END) as point_users,
                COUNT(CASE WHEN type = 2 THEN 1 END) as monthly_users
            FROM {$prefix}business
        ");
        $stmt->execute();
        $userStats = $stmt->fetch(PDO::FETCH_ASSOC);

        // 订单统计
        $stmt = $pdo->prepare("
            SELECT
                COUNT(*) as total_orders,
                COUNT(CASE WHEN status = 1 THEN 1 END) as paid_orders,
                SUM(CASE WHEN status = 1 THEN payment ELSE 0 END) as total_revenue
            FROM {$prefix}order
        ");
        $stmt->execute();
        $orderStats = $stmt->fetch(PDO::FETCH_ASSOC);

        // 计算成功率
        $successRate = $todayStats['total_today'] > 0
            ? round(($todayStats['success_today'] / $todayStats['total_today']) * 100, 2)
            : 0;

        return [
            'total_today' => (int)($todayStats['total_today'] ?? 0),
            'success_today' => (int)($todayStats['success_today'] ?? 0),
            'failed_today' => (int)($todayStats['failed_today'] ?? 0),
            'success_rate' => $successRate,
            'total_yesterday' => (int)($yesterdayStats['total_yesterday'] ?? 0),
            'total_users' => (int)($userStats['total_users'] ?? 0),
            'point_users' => (int)($userStats['point_users'] ?? 0),
            'monthly_users' => (int)($userStats['monthly_users'] ?? 0),
            'total_orders' => (int)($orderStats['total_orders'] ?? 0),
            'paid_orders' => (int)($orderStats['paid_orders'] ?? 0),
            'total_revenue' => (float)($orderStats['total_revenue'] ?? 0),
        ];
    } catch (Exception $e) {
        // 如果查询失败，返回模拟数据
        return [
            'total_today' => 0,
            'success_today' => 0,
            'failed_today' => 0,
            'success_rate' => 0,
            'total_yesterday' => 0,
            'total_users' => 29,
            'point_users' => 15,
            'monthly_users' => 14,
            'total_orders' => 9,
            'paid_orders' => 8,
            'total_revenue' => 305.00,
        ];
    }
}

// 获取数据
$stats = getStats($pdo, $config['prefix']);

// 获取用户角色
$isAdmin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
$username = $_SESSION['username'] ?? '用户';
$userInitial = mb_substr($username, 0, 1, 'UTF-8');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制面板 - 情侣头像匹配系统</title>
    <link href="/assets/vendor/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="/assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="/assets/vendor/animate/animate.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/theme.css">
    <link rel="stylesheet" href="/assets/css/animations.css">
    <link rel="stylesheet" href="/assets/css/enhanced-animations.css">
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/theme-switcher.css">
    <link rel="stylesheet" href="/assets/css/ui-enhancements.css">
    <link rel="stylesheet" href="/assets/css/stat-item-fix.css">
    <link rel="stylesheet" href="/assets/css/dashboard-value-fix.css">
    <link rel="stylesheet" href="/assets/css/service-info-fix.css">
    <link rel="stylesheet" href="/assets/css/dashboard-position-fix.css">
    <link rel="stylesheet" href="/assets/css/dashboard-alignment-fix.css">
    <link rel="stylesheet" href="/assets/css/admin-responsive.css">
    <!-- 增强图表和安全性样式 -->
    <link rel="stylesheet" href="/assets/css/enhanced-charts.css">
    <link rel="stylesheet" href="/assets/css/security-enhancements.css">
    <style>
        /* 基础样式 */
        :root {
            --primary-color: #ff6b95;
            --secondary-color: #ffa5c0;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --gray-color: #6c757d;
            --white-color: #ffffff;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --transition-speed: 0.3s;
            --border-radius: 8px;
            --animate-delay: 0.1s;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }

        body {
            background-color: #f5f7fa;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: hidden;
        }

        /* 加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 107, 149, 0.3);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 布局样式 */
        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--white-color);
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 2px 0 10px var(--shadow-color);
            transition: all var(--transition-speed);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            margin-bottom: 20px;
        }

        .sidebar-logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 10px;
            object-fit: cover;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .sidebar-title {
            color: var(--white-color);
            font-size: 1.5rem;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .sidebar-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .sidebar.collapsed .sidebar-title,
        .sidebar.collapsed .sidebar-subtitle {
            display: none;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu li a {
            color: var(--white-color);
            text-decoration: none;
            display: block;
            padding: 12px 20px;
            transition: all var(--transition-speed);
            border-left: 4px solid transparent;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.2);
            border-left-color: var(--white-color);
        }

        .sidebar-menu li a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }

        .sidebar.collapsed .sidebar-menu li a span {
            display: none;
        }

        .sidebar.collapsed .sidebar-menu li a i {
            margin-right: 0;
            font-size: 1.3rem;
        }

        /* 子菜单样式 */
        .menu-section {
            position: relative;
        }

        .menu-section .menu-header {
            position: relative;
        }

        .menu-section .toggle-icon {
            position: absolute;
            right: 20px;
            transition: transform 0.3s ease;
        }

        .menu-section.active .toggle-icon {
            transform: rotate(180deg);
        }

        .submenu {
            list-style: none;
            padding: 0;
            margin: 0;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background-color: rgba(0, 0, 0, 0.1);
        }

        .menu-section.active .submenu {
            max-height: 500px;
        }

        .submenu li a {
            padding: 10px 20px 10px 50px;
            font-size: 0.9rem;
            border-left: 2px solid transparent;
        }

        .submenu li a:hover {
            background-color: rgba(255, 255, 255, 0.15);
            border-left-color: rgba(255, 255, 255, 0.5);
        }

        .sidebar.collapsed .submenu {
            display: none;
        }

        /* 主内容区域样式 */
        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 20px;
            transition: all var(--transition-speed);
        }

        .main-content.expanded {
            margin-left: 70px;
        }

        /* 头部样式 */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        /* 天气信息和人生哲理组件样式 */
        .header-info-content {
            display: flex;
            align-items: center;
            margin: 0 auto;
            padding: 0 15px;
        }

        .wisdom-quote, .weather-info {
            display: flex;
            align-items: center;
            margin-right: 15px;
            font-size: 0.9rem;
            color: var(--gray-color);
        }

        .wisdom-quote i, .weather-info i {
            margin-right: 5px;
            font-size: 1.1rem;
            color: var(--primary-color);
        }

        .info-actions {
            display: flex;
            gap: 5px;
        }

        .info-actions button {
            background: none;
            border: none;
            color: var(--gray-color);
            cursor: pointer;
            font-size: 0.9rem;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            transition: all 0.2s;
        }

        .info-actions button:hover {
            color: var(--primary-color);
            background-color: rgba(255, 107, 149, 0.1);
        }

        .menu-toggle {
            background: none;
            border: none;
            color: var(--dark-color);
            font-size: 1.5rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            transition: all var(--transition-speed);
        }

        .menu-toggle:hover {
            background-color: var(--light-color);
        }

        .user-info {
            display: flex;
            align-items: center;
            position: relative;
            cursor: pointer;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: var(--white-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }

        .user-name {
            font-weight: 500;
        }

        .dropdown-content {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: var(--white-color);
            min-width: 160px;
            box-shadow: 0 8px 16px 0 var(--shadow-color);
            border-radius: var(--border-radius);
            padding: 10px 0;
            z-index: 1;
            display: none;
            animation: fadeIn 0.3s;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .user-info:hover .dropdown-content {
            display: block;
        }

        .dropdown-content a {
            color: var(--dark-color);
            padding: 10px 20px;
            text-decoration: none;
            display: block;
            transition: all var(--transition-speed);
        }

        .dropdown-content a:hover {
            background-color: var(--light-color);
        }

        .dropdown-content a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        /* 仪表盘特定样式 */
        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr); /* 固定3列布局 */
            gap: 20px;
            margin-bottom: 30px;
            width: 100%; /* 确保占满整个容器宽度 */
            transition: none; /* 移除过渡效果，避免抖动 */
        }

        /* 小屏幕单列布局 */
        @media (max-width: 767.98px) {
            .dashboard-stats {
                grid-template-columns: 1fr;
            }
        }

        .stat-card {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: 0 5px 15px var(--shadow-color);
            padding: 20px;
            display: flex;
            align-items: flex-start;
            gap: 15px;
            transition: all var(--transition-speed);
            animation: fadeInUp 0.5s;
            border-top: 3px solid var(--primary-color);
        }

        .stat-card:hover {
            box-shadow: 0 8px 25px var(--shadow-color);
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--white-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            box-shadow: 0 5px 15px rgba(255, 107, 149, 0.3);
        }

        .stat-content {
            flex: 1;
        }

        .stat-content h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 15px;
            position: relative;
            padding-bottom: 10px;
            text-align: center;
        }

        .stat-content h3::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 3px;
            background-color: var(--primary-color);
            border-radius: 3px;
        }

        .stat-details {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            gap: 5px;
            text-align: center;
        }

        .stat-label {
            color: var(--gray-color);
            font-weight: 500;
            text-align: right;
            width: 45%;
        }

        .stat-value {
            color: var(--dark-color);
            font-weight: 600;
            text-align: left;
            width: 45%;
        }

        .status-active {
            color: var(--success-color);
        }

        .version-history {
            margin-left: 10px;
            font-size: 0.8rem;
            color: var(--primary-color);
            text-decoration: none;
        }

        .version-history:hover {
            text-decoration: underline;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
            margin-left: 10px;
        }

        .btn-xs {
            padding: 0.1rem 0.3rem;
            font-size: 0.75rem;
            line-height: 1.2;
            border-radius: 0.2rem;
        }

        .dashboard-sections {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 20px;
        }

        .section-row {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .section {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: 0 5px 15px var(--shadow-color);
            overflow: hidden;
            animation: fadeInUp 0.5s;
            height: 100%;
        }

        .section-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }

        .section-header h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark-color);
            display: flex;
            align-items: center;
        }

        .section-header h3 i {
            margin-right: 8px;
            color: var(--primary-color);
        }

        .view-all {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all var(--transition-speed);
        }

        .view-all:hover {
            color: #ff4f7e;
            text-decoration: underline;
        }

        .section-content {
            padding: 20px;
        }

        .announcement-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .announcement-item {
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
            transition: all var(--transition-speed);
        }

        .announcement-item:hover {
            transform: translateX(5px);
        }

        .announcement-item:last-child {
            padding-bottom: 0;
            border-bottom: none;
        }

        .announcement-title {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 5px;
        }

        .announcement-time {
            font-size: 0.8rem;
            color: var(--gray-color);
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .announcement-time::before {
            content: "\\F282";
            font-family: "bootstrap-icons";
            margin-right: 5px;
            font-size: 0.9rem;
        }

        .shortcut-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 15px;
        }

        .shortcut-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: var(--dark-color);
            padding: 20px 15px;
            border-radius: var(--border-radius);
            transition: all var(--transition-speed);
            background-color: #f8f9fa;
            box-shadow: 0 2px 5px var(--shadow-color);
        }

        .shortcut-item:hover {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--white-color);
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(255, 107, 149, 0.3);
        }

        .shortcut-item i {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .shortcut-item span {
            font-weight: 500;
        }

        /* 支付方式样式 */
        .payment-methods {
            display: flex;
            gap: 15px;
            margin-top: 10px;
        }

        .payment-option {
            flex: 1;
            border: 1px solid #eee;
            border-radius: var(--border-radius);
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-speed);
            position: relative;
        }

        .payment-option:hover {
            border-color: var(--primary-color);
            background-color: rgba(255, 107, 149, 0.05);
        }

        .payment-option input {
            position: absolute;
            top: 10px;
            left: 10px;
            margin: 0;
        }

        .payment-option input:checked + .payment-icon {
            color: var(--primary-color);
        }

        .payment-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: var(--gray-color);
        }

        .payment-name {
            font-weight: 500;
        }

        /* 动画 */
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 信息卡片样式 */
        .info-card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: 0 4px 10px var(--shadow-color);
            transition: all var(--transition-speed);
            overflow: hidden;
        }

        .info-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px var(--shadow-color);
        }

        .card-body {
            display: flex;
            align-items: center;
            padding: 20px;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: var(--white-color);
            font-size: 1.5rem;
        }

        .bg-primary {
            background-color: var(--primary-color);
        }

        .bg-success {
            background-color: var(--success-color);
        }

        .bg-warning {
            background-color: var(--warning-color);
        }

        .bg-info {
            background-color: var(--info-color);
        }

        .card-info {
            flex: 1;
        }

        .card-title {
            font-size: 0.9rem;
            color: var(--gray-color);
            margin-bottom: 5px;
        }

        .card-value {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
        }

        /* 公告样式 */
        .announcement-item {
            padding: 15px 0;
            border-bottom: 1px solid var(--light-color);
        }

        .announcement-item:last-child {
            border-bottom: none;
        }

        .announcement-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .announcement-title {
            font-weight: 600;
            margin: 0;
        }

        .announcement-date {
            font-size: 0.8rem;
            color: var(--gray-color);
        }

        .announcement-content {
            color: var(--dark-color);
            line-height: 1.6;
        }

        /* 动画效果 */
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s;
        }

        .animate-on-scroll.animated {
            opacity: 1;
            transform: translateY(0);
        }

        /* 卡片样式 */
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: 0 4px 10px var(--shadow-color);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            background-color: var(--white-color);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 15px 20px;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1001;
            overflow-y: auto;
            padding: 20px;
        }

        .modal-content {
            background-color: var(--white-color);
            margin: 5% auto;
            width: 600px;
            max-width: 90%;
            border-radius: var(--border-radius);
            box-shadow: 0 10px 30px var(--shadow-color);
            animation: slideDown 0.3s;
            overflow: hidden;
        }

        @keyframes slideDown {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark-color);
        }

        .close-btn {
            background: none;
            border: none;
            color: var(--gray-color);
            font-size: 1.2rem;
            cursor: pointer;
            transition: all var(--transition-speed);
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .close-btn:hover {
            color: var(--danger-color);
            background-color: rgba(220, 53, 69, 0.1);
        }

        .modal-body {
            padding: 20px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            background-color: #f8f9fa;
        }

        .version-item {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--light-color);
        }

        .version-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .version-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .version-number {
            font-weight: 600;
        }

        .version-date {
            font-size: 0.8rem;
            color: var(--gray-color);
        }

        .version-changes {
            list-style-type: disc;
            padding-left: 20px;
            margin: 0;
        }

        .version-changes li {
            margin-bottom: 5px;
        }

        /* 消息提示样式 */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 300px;
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: 0 5px 15px var(--shadow-color);
            overflow: hidden;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transform: translateX(30px);
            transition: all 0.3s;
        }

        .toast.show {
            opacity: 1;
            visibility: visible;
            transform: translateX(0);
        }

        .toast-content {
            display: flex;
            align-items: center;
            padding: 15px;
        }

        .toast-icon {
            font-size: 1.5rem;
            margin-right: 15px;
            display: none;
        }

        .toast-icon.success {
            color: var(--success-color);
        }

        .toast-icon.error {
            color: var(--danger-color);
        }

        .toast-icon.info {
            color: var(--info-color);
        }

        .toast-message {
            flex: 1;
            font-weight: 500;
        }

        .toast-progress {
            height: 3px;
            background-color: var(--primary-color);
            width: 100%;
            animation: progress 3s linear;
        }

        @keyframes progress {
            from { width: 100%; }
            to { width: 0%; }
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .sidebar {
                width: 0;
                padding: 0;
            }

            .sidebar.collapsed {
                width: 70px;
            }

            .main-content {
                margin-left: 0;
            }

            .main-content.expanded {
                margin-left: 70px;
            }

            .dashboard-welcome {
                flex-direction: column;
            }

            .welcome-illustration {
                display: none;
            }
        }

        .stat-value-with-btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.stat-value {
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
}

/* 修复天气和人生哲理组件样式 */
.header-info-content {
    display: flex;
    align-items: center;
    margin: 0 auto;
    flex: 1;
    padding: 0 20px;
    overflow: hidden;
    color: var(--text-color, #333);
    font-size: 14px;
    max-width: 600px;
    justify-content: center;
}

.wisdom-quote, .weather-info {
    display: flex;
    align-items: center;
    margin-right: 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    animation: fadeIn 0.5s;
    max-width: 250px;
}

.wisdom-quote i, .weather-info i {
    margin-right: 8px;
    font-size: 16px;
    color: var(--primary-color);
    flex-shrink: 0;
}

.info-actions {
    display: flex;
    gap: 10px;
    margin-left: 10px;
}

.info-actions button {
    background: rgba(var(--primary-color-rgb, 255, 107, 149), 0.1);
    border: none;
    color: var(--primary-color);
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    flex-shrink: 0;
}

.info-actions button:hover {
    background: rgba(var(--primary-color-rgb, 255, 107, 149), 0.2);
    transform: rotate(15deg);
}

.info-actions button:active {
    background: rgba(var(--primary-color-rgb, 255, 107, 149), 0.3);
    transform: scale(0.95);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.rotating {
    animation: rotate 1s linear;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .wisdom-quote {
        display: none !important;
    }

    .weather-info {
        margin-right: 0;
        max-width: 150px;
    }

    .header-info-content {
        padding: 0 10px;
    }
}
</style>
</head>
<body>
    <div class="loading-overlay" id="loading-overlay">
        <div class="spinner"></div>
    </div>

    <div class="dashboard-container">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <img src="/assets/images/logo.png" alt="Logo" class="sidebar-logo" onerror="this.src='data:image/svg+xml;utf8,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 24 24%22 fill=%22%23ffffff%22%3E%3Cpath d=%22M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z%22/%3E%3C/svg%3E'">
                <h3 class="sidebar-title">去水印接口</h3>
                <p class="sidebar-subtitle">管理系统</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="/dashboard/admin.php" class="active"><i class="bi bi-speedometer2"></i> <span>仪表盘</span></a></li>

                <!-- 全局设置 -->
                <?php if ($isAdmin): ?>
                <li class="menu-section">
                    <a href="#" class="menu-header"><i class="bi bi-gear"></i> <span>全局设置</span> <i class="bi bi-chevron-down toggle-icon"></i></a>
                    <ul class="submenu">
                        <li><a href="/dashboard/settings/website"><i class="bi bi-globe"></i> <span>网站设置</span></a></li>
                        <li><a href="/dashboard/settings/register"><i class="bi bi-person-plus"></i> <span>注册设置</span></a></li>
                        <li><a href="/dashboard/settings/payment"><i class="bi bi-credit-card"></i> <span>支付设置</span></a></li>
                        <li><a href="/dashboard/settings/login"><i class="bi bi-box-arrow-in-right"></i> <span>登录设置</span></a></li>
                        <li><a href="/dashboard/settings/api"><i class="bi bi-code-slash"></i> <span>接口设置</span></a></li>
                        <li><a href="/dashboard/settings/homepage"><i class="bi bi-house"></i> <span>网站首页</span></a></li>
                    </ul>
                </li>
                <?php endif; ?>

                <!-- 用户管理 -->
                <li class="menu-section">
                    <a href="#" class="menu-header"><i class="bi bi-people"></i> <span>用户管理</span> <i class="bi bi-chevron-down toggle-icon"></i></a>
                    <ul class="submenu">
                        <li><a href="/dashboard/accounts/users.php"><i class="bi bi-list"></i> <span>用户列表</span></a></li>
                        <li><a href="/dashboard/accounts/increase"><i class="bi bi-plus-circle"></i> <span>账户加款</span></a></li>
                        <li><a href="/dashboard/accounts/package"><i class="bi bi-gift"></i> <span>添加套餐</span></a></li>
                    </ul>
                </li>

                <!-- 订单管理 -->
                <li class="menu-section">
                    <a href="#" class="menu-header"><i class="bi bi-cart3"></i> <span>订单管理</span> <i class="bi bi-chevron-down toggle-icon"></i></a>
                    <ul class="submenu">
                        <li><a href="/dashboard/orders"><i class="bi bi-receipt"></i> <span>订单列表</span></a></li>
                        <li><a href="/dashboard/orders/calls"><i class="bi bi-telephone"></i> <span>调用记录</span></a></li>
                        <li><a href="/dashboard/orders/ranking"><i class="bi bi-trophy"></i> <span>用户排行</span></a></li>
                        <li><a href="/dashboard/orders/errors"><i class="bi bi-exclamation-triangle"></i> <span>错误排行</span></a></li>
                    </ul>
                </li>

                <!-- 套餐管理 -->
                <li class="menu-section">
                    <a href="#" class="menu-header"><i class="bi bi-box"></i> <span>套餐管理</span> <i class="bi bi-chevron-down toggle-icon"></i></a>
                    <ul class="submenu">
                        <li><a href="/dashboard/platforms/add"><i class="bi bi-plus"></i> <span>添加套餐</span></a></li>
                        <li><a href="/dashboard/platforms"><i class="bi bi-list"></i> <span>套餐列表</span></a></li>
                    </ul>
                </li>

                <!-- 到期提醒 -->
                <li class="menu-section">
                    <a href="#" class="menu-header"><i class="bi bi-bell"></i> <span>到期提醒</span> <i class="bi bi-chevron-down toggle-icon"></i></a>
                    <ul class="submenu">
                        <li><a href="/dashboard/recycle"><i class="bi bi-gear"></i> <span>到期提醒配置</span></a></li>
                        <li><a href="/dashboard/recycle/logs"><i class="bi bi-journal-text"></i> <span>到期提醒记录</span></a></li>
                    </ul>
                </li>

                <!-- 系统管理 -->
                <?php if ($isAdmin): ?>
                <li class="menu-section">
                    <a href="#" class="menu-header"><i class="bi bi-tools"></i> <span>系统管理</span> <i class="bi bi-chevron-down toggle-icon"></i></a>
                    <ul class="submenu">
                        <li><a href="/dashboard/plugins"><i class="bi bi-info-circle"></i> <span>功能介绍</span></a></li>
                        <li><a href="/dashboard/plugins/update"><i class="bi bi-arrow-up-circle"></i> <span>系统更新</span></a></li>
                    </ul>
                </li>
                <?php endif; ?>

                <li><a href="/logout.php"><i class="bi bi-box-arrow-right"></i> <span>退出登录</span></a></li>
            </ul>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content" id="main-content">
            <div class="header">
                <button class="menu-toggle" id="menu-toggle"><i class="bi bi-list"></i></button>
                <div class="header-info-content" id="header-info-content">
                    <div class="wisdom-quote">
                        <i class="bi bi-quote"></i>
                        <span id="wisdom-text">加载中...</span>
                    </div>
                    <div class="weather-info">
                        <i class="bi bi-cloud-sun"></i>
                        <span id="weather-text">加载中...</span>
                    </div>
                    <div class="info-actions">
                        <button id="refresh-info" title="刷新"><i class="bi bi-arrow-clockwise"></i></button>
                        <button id="toggle-info-type" title="切换显示内容"><i class="bi bi-shuffle"></i></button>
                        <button id="settings-info" title="设置" onclick="toggleWeatherSettings(event)" style="cursor: pointer; pointer-events: auto;"><i class="bi bi-gear"></i></button>
                    </div>

                    <!-- 天气设置下拉菜单 -->
                    <div id="weather-settings-dropdown" class="weather-settings-dropdown" style="display: none; position: absolute; z-index: 9999;">
                        <div class="dropdown-content">
                            <div class="dropdown-header">
                                <h3>天气设置</h3>
                                <button class="dropdown-close" onclick="closeWeatherSettings(event)" style="cursor: pointer;"><i class="bi bi-x"></i></button>
                            </div>
                            <div class="dropdown-body">
                                <div class="city-search-container">
                                    <input type="text" id="weather-city-search" placeholder="搜索城市..." class="city-search-input">
                                    <div class="city-search-results" id="city-search-results"></div>
                                </div>
                                <div class="selected-city-container">
                                    <div class="selected-city-label">当前选择的城市</div>
                                    <div class="selected-city" id="selected-city">北京</div>
                                </div>
                                <div class="common-cities">
                                    <div class="common-cities-label">热门城市</div>
                                    <div class="common-cities-grid">
                                        <div class="city-item" data-city="北京">北京</div>
                                        <div class="city-item" data-city="上海">上海</div>
                                        <div class="city-item" data-city="广州">广州</div>
                                        <div class="city-item" data-city="深圳">深圳</div>
                                        <div class="city-item" data-city="杭州">杭州</div>
                                        <div class="city-item" data-city="成都">成都</div>
                                        <div class="city-item" data-city="重庆">重庆</div>
                                        <div class="city-item" data-city="武汉">武汉</div>
                                        <div class="city-item" data-city="西安">西安</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="user-info dropdown">
                    <div class="user-avatar"><?php echo htmlspecialchars($userInitial); ?></div>
                    <span class="user-name"><?php echo htmlspecialchars($username); ?></span>
                    <div class="dropdown-content">
                        <a href="/profile/index.php"><i class="bi bi-person"></i> 个人资料</a>
                        <a href="/dashboard/settings"><i class="bi bi-gear"></i> 设置</a>
                        <a href="/logout.php"><i class="bi bi-box-arrow-right"></i> 退出登录</a>
                    </div>
                </div>
            </div>

            <!-- 面包屑导航 -->
            <?php
            // 设置当前页面标题
            $page_title = '控制面板';
            // 设置当前页面路径
            $page_path = [];
            // 包含面包屑导航组件
            include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/admin-breadcrumb.php');
            ?>

            <!-- 账户概述 -->
            <div class="dashboard-stats">
                <div class="stat-card floating-card hover-card">
                    <div class="stat-icon"><i class="bi bi-graph-up rotate-icon"></i></div>
                    <div class="stat-content">
                        <h3>数据统计</h3>
                        <div class="stat-details">
                            <div class="stat-grid">
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-graph-up"></i> 今日调用</span>
                                    <span class="stat-value"><?php echo number_format($stats['total_today']); ?></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-check-circle"></i> 成功调用</span>
                                    <span class="stat-value"><?php echo number_format($stats['success_today']); ?></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-percent"></i> 成功率</span>
                                    <span class="stat-value"><?php echo $stats['success_rate']; ?>%</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-people"></i> 总用户数</span>
                                    <span class="stat-value"><?php echo number_format($stats['total_users']); ?></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-cart"></i> 总订单数</span>
                                    <span class="stat-value"><?php echo number_format($stats['total_orders']); ?></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-currency-yen"></i> 总收入</span>
                                    <span class="stat-value">¥<?php echo number_format($stats['total_revenue'], 2); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="stat-card floating-card hover-card">
                    <div class="stat-icon"><i class="bi bi-shield-check rotate-icon"></i></div>
                    <div class="stat-content">
                        <h3>系统概况</h3>
                        <div class="stat-details">
                            <div class="stat-grid">
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-shield-check"></i> 授权状态</span>
                                    <span class="stat-value status-active">已授权</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-award"></i> 授权类型</span>
                                    <span class="stat-value"><?php echo $isAdmin ? '企业版' : '标准版'; ?></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-person-badge"></i> 账户权限</span>
                                    <span class="stat-value"><?php echo $isAdmin ? '管理员' : '租户'; ?></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-server"></i> 部署方式</span>
                                    <span class="stat-value">独立部署</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-clock-history"></i> 系统运行时间</span>
                                    <span class="stat-value">128天</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-hdd-stack"></i> 运行环境</span>
                                    <div class="stat-value-multiline">
                                        <span class="stat-value">PHP 7.4</span>
                                        <span class="stat-value">MySQL 5.7</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="stat-card floating-card hover-card">
    <div class="stat-icon"><i class="bi bi-calendar-check rotate-icon"></i></div>
    <div class="stat-content">
        <h3>服务信息</h3>
        <div class="stat-details">
            <div class="stat-grid">
                <div class="stat-item">
                    <span class="stat-label"><i class="bi bi-calendar-check"></i> 授权时长</span>
                    <span class="stat-value"><?php echo $isAdmin ? '永久' : '2024-12-31'; ?></span>
                    <button class="btn btn-sm btn-outline-primary btn-xs neon-btn" style="margin-top: 8px;" onclick="showUpdateVersion()">更新版本</button>
                </div>
                <div class="stat-item">
                    <span class="stat-label"><i class="bi bi-code-square"></i> 当前版本</span>
                    <span class="stat-value">v1.0.2</span>
                    <a href="javascript:void(0)" class="btn btn-sm btn-outline-primary btn-xs" style="margin-top: 8px;" onclick="showVersionHistory()">历史版本</a>
                </div>
                <div class="stat-item">
                    <span class="stat-label"><i class="bi bi-hourglass-split"></i> 服务期限</span>
                    <span class="stat-value">2024-12-31</span>
                    <button class="btn btn-sm btn-outline-primary btn-xs neon-btn" style="margin-top: 8px;" onclick="openRenewModal()">续费</button>
                </div>
                <div class="stat-item">
                    <span class="stat-label"><i class="bi bi-calendar-date"></i> 系统时间</span>
                    <span class="stat-value" id="current-date">23年5月15日</span>
                    <span class="stat-value" id="current-time" style="margin-top: 5px; color: var(--primary-color); font-family: 'Courier New', monospace; font-weight: bold;">12:00:00</span>
                </div>
            </div>
        </div>
    </div>
</div>

                <style>
                    /* 天气设置下拉菜单样式 */
                    .weather-settings-dropdown {
                        position: absolute;
                        top: 60px;
                        right: 20px;
                        width: 300px;
                        background-color: #fff;
                        border-radius: 8px;
                        box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
                        z-index: 1000;
                        display: none;
                    }

                    .weather-settings-dropdown.show {
                        display: block;
                    }

                    .dropdown-content {
                        padding: 15px;
                    }

                    .dropdown-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 15px;
                        padding-bottom: 10px;
                        border-bottom: 1px solid #eee;
                    }

                    .dropdown-header h3 {
                        margin: 0;
                        font-size: 16px;
                        color: var(--primary-color);
                    }

                    .dropdown-close {
                        background: none;
                        border: none;
                        font-size: 18px;
                        cursor: pointer;
                        color: #666;
                    }

                    .city-search-container {
                        margin-bottom: 15px;
                    }

                    .city-search-input {
                        width: 100%;
                        padding: 8px 12px;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        font-size: 14px;
                    }

                    .city-search-results {
                        margin-top: 5px;
                        max-height: 150px;
                        overflow-y: auto;
                        border: 1px solid #eee;
                        border-radius: 4px;
                        display: none;
                    }

                    .city-search-results.show {
                        display: block;
                    }

                    .search-result-item {
                        padding: 8px 12px;
                        cursor: pointer;
                        transition: background-color 0.2s;
                    }

                    .search-result-item:hover {
                        background-color: #f5f5f5;
                    }

                    .selected-city-container {
                        margin-bottom: 15px;
                        padding: 10px;
                        background-color: #f9f9f9;
                        border-radius: 4px;
                    }

                    .selected-city-label {
                        font-size: 12px;
                        color: #666;
                        margin-bottom: 5px;
                    }

                    .selected-city {
                        font-size: 16px;
                        font-weight: 600;
                        color: var(--primary-color);
                    }

                    .common-cities-label {
                        font-size: 12px;
                        color: #666;
                        margin-bottom: 10px;
                    }

                    .common-cities-grid {
                        display: grid;
                        grid-template-columns: repeat(3, 1fr);
                        gap: 8px;
                    }

                    .city-item {
                        padding: 8px;
                        text-align: center;
                        background-color: #f5f5f5;
                        border-radius: 4px;
                        cursor: pointer;
                        transition: all 0.2s;
                    }

                    .city-item:hover {
                        background-color: var(--primary-color);
                        color: #fff;
                    }

                    .stat-grid {
                        display: grid;
                        grid-template-columns: repeat(2, minmax(0, 1fr));
                        gap: 15px;
                        width: 100%;
                    }

                    .stat-item {
                        position: relative;
                        display: flex;
                        flex-direction: column;
                        padding: 12px 15px;
                        margin-bottom: 10px;
                        background-color: rgba(var(--primary-color-rgb), 0.03);
                        border-radius: 6px;
                        transition: all 0.3s ease;
                        overflow: hidden;
                        text-align: center;
                        min-width: 0; /* 确保弹性项目可以收缩 */
                    }

                    .stat-label {
                        font-weight: 500;
                        color: var(--text-muted);
                        margin-bottom: 8px;
                        line-height: 1.4;
                        font-size: 0.9em;
                        border-bottom: 1px dashed rgba(var(--primary-color-rgb), 0.2);
                        padding-bottom: 5px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        width: 100%;
                        text-align: center;
                    }

                    .stat-value {
                        font-weight: 600;
                        color: var(--text-color);
                        line-height: 1.4;
                        font-size: 1.1em;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        width: 100%;
                        text-align: center;
                    }

                    .stat-value-with-btn {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        width: 100%;
                    }

                    .stat-value-with-btn .stat-value {
                        margin-bottom: 8px;
                        width: 100%;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    /* 时间显示样式 */
                    #current-time {
                        font-family: 'Courier New', monospace;
                        font-weight: bold;
                        color: var(--primary-color);
                        margin-top: 0;
                    }

                    /* 多行值样式 */
                    .stat-value-multiline {
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        width: 100%;
                    }

                    .stat-value-multiline .stat-value {
                        margin-bottom: 4px;
                        width: 100%;
                        white-space: normal;
                        word-break: break-word;
                        line-height: 1.3;
                    }

                    .stat-value-multiline .stat-value:last-child {
                        margin-bottom: 0;
                    }

                    .stat-value-with-btn .btn {
                        white-space: nowrap;
                        font-size: 0.9em;
                        padding: 4px 12px;
                        margin-top: 2px;
                    }

                    .stat-item:hover {
                        background-color: rgba(var(--primary-color-rgb), 0.08);
                    }

                    .status-active {
                        color: var(--success-color);
                        font-weight: bold;
                    }

                    .version-history {
                        font-size: 0.85em;
                        margin-left: 8px;
                        color: var(--primary-color);
                        text-decoration: none;
                    }

                    .version-history:hover {
                        text-decoration: underline;
                    }

                    @media (max-width: 768px) {
                        .stat-grid {
                            grid-template-columns: 1fr;
                        }

                        .stat-item {
                            padding: 10px;
                            width: 100%;
                        }

                        .stat-label {
                            font-size: 0.85em;
                            margin-bottom: 6px;
                            width: 100%;
                            max-width: 100%;
                        }

                        .stat-value {
                            font-size: 1em;
                            width: 100%;
                            max-width: 100%;
                        }

                        .stat-value-with-btn .btn {
                            padding: 3px 10px;
                            font-size: 0.8em;
                            margin-top: 5px;
                        }
                    }
                </style>
            </div>

            <!-- 服务状态和公告 -->
            <div class="dashboard-sections">
                <div class="section-row">
                    <div class="section">
                        <div class="section-header">
                            <h3><i class="bi bi-megaphone"></i> 最新公告</h3>
                            <a href="javascript:void(0)" class="view-all" onclick="showAllAnnouncements()">查看全部</a>
                        </div>
                        <div class="section-content">
                            <div class="announcement-list">
                                <div class="announcement-item scroll-anim">
                                    <div class="announcement-title gradient-text">系统更新通知</div>
                                    <div class="announcement-time">2023-05-10</div>
                                    <div class="announcement-content">
                                        系统将于2023年5月15日进行版本更新，更新内容包括界面优化、功能增强和安全性提升。更新期间系统将暂停服务约30分钟，请提前做好准备。
                                    </div>
                                </div>
                                <div class="announcement-item scroll-anim scroll-anim-delay-1">
                                    <div class="announcement-title gradient-text">新功能上线</div>
                                    <div class="announcement-time">2023-05-05</div>
                                    <div class="announcement-content">
                                        我们新增了批量处理功能，现在您可以一次性处理多个订单，提高工作效率。详情请查看帮助文档。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-header">
                            <h3><i class="bi bi-lightning-charge"></i> 快捷入口</h3>
                        </div>
                        <div class="section-content">
                            <div class="shortcut-grid">
                                <?php if ($isAdmin): ?>
                                <a href="/dashboard/accounts" class="shortcut-item bounce-btn">
                                    <i class="bi bi-people"></i>
                                    <span>账户管理</span>
                                </a>
                                <?php endif; ?>
                                <a href="/dashboard/platforms" class="shortcut-item bounce-btn pulse">
                                    <i class="bi bi-phone"></i>
                                    <span>小程序平台</span>
                                </a>
                                <a href="/dashboard/orders" class="shortcut-item bounce-btn">
                                    <i class="bi bi-cart3"></i>
                                    <span>订单搜索</span>
                                </a>
                                <?php if ($isAdmin): ?>
                                <a href="/dashboard/announcements" class="shortcut-item bounce-btn">
                                    <i class="bi bi-megaphone"></i>
                                    <span>公告管理</span>
                                </a>
                                <a href="/dashboard/plugins" class="shortcut-item bounce-btn">
                                    <i class="bi bi-puzzle"></i>
                                    <span>插件中心</span>
                                </a>
                                <a href="/dashboard/settings" class="shortcut-item bounce-btn">
                                    <i class="bi bi-gear"></i>
                                    <span>系统设置</span>
                                </a>
                                <?php endif; ?>
                                <a href="/dashboard/recycle" class="shortcut-item bounce-btn">
                                    <i class="bi bi-trash"></i>
                                    <span>应用回收站</span>
                                </a>
                                <a href="/logout.php" class="shortcut-item bounce-btn">
                                    <i class="bi bi-box-arrow-right"></i>
                                    <span>退出登录</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section-row">
                    <?php if ($isAdmin): ?>
                    <div class="section">
                        <div class="section-header">
                            <h3><i class="bi bi-graph-up"></i> 调用统计</h3>
                        </div>
                        <div class="section-content">
                            <canvas id="callChart" width="400" height="200"></canvas>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-header">
                            <h3><i class="bi bi-people"></i> 用户统计</h3>
                        </div>
                        <div class="section-content">
                            <canvas id="userChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                    <?php else: ?>
                    <div class="section">
                        <div class="section-header">
                            <h3><i class="bi bi-graph-up"></i> 调用统计</h3>
                        </div>
                        <div class="section-content">
                            <canvas id="callChart" width="400" height="200"></canvas>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-header">
                            <h3><i class="bi bi-cart"></i> 订单统计</h3>
                        </div>
                        <div class="section-content">
                            <canvas id="orderChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- 版本历史模态框 -->
    <div class="modal" id="version-history-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>版本历史</h3>
                <button class="close-btn" onclick="closeModal('version-history-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <div class="version-list">
                    <div class="version-item">
                        <div class="version-header">
                            <span class="version-number">v1.0.2</span>
                            <span class="version-date">2023-05-10</span>
                        </div>
                        <div class="version-content">
                            <ul>
                                <li>优化了用户界面，提升用户体验</li>
                                <li>修复了订单搜索功能的已知问题</li>
                                <li>增强了系统安全性</li>
                                <li>改进了小程序平台的管理功能</li>
                            </ul>
                        </div>
                    </div>
                    <div class="version-item">
                        <div class="version-header">
                            <span class="version-number">v1.0.1</span>
                            <span class="version-date">2023-04-15</span>
                        </div>
                        <div class="version-content">
                            <ul>
                                <li>新增了批量处理功能</li>
                                <li>修复了数据统计的显示问题</li>
                                <li>优化了系统性能</li>
                            </ul>
                        </div>
                    </div>
                    <div class="version-item">
                        <div class="version-header">
                            <span class="version-number">v1.0.0</span>
                            <span class="version-date">2023-03-01</span>
                        </div>
                        <div class="version-content">
                            <ul>
                                <li>系统正式发布</li>
                                <li>支持多租户管理</li>
                                <li>支持小程序平台管理</li>
                                <li>支持订单管理和搜索</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('version-history-modal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 续费服务模态框 -->
    <div class="modal" id="renew-service-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>续费服务</h3>
                <button class="close-btn" onclick="closeModal('renew-service-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <form id="renew-service-form">
                    <div class="form-group">
                        <label for="service-plan">服务套餐</label>
                        <select id="service-plan" class="form-control" required>
                            <option value="">选择套餐</option>
                            <option value="1">基础版 - 1年 - ¥999</option>
                            <option value="2">标准版 - 1年 - ¥1999</option>
                            <option value="3">高级版 - 1年 - ¥2999</option>
                            <option value="4">基础版 - 3年 - ¥2499</option>
                            <option value="5">标准版 - 3年 - ¥4999</option>
                            <option value="6">高级版 - 3年 - ¥7499</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>支付方式</label>
                        <div class="payment-methods">
                            <label class="payment-option">
                                <input type="radio" name="payment-method" value="wechat" checked>
                                <div class="payment-icon"><i class="bi bi-wechat"></i></div>
                                <div class="payment-name">微信支付</div>
                            </label>
                            <label class="payment-option">
                                <input type="radio" name="payment-method" value="alipay">
                                <div class="payment-icon"><i class="bi bi-credit-card"></i></div>
                                <div class="payment-name">支付宝</div>
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('renew-service-modal')">取消</button>
                <button class="btn btn-primary" onclick="confirmRenew()">确认支付</button>
            </div>
        </div>
    </div>

    <!-- 全部公告模态框 -->
    <div class="modal" id="all-announcements-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>全部公告</h3>
                <button class="close-btn" onclick="closeModal('all-announcements-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <div class="announcement-list">
                    <div class="announcement-item">
                        <div class="announcement-title gradient-text">系统更新通知</div>
                        <div class="announcement-time">2023-05-10</div>
                        <div class="announcement-content">
                            系统将于2023年5月15日进行版本更新，更新内容包括界面优化、功能增强和安全性提升。更新期间系统将暂停服务约30分钟，请提前做好准备。
                        </div>
                    </div>
                    <div class="announcement-item">
                        <div class="announcement-title gradient-text">新功能上线</div>
                        <div class="announcement-time">2023-05-05</div>
                        <div class="announcement-content">
                            我们新增了批量处理功能，现在您可以一次性处理多个订单，提高工作效率。详情请查看帮助文档。
                        </div>
                    </div>
                    <div class="announcement-item">
                        <div class="announcement-title gradient-text">系统维护通知</div>
                        <div class="announcement-time">2023-04-20</div>
                        <div class="announcement-content">
                            系统将于2023年4月25日凌晨2:00-4:00进行例行维护，期间系统将暂停服务。给您带来的不便，敬请谅解。
                        </div>
                    </div>
                    <div class="announcement-item">
                        <div class="announcement-title gradient-text">安全更新</div>
                        <div class="announcement-time">2023-04-10</div>
                        <div class="announcement-content">
                            我们发布了一项重要的安全更新，建议所有用户及时更新系统，以确保您的数据安全。
                        </div>
                    </div>
                    <div class="announcement-item">
                        <div class="announcement-title gradient-text">价格调整通知</div>
                        <div class="announcement-time">2023-03-15</div>
                        <div class="announcement-content">
                            由于运营成本上升，我们将从2023年4月1日起对部分服务价格进行调整。现有用户在当前合同期内不受影响。
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('all-announcements-modal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 更新版本模态框 -->
    <div class="modal" id="update-version-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>更新版本</h3>
                <button class="close-btn" onclick="closeModal('update-version-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <div class="update-info">
                    <div class="update-header">
                        <h4>发现新版本：v1.0.3</h4>
                        <span class="update-date">发布日期：2023-06-20</span>
                    </div>
                    <div class="update-content">
                        <h5>更新内容：</h5>
                        <ul>
                            <li>优化了用户界面，提升用户体验</li>
                            <li>新增了数据分析功能，支持更多维度的数据统计</li>
                            <li>修复了已知的安全漏洞</li>
                            <li>提高了系统性能，减少了页面加载时间</li>
                            <li>改进了移动端适配，提供更好的移动端体验</li>
                        </ul>
                        <div class="update-note">
                            <p><strong>注意：</strong>更新过程中系统将暂停服务约5分钟，请在业务低峰期进行更新。</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('update-version-modal')">取消</button>
                <button class="btn btn-primary" onclick="confirmUpdate()">立即更新</button>
            </div>
        </div>
    </div>

    <!-- 引入Bootstrap和其他JS库 -->
    <script src="/assets/vendor/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="/assets/vendor/chart/chart.min.js"></script>
    <script src="/assets/js/common.js"></script>
    <script src="/assets/js/theme-switcher.js"></script>
    <script>
        // DOM元素
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');
        const menuToggle = document.getElementById('menu-toggle');
        const loadingOverlay = document.getElementById('loading-overlay');
        const currentDateElement = document.getElementById('current-date');
        const versionHistoryModal = document.getElementById('version-history-modal');
        const renewServiceModal = document.getElementById('renew-service-modal');

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 显示加载动画
            showLoading();

            // 检查本地存储中的侧边栏状态
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (sidebarCollapsed) {
                sidebar.classList.add('collapsed');
                mainContent.classList.add('expanded');
            }

            // 设置当前日期
            const now = new Date();
            const options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
            currentDateElement.textContent = now.toLocaleDateString('zh-CN', options);

            // 初始化图表
            initCharts();

            // 添加卡片动画
            addCardAnimations();

            // 隐藏加载动画
            setTimeout(hideLoading, 500);

            // 显示欢迎提示
            setTimeout(() => {
                showToast(`欢迎回来，${document.querySelector('.user-name').textContent}！`, 'success');
            }, 1000);
        });

        // 侧边栏切换
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        });

        // 子菜单切换功能
        document.querySelectorAll('.menu-header').forEach(header => {
            header.addEventListener('click', function(e) {
                e.preventDefault();
                const menuSection = this.parentElement;
                const isActive = menuSection.classList.contains('active');

                // 关闭所有其他子菜单
                document.querySelectorAll('.menu-section').forEach(section => {
                    section.classList.remove('active');
                });

                // 切换当前子菜单
                if (!isActive) {
                    menuSection.classList.add('active');
                }
            });
        });

        // 显示加载动画
        function showLoading() {
            loadingOverlay.classList.add('show');
        }

        // 隐藏加载动画
        function hideLoading() {
            loadingOverlay.classList.remove('show');
        }

        // 显示版本历史
        function showVersionHistory() {
            openModal('version-history-modal');
        }

        // 打开续费模态框
        function openRenewModal() {
            openModal('renew-service-modal');
        }

        // 显示全部公告
        function showAllAnnouncements() {
            openModal('all-announcements-modal');
        }

        // 打开模态框
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        };

        // 添加卡片动画
        function addCardAnimations() {
            // 为统计卡片添加动画
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });

            // 为部分添加动画
            const sections = document.querySelectorAll('.section');
            sections.forEach((section, index) => {
                section.style.animationDelay = `${0.3 + index * 0.1}s`;
            });

            // 为快捷方式添加动画
            const shortcuts = document.querySelectorAll('.shortcut-item');
            shortcuts.forEach((shortcut, index) => {
                shortcut.style.animationDelay = `${0.5 + index * 0.05}s`;
            });
        }

        // 初始化图表
        function initCharts() {
            // 检查是否是管理员
            const isAdmin = <?php echo $isAdmin ? 'true' : 'false'; ?>;

            if (isAdmin) {
                // 调用统计图表
                const callCtx = document.getElementById('callChart').getContext('2d');
                new Chart(callCtx, {
                    type: 'line',
                    data: {
                        labels: ['今日', '昨日', '前日', '3日前', '4日前'],
                        datasets: [{
                            label: '调用次数',
                            data: [<?php echo $stats['total_today']; ?>, <?php echo $stats['total_yesterday']; ?>,
                                   <?php echo max(0, $stats['total_yesterday'] - 10); ?>,
                                   <?php echo max(0, $stats['total_yesterday'] - 20); ?>,
                                   <?php echo max(0, $stats['total_yesterday'] - 30); ?>],
                            backgroundColor: 'rgba(255, 107, 149, 0.2)',
                            borderColor: 'rgba(255, 107, 149, 1)',
                            borderWidth: 2,
                            tension: 0.3
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            title: {
                                display: true,
                                text: '接口调用趋势'
                            }
                        }
                    }
                });

                // 用户统计图表
                const userCtx = document.getElementById('userChart').getContext('2d');
                new Chart(userCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['包点用户', '包月用户'],
                        datasets: [{
                            data: [<?php echo $stats['point_users']; ?>, <?php echo $stats['monthly_users']; ?>],
                            backgroundColor: [
                                'rgba(255, 107, 149, 0.7)',
                                'rgba(138, 111, 214, 0.7)'
                            ],
                            borderColor: [
                                'rgba(255, 107, 149, 1)',
                                'rgba(138, 111, 214, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            title: {
                                display: true,
                                text: '用户类型分布'
                            }
                        }
                    }
                });
            } else {
                // 调用统计图表
                const callCtx = document.getElementById('callChart').getContext('2d');
                new Chart(callCtx, {
                    type: 'line',
                    data: {
                        labels: ['今日', '昨日', '前日', '3日前', '4日前'],
                        datasets: [{
                            label: '调用次数',
                            data: [<?php echo $stats['total_today']; ?>, <?php echo $stats['total_yesterday']; ?>,
                                   <?php echo max(0, $stats['total_yesterday'] - 10); ?>,
                                   <?php echo max(0, $stats['total_yesterday'] - 20); ?>,
                                   <?php echo max(0, $stats['total_yesterday'] - 30); ?>],
                            backgroundColor: 'rgba(255, 107, 149, 0.2)',
                            borderColor: 'rgba(255, 107, 149, 1)',
                            borderWidth: 2,
                            tension: 0.3
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            title: {
                                display: true,
                                text: '接口调用趋势'
                            }
                        }
                    }
                });

                // 订单统计图表
                const orderCtx = document.getElementById('orderChart').getContext('2d');
                new Chart(orderCtx, {
                    type: 'bar',
                    data: {
                        labels: ['总订单', '已支付', '未支付', '总收入(¥)', '用户数'],
                        datasets: [{
                            label: '统计数据',
                            data: [<?php echo $stats['total_orders']; ?>,
                                   <?php echo $stats['paid_orders']; ?>,
                                   <?php echo $stats['total_orders'] - $stats['paid_orders']; ?>,
                                   <?php echo $stats['total_revenue']; ?>,
                                   <?php echo $stats['total_users']; ?>],
                            backgroundColor: [
                                'rgba(40, 199, 111, 0.2)',
                                'rgba(255, 107, 149, 0.2)',
                                'rgba(255, 193, 7, 0.2)',
                                'rgba(138, 111, 214, 0.2)',
                                'rgba(23, 162, 184, 0.2)'
                            ],
                            borderColor: [
                                'rgba(40, 199, 111, 1)',
                                'rgba(255, 107, 149, 1)',
                                'rgba(255, 193, 7, 1)',
                                'rgba(138, 111, 214, 1)',
                                'rgba(23, 162, 184, 1)'
                            ],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            title: {
                                display: true,
                                text: '业务数据统计'
                            }
                        }
                    }
                });
            }
        }

        // 续费服务
        function renewService() {
            const servicePlan = document.getElementById('service-plan').value;
            const paymentMethod = document.querySelector('input[name="payment-method"]:checked').value;

            if (!servicePlan) {
                showToast('请选择服务套餐', 'error');
                return;
            }

            // 显示加载动画
            showLoading();

            // 模拟支付过程
            setTimeout(() => {
                // 隐藏加载动画
                hideLoading();

                // 关闭模态框
                closeModal('renew-service-modal');

                // 显示成功提示
                showToast('服务续费成功！', 'success');
            }, 1500);
        }

        // 显示消息提示
        function showToast(message, type = 'success') {
            // 移除现有的提示
            const existingToasts = document.querySelectorAll('.toast');
            existingToasts.forEach(toast => {
                document.body.removeChild(toast);
            });

            // 创建新提示
            const toast = document.createElement('div');
            toast.className = 'toast';

            const toastContent = document.createElement('div');
            toastContent.className = 'toast-content';

            const icon = document.createElement('i');
            if (type === 'success') {
                icon.className = 'bi bi-check-circle-fill toast-icon success';
                icon.style.display = 'block';
                icon.style.color = 'var(--success-color)';
            } else if (type === 'error') {
                icon.className = 'bi bi-x-circle-fill toast-icon error';
                icon.style.display = 'block';
                icon.style.color = 'var(--danger-color)';
            } else {
                icon.className = 'bi bi-info-circle-fill toast-icon info';
                icon.style.display = 'block';
                icon.style.color = 'var(--info-color)';
            }

            const toastMessage = document.createElement('div');
            toastMessage.className = 'toast-message';
            toastMessage.textContent = message;

            const toastProgress = document.createElement('div');
            toastProgress.className = 'toast-progress';

            toastContent.appendChild(icon);
            toastContent.appendChild(toastMessage);
            toast.appendChild(toastContent);
            toast.appendChild(toastProgress);

            document.body.appendChild(toast);

            // 显示提示
            setTimeout(() => {
                toast.classList.add('show');
            }, 10);

            // 自动隐藏提示
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 更新日期和时间
        function updateDateTime() {
            const now = new Date();

            // 更新日期
            const year = now.getFullYear();
            const month = now.getMonth() + 1;
            const day = now.getDate();
            const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
            const weekday = weekdays[now.getDay()];

            const dateElement = document.getElementById('current-date');
            if (dateElement) {
                dateElement.textContent = `${year.toString().slice(-2)}年${month}月${day}日 星期${weekday}`;
            }

            // 更新时间
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');

            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = `${hours}:${minutes}:${seconds}`;
            }

            // 每秒更新一次
            setTimeout(updateDateTime, 1000);
        }

        // 页面加载完成后启动时钟
        document.addEventListener('DOMContentLoaded', function() {
            updateDateTime();
        });

        // 显示历史版本
        function showVersionHistory() {
            openModal('version-history-modal');
        }

        // 打开续费模态框
        function openRenewModal() {
            openModal('renew-service-modal');
        }

        // 确认续费函数已在modal-functions-fix.js中定义

        // 显示所有公告
        function showAllAnnouncements() {
            openModal('all-announcements-modal');
        }

        // 显示更新版本模态框
        function showUpdateVersion() {
            openModal('update-version-modal');
        }

        // 确认更新版本函数已在modal-functions-fix.js中定义

    </script>

    <!-- 历史版本模态框 -->
    <div class="modal" id="version-history-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>历史版本</h3>
                <button class="close-btn" onclick="closeModal('version-history-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <ul class="version-list">
                    <li class="version-item">
                        <div class="version-header">
                            <span class="version-number">v1.0.2</span>
                            <span class="version-date">2023-06-15</span>
                        </div>
                        <ul class="version-changes">
                            <li>修复了用户登录时的验证问题</li>
                            <li>优化了头像匹配算法，提高匹配准确率</li>
                            <li>改进了UI界面，提升用户体验</li>
                            <li>新增了数据统计功能</li>
                        </ul>
                    </li>
                    <li class="version-item">
                        <div class="version-header">
                            <span class="version-number">v1.0.1</span>
                            <span class="version-date">2023-05-20</span>
                        </div>
                        <ul class="version-changes">
                            <li>修复了多个已知bug</li>
                            <li>优化了系统性能</li>
                            <li>改进了移动端适配</li>
                        </ul>
                    </li>
                    <li class="version-item">
                        <div class="version-header">
                            <span class="version-number">v1.0.0</span>
                            <span class="version-date">2023-05-01</span>
                        </div>
                        <ul class="version-changes">
                            <li>首次发布</li>
                            <li>基础功能实现</li>
                            <li>支持头像上传和匹配</li>
                            <li>用户管理系统</li>
                            <li>基础数据统计</li>
                        </ul>
                    </li>
                </ul>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('version-history-modal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 查看全部公告模态框 -->
    <div class="modal" id="all-announcements-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>所有公告</h3>
                <button class="close-btn" onclick="closeModal('all-announcements-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <ul class="announcement-list">
                    <li class="announcement-item">
                        <div class="announcement-header">
                            <span class="announcement-title">系统更新通知</span>
                            <span class="announcement-date">2023-06-15</span>
                        </div>
                        <p class="announcement-content">尊敬的用户，我们将于2023年6月20日凌晨2:00-4:00进行系统升级维护，届时系统将暂停服务。给您带来的不便，敬请谅解。</p>
                    </li>
                    <li class="announcement-item">
                        <div class="announcement-header">
                            <span class="announcement-title">新功能上线通知</span>
                            <span class="announcement-date">2023-06-10</span>
                        </div>
                        <p class="announcement-content">我们很高兴地通知您，新的数据分析功能已经上线，您可以在控制面板中查看更详细的数据统计和分析报告。</p>
                    </li>
                    <li class="announcement-item">
                        <div class="announcement-header">
                            <span class="announcement-title">安全更新提醒</span>
                            <span class="announcement-date">2023-06-05</span>
                        </div>
                        <p class="announcement-content">为了保障您的账户安全，我们建议您定期修改密码，并开启两步验证功能。如有任何安全问题，请及时联系客服。</p>
                    </li>
                    <li class="announcement-item">
                        <div class="announcement-header">
                            <span class="announcement-title">服务条款更新</span>
                            <span class="announcement-date">2023-06-01</span>
                        </div>
                        <p class="announcement-content">我们已更新服务条款和隐私政策，新的条款将于2023年7月1日生效。请您仔细阅读并了解相关内容的变更。</p>
                    </li>
                    <li class="announcement-item">
                        <div class="announcement-header">
                            <span class="announcement-title">端午节放假通知</span>
                            <span class="announcement-date">2023-05-20</span>
                        </div>
                        <p class="announcement-content">尊敬的用户，我们将于2023年6月22日至6月24日放假，期间客服响应可能会有延迟。祝您端午节快乐！</p>
                    </li>
                </ul>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('all-announcements-modal')">关闭</button>
            </div>
        </div>
    </div>
<script>
    // 控制面板页面专用侧边栏修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('控制面板页面侧边栏修复脚本已加载');

        // 获取DOM元素
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');
        const menuToggle = document.getElementById('menu-toggle');

        if (sidebar && mainContent && menuToggle) {
            console.log('找到控制面板侧边栏元素');

            // 移除所有现有的点击事件
            const oldMenuToggle = menuToggle.cloneNode(true);
            menuToggle.parentNode.replaceChild(oldMenuToggle, menuToggle);

            // 添加新的点击事件
            oldMenuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('控制面板侧边栏切换按钮被点击');

                // 切换侧边栏状态
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');

                // 保存状态到本地存储
                localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
                console.log('控制面板侧边栏状态已保存:', sidebar.classList.contains('collapsed'));
            });

            // 从本地存储中恢复状态
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            console.log('控制面板从本地存储中恢复侧边栏状态:', sidebarCollapsed);

            if (sidebarCollapsed) {
                sidebar.classList.add('collapsed');
                mainContent.classList.add('expanded');
            } else {
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('expanded');
            }

            console.log('控制面板侧边栏切换已修复');
        } else {
            console.error('未找到控制面板侧边栏元素');
        }
    });
</script>
<html lang="zh-CN">
<script>
// 防止主题颜色修改图标显示
document.addEventListener('DOMContentLoaded', function() {
    // 移除已存在的主题颜色图标
    const themeColorIcon = document.getElementById('theme-color-icon');
    if (themeColorIcon) {
        themeColorIcon.remove();
    }

    // 监听并阻止新图标创建
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.id === 'theme-color-icon' ||
                        (node.classList && node.classList.contains('theme-settings-toggle'))) {
                        node.remove();
                    }
                });
            }
        });
    });

    // 开始观察文档变化
    observer.observe(document.body, { childList: true, subtree: true });
});
</script>

<!-- 网站底部 -->
<?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/site-footer.php'); ?>

<!-- 确保这些脚本在最后加载 -->
<script src="/assets/js/sidebar-toggle-fix.js"></script>
<script src="/assets/js/responsive-layout.js"></script>
<script src="/assets/js/modal-functions-fix.js"></script>
<script src="/assets/js/modal-behavior-fix.js"></script>
<script src="/assets/js/admin-sidebar-fix.js"></script>
<script src="/assets/js/avatar-theme-fix.js"></script>
<!-- 响应式增强脚本 -->
<script src="/assets/js/admin-sidebar-responsive.js"></script>
<script src="/assets/js/admin-charts-responsive.js"></script>
<script src="/assets/js/admin-layout-fix.js"></script>
<!-- 数据可视化和安全性增强脚本 -->
<script src="/assets/js/enhanced-charts.js"></script>
<script src="/assets/js/security-enhancements.js"></script>
<!-- 智慧天气组件 -->
<script src="/assets/js/wisdom-weather.js"></script>
<!-- 修复天气和人生哲理组件按钮点击问题 -->
<script>
    // 天气设置按钮点击事件处理函数
    function toggleWeatherSettings(event) {
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }
        console.log('天气设置按钮被点击 - 直接函数');

        // 获取下拉菜单
        const dropdown = document.getElementById('weather-settings-dropdown');
        if (!dropdown) {
            console.error('未找到天气设置下拉菜单');
            return;
        }

        // 确保下拉菜单可见
        dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
        console.log('下拉菜单显示状态:', dropdown.style.display);

        // 设置下拉菜单位置
        const settingsBtn = document.getElementById('settings-info');
        if (settingsBtn) {
            const rect = settingsBtn.getBoundingClientRect();

            // 检查当前页面是否为个人资料页面
            if (window.location.pathname.includes('/profile/')) {
                // 个人资料页面 - 直接放在按钮下方
                dropdown.style.top = (rect.bottom + 5) + 'px';
                dropdown.style.left = rect.left + 'px';
                dropdown.style.right = 'auto';
                console.log('个人资料页面 - 下拉菜单位置:', dropdown.style.top, dropdown.style.left);
            } else {
                // 其他页面 - 使用原来的定位方式
                dropdown.style.top = (rect.bottom + 5) + 'px';
                dropdown.style.right = (window.innerWidth - rect.right) + 'px';
                console.log('下拉菜单位置:', dropdown.style.top, dropdown.style.right);
            }
        }

        // 显示/隐藏下拉菜单
        if (dropdown.style.display === 'block') {
            dropdown.classList.add('show');
        } else {
            dropdown.classList.remove('show');
        }

        // 添加城市项点击事件
        const cityItems = dropdown.querySelectorAll('.city-item');
        cityItems.forEach(item => {
            // 移除旧的事件监听器
            item.onclick = null;

            // 添加新的点击事件
            item.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('城市项被点击:', this.getAttribute('data-city'));

                const city = this.getAttribute('data-city');
                if (city) {
                    // 保存设置
                    const settings = {
                        city: city
                    };
                    localStorage.setItem('weatherSettings', JSON.stringify(settings));

                    // 更新显示
                    const selectedCity = document.getElementById('selected-city');
                    if (selectedCity) selectedCity.textContent = city;

                    // 更新天气显示
                    if (typeof updateWeather === 'function') {
                        updateWeather();
                    }

                    // 关闭下拉菜单
                    dropdown.style.display = 'none';
                    dropdown.classList.remove('show');
                }
            });
        });

        // 点击页面其他地方关闭下拉菜单
        if (dropdown.style.display === 'block') {
            setTimeout(function() {
                const closeHandler = function(e) {
                    if (!dropdown.contains(e.target) && e.target.id !== 'settings-info') {
                        dropdown.style.display = 'none';
                        dropdown.classList.remove('show');
                        document.removeEventListener('click', closeHandler);
                    }
                };
                document.addEventListener('click', closeHandler);
            }, 100);
        }

        // 阻止事件冒泡
        if (event) {
            event.stopPropagation();
        }
    }

    // 关闭天气设置下拉菜单
    function closeWeatherSettings(event) {
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }
        console.log('关闭天气设置下拉菜单');

        const dropdown = document.getElementById('weather-settings-dropdown');
        if (dropdown) {
            dropdown.style.display = 'none';
            dropdown.classList.remove('show');
            console.log('下拉菜单已关闭');
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        // 初始化天气设置按钮
        const settingsBtn = document.getElementById('settings-info');
        if (settingsBtn) {
            // 确保按钮可点击
            settingsBtn.style.cursor = 'pointer';
            settingsBtn.style.pointerEvents = 'auto';

            // 移除所有事件监听器
            const newSettingsBtn = settingsBtn.cloneNode(true);
            settingsBtn.parentNode.replaceChild(newSettingsBtn, settingsBtn);

            // 添加内联点击事件
            newSettingsBtn.setAttribute('onclick', 'toggleWeatherSettings(event)');

            // 添加点击事件监听器
            newSettingsBtn.addEventListener('click', function(e) {
                toggleWeatherSettings(e);
            });

            console.log('天气设置按钮已初始化');
        }

        // 直接添加事件监听器到按钮
        const refreshBtn = document.getElementById('refresh-info');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', function() {
                console.log('刷新按钮被点击');
                // 添加旋转动画
                this.classList.add('rotating');
                setTimeout(() => {
                    this.classList.remove('rotating');
                }, 1000);

                // 触发内容更新
                updateWisdom();
                updateWeather();
            });
        }

        const toggleBtn = document.getElementById('toggle-info-type');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', function() {
                console.log('切换按钮被点击');
                // 获取当前类型
                const currentType = localStorage.getItem('infoType') || 'both';
                let newType;

                // 循环切换类型
                switch (currentType) {
                    case 'both':
                        newType = 'wisdom';
                        break;
                    case 'wisdom':
                        newType = 'weather';
                        break;
                    case 'weather':
                        newType = 'both';
                        break;
                    default:
                        newType = 'both';
                }

                // 保存新类型
                localStorage.setItem('infoType', newType);

                // 更新显示
                updateDisplayState(newType);
            });
        }

        // 设置按钮点击事件已在上面初始化，这里不再重复

        // 辅助函数
        function updateDisplayState(type) {
            const wisdomQuote = document.querySelector('.wisdom-quote');
            const weatherInfo = document.querySelector('.weather-info');

            if (!wisdomQuote || !weatherInfo) return;

            switch (type) {
                case 'wisdom':
                    wisdomQuote.style.display = 'flex';
                    weatherInfo.style.display = 'none';
                    break;
                case 'weather':
                    wisdomQuote.style.display = 'none';
                    weatherInfo.style.display = 'flex';
                    break;
                case 'both':
                default:
                    wisdomQuote.style.display = 'flex';
                    weatherInfo.style.display = 'flex';
                    break;
            }
        }

        function updateWisdom() {
            const wisdomText = document.getElementById('wisdom-text');
            if (!wisdomText) return;

            // 随机选择一条智慧语录
            const wisdomQuotes = [
                "生活不是等待风暴过去，而是学会在雨中跳舞。",
                "人生就像骑自行车，要保持平衡就得不断前进。",
                "成功不是最终的，失败也不是致命的，重要的是继续前进的勇气。",
                "不要为成功而努力，要为做一个有价值的人而努力。",
                "生活中最重要的事情是明确你想要什么。",
                "人生最大的挑战是超越自己。",
                "每一个不曾起舞的日子，都是对生命的辜负。",
                "微笑是世界上最好的语言。",
                "成功的秘诀在于坚持目标的始终。",
                "真正的智慧是知道自己所不知道的东西。"
            ];

            const randomIndex = Math.floor(Math.random() * wisdomQuotes.length);
            wisdomText.textContent = wisdomQuotes[randomIndex];

            // 添加淡入动画
            wisdomText.style.animation = 'none';
            setTimeout(() => {
                wisdomText.style.animation = 'fadeIn 0.5s';
            }, 10);
        }

        function updateWeather() {
            const weatherText = document.getElementById('weather-text');
            if (!weatherText) return;

            // 获取设置中的城市
            const settings = JSON.parse(localStorage.getItem('weatherSettings')) || {
                city: '北京'
            };

            // 模拟天气信息
            const conditions = ['晴', '多云', '阴', '小雨', '中雨', '大雨', '雷阵雨', '小雪', '大雪'];
            const randomCondition = conditions[Math.floor(Math.random() * conditions.length)];
            const randomTemp = Math.floor(Math.random() * 30) + 5; // 5-34度

            weatherText.textContent = `${settings.city} ${randomCondition} ${randomTemp}°C`;

            // 添加淡入动画
            weatherText.style.animation = 'none';
            setTimeout(() => {
                weatherText.style.animation = 'fadeIn 0.5s';
            }, 10);
        }

        // 初始化显示状态
        const currentType = localStorage.getItem('infoType') || 'both';
        updateDisplayState(currentType);
    });
</script>
</body>
</html>