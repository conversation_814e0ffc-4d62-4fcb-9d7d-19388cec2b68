/**
 * 情侣头像匹配系统 - 高级动效脚本
 */

document.addEventListener('DOMContentLoaded', function() {
  // 初始化所有动效
  initHoverUnderline();
  initSubmenuAnimation();
  initScrollFadeIn();
  initRippleEffect();
  init3DCardEffect();
  initMenuAnimation();
});

/**
 * 初始化悬停下划线动画
 */
function initHoverUnderline() {
  // 为所有导航链接添加悬停下划线效果
  const navLinks = document.querySelectorAll('.nav-link, .navbar-brand, .sidebar .nav-link');
  navLinks.forEach(link => {
    if (!link.classList.contains('hover-underline-animation')) {
      link.classList.add('hover-underline-animation');
    }
  });
}

/**
 * 初始化子菜单滑动动画
 */
function initSubmenuAnimation() {
  // 查找所有带有子菜单的菜单项
  const menuItems = document.querySelectorAll('.menu-item');
  
  menuItems.forEach(item => {
    const toggle = item.querySelector('.menu-item-content');
    const submenu = item.querySelector('.submenu');
    
    if (toggle && submenu) {
      toggle.addEventListener('click', (e) => {
        e.preventDefault();
        
        // 切换子菜单的展开状态
        item.classList.toggle('open');
        submenu.classList.toggle('open');
        
        // 播放点击音效
        if (typeof SoundEffects !== 'undefined') {
          SoundEffects.play('click');
        }
      });
    }
  });
}

/**
 * 初始化滚动渐入效果
 */
function initScrollFadeIn() {
  // 查找所有需要滚动渐入的元素
  const fadeElements = document.querySelectorAll('.fade-in-section');
  
  // 创建Intersection Observer
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('is-visible');
        // 一旦显示，不再观察
        observer.unobserve(entry.target);
      }
    });
  }, {
    threshold: 0.1 // 当元素10%可见时触发
  });
  
  // 观察所有元素
  fadeElements.forEach(element => {
    observer.observe(element);
  });
}

/**
 * 初始化按钮波纹效果
 */
function initRippleEffect() {
  // 为所有按钮添加波纹效果
  const buttons = document.querySelectorAll('.btn');
  
  buttons.forEach(button => {
    if (!button.classList.contains('btn-ripple')) {
      button.classList.add('btn-ripple');
    }
    
    // 添加点击事件监听器
    button.addEventListener('click', function(e) {
      // 创建波纹元素
      const ripple = document.createElement('span');
      ripple.classList.add('ripple');
      this.appendChild(ripple);
      
      // 设置波纹位置
      const rect = this.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;
      
      ripple.style.width = ripple.style.height = `${size}px`;
      ripple.style.left = `${x}px`;
      ripple.style.top = `${y}px`;
      
      // 动画结束后移除波纹元素
      ripple.addEventListener('animationend', function() {
        this.remove();
      });
    });
  });
}

/**
 * 初始化3D卡片效果
 */
function init3DCardEffect() {
  // 为所有卡片添加3D效果
  const cards = document.querySelectorAll('.card');
  
  cards.forEach(card => {
    if (!card.classList.contains('card-3d')) {
      card.classList.add('card-3d');
    }
    
    // 添加鼠标移动事件监听器，实现视差效果
    card.addEventListener('mousemove', function(e) {
      const rect = this.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      
      const rotateX = (y - centerY) / 20;
      const rotateY = (centerX - x) / 20;
      
      this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
    });
    
    // 鼠标离开时重置变换
    card.addEventListener('mouseleave', function() {
      this.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
    });
  });
}

/**
 * 初始化菜单动画
 */
function initMenuAnimation() {
  // 为侧边栏菜单项添加动画
  const sidebarItems = document.querySelectorAll('.sidebar .nav-item');
  
  sidebarItems.forEach((item, index) => {
    // 添加延迟动画
    item.style.animation = `fadeInUp 0.5s ease-out ${index * 0.1}s both`;
    
    // 添加悬停效果
    item.addEventListener('mouseenter', function() {
      this.style.transform = 'translateX(5px)';
    });
    
    item.addEventListener('mouseleave', function() {
      this.style.transform = 'translateX(0)';
    });
  });
  
  // 为下拉菜单添加动画
  const dropdowns = document.querySelectorAll('.dropdown');
  
  dropdowns.forEach(dropdown => {
    const toggle = dropdown.querySelector('.dropdown-toggle');
    const menu = dropdown.querySelector('.dropdown-menu');
    
    if (toggle && menu) {
      // 添加过渡效果
      menu.style.transition = 'transform 0.3s ease-out, opacity 0.3s ease-out';
      menu.style.transformOrigin = 'top center';
      
      // 显示时的动画
      dropdown.addEventListener('show.bs.dropdown', function() {
        menu.style.opacity = '0';
        menu.style.transform = 'translateY(-10px) scale(0.95)';
        
        setTimeout(() => {
          menu.style.opacity = '1';
          menu.style.transform = 'translateY(0) scale(1)';
        }, 10);
      });
      
      // 隐藏时的动画
      dropdown.addEventListener('hide.bs.dropdown', function() {
        menu.style.opacity = '0';
        menu.style.transform = 'translateY(-10px) scale(0.95)';
      });
    }
  });
}

/**
 * 添加打字机效果
 * @param {string} selector 目标元素选择器
 * @param {string} text 要显示的文本
 * @param {number} speed 打字速度（毫秒）
 */
function typeWriter(selector, text, speed = 50) {
  const element = document.querySelector(selector);
  if (!element) return;
  
  element.innerHTML = '';
  element.classList.add('typewriter');
  
  let i = 0;
  const timer = setInterval(() => {
    if (i < text.length) {
      element.innerHTML += text.charAt(i);
      i++;
    } else {
      clearInterval(timer);
      element.classList.remove('typewriter');
    }
  }, speed);
}

/**
 * 添加页面过渡效果
 * @param {string} selector 目标元素选择器
 */
function pageTransition(selector) {
  const element = document.querySelector(selector);
  if (!element) return;
  
  // 先隐藏元素
  element.style.opacity = '0';
  element.style.transform = 'translateY(20px)';
  
  // 然后淡入
  setTimeout(() => {
    element.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';
    element.style.opacity = '1';
    element.style.transform = 'translateY(0)';
  }, 100);
}

/**
 * 添加脉冲动画
 * @param {string} selector 目标元素选择器
 */
function addPulseEffect(selector) {
  const elements = document.querySelectorAll(selector);
  
  elements.forEach(element => {
    element.classList.add('pulse');
  });
}

/**
 * 添加闪光效果
 * @param {string} selector 目标元素选择器
 */
function addShineEffect(selector) {
  const elements = document.querySelectorAll(selector);
  
  elements.forEach(element => {
    element.classList.add('shine');
  });
}

/**
 * 添加渐变边框效果
 * @param {string} selector 目标元素选择器
 */
function addGradientBorder(selector) {
  const elements = document.querySelectorAll(selector);
  
  elements.forEach(element => {
    element.classList.add('gradient-border');
  });
}

/**
 * 创建加载动画
 * @param {string} selector 目标容器选择器
 * @param {string} message 加载消息
 * @returns {HTMLElement} 加载动画元素
 */
function createLoadingSpinner(selector, message = '加载中...') {
  const container = document.querySelector(selector);
  if (!container) return null;
  
  const spinner = document.createElement('div');
  spinner.className = 'loading-container text-center p-5';
  spinner.innerHTML = `
    <div class="loading-spinner mb-3"></div>
    <p class="loading-message">${message}</p>
  `;
  
  container.appendChild(spinner);
  return spinner;
}

/**
 * 移除加载动画
 * @param {HTMLElement} spinner 加载动画元素
 */
function removeLoadingSpinner(spinner) {
  if (spinner && spinner.parentNode) {
    spinner.parentNode.removeChild(spinner);
  }
}
