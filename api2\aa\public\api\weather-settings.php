<?php
// 开启会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 检查用户是否已登录
if (!isset($_SESSION['username'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => '未授权访问']);
    exit;
}

// 包含数据库配置
$configPath = __DIR__ . '/../../config/config.php';
if (!file_exists($configPath)) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '配置文件不存在: ' . $configPath]);
    exit;
}

$config = require_once($configPath);

if (!$config || !is_array($config)) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '配置文件加载失败或格式错误']);
    exit;
}

try {
    // 连接数据库
    $dbConfig = $config['database'];

    // 调试信息
    error_log("Database config: " . print_r($dbConfig, true));

    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    error_log("DSN: " . $dsn);

    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // 确保天气设置表存在
    $prefix = $dbConfig['prefix']; // 使用配置中的前缀
    error_log("Table prefix: " . $prefix);

    createWeatherSettingsTable($pdo, $prefix);
    
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // 获取天气设置
        $settings = getWeatherSettings($pdo, $prefix);
        echo json_encode(['success' => true, 'settings' => $settings]);

    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // 保存天气设置
        $input = json_decode(file_get_contents('php://input'), true);

        if (!$input) {
            throw new Exception('无效的请求数据');
        }

        $result = saveWeatherSettings($pdo, $prefix, $input);
        echo json_encode(['success' => true, 'message' => '设置保存成功']);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

/**
 * 创建天气设置表
 */
function createWeatherSettingsTable($pdo, $prefix) {
    $sql = "CREATE TABLE IF NOT EXISTS {$prefix}weather_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        api_provider VARCHAR(50) DEFAULT 'openweather',
        api_key VARCHAR(255) DEFAULT '',
        private_key VARCHAR(255) DEFAULT '',
        default_city VARCHAR(100) DEFAULT '北京',
        enabled TINYINT(1) DEFAULT 1,
        cache_duration INT DEFAULT 30,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $pdo->exec($sql);
    
    // 插入默认设置（如果表为空）
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM {$prefix}weather_settings");
    $stmt->execute();
    $count = $stmt->fetchColumn();

    if ($count == 0) {
        $stmt = $pdo->prepare("INSERT INTO {$prefix}weather_settings (api_provider, api_key, private_key, default_city, enabled, cache_duration) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(['openweather', '', '', '北京', 1, 30]);
    }
}

/**
 * 获取天气设置
 */
function getWeatherSettings($pdo, $prefix) {
    $stmt = $pdo->prepare("SELECT * FROM {$prefix}weather_settings LIMIT 1");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$result) {
        // 返回默认设置
        return [
            'api_provider' => 'openweather',
            'api_key' => '',
            'private_key' => '',
            'default_city' => '北京',
            'enabled' => true,
            'cache_duration' => 30
        ];
    }

    return [
        'api_provider' => $result['api_provider'],
        'api_key' => $result['api_key'],
        'private_key' => $result['private_key'] ?? '',
        'default_city' => $result['default_city'],
        'enabled' => (bool)$result['enabled'],
        'cache_duration' => (int)$result['cache_duration']
    ];
}

/**
 * 保存天气设置
 */
function saveWeatherSettings($pdo, $prefix, $settings) {
    $api_provider = $settings['api_provider'] ?? 'openweather';
    $api_key = $settings['api_key'] ?? '';
    $private_key = $settings['private_key'] ?? '';
    $default_city = $settings['default_city'] ?? '北京';
    $enabled = isset($settings['enabled']) && $settings['enabled'] ? 1 : 0;
    $cache_duration = (int)($settings['cache_duration'] ?? 30);

    // 检查是否已有记录
    $stmt = $pdo->prepare("SELECT id FROM {$prefix}weather_settings LIMIT 1");
    $stmt->execute();
    $exists = $stmt->fetch();

    if ($exists) {
        // 更新现有记录
        $stmt = $pdo->prepare("UPDATE {$prefix}weather_settings SET api_provider = ?, api_key = ?, private_key = ?, default_city = ?, enabled = ?, cache_duration = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
        $stmt->execute([$api_provider, $api_key, $private_key, $default_city, $enabled, $cache_duration, $exists['id']]);
    } else {
        // 插入新记录
        $stmt = $pdo->prepare("INSERT INTO {$prefix}weather_settings (api_provider, api_key, private_key, default_city, enabled, cache_duration) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([$api_provider, $api_key, $private_key, $default_city, $enabled, $cache_duration]);
    }

    return true;
}
?>
