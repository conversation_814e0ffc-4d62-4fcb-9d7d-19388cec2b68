<?php

/**
 * Migration for creating the orders table
 * This table stores order information with tenant isolation
 */
class CreateOrdersTable
{
    /**
     * Run the migration
     */
    public function up()
    {
        $sql = "CREATE TABLE IF NOT EXISTS `orders` (
            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            `tenant_id` bigint(20) UNSIGNED NOT NULL COMMENT 'Reference to tenants table',
            `user_id` bigint(20) UNSIGNED NOT NULL COMMENT 'Reference to users table',
            `package_id` int(11) UNSIGNED NOT NULL COMMENT 'Reference to packages table',
            `order_no` varchar(64) NOT NULL COMMENT 'Order number',
            `amount` decimal(10,2) NOT NULL COMMENT 'Order amount',
            `payment_method` varchar(20) NOT NULL COMMENT 'wechat/alipay',
            `transaction_id` varchar(64) DEFAULT NULL COMMENT 'Payment transaction ID',
            `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=unpaid, 1=paid, 2=cancelled',
            `paid_time` datetime DEFAULT NULL COMMENT 'Payment time',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `order_no` (`order_no`),
            KEY `tenant_id` (`tenant_id`),
            KEY `user_id` (`user_id`),
            KEY `package_id` (`package_id`),
            KEY `status` (`status`),
            CONSTRAINT `orders_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE,
            CONSTRAINT `orders_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
            CONSTRAINT `orders_package_id_foreign` FOREIGN KEY (`package_id`) REFERENCES `packages` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        
        return $sql;
    }

    /**
     * Reverse the migration
     */
    public function down()
    {
        return "DROP TABLE IF EXISTS `orders`;";
    }
}
