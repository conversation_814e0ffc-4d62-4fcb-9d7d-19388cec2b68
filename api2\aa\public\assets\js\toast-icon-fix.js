/**
 * Toast图标修复脚本
 * 修复Toast提示同时显示多个图标的问题
 */

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('Toast图标修复脚本已加载');
    
    // 重写showToast函数
    window.originalShowToast = window.showToast;
    
    window.showToast = function(message, type = "success") {
        console.log('显示Toast提示：', message, type);
        
        // 获取toast元素
        const toast = document.getElementById("toast");
        
        // 如果toast元素不存在，则使用原始函数
        if (!toast) {
            if (window.originalShowToast) {
                window.originalShowToast(message, type);
            }
            return;
        }
        
        // 获取toast元素中的子元素
        const toastMessage = toast.querySelector(".toast-message");
        const successIcon = toast.querySelector(".toast-icon.success");
        const errorIcon = toast.querySelector(".toast-icon.error");
        const infoIcon = toast.querySelector(".toast-icon.info");
        
        // 设置消息
        toastMessage.textContent = message;
        
        // 隐藏所有图标
        if (successIcon) successIcon.style.display = "none";
        if (errorIcon) errorIcon.style.display = "none";
        if (infoIcon) infoIcon.style.display = "none";
        
        // 根据类型显示对应图标
        if (type === "success" && successIcon) {
            successIcon.style.display = "inline-block";
        } else if (type === "error" && errorIcon) {
            errorIcon.style.display = "inline-block";
        } else if (type === "info" && infoIcon) {
            infoIcon.style.display = "inline-block";
        }
        
        // 显示toast
        toast.classList.add("show");
        
        // 3秒后自动隐藏
        setTimeout(() => {
            toast.classList.remove("show");
        }, 3000);
    };
    
    // 修复所有页面的toast
    fixAllToasts();
});

// 修复所有页面的toast
function fixAllToasts() {
    // 获取所有toast
    const toasts = document.querySelectorAll('.toast');
    
    if (toasts.length > 0) {
        console.log('找到toast：', toasts.length, '个');
        
        toasts.forEach(toast => {
            // 获取toast元素中的子元素
            const successIcon = toast.querySelector(".toast-icon.success");
            const errorIcon = toast.querySelector(".toast-icon.error");
            const infoIcon = toast.querySelector(".toast-icon.info");
            
            // 隐藏所有图标
            if (successIcon) successIcon.style.display = "none";
            if (errorIcon) errorIcon.style.display = "none";
            if (infoIcon) errorIcon.style.display = "none";
        });
    } else {
        console.log('未找到toast');
    }
}
