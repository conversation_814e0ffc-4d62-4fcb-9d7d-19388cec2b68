/**
 * 账户管理页面编辑按钮修复脚本
 * 修复账户管理页面编辑按钮点击没有弹窗模态框的问题
 * 添加toggleExpiryDateField函数
 */

// 切换到期日期字段
function toggleExpiryDateField() {
    const permanentCheckbox = document.getElementById("permanent-account");
    const expiryDateInput = document.getElementById("expiry-date");

    if (permanentCheckbox && expiryDateInput) {
        if (permanentCheckbox.checked) {
            expiryDateInput.value = "永久";
            expiryDateInput.disabled = true;
            expiryDateInput.style.backgroundColor = "#f5f5f5";
            expiryDateInput.style.color = "#666";
            expiryDateInput.removeAttribute("required");
        } else {
            // 设置默认日期为一年后
            const oneYearLater = new Date();
            oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);
            expiryDateInput.value = oneYearLater.toISOString().split('T')[0];
            expiryDateInput.disabled = false;
            expiryDateInput.style.backgroundColor = "";
            expiryDateInput.style.color = "";
            expiryDateInput.setAttribute("required", "required");
        }
    } else {
        console.error("找不到永久账户复选框或到期日期输入框");
    }
}

// 切换编辑模态框中的到期日期字段
function toggleEditExpiryDateField() {
    const permanentCheckbox = document.getElementById("edit-permanent-account");
    const expiryDateInput = document.getElementById("edit-expiry-date");

    if (permanentCheckbox && expiryDateInput) {
        if (permanentCheckbox.checked) {
            expiryDateInput.value = "永久";
            expiryDateInput.disabled = true;
            expiryDateInput.style.backgroundColor = "#f5f5f5";
            expiryDateInput.style.color = "#666";
            expiryDateInput.removeAttribute("required");
        } else {
            // 设置默认日期为一年后
            const oneYearLater = new Date();
            oneYearLater.setFullYear(oneYearLater.getFullYear() + 1);
            expiryDateInput.value = oneYearLater.toISOString().split('T')[0];
            expiryDateInput.disabled = false;
            expiryDateInput.style.backgroundColor = "";
            expiryDateInput.style.color = "";
            expiryDateInput.setAttribute("required", "required");
        }
    } else {
        console.error("找不到编辑模态框中的永久账户复选框或到期日期输入框");
    }
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('账户管理页面编辑按钮修复脚本已加载');

    // 修复编辑按钮点击事件
    const editButtons = document.querySelectorAll('.edit');

    if (editButtons.length > 0) {
        console.log('找到编辑按钮：', editButtons.length, '个');

        editButtons.forEach(button => {
            // 移除原有的onclick属性
            const onclickValue = button.getAttribute('onclick');
            if (onclickValue) {
                const match = onclickValue.match(/openEditModal\((\d+)\)/);
                if (match) {
                    const id = match[1];
                    button.removeAttribute('onclick');

                    // 添加新的点击事件
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('编辑按钮被点击，ID：', id);
                        openEditModal(id);
                    });

                    console.log('已为编辑按钮添加点击事件，ID：', id);
                }
            }
        });
    } else {
        console.log('未找到编辑按钮');
    }

    // 修复重置密码按钮点击事件
    const resetButtons = document.querySelectorAll('.reset-pwd');

    if (resetButtons.length > 0) {
        console.log('找到重置密码按钮：', resetButtons.length, '个');

        resetButtons.forEach(button => {
            // 移除原有的onclick属性
            const onclickValue = button.getAttribute('onclick');
            if (onclickValue) {
                const match = onclickValue.match(/openResetPwdModal\((\d+)\)/);
                if (match) {
                    const id = match[1];
                    button.removeAttribute('onclick');

                    // 添加新的点击事件
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('重置密码按钮被点击，ID：', id);
                        openResetPwdModal(id);
                    });

                    console.log('已为重置密码按钮添加点击事件，ID：', id);
                }
            }
        });
    } else {
        console.log('未找到重置密码按钮');
    }

    // 修复新增账户按钮点击事件
    const addAccountBtn = document.getElementById('add-account-btn');
    if (addAccountBtn) {
        console.log('找到新增账户按钮');

        // 移除原有的onclick属性
        addAccountBtn.removeAttribute('onclick');

        // 添加新的点击事件
        addAccountBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('新增账户按钮被点击');
            openModal('add-account-modal');
        });

        console.log('已为新增账户按钮添加点击事件');
    } else {
        console.log('未找到新增账户按钮');
    }

    // 修复关闭按钮点击事件
    const closeButtons = document.querySelectorAll('.close-btn');

    if (closeButtons.length > 0) {
        console.log('找到关闭按钮：', closeButtons.length, '个');

        closeButtons.forEach(button => {
            // 移除原有的onclick属性
            const onclickValue = button.getAttribute('onclick');
            if (onclickValue) {
                const match = onclickValue.match(/closeModal\('(.+)'\)/);
                if (match) {
                    const modalId = match[1];
                    button.removeAttribute('onclick');

                    // 添加新的点击事件
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('关闭按钮被点击，模态框ID：', modalId);
                        closeModal(modalId);
                    });

                    console.log('已为关闭按钮添加点击事件，模态框ID：', modalId);
                }
            }
        });
    } else {
        console.log('未找到关闭按钮');
    }

    // 修复模态框中的取消按钮点击事件
    const cancelButtons = document.querySelectorAll('.modal-footer .btn-secondary');

    if (cancelButtons.length > 0) {
        console.log('找到取消按钮：', cancelButtons.length, '个');

        cancelButtons.forEach(button => {
            // 移除原有的onclick属性
            const onclickValue = button.getAttribute('onclick');
            if (onclickValue) {
                const match = onclickValue.match(/closeModal\('(.+)'\)/);
                if (match) {
                    const modalId = match[1];
                    button.removeAttribute('onclick');

                    // 添加新的点击事件
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('取消按钮被点击，模态框ID：', modalId);
                        closeModal(modalId);
                    });

                    console.log('已为取消按钮添加点击事件，模态框ID：', modalId);
                }
            }
        });
    } else {
        console.log('未找到取消按钮');
    }
});
