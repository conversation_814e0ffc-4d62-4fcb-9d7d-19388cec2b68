/**
 * 安全性增强样式
 * 为安全功能提供样式支持
 */

/* 输入验证样式 */
.is-invalid {
    border-color: var(--danger-color) !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    padding-right: calc(1.5em + 0.75rem) !important;
}

.is-invalid:focus {
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: var(--danger-color);
}

/* 安全警报样式 */
.security-alert {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.security-alert.show {
    opacity: 1;
    visibility: visible;
}

.security-alert-content {
    width: 90%;
    max-width: 500px;
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    transform: translateY(-20px);
    transition: transform 0.3s ease;
}

.security-alert.show .security-alert-content {
    transform: translateY(0);
}

.security-alert-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: var(--danger-color);
    color: var(--white-color);
}

.security-alert-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.security-alert-close {
    background: none;
    border: none;
    color: var(--white-color);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.security-alert-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.security-alert-body {
    padding: 20px;
}

.security-alert-body p {
    margin: 0;
    line-height: 1.5;
}

/* 密码强度指示器 */
.password-strength {
    margin-top: 10px;
    height: 5px;
    border-radius: 2px;
    background-color: #eee;
    overflow: hidden;
}

.password-strength-bar {
    height: 100%;
    width: 0;
    transition: width 0.3s ease, background-color 0.3s ease;
}

.password-strength-bar.weak {
    width: 25%;
    background-color: var(--danger-color);
}

.password-strength-bar.medium {
    width: 50%;
    background-color: var(--warning-color);
}

.password-strength-bar.strong {
    width: 75%;
    background-color: var(--info-color);
}

.password-strength-bar.very-strong {
    width: 100%;
    background-color: var(--success-color);
}

.password-strength-text {
    margin-top: 5px;
    font-size: 0.8rem;
    color: var(--gray-color);
}

/* 安全表单样式 */
.secure-form {
    position: relative;
}

.secure-form::before {
    content: '\f023';
    font-family: 'bootstrap-icons';
    position: absolute;
    top: -10px;
    right: -10px;
    width: 24px;
    height: 24px;
    background-color: var(--success-color);
    color: var(--white-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    z-index: 1;
}

.secure-form-footer {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.secure-form-info {
    font-size: 0.85rem;
    color: var(--gray-color);
    display: flex;
    align-items: center;
}

.secure-form-info i {
    margin-right: 5px;
    color: var(--success-color);
}

/* 安全提示样式 */
.security-tip {
    padding: 10px 15px;
    background-color: rgba(var(--info-color-rgb), 0.1);
    border-left: 3px solid var(--info-color);
    border-radius: 3px;
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.security-tip i {
    margin-right: 5px;
    color: var(--info-color);
}

/* 响应式调整 */
@media (max-width: 576px) {
    .security-alert-content {
        width: 95%;
    }
    
    .security-alert-header {
        padding: 10px 15px;
    }
    
    .security-alert-body {
        padding: 15px;
    }
}

/* 安全徽章 */
.security-badge {
    display: inline-flex;
    align-items: center;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 5px;
}

.security-badge.secure {
    background-color: rgba(var(--success-color-rgb), 0.1);
    color: var(--success-color);
}

.security-badge.warning {
    background-color: rgba(var(--warning-color-rgb), 0.1);
    color: var(--warning-color);
}

.security-badge i {
    margin-right: 3px;
    font-size: 0.8rem;
}

/* 安全加载指示器 */
.secure-loading {
    position: relative;
    padding-left: 25px;
}

.secure-loading::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid rgba(var(--primary-color-rgb), 0.2);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: secure-spin 1s linear infinite;
}

@keyframes secure-spin {
    to { transform: translateY(-50%) rotate(360deg); }
}
