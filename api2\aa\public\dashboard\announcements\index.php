﻿<?php
// 包含身份验证检查
include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/auth-check.php');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公告管理 - 情侣头像匹配系统</title>
    <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/header-scripts.php'); ?>
<style>
    /* 基础样式 */
    :root {
        --primary-color: #ff6b95;
        --secondary-color: #ffa5c0;
        --success-color: #28a745;
        --danger-color: #dc3545;
        --warning-color: #ffc107;
        --info-color: #17a2b8;
        --light-color: #f8f9fa;
        --dark-color: #343a40;
        --gray-color: #6c757d;
        --white-color: #ffffff;
        --shadow-color: rgba(0, 0, 0, 0.1);
        --transition-speed: 0.3s;
        --border-radius: 8px;
        --animate-delay: 0.1s;
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: "Microsoft YaHei", sans-serif;
    }

    body {
        background-color: #f5f7fa;
        color: #333;
        font-size: 14px;
        line-height: 1.5;
        overflow-x: hidden;
    }

    /* 加载动画 */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s;
    }

    .loading-overlay.show {
        opacity: 1;
        visibility: visible;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 107, 149, 0.3);
        border-top: 4px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* 布局样式 */
    .dashboard-container {
        display: flex;
        min-height: 100vh;
    }

    /* 侧边栏样式 */
    .sidebar {
        width: 250px;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: var(--white-color);
        padding: 20px 0;
        position: fixed;
        height: 100vh;
        overflow-y: auto;
        z-index: 1000;
        box-shadow: 2px 0 10px var(--shadow-color);
        transition: all var(--transition-speed);
    }

    .sidebar.collapsed {
        width: 70px;
    }

    .sidebar-header {
        padding: 0 20px 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        text-align: center;
        margin-bottom: 20px;
    }

    .sidebar-logo {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        margin-bottom: 10px;
        object-fit: cover;
        background-color: rgba(255, 255, 255, 0.2);
    }

    .sidebar-title {
        color: var(--white-color);
        font-size: 1.5rem;
        margin-bottom: 5px;
        font-weight: 600;
    }

    .sidebar-subtitle {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
    }

    .sidebar.collapsed .sidebar-title,
    .sidebar.collapsed .sidebar-subtitle {
        display: none;
    }

    .sidebar-menu {
        list-style: none;
        padding: 0;
    }

    .sidebar-menu li {
        margin-bottom: 5px;
    }

    .sidebar-menu li a {
        color: var(--white-color);
        text-decoration: none;
        display: block;
        padding: 12px 20px;
        transition: all var(--transition-speed);
        border-left: 4px solid transparent;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .sidebar-menu li a:hover,
    .sidebar-menu li a.active {
        background-color: rgba(255, 255, 255, 0.2);
        border-left-color: var(--white-color);
    }

    .sidebar-menu li a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
        font-size: 1.1rem;
    }

    .sidebar.collapsed .sidebar-menu li a span {
        display: none;
    }

    .sidebar.collapsed .sidebar-menu li a i {
        margin-right: 0;
        font-size: 1.3rem;
    }

    /* 主内容区域样式 */
    .main-content {
        flex: 1;
        margin-left: 250px;
        padding: 20px;
        transition: all var(--transition-speed);
    }

    .main-content.expanded {
        margin-left: 70px;
    }

    /* 头部样式 */
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }

    .menu-toggle {
        background: none;
        border: none;
        color: var(--dark-color);
        font-size: 1.5rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        transition: all var(--transition-speed);
    }

    .menu-toggle:hover {
        background-color: var(--light-color);
    }

    .user-info {
        display: flex;
        align-items: center;
        position: relative;
        cursor: pointer;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: var(--white-color);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 10px;
    }

    .user-name {
        font-weight: 500;
    }

    .dropdown-content {
        position: absolute;
        top: 100%;
        right: 0;
        background-color: var(--white-color);
        min-width: 160px;
        box-shadow: 0 8px 16px 0 var(--shadow-color);
        border-radius: var(--border-radius);
        padding: 10px 0;
        z-index: 1;
        display: none;
        animation: fadeIn 0.3s;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .user-info:hover .dropdown-content {
        display: block;
    }

    .dropdown-content a {
        color: var(--dark-color);
        padding: 10px 20px;
        text-decoration: none;
        display: block;
        transition: all var(--transition-speed);
    }

    .dropdown-content a:hover {
        background-color: var(--light-color);
    }

    .dropdown-content a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }

    /* 操作栏样式 */
    .action-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .search-container {
        display: flex;
        align-items: center;
        background-color: var(--white-color);
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: 0 2px 5px var(--shadow-color);
        width: 300px;
    }

    .search-input {
        flex: 1;
        border: none;
        padding: 10px 15px;
        outline: none;
        font-size: 14px;
    }

    .search-button {
        background-color: var(--primary-color);
        color: var(--white-color);
        border: none;
        padding: 10px 15px;
        cursor: pointer;
        transition: all var(--transition-speed);
    }

    .search-button:hover {
        background-color: #ff4f7e;
    }

    /* 按钮样式 */
    .btn {
        display: inline-block;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        user-select: none;
        border: 1px solid transparent;
        padding: 0.5rem 1rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: var(--border-radius);
        transition: all var(--transition-speed);
        cursor: pointer;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .btn:active {
        transform: translateY(0);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .btn-primary {
        color: var(--white-color);
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        border-color: var(--primary-color);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #ff4f7e 0%, #ffa5c0 100%);
        border-color: #ff4f7e;
    }

    .btn-secondary {
        color: var(--white-color);
        background-color: var(--gray-color);
        border-color: var(--gray-color);
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #545b62;
    }

    .btn-danger {
        color: var(--white-color);
        background-color: var(--danger-color);
        border-color: var(--danger-color);
    }

    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }

    .btn-outline-primary {
        color: var(--primary-color);
        background-color: transparent;
        border-color: var(--primary-color);
    }

    .btn-outline-primary:hover {
        color: var(--white-color);
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }

    /* 表格样式 */
    .table-container {
        background-color: var(--white-color);
        border-radius: var(--border-radius);
        box-shadow: 0 2px 5px var(--shadow-color);
        overflow: hidden;
        margin-bottom: 20px;
        animation: fadeInUp 0.5s;
    }

    @keyframes fadeInUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .table {
        width: 100%;
        border-collapse: collapse;
    }

    .table th,
    .table td {
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid #eee;
    }

    .table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
    }

    .table tr:last-child td {
        border-bottom: none;
    }

    .table tr:hover td {
        background-color: #f8f9fa;
    }

    .status {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
    }

    .status.active {
        background-color: rgba(40, 167, 69, 0.1);
        color: var(--success-color);
    }

    .status.inactive {
        background-color: rgba(220, 53, 69, 0.1);
        color: var(--danger-color);
    }

    .actions {
        display: flex;
        gap: 5px;
    }

    .actions button {
        width: auto;
        height: 32px;
        border-radius: 16px;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all var(--transition-speed);
        padding: 0 12px;
        margin-right: 5px;
    }

    .actions button i {
        margin-right: 5px;
    }

    .actions .enter {
        background-color: rgba(23, 162, 184, 0.1);
        color: var(--info-color);
    }

    .actions .enter:hover {
        background-color: var(--info-color);
        color: var(--white-color);
    }

    .actions .edit {
        background-color: rgba(255, 193, 7, 0.1);
        color: var(--warning-color);
    }

    .actions .edit:hover {
        background-color: var(--warning-color);
        color: var(--white-color);
    }

    .actions .delete {
        background-color: rgba(220, 53, 69, 0.1);
        color: var(--danger-color);
    }

    .actions .delete:hover {
        background-color: var(--danger-color);
        color: var(--white-color);
    }

    /* 分页样式 */
    .pagination-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;
    }

    .pagination-info {
        color: var(--gray-color);
    }

    .pagination-info span {
        font-weight: 500;
        color: var(--dark-color);
    }

    .pagination-controls {
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .pagination {
        display: flex;
        align-items: center;
    }

    .pagination button {
        width: 32px;
        height: 32px;
        border-radius: 4px;
        border: 1px solid #eee;
        background-color: var(--white-color);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all var(--transition-speed);
        margin: 0 2px;
    }

    .pagination button:hover:not(:disabled) {
        background-color: #f8f9fa;
    }

    .pagination button.active {
        background-color: var(--primary-color);
        color: var(--white-color);
        border-color: var(--primary-color);
    }

    .pagination button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .pagination-jump {
        display: flex;
        align-items: center;
        margin-left: 10px;
    }

    .pagination-jump span {
        margin: 0 5px;
        color: var(--gray-color);
    }

    .pagination-jump input {
        width: 40px;
        height: 32px;
        border-radius: 4px;
        border: 1px solid #eee;
        text-align: center;
        padding: 0 5px;
    }

    .pagination-jump button {
        width: auto;
        padding: 0 10px;
    }

    .pagination-size {
        display: flex;
        align-items: center;
    }

    .pagination-size span {
        margin: 0 5px;
        color: var(--gray-color);
    }

    .pagination-size select {
        height: 32px;
        border-radius: 4px;
        border: 1px solid #eee;
        padding: 0 5px;
    }

    /* 模态框样式 */
    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1001;
        overflow-y: auto;
        padding: 20px;
    }

    .modal-content {
        background-color: var(--white-color);
        margin: 5% auto;
        width: 600px;
        max-width: 90%;
        border-radius: var(--border-radius);
        box-shadow: 0 10px 30px var(--shadow-color);
        animation: slideDown 0.3s;
        overflow: hidden;
    }

    @keyframes slideDown {
        from { transform: translateY(-50px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }

    .modal-header {
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    .modal-header h3 {
        margin: 0;
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--dark-color);
    }

    .close-btn {
        background: none;
        border: none;
        color: var(--gray-color);
        font-size: 1.2rem;
        cursor: pointer;
        transition: all var(--transition-speed);
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }

    .close-btn:hover {
        color: var(--danger-color);
        background-color: rgba(220, 53, 69, 0.1);
    }

    .modal-body {
        padding: 20px;
        max-height: 70vh;
        overflow-y: auto;
    }

    .modal-footer {
        padding: 15px 20px;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        background-color: #f8f9fa;
    }

    /* 表单样式 */
    .form-group {
        margin-bottom: 15px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: var(--border-radius);
        font-size: 14px;
        transition: all var(--transition-speed);
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(255, 107, 149, 0.25);
        outline: none;
    }

    .form-group.checkbox {
        display: flex;
        align-items: center;
    }

    .form-group.checkbox input {
        width: auto;
        margin-right: 10px;
    }

    .required {
        color: var(--danger-color);
    }

    /* 消息提示样式 */
    .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 300px;
        background-color: var(--white-color);
        border-radius: var(--border-radius);
        box-shadow: 0 5px 15px var(--shadow-color);
        overflow: hidden;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transform: translateX(30px);
        transition: all 0.3s;
    }

    .toast.show {
        opacity: 1;
        visibility: visible;
        transform: translateX(0);
    }

    .toast-content {
        display: flex;
        align-items: center;
        padding: 15px;
    }

    .toast-icon {
        font-size: 1.5rem;
        margin-right: 15px;
        display: none;
    }

    .toast-icon.success {
        color: var(--success-color);
    }

    .toast-icon.error {
        color: var(--danger-color);
    }

    .toast-icon.info {
        color: var(--info-color);
    }

    .toast-message {
        flex: 1;
        font-weight: 500;
    }

    .toast-progress {
        height: 3px;
        background-color: var(--primary-color);
        width: 100%;
        animation: progress 3s linear;
    }

    @keyframes progress {
        from { width: 100%; }
        to { width: 0%; }
    }

    /* 响应式样式 */
    @media (max-width: 768px) {
        .sidebar {
            width: 70px;
        }

        .sidebar-title,
        .sidebar-subtitle {
            display: none;
        }

        .sidebar-menu li a span {
            display: none;
        }

        .sidebar-menu li a i {
            margin-right: 0;
            font-size: 1.3rem;
        }

        .main-content {
            margin-left: 70px;
        }

        .action-bar {
            flex-direction: column;
            align-items: stretch;
            gap: 10px;
        }

        .search-container {
            width: 100%;
        }

        .pagination-container {
            flex-direction: column;
            gap: 10px;
        }

        .pagination-controls {
            flex-direction: column;
            gap: 10px;
        }
    }
</style>

</head>
<body>
    <div class="loading-overlay" id="loading-overlay">
        <div class="spinner"></div>
    </div>

    <div class="dashboard-container">
        <!-- 引入通用头部导航 -->
        <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/header-nav.php'); ?>

            <!-- 面包屑导航 -->
            <?php
            // 设置当前页面标题
            $page_title = '公告列表';
            // 设置当前页面路径
            $page_path = [
                ['title' => '公告管理', 'url' => '/dashboard/announcements']
            ];
            // 包含面包屑导航组件
            include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/admin-breadcrumb.php');
            ?>

            <div class="action-bar">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="搜索公告标题...">
                    <button class="search-button"><i class="bi bi-search"></i></button>
                </div>
                <button class="btn btn-primary" id="add-announcement-btn">新增公告</button>
            </div>

            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>类型</th>
                            <th>标题</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td>系统公告</td>
                            <td>系统维护通知</td>
                            <td><span class="status active">已发布</span></td>
                            <td>2023-01-15</td>
                            <td class="actions">
                                <button class="edit" onclick="openEditModal(1)" title="编辑"><i class="bi bi-pencil"></i> <span class="btn-text">编辑</span></button>
                                <button class="delete" onclick="deleteAnnouncement(1)" title="删除"><i class="bi bi-trash"></i> <span class="btn-text">删除</span></button>
                            </td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>活动公告</td>
                            <td>新年优惠活动</td>
                            <td><span class="status active">已发布</span></td>
                            <td>2023-01-20</td>
                            <td class="actions">
                                <button class="edit" onclick="openEditModal(2)" title="编辑"><i class="bi bi-pencil"></i> <span class="btn-text">编辑</span></button>
                                <button class="delete" onclick="deleteAnnouncement(2)" title="删除"><i class="bi bi-trash"></i> <span class="btn-text">删除</span></button>
                            </td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>版本更新</td>
                            <td>V2.0版本更新说明</td>
                            <td><span class="status inactive">未发布</span></td>
                            <td>2023-02-05</td>
                            <td class="actions">
                                <button class="edit" onclick="openEditModal(3)" title="编辑"><i class="bi bi-pencil"></i> <span class="btn-text">编辑</span></button>
                                <button class="delete" onclick="deleteAnnouncement(3)" title="删除"><i class="bi bi-trash"></i> <span class="btn-text">删除</span></button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="pagination-container">
                <div class="pagination-info">
                    显示 <span>1-10</span> 条，共 <span>25</span> 条
                </div>
                <div class="pagination-controls">
                    <div class="pagination">
                        <button disabled><i class="bi bi-chevron-left"></i></button>
                        <button class="active">1</button>
                        <button>2</button>
                        <button>3</button>
                        <button><i class="bi bi-chevron-right"></i></button>
                        <div class="pagination-jump">
                            <span>跳至</span>
                            <input type="number" min="1" max="3" value="1">
                            <span>页</span>
                            <button>GO</button>
                        </div>
                    </div>
                    <div class="pagination-size">
                        <span>每页显示</span>
                        <select>
                            <option value="10" selected>10</option>
                            <option value="20">20</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span>条</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 新增公告模态框 -->
    <div class="modal" id="add-announcement-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>新增公告</h3>
                <button class="close-btn" onclick="closeModal('add-announcement-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <form id="add-announcement-form">
                    <div class="form-group">
                        <label for="announcement-type">公告类型 <span class="required">*</span></label>
                        <select id="announcement-type" required>
                            <option value="">选择类型</option>
                            <option value="系统公告">系统公告</option>
                            <option value="活动公告">活动公告</option>
                            <option value="版本更新">版本更新</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="announcement-title">公告标题 <span class="required">*</span></label>
                        <input type="text" id="announcement-title" required>
                    </div>
                    <div class="form-group">
                        <label for="announcement-content">公告内容 <span class="required">*</span></label>
                        <textarea id="announcement-content" rows="5" required></textarea>
                    </div>
                    <div class="form-group checkbox">
                        <input type="checkbox" id="announcement-status" checked>
                        <label for="announcement-status">立即发布</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('add-announcement-modal')">取消</button>
                <button class="btn btn-primary" onclick="addAnnouncement()">确定</button>
            </div>
        </div>
    </div>

    <!-- 编辑公告模态框 -->
    <div class="modal" id="edit-announcement-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑公告</h3>
                <button class="close-btn" onclick="closeModal('edit-announcement-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <form id="edit-announcement-form">
                    <input type="hidden" id="edit-announcement-id">
                    <div class="form-group">
                        <label for="edit-announcement-type">公告类型 <span class="required">*</span></label>
                        <select id="edit-announcement-type" required>
                            <option value="">选择类型</option>
                            <option value="系统公告">系统公告</option>
                            <option value="活动公告">活动公告</option>
                            <option value="版本更新">版本更新</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="edit-announcement-title">公告标题 <span class="required">*</span></label>
                        <input type="text" id="edit-announcement-title" required>
                    </div>
                    <div class="form-group">
                        <label for="edit-announcement-content">公告内容 <span class="required">*</span></label>
                        <textarea id="edit-announcement-content" rows="5" required></textarea>
                    </div>
                    <div class="form-group checkbox">
                        <input type="checkbox" id="edit-announcement-status">
                        <label for="edit-announcement-status">发布公告</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('edit-announcement-modal')">取消</button>
                <button class="btn btn-primary" onclick="updateAnnouncement()">保存</button>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div class="modal" id="confirm-delete-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>确认删除</h3>
                <button class="close-btn" onclick="closeModal('confirm-delete-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <p>确定要删除该公告吗？此操作不可恢复！</p>
                <input type="hidden" id="delete-announcement-id">
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('confirm-delete-modal')">取消</button>
                <button class="btn btn-danger" onclick="confirmDelete()">删除</button>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div class="toast" id="toast">
        <div class="toast-content">
            <i class="bi bi-check-circle-fill toast-icon success"></i>
            <i class="bi bi-x-circle-fill toast-icon error"></i>
            <i class="bi bi-info-circle-fill toast-icon info"></i>
            <div class="toast-message">操作成功</div>
        </div>
        <div class="toast-progress"></div>
    </div>

    <!-- 引入通用JS文件 -->
    <script src="/assets/js/common.js"></script>

<script>
    // DOM元素
    const addAnnouncementBtn = document.getElementById("add-announcement-btn");
    const searchButton = document.querySelector(".search-button");

    // 初始化
    document.addEventListener("DOMContentLoaded", function() {
        // 显示加载动画
        showLoading();

        // 添加表格行的动画延迟
        const tableRows = document.querySelectorAll(".table tbody tr");
        tableRows.forEach((row, index) => {
            row.style.animationDelay = `${index * 0.1}s`;
        });

        // 添加状态切换功能
        const statusElements = document.querySelectorAll(".status");
        statusElements.forEach(status => {
            status.addEventListener("click", function() {
                toggleStatus(this);
            });
        });

        // 添加搜索功能
        if (searchButton) {
            searchButton.addEventListener("click", function() {
                searchAnnouncements();
            });
        }

        // 添加按钮点击事件
        setupButtonEvents();

        // 隐藏加载动画
        setTimeout(hideLoading, 500);

        // 新增公告按钮点击事件
        if (addAnnouncementBtn) {
            addAnnouncementBtn.addEventListener("click", function() {
                openModal("add-announcement-modal");
            });
        }
    });

    // 设置按钮点击事件
    function setupButtonEvents() {
        // 编辑按钮
        const editButtons = document.querySelectorAll(".actions .edit");
        editButtons.forEach(button => {
            button.addEventListener("click", function() {
                const id = this.closest("tr").getAttribute("data-id");
                openEditModal(id);
            });
        });

        // 删除按钮
        const deleteButtons = document.querySelectorAll(".actions .delete");
        deleteButtons.forEach(button => {
            button.addEventListener("click", function() {
                const id = this.closest("tr").getAttribute("data-id");
                deleteAnnouncement(id);
            });
        });
    }

    // 搜索公告
    function searchAnnouncements() {
        const searchInput = document.querySelector(".search-input");
        const searchTerm = searchInput.value.trim();

        if (!searchTerm) {
            showToast("请输入搜索关键词", "info");
            return;
        }

        showLoading();

        // 模拟搜索请求
        setTimeout(() => {
            hideLoading();
            showToast(`已搜索: ${searchTerm}`, "success");
        }, 500);
    }

    // 切换状态
    function toggleStatus(element) {
        showLoading();

        // 获取当前状态
        const isActive = element.classList.contains("active");
        const announcementId = element.getAttribute("data-id") || element.closest("tr").getAttribute("data-id");

        // 模拟API请求
        setTimeout(() => {
            hideLoading();

            // 切换状态
            if (isActive) {
                element.classList.remove("active");
                element.classList.add("inactive");
                element.textContent = "未发布";
                showToast("公告已设为未发布", "info");
            } else {
                element.classList.remove("inactive");
                element.classList.add("active");
                element.textContent = "已发布";
                showToast("公告已发布", "success");
            }
        }, 500);
    }

    // 新增公告按钮点击事件
    addAnnouncementBtn.addEventListener("click", function() {
        openModal("add-announcement-modal");
    });

    // 打开编辑模态框
    function openEditModal(id) {
        showLoading();

        // 模拟API请求获取公告数据
        setTimeout(() => {
            hideLoading();

            // 模拟数据
            const announcementData = {
                id: id,
                type: id === 1 ? '系统公告' : (id === 2 ? '活动公告' : '版本更新'),
                title: id === 1 ? '系统维护通知' : (id === 2 ? '新年优惠活动' : 'V2.0版本更新说明'),
                content: id === 1 ? '尊敬的用户，系统将于2023年1月20日凌晨2:00-4:00进行维护升级，期间服务将暂时不可用，请您合理安排使用时间。' :
                         (id === 2 ? '新年特惠活动开始啦！即日起至2023年2月5日，充值会员享受8折优惠，多充多送，最高可获得100元代金券。' :
                         'V2.0版本更新内容：1. 优化头像匹配算法；2. 新增多种滤镜效果；3. 修复已知bug；4. 提升整体性能。'),
                status: id === 3 ? 0 : 1
            };

            // 填充表单
            document.getElementById("edit-announcement-id").value = announcementData.id;
            document.getElementById("edit-announcement-type").value = announcementData.type;
            document.getElementById("edit-announcement-title").value = announcementData.title;
            document.getElementById("edit-announcement-content").value = announcementData.content;
            document.getElementById("edit-announcement-status").checked = announcementData.status === 1;

            // 打开模态框
            openModal("edit-announcement-modal");
        }, 500);
    }

    // 更新公告
    function updateAnnouncement() {
        // 获取表单数据
        const type = document.getElementById("edit-announcement-type").value;
        const title = document.getElementById("edit-announcement-title").value;
        const content = document.getElementById("edit-announcement-content").value;
        const status = document.getElementById("edit-announcement-status").checked;

        // 表单验证
        if (!type) {
            showToast("请选择公告类型", "error");
            return;
        }

        if (!title) {
            showToast("请输入公告标题", "error");
            return;
        }

        if (!content) {
            showToast("请输入公告内容", "error");
            return;
        }

        showLoading();
        // 模拟API请求
        setTimeout(() => {
            hideLoading();
            closeModal("edit-announcement-modal");
            showToast("公告更新成功", "success");

            // 刷新页面或更新UI
            // location.reload();
        }, 800);
    }

    // 删除公告
    function deleteAnnouncement(id) {
        document.getElementById("delete-announcement-id").value = id;
        openModal("confirm-delete-modal");
    }

    // 确认删除
    function confirmDelete() {
        const id = document.getElementById("delete-announcement-id").value;
        showLoading();
        // 模拟API请求
        setTimeout(() => {
            hideLoading();
            closeModal("confirm-delete-modal");
            showToast("公告已删除");

            // 刷新页面或更新UI
            // location.reload();
        }, 800);
    }

    // 添加公告
    function addAnnouncement() {
        // 获取表单数据
        const type = document.getElementById("announcement-type").value;
        const title = document.getElementById("announcement-title").value;
        const content = document.getElementById("announcement-content").value;
        const status = document.getElementById("announcement-status").checked;

        // 表单验证
        if (!type) {
            showToast("请选择公告类型", "error");
            return;
        }

        if (!title) {
            showToast("请输入公告标题", "error");
            return;
        }

        if (!content) {
            showToast("请输入公告内容", "error");
            return;
        }

        showLoading();
        // 模拟API请求
        setTimeout(() => {
            hideLoading();
            closeModal("add-announcement-modal");
            showToast("公告添加成功", "success");

            // 清空表单
            document.getElementById("announcement-type").value = "";
            document.getElementById("announcement-title").value = "";
            document.getElementById("announcement-content").value = "";
            document.getElementById("announcement-status").checked = true;

            // 刷新页面或更新UI
            // location.reload();
        }, 800);
    }
</script>
<?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/footer-scripts.php'); ?>

<!-- 网站底部 -->
<?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/site-footer.php'); ?>

<script src="/assets/js/sidebar-toggle-fix.js"></script>
<script src="/assets/js/responsive-layout.js"></script>
<script src="/assets/js/avatar-theme-fix.js"></script>

<!-- 模态框精确修复脚本 -->
<script src="/assets/js/modal-precise-fix.js"></script>
</body>
</html>




