<?php
// 验证和风天气API密钥

header('Content-Type: application/json');

$apiKey = $_GET['key'] ?? '70a2c59b6b194c92bbed4dbb4bcae572';
$city = $_GET['city'] ?? '北京';

// 测试API密钥
$url = "https://devapi.qweather.com/v7/weather/now?location=" . urlencode($city) . "&key=" . $apiKey;

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 15);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_USERAGENT, 'Weather App/1.0');

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

$result = [
    'api_key' => $apiKey,
    'api_key_length' => strlen($apiKey),
    'city' => $city,
    'url' => $url,
    'http_code' => $httpCode,
    'curl_error' => $error,
    'response_length' => $response ? strlen($response) : 0,
    'raw_response' => $response
];

if ($response) {
    $data = json_decode($response, true);
    $result['parsed_response'] = $data;
    
    if ($data) {
        $result['api_status'] = $data['code'] ?? 'unknown';
        $result['api_message'] = $data['message'] ?? '';
        
        if (isset($data['code'])) {
            switch ($data['code']) {
                case '200':
                    $result['status'] = 'success';
                    $result['message'] = 'API密钥有效，天气数据获取成功';
                    break;
                case '401':
                    $result['status'] = 'error';
                    $result['message'] = 'API密钥无效或未授权';
                    break;
                case '402':
                    $result['status'] = 'error';
                    $result['message'] = 'API密钥已超出使用限制';
                    break;
                case '403':
                    $result['status'] = 'error';
                    $result['message'] = 'API密钥无权限访问该接口';
                    break;
                case '404':
                    $result['status'] = 'error';
                    $result['message'] = '请求的资源不存在';
                    break;
                case '429':
                    $result['status'] = 'error';
                    $result['message'] = '请求频率超出限制';
                    break;
                default:
                    $result['status'] = 'error';
                    $result['message'] = '未知错误: ' . $data['code'];
            }
        }
    }
} else {
    $result['status'] = 'error';
    $result['message'] = '网络请求失败: ' . $error;
}

echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
