/**
 * 统一侧边栏样式
 * 确保所有页面的侧边栏样式一致
 */

/* 侧边栏样式 */
.sidebar {
    width: 250px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
    color: #ffffff !important;
    padding: 20px 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 0 20px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
    margin-bottom: 20px;
}

.sidebar-logo {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin-bottom: 10px;
    object-fit: cover;
    background-color: rgba(255, 255, 255, 0.2);
}

.sidebar-title {
    color: #ffffff !important;
    font-size: 1.5rem;
    margin-bottom: 5px;
    font-weight: 600;
}

.sidebar-subtitle {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 0.9rem;
}

.sidebar.collapsed .sidebar-title,
.sidebar.collapsed .sidebar-subtitle {
    display: none;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
}

.sidebar-menu li {
    margin-bottom: 5px;
}

.sidebar-menu li a {
    color: #ffffff !important;
    text-decoration: none;
    display: block;
    padding: 12px 20px;
    transition: all 0.3s;
    border-left: 4px solid transparent;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar-menu li a:hover,
.sidebar-menu li a.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-left-color: #ffffff;
}

.sidebar-menu li a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
    color: #ffffff !important;
}

.sidebar.collapsed .sidebar-menu li a span {
    display: none;
}

.sidebar.collapsed .sidebar-menu li a i {
    margin-right: 0;
    font-size: 1.3rem;
}

/* 模态框样式修复 */
.modal {
    display: none;
    position: fixed;
    z-index: 1050;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: #ffffff;
    margin: 10% auto;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: 80%;
    max-width: 600px;
    position: relative;
    animation: fadeInDown 0.3s;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
    margin-bottom: 15px;
}

.modal-header h3 {
    margin: 0;
    color: var(--dark-color);
    font-size: 1.2rem;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--gray-color);
    transition: color 0.3s;
}

.close-btn:hover {
    color: var(--danger-color);
}

.modal-body {
    margin-bottom: 20px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

/* 确保模态框按钮样式一致 */
.modal .btn {
    display: inline-block;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 8px;
    transition: all 0.3s;
    cursor: pointer;
}

.modal .btn-primary {
    color: #ffffff;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-color: var(--primary-color);
}

.modal .btn-primary:hover {
    background: linear-gradient(135deg, #ff4f7e 0%, #ffa5c0 100%);
    border-color: #ff4f7e;
}

.modal .btn-secondary {
    color: #ffffff;
    background-color: var(--gray-color);
    border-color: var(--gray-color);
}

.modal .btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

.modal .btn-danger {
    color: #ffffff;
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.modal .btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

/* 确保表单样式一致 */
.modal .form-group {
    margin-bottom: 15px;
}

.modal label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--dark-color);
}

.modal input[type="text"],
.modal input[type="email"],
.modal input[type="password"],
.modal select,
.modal textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s;
}

.modal input[type="text"]:focus,
.modal input[type="email"]:focus,
.modal input[type="password"]:focus,
.modal select:focus,
.modal textarea:focus {
    border-color: var(--primary-color);
    outline: none;
}

/* 支付方式样式 */
.payment-methods {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.payment-method {
    border: 2px solid #eee;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
}

.payment-method:hover {
    border-color: var(--primary-color);
    background-color: rgba(var(--primary-color-rgb), 0.05);
}

.payment-method.selected {
    border-color: var(--primary-color);
    background-color: rgba(var(--primary-color-rgb), 0.1);
}

.payment-method img {
    width: 40px;
    height: 40px;
    margin-bottom: 10px;
}

.payment-method-name {
    font-size: 14px;
    font-weight: 500;
}
