/* 头部样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.menu-toggle {
    background: none;
    border: none;
    color: var(--dark-color);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 8px;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed);
}

.menu-toggle:hover {
    background-color: var(--light-color);
}

.user-info {
    display: flex;
    align-items: center;
    position: relative;
    cursor: pointer;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--white-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 10px;
}

.user-name {
    font-weight: 500;
    color: var(--dark-color);
}

.dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    top: 100%;
    background-color: var(--white-color);
    min-width: 160px;
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    border-radius: var(--border-radius);
    z-index: 1000;
    padding: 10px 0;
    margin-top: 5px;
    animation: fadeIn 0.3s;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.user-info:hover .dropdown-content {
    display: block;
}

.dropdown-content a {
    color: var(--dark-color);
    padding: 10px 15px;
    text-decoration: none;
    display: block;
    transition: all var(--transition-speed);
}

.dropdown-content a:hover {
    background-color: var(--light-color);
}

.dropdown-content a i {
    margin-right: 8px;
    width: 16px;
}

/* 头部信息区域样式 - 与仪表盘页面完全一致 */
.header-info-content {
    display: flex;
    align-items: center;
    margin: 0 auto;
    flex: 1;
    padding: 0 20px;
    max-width: 800px;
    justify-content: center;
    gap: 30px;
    min-height: 40px;
    position: relative;
}

.wisdom-quote {
    display: flex;
    align-items: center;
    animation: fadeIn 0.5s;
    font-size: 0.9rem;
    color: var(--gray-color);
    flex-shrink: 1;
    min-width: 200px;
    max-width: 400px;
}

.wisdom-quote span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    max-width: 100%;
}

.weather-info {
    display: flex;
    align-items: center;
    white-space: nowrap;
    flex-shrink: 0;
    min-width: 150px;
    font-size: 0.9rem;
    color: var(--gray-color);
}

.wisdom-quote i, .weather-info i {
    margin-right: 8px;
    font-size: 16px;
    color: var(--primary-color);
    flex-shrink: 0;
}

.info-actions {
    display: flex;
    gap: 5px;
}

.info-actions button {
    background: none;
    border: none;
    color: var(--gray-color);
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: all var(--transition-speed);
}

.info-actions button:hover {
    background-color: var(--light-color);
    color: var(--primary-color);
}

/* 响应式处理 */
@media (max-width: 768px) {
    .header-info-content {
        max-width: 100%;
        gap: 15px;
        padding: 0 10px;
    }

    .wisdom-quote {
        max-width: 60%;
    }

    .wisdom-quote span {
        font-size: 13px;
    }

    .weather-info {
        min-width: 120px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .wisdom-quote {
        display: none !important;
    }

    .header-info-content {
        justify-content: center;
    }

    .weather-info {
        margin-right: 0;
        max-width: 150px;
    }
}

/* 天气设置模态框样式 */
.modal-backdrop {
    display: none !important;
}

.modal {
    z-index: 1050 !important;
}

.modal-dialog {
    margin: 1.75rem auto !important;
}

#weatherSettingsModal .modal-content {
    background-color: #fff !important;
    border: 1px solid #dee2e6 !important;
    border-radius: 0.375rem !important;
}

#weatherSettingsModal .modal-header {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6 !important;
}

#weatherSettingsModal .modal-body {
    padding: 1.5rem !important;
}

#weatherSettingsModal .form-label {
    font-weight: 600 !important;
    color: #495057 !important;
}

#weatherSettingsModal .form-control,
#weatherSettingsModal .form-select {
    border: 1px solid #ced4da !important;
    border-radius: 0.375rem !important;
}

#weatherSettingsModal .form-control:focus,
#weatherSettingsModal .form-select:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 107, 149, 0.25) !important;
}

#weatherSettingsModal .btn-primary {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

#weatherSettingsModal .btn-primary:hover {
    background-color: #e05a84 !important;
    border-color: #e05a84 !important;
}
