<?php
// ????
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// ??????
ini_set('display_errors', 1);
error_reporting(E_ALL);

// ??????
$logFile = __DIR__ . '/../../../storage/logs/dashboard.log';
$logDir = dirname($logFile);
if (!is_dir($logDir)) {
    mkdir($logDir, 0777, true);
}

file_put_contents($logFile, date('Y-m-d H:i:s') . " - ???????\n", FILE_APPEND);
file_put_contents($logFile, "SESSION ??: " . print_r($_SESSION, true) . "\n", FILE_APPEND);

// ????????
if (!isset($_SESSION['admin_id'])) {
    file_put_contents($logFile, "??: ???\n", FILE_APPEND);
    header('Location: /admin/login');
    exit;
}

// ???????
header('Content-Type: text/html; charset=utf-8');
readfile(__DIR__ . '/index.html');
exit;