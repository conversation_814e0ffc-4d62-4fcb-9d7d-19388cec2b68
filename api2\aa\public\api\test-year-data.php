<?php
// 测试年度图表数据

// 开启会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 设置响应头
header('Content-Type: application/json');

// 检查用户是否已登录
if (!isset($_SESSION['username'])) {
    http_response_code(401);
    echo json_encode(['error' => '未授权访问']);
    exit;
}

try {
    // 模拟POST数据
    $input = [
        'chartId' => 'orderChart',
        'timeRange' => 'year'
    ];
    
    // 包含数据库配置
    $configPath = __DIR__ . '/../../config/config.php';
    $config = require_once($configPath);
    
    $dbConfig = $config['database'];
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $prefix = $dbConfig['prefix'];
    $tableName = $prefix . 'order';
    
    // 检查表是否存在
    $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
    $stmt->execute([$tableName]);
    $tableExists = $stmt->fetch() !== false;
    
    if (!$tableExists) {
        throw new Exception("数据表 {$tableName} 不存在");
    }
    
    // 先检查数据库中的时间范围
    $stmt = $pdo->prepare("SELECT MIN(intime) as min_time, MAX(intime) as max_time, COUNT(*) as total FROM {$tableName}");
    $stmt->execute();
    $timeRange = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // 检查每个月的数据
    $currentYear = date('Y');
    $monthlyData = [];
    $labels = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
    $data = [];
    
    for ($month = 1; $month <= 12; $month++) {
        $startTime = strtotime("$currentYear-$month-01 00:00:00");
        $endTime = strtotime("$currentYear-$month-" . date('t', $startTime) . " 23:59:59");
        
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM {$tableName} WHERE intime >= ? AND intime <= ?");
        $stmt->execute([$startTime, $endTime]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $count = (int)($result['count'] ?? 0);
        $data[] = $count;
        
        $monthlyData[] = [
            'month' => $month,
            'label' => $labels[$month - 1],
            'start_time' => $startTime,
            'end_time' => $endTime,
            'start_date' => date('Y-m-d H:i:s', $startTime),
            'end_date' => date('Y-m-d H:i:s', $endTime),
            'count' => $count
        ];
    }
    
    // 检查实际数据分布
    $stmt = $pdo->prepare("
        SELECT 
            YEAR(FROM_UNIXTIME(intime)) as year,
            MONTH(FROM_UNIXTIME(intime)) as month,
            COUNT(*) as count,
            MIN(intime) as min_time,
            MAX(intime) as max_time
        FROM {$tableName} 
        GROUP BY YEAR(FROM_UNIXTIME(intime)), MONTH(FROM_UNIXTIME(intime))
        ORDER BY year, month
    ");
    $stmt->execute();
    $actualData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 返回详细调试信息
    echo json_encode([
        'table_info' => [
            'table_name' => $tableName,
            'total_records' => $timeRange['total']
        ],
        'time_range_info' => [
            'min_time' => $timeRange['min_time'],
            'max_time' => $timeRange['max_time'],
            'min_date' => date('Y-m-d H:i:s', $timeRange['min_time']),
            'max_date' => date('Y-m-d H:i:s', $timeRange['max_time'])
        ],
        'query_info' => [
            'current_year' => $currentYear,
            'query_logic' => '查询当前年份(' . $currentYear . ')每个月的数据'
        ],
        'monthly_breakdown' => $monthlyData,
        'actual_data_distribution' => $actualData,
        'chart_data' => [
            'labels' => $labels,
            'datasets' => [['data' => $data]]
        ]
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
