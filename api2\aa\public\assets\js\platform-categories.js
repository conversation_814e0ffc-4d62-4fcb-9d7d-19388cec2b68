/**
 * 小程序平台分类功能
 * 提供平台分类管理功能，便于管理大量平台
 */

// 在DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('平台分类功能已加载');
    
    // 初始化平台分类功能
    initPlatformCategories();
});

/**
 * 初始化平台分类功能
 */
function initPlatformCategories() {
    // 添加分类筛选器
    setupCategoryFilter();
    
    // 为平台添加分类标签
    addCategoryLabels();
    
    // 添加分类管理功能
    setupCategoryManagement();
}

/**
 * 设置分类筛选器
 */
function setupCategoryFilter() {
    // 检查是否存在筛选器容器
    const filterContainer = document.querySelector('.filter-container');
    if (!filterContainer) return;
    
    // 创建分类筛选器
    const categoryFilter = document.createElement('div');
    categoryFilter.className = 'filter-group';
    categoryFilter.innerHTML = `
        <select id="category-filter" class="filter-select">
            <option value="">全部分类</option>
            <option value="entertainment">娱乐</option>
            <option value="utility">工具</option>
            <option value="social">社交</option>
            <option value="education">教育</option>
            <option value="other">其他</option>
        </select>
    `;
    
    // 添加到筛选器容器
    filterContainer.appendChild(categoryFilter);
    
    // 添加筛选器事件
    const categorySelect = categoryFilter.querySelector('#category-filter');
    if (categorySelect) {
        categorySelect.addEventListener('change', function() {
            filterPlatformsByCategory(this.value);
        });
    }
    
    console.log('分类筛选器已设置');
}

/**
 * 为平台添加分类标签
 */
function addCategoryLabels() {
    // 获取所有平台行
    const platformRows = document.querySelectorAll('tbody tr');
    
    platformRows.forEach(row => {
        // 获取平台ID
        const platformId = row.getAttribute('data-id');
        if (!platformId) return;
        
        // 获取平台名称单元格
        const nameCell = row.querySelector('td:nth-child(2)');
        if (!nameCell) return;
        
        // 获取平台名称
        const platformName = nameCell.textContent.trim();
        
        // 根据平台ID生成分类
        const categoryType = getCategoryByPlatformId(platformId);
        
        // 创建分类标签
        const categoryLabel = document.createElement('span');
        categoryLabel.className = `platform-category ${categoryType}`;
        categoryLabel.textContent = getCategoryName(categoryType);
        
        // 设置分类属性
        row.setAttribute('data-category', categoryType);
        
        // 添加到名称单元格
        nameCell.innerHTML = `
            <span class="platform-name">${platformName}</span>
            ${categoryLabel.outerHTML}
        `;
    });
    
    console.log('平台分类标签已添加');
}

/**
 * 根据平台ID获取分类
 * @param {string} platformId - 平台ID
 * @returns {string} 分类类型
 */
function getCategoryByPlatformId(platformId) {
    // 模拟根据平台ID分配分类
    const id = parseInt(platformId);
    
    if (id % 5 === 0) return 'education';
    if (id % 4 === 0) return 'social';
    if (id % 3 === 0) return 'utility';
    if (id % 2 === 0) return 'entertainment';
    
    return 'other';
}

/**
 * 获取分类名称
 * @param {string} categoryType - 分类类型
 * @returns {string} 分类名称
 */
function getCategoryName(categoryType) {
    switch (categoryType) {
        case 'entertainment': return '娱乐';
        case 'utility': return '工具';
        case 'social': return '社交';
        case 'education': return '教育';
        default: return '其他';
    }
}

/**
 * 根据分类筛选平台
 * @param {string} category - 分类类型
 */
function filterPlatformsByCategory(category) {
    console.log(`筛选分类: ${category}`);
    
    // 获取所有平台行
    const platformRows = document.querySelectorAll('tbody tr');
    
    platformRows.forEach(row => {
        // 如果没有选择分类或分类匹配，则显示行
        if (!category || row.getAttribute('data-category') === category) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

/**
 * 设置分类管理功能
 */
function setupCategoryManagement() {
    // 检查是否存在操作栏
    const actionBar = document.querySelector('.action-bar');
    if (!actionBar) return;
    
    // 创建分类管理按钮
    const categoryManageBtn = document.createElement('button');
    categoryManageBtn.className = 'btn btn-outline-secondary';
    categoryManageBtn.innerHTML = '<i class="bi bi-tags"></i> 分类管理';
    categoryManageBtn.addEventListener('click', openCategoryManagementModal);
    
    // 添加到操作栏
    actionBar.appendChild(categoryManageBtn);
    
    console.log('分类管理功能已设置');
}

/**
 * 打开分类管理模态框
 */
function openCategoryManagementModal() {
    console.log('打开分类管理模态框');
    
    // 检查是否存在分类管理模态框
    let categoryModal = document.getElementById('category-management-modal');
    
    // 如果不存在，创建一个
    if (!categoryModal) {
        categoryModal = document.createElement('div');
        categoryModal.className = 'modal';
        categoryModal.id = 'category-management-modal';
        categoryModal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>分类管理</h3>
                    <button class="close-btn" onclick="closeModal('category-management-modal')"><i class="bi bi-x-lg"></i></button>
                </div>
                <div class="modal-body">
                    <div class="category-list">
                        <div class="category-item">
                            <div class="category-info">
                                <span class="platform-category entertainment">娱乐</span>
                                <span class="category-count">12个平台</span>
                            </div>
                            <div class="category-actions">
                                <button class="btn btn-sm btn-outline-primary edit-category-btn" data-category="entertainment">编辑</button>
                                <button class="btn btn-sm btn-outline-danger delete-category-btn" data-category="entertainment">删除</button>
                            </div>
                        </div>
                        <div class="category-item">
                            <div class="category-info">
                                <span class="platform-category utility">工具</span>
                                <span class="category-count">8个平台</span>
                            </div>
                            <div class="category-actions">
                                <button class="btn btn-sm btn-outline-primary edit-category-btn" data-category="utility">编辑</button>
                                <button class="btn btn-sm btn-outline-danger delete-category-btn" data-category="utility">删除</button>
                            </div>
                        </div>
                        <div class="category-item">
                            <div class="category-info">
                                <span class="platform-category social">社交</span>
                                <span class="category-count">6个平台</span>
                            </div>
                            <div class="category-actions">
                                <button class="btn btn-sm btn-outline-primary edit-category-btn" data-category="social">编辑</button>
                                <button class="btn btn-sm btn-outline-danger delete-category-btn" data-category="social">删除</button>
                            </div>
                        </div>
                        <div class="category-item">
                            <div class="category-info">
                                <span class="platform-category education">教育</span>
                                <span class="category-count">4个平台</span>
                            </div>
                            <div class="category-actions">
                                <button class="btn btn-sm btn-outline-primary edit-category-btn" data-category="education">编辑</button>
                                <button class="btn btn-sm btn-outline-danger delete-category-btn" data-category="education">删除</button>
                            </div>
                        </div>
                        <div class="category-item">
                            <div class="category-info">
                                <span class="platform-category other">其他</span>
                                <span class="category-count">3个平台</span>
                            </div>
                            <div class="category-actions">
                                <button class="btn btn-sm btn-outline-primary edit-category-btn" data-category="other">编辑</button>
                                <button class="btn btn-sm btn-outline-danger delete-category-btn" data-category="other">删除</button>
                            </div>
                        </div>
                    </div>
                    <div class="add-category-form">
                        <h4>添加新分类</h4>
                        <div class="form-group">
                            <label for="new-category-name">分类名称</label>
                            <input type="text" id="new-category-name" class="form-control" placeholder="输入分类名称">
                        </div>
                        <div class="form-group">
                            <label for="new-category-color">分类颜色</label>
                            <input type="color" id="new-category-color" class="form-control" value="#3498db">
                        </div>
                        <button class="btn btn-primary" id="add-category-btn">添加分类</button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal('category-management-modal')">关闭</button>
                </div>
            </div>
        `;
        document.body.appendChild(categoryModal);
        
        // 添加分类管理事件
        setupCategoryManagementEvents(categoryModal);
    }
    
    // 显示模态框
    openModal('category-management-modal');
}

/**
 * 设置分类管理事件
 * @param {HTMLElement} modal - 模态框元素
 */
function setupCategoryManagementEvents(modal) {
    // 编辑分类按钮
    const editButtons = modal.querySelectorAll('.edit-category-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const category = this.getAttribute('data-category');
            editCategory(category);
        });
    });
    
    // 删除分类按钮
    const deleteButtons = modal.querySelectorAll('.delete-category-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const category = this.getAttribute('data-category');
            deleteCategory(category);
        });
    });
    
    // 添加分类按钮
    const addButton = modal.querySelector('#add-category-btn');
    if (addButton) {
        addButton.addEventListener('click', addNewCategory);
    }
}

/**
 * 编辑分类
 * @param {string} category - 分类类型
 */
function editCategory(category) {
    console.log(`编辑分类: ${category}`);
    
    // 显示编辑分类对话框
    showPromptDialog(
        '编辑分类',
        '请输入新的分类名称:',
        getCategoryName(category),
        (newName) => {
            if (newName && newName.trim() !== '') {
                // 更新分类名称
                updateCategoryName(category, newName);
                
                // 显示成功消息
                showToast(`分类 "${getCategoryName(category)}" 已更新为 "${newName}"`, 'success');
            }
        }
    );
}

/**
 * 删除分类
 * @param {string} category - 分类类型
 */
function deleteCategory(category) {
    console.log(`删除分类: ${category}`);
    
    // 显示确认对话框
    showConfirmDialog(
        '删除分类',
        `确定要删除分类 "${getCategoryName(category)}" 吗？该分类下的平台将被移至"其他"分类。`,
        () => {
            // 将该分类下的平台移至"其他"分类
            const platformRows = document.querySelectorAll(`tbody tr[data-category="${category}"]`);
            platformRows.forEach(row => {
                row.setAttribute('data-category', 'other');
                
                // 更新分类标签
                const nameCell = row.querySelector('td:nth-child(2)');
                if (nameCell) {
                    const platformName = nameCell.querySelector('.platform-name').textContent;
                    const categoryLabel = document.createElement('span');
                    categoryLabel.className = 'platform-category other';
                    categoryLabel.textContent = '其他';
                    
                    nameCell.innerHTML = `
                        <span class="platform-name">${platformName}</span>
                        ${categoryLabel.outerHTML}
                    `;
                }
            });
            
            // 从分类管理模态框中移除该分类
            const categoryItem = document.querySelector(`.category-item .platform-category.${category}`).closest('.category-item');
            if (categoryItem) {
                categoryItem.remove();
            }
            
            // 更新分类筛选器
            const categoryOption = document.querySelector(`#category-filter option[value="${category}"]`);
            if (categoryOption) {
                categoryOption.remove();
            }
            
            // 显示成功消息
            showToast(`分类 "${getCategoryName(category)}" 已删除`, 'success');
        }
    );
}

/**
 * 添加新分类
 */
function addNewCategory() {
    const nameInput = document.getElementById('new-category-name');
    const colorInput = document.getElementById('new-category-color');
    
    if (!nameInput || !colorInput) return;
    
    const name = nameInput.value.trim();
    const color = colorInput.value;
    
    if (name === '') {
        showToast('请输入分类名称', 'error');
        return;
    }
    
    console.log(`添加新分类: ${name}, 颜色: ${color}`);
    
    // 生成分类ID
    const categoryId = 'category-' + Date.now();
    
    // 添加到分类列表
    const categoryList = document.querySelector('.category-list');
    if (categoryList) {
        const categoryItem = document.createElement('div');
        categoryItem.className = 'category-item';
        categoryItem.innerHTML = `
            <div class="category-info">
                <span class="platform-category ${categoryId}" style="background-color: ${color}; color: white;">${name}</span>
                <span class="category-count">0个平台</span>
            </div>
            <div class="category-actions">
                <button class="btn btn-sm btn-outline-primary edit-category-btn" data-category="${categoryId}">编辑</button>
                <button class="btn btn-sm btn-outline-danger delete-category-btn" data-category="${categoryId}">删除</button>
            </div>
        `;
        
        categoryList.appendChild(categoryItem);
        
        // 添加事件
        const editButton = categoryItem.querySelector('.edit-category-btn');
        if (editButton) {
            editButton.addEventListener('click', function() {
                const category = this.getAttribute('data-category');
                editCategory(category);
            });
        }
        
        const deleteButton = categoryItem.querySelector('.delete-category-btn');
        if (deleteButton) {
            deleteButton.addEventListener('click', function() {
                const category = this.getAttribute('data-category');
                deleteCategory(category);
            });
        }
    }
    
    // 添加到分类筛选器
    const categoryFilter = document.getElementById('category-filter');
    if (categoryFilter) {
        const option = document.createElement('option');
        option.value = categoryId;
        option.textContent = name;
        categoryFilter.appendChild(option);
    }
    
    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        .platform-category.${categoryId} {
            background-color: ${color}10;
            color: ${color};
        }
    `;
    document.head.appendChild(style);
    
    // 清空输入
    nameInput.value = '';
    
    // 显示成功消息
    showToast(`分类 "${name}" 已添加`, 'success');
}

/**
 * 更新分类名称
 * @param {string} category - 分类类型
 * @param {string} newName - 新名称
 */
function updateCategoryName(category, newName) {
    // 更新分类标签
    const categoryLabels = document.querySelectorAll(`.platform-category.${category}`);
    categoryLabels.forEach(label => {
        label.textContent = newName;
    });
    
    // 更新分类筛选器
    const categoryOption = document.querySelector(`#category-filter option[value="${category}"]`);
    if (categoryOption) {
        categoryOption.textContent = newName;
    }
}
