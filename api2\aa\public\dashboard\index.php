﻿<?php
// 开启会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 包含安全头部设置
require_once($_SERVER['DOCUMENT_ROOT'] . '/includes/security-headers.php');

// 启用错误报告
ini_set('display_errors', 1);
error_reporting(E_ALL);

// 记录请求信息
$logFile = __DIR__ . '/../../storage/logs/admin.log';
$logDir = dirname($logFile);
if (!is_dir($logDir)) {
    mkdir($logDir, 0777, true);
}

file_put_contents($logFile, date('Y-m-d H:i:s') . " - 控制面板请求开始\n", FILE_APPEND);
file_put_contents($logFile, "SESSION 数据: " . print_r($_SESSION, true) . "\n", FILE_APPEND);

// 记录安全日志
logSecurityEvent('页面访问', [
    'page' => 'admin.php',
    'user_id' => $_SESSION['user_id'] ?? 'unknown'
]);

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    // 未登录，重定向到登录页面
    header('Location: /login');
    exit;
}

// 加载统一配置文件
$configPath = dirname(__DIR__, 2) . '/config/config.php';
if (!file_exists($configPath)) {
    die('配置文件不存在: ' . $configPath);
}
$config = require_once($configPath);
$dbConfig = $config['database'];

// 连接数据库
try {
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $prefix = $dbConfig['prefix'];

    // 获取网站基本设置
    $stmt = $pdo->prepare("SELECT * FROM {$prefix}webpz WHERE id = 1");
    $stmt->execute();
    $webConfig = $stmt->fetch(PDO::FETCH_ASSOC);

    // 获取系统信息设置
    $stmt = $pdo->prepare("SELECT * FROM {$prefix}information WHERE id = 1");
    $stmt->execute();
    $infoConfig = $stmt->fetch(PDO::FETCH_ASSOC);

    // 映射设置数据
    $settings = [
        'site_name' => $webConfig['name'] ?? '去水印接口',
        'site_logo' => $infoConfig['site_logo'] ?? '/assets/images/logo.png',
        'site_favicon' => $infoConfig['site_favicon'] ?? '/assets/images/favicon.ico',
        'site_copyright' => $infoConfig['site_copyright'] ?? ('© ' . date('Y') . ' ' . ($webConfig['name'] ?? '管理系统')),
        'icp_number' => $infoConfig['beian_icp'] ?? '',
        'police_number' => $infoConfig['beian_police'] ?? '',
    ];

} catch (PDOException $e) {
    // 数据库连接失败，使用默认设置
    $settings = [
        'site_name' => '去水印接口',
        'site_logo' => '/assets/images/logo.png',
        'site_favicon' => '/assets/images/favicon.ico',
        'site_copyright' => '© ' . date('Y') . ' 管理系统',
        'icp_number' => '',
        'police_number' => '',
    ];
    // 如果数据库连接失败，使用模拟数据
    $pdo = null;
}

// 获取统计数据
function getStats($pdo, $prefix) {
    if (!$pdo) {
        // 数据库连接失败，返回空数据
        return [
            'total_today' => 0,
            'success_today' => 0,
            'failed_today' => 0,
            'success_rate' => 0,
            'total_yesterday' => 0,
            'total_users' => 0,
            'point_users' => 0,
            'monthly_users' => 0,
            'total_orders' => 0,
            'paid_orders' => 0,
            'total_revenue' => 0,
            'recent_days' => [0, 0, 0, 0, 0],
        ];
    }

    $today = date('Y-m-d');
    $yesterday = date('Y-m-d', strtotime('-1 day'));

    try {
        // 今日调用统计
        $stmt = $pdo->prepare("
            SELECT
                COUNT(*) as total_today,
                COUNT(CASE WHEN status = 1 THEN 1 END) as success_today,
                COUNT(CASE WHEN status = 0 THEN 1 END) as failed_today
            FROM {$prefix}record
            WHERE DATE(FROM_UNIXTIME(intime)) = ?
        ");
        $stmt->execute([$today]);
        $todayStats = $stmt->fetch(PDO::FETCH_ASSOC);

        // 昨日调用统计
        $stmt = $pdo->prepare("SELECT COUNT(*) as total_yesterday FROM {$prefix}record WHERE DATE(FROM_UNIXTIME(intime)) = ?");
        $stmt->execute([$yesterday]);
        $yesterdayStats = $stmt->fetch(PDO::FETCH_ASSOC);

        // 获取最近5天的数据
        $recentDays = [];
        for ($i = 0; $i < 5; $i++) {
            $date = date('Y-m-d', strtotime("-{$i} day"));
            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM {$prefix}record WHERE DATE(FROM_UNIXTIME(intime)) = ?");
            $stmt->execute([$date]);
            $dayStats = $stmt->fetch(PDO::FETCH_ASSOC);
            $recentDays[] = (int)($dayStats['total'] ?? 0);
        }

        // 用户统计
        $stmt = $pdo->prepare("
            SELECT
                COUNT(*) as total_users,
                COUNT(CASE WHEN type = 1 THEN 1 END) as point_users,
                COUNT(CASE WHEN type = 2 THEN 1 END) as monthly_users
            FROM {$prefix}business
        ");
        $stmt->execute();
        $userStats = $stmt->fetch(PDO::FETCH_ASSOC);

        // 订单统计
        $stmt = $pdo->prepare("
            SELECT
                COUNT(*) as total_orders,
                COUNT(CASE WHEN status = 1 THEN 1 END) as paid_orders,
                SUM(CASE WHEN status = 1 THEN payment ELSE 0 END) as total_revenue
            FROM {$prefix}order
        ");
        $stmt->execute();
        $orderStats = $stmt->fetch(PDO::FETCH_ASSOC);

        // 计算成功率
        $successRate = $todayStats['total_today'] > 0
            ? round(($todayStats['success_today'] / $todayStats['total_today']) * 100, 2)
            : 0;

        return [
            'total_today' => (int)($todayStats['total_today'] ?? 0),
            'success_today' => (int)($todayStats['success_today'] ?? 0),
            'failed_today' => (int)($todayStats['failed_today'] ?? 0),
            'success_rate' => $successRate,
            'total_yesterday' => (int)($yesterdayStats['total_yesterday'] ?? 0),
            'total_users' => (int)($userStats['total_users'] ?? 0),
            'point_users' => (int)($userStats['point_users'] ?? 0),
            'monthly_users' => (int)($userStats['monthly_users'] ?? 0),
            'total_orders' => (int)($orderStats['total_orders'] ?? 0),
            'paid_orders' => (int)($orderStats['paid_orders'] ?? 0),
            'total_revenue' => (float)($orderStats['total_revenue'] ?? 0),
            'recent_days' => $recentDays, // 最近5天的数据
        ];
    } catch (Exception $e) {
        // 如果查询失败，记录错误并返回空数据
        error_log("数据库查询失败: " . $e->getMessage());
        return [
            'total_today' => 0,
            'success_today' => 0,
            'failed_today' => 0,
            'success_rate' => 0,
            'total_yesterday' => 0,
            'total_users' => 0,
            'point_users' => 0,
            'monthly_users' => 0,
            'total_orders' => 0,
            'paid_orders' => 0,
            'total_revenue' => 0,
            'recent_days' => [0, 0, 0, 0, 0],
        ];
    }
}

// 获取数据
$stats = getStats($pdo, $dbConfig['prefix']);

// 获取用户信息（所有登录用户都是管理员）
$isAdmin = true; // 简化逻辑，所有能访问此页面的用户都是管理员
$username = $_SESSION['username'] ?? '管理员';
$userInitial = mb_substr($username, 0, 1, 'UTF-8');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制面板 - <?php echo htmlspecialchars($settings['site_name']); ?></title>

    <!-- 动态favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo htmlspecialchars($settings['site_favicon']); ?>">
    <link rel="shortcut icon" href="<?php echo htmlspecialchars($settings['site_favicon']); ?>">

    <link href="/assets/vendor/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="/assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="/assets/vendor/animate/animate.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/theme.css">
    <link rel="stylesheet" href="/assets/css/animations.css">
    <link rel="stylesheet" href="/assets/css/enhanced-animations.css">
    <link rel="stylesheet" href="/assets/css/common.css">
    <link rel="stylesheet" href="/assets/css/theme-switcher.css">
    <link rel="stylesheet" href="/assets/css/ui-enhancements.css">
    <link rel="stylesheet" href="/assets/css/stat-item-fix.css">
    <link rel="stylesheet" href="/assets/css/dashboard-value-fix.css">
    <link rel="stylesheet" href="/assets/css/service-info-fix.css">
    <link rel="stylesheet" href="/assets/css/dashboard-position-fix.css">
    <link rel="stylesheet" href="/assets/css/dashboard-alignment-fix.css">
    <link rel="stylesheet" href="/assets/css/admin-responsive.css">
    <!-- 增强图表和安全性样式 -->
    <link rel="stylesheet" href="/assets/css/enhanced-charts.css">
    <link rel="stylesheet" href="/assets/css/security-enhancements.css">
    <style>
        /* 基础样式 */
        :root {
            --primary-color: #ff6b95;
            --secondary-color: #ffa5c0;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --light-color: #f8f9fa;
            --dark-color: #343a40;
            --gray-color: #6c757d;
            --white-color: #ffffff;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --transition-speed: 0.3s;
            --border-radius: 8px;
            --animate-delay: 0.1s;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", sans-serif;
        }

        body {
            background-color: #f5f7fa;
            color: #333;
            font-size: 14px;
            line-height: 1.5;
            overflow-x: hidden;
        }

        /* 加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 107, 149, 0.3);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 布局样式 */
        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--white-color);
            padding: 20px 0;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 2px 0 10px var(--shadow-color);
            transition: all var(--transition-speed);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 0 20px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
            margin-bottom: 20px;
        }

        .sidebar-logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 10px;
            object-fit: cover;
            background-color: rgba(255, 255, 255, 0.2);
        }

        .sidebar-title {
            color: var(--white-color);
            font-size: 1.5rem;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .sidebar-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .sidebar.collapsed .sidebar-title,
        .sidebar.collapsed .sidebar-subtitle {
            display: none;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu li a {
            color: var(--white-color);
            text-decoration: none;
            display: block;
            padding: 12px 20px;
            transition: all var(--transition-speed);
            border-left: 4px solid transparent;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .sidebar-menu li a:hover,
        .sidebar-menu li a.active {
            background-color: rgba(255, 255, 255, 0.2);
            border-left-color: var(--white-color);
        }

        .sidebar-menu li a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }

        .sidebar.collapsed .sidebar-menu li a span {
            display: none;
        }

        .sidebar.collapsed .sidebar-menu li a i {
            margin-right: 0;
            font-size: 1.3rem;
        }

        /* 子菜单样式 */
        .menu-section {
            position: relative;
        }

        .menu-section .menu-header {
            position: relative;
        }

        .menu-section .toggle-icon {
            position: absolute;
            right: 20px;
            transition: transform 0.3s ease;
        }

        .menu-section.active .toggle-icon {
            transform: rotate(180deg);
        }

        .submenu {
            list-style: none;
            padding: 0;
            margin: 0;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background-color: rgba(0, 0, 0, 0.1);
        }

        .menu-section.active .submenu {
            max-height: 500px;
        }

        .submenu li a {
            padding: 10px 20px 10px 50px;
            font-size: 0.9rem;
            border-left: 2px solid transparent;
        }

        .submenu li a:hover {
            background-color: rgba(255, 255, 255, 0.15);
            border-left-color: rgba(255, 255, 255, 0.5);
        }

        .sidebar.collapsed .submenu {
            display: none;
        }

        /* 主内容区域样式 */
        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 20px;
            transition: all var(--transition-speed);
        }

        .main-content.expanded {
            margin-left: 70px;
        }

        /* 头部样式 */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        /* 天气信息和人生哲理组件样式 */
        .header-info-content {
            display: flex;
            align-items: center;
            margin: 0 auto;
            padding: 0 15px;
        }

        .wisdom-quote, .weather-info {
            display: flex;
            align-items: center;
            margin-right: 15px;
            font-size: 0.9rem;
            color: var(--gray-color);
        }

        .wisdom-quote i, .weather-info i {
            margin-right: 5px;
            font-size: 1.1rem;
            color: var(--primary-color);
        }

        .info-actions {
            display: flex;
            gap: 5px;
        }

        .info-actions button {
            background: none;
            border: none;
            color: var(--gray-color);
            cursor: pointer;
            font-size: 0.9rem;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            transition: all 0.2s;
        }

        .info-actions button:hover {
            color: var(--primary-color);
            background-color: rgba(255, 107, 149, 0.1);
        }

        .menu-toggle {
            background: none;
            border: none;
            color: var(--dark-color);
            font-size: 1.5rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            transition: all var(--transition-speed);
        }

        .menu-toggle:hover {
            background-color: var(--light-color);
        }

        .user-info {
            display: flex;
            align-items: center;
            position: relative;
            cursor: pointer;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: var(--white-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }

        .user-name {
            font-weight: 500;
        }

        .dropdown-content {
            position: absolute;
            top: 100%;
            right: 0;
            background-color: var(--white-color);
            min-width: 160px;
            box-shadow: 0 8px 16px 0 var(--shadow-color);
            border-radius: var(--border-radius);
            padding: 10px 0;
            z-index: 1;
            display: none;
            animation: fadeIn 0.3s;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .user-info:hover .dropdown-content {
            display: block;
        }

        .dropdown-content a {
            color: var(--dark-color);
            padding: 10px 20px;
            text-decoration: none;
            display: block;
            transition: all var(--transition-speed);
        }

        .dropdown-content a:hover {
            background-color: var(--light-color);
        }

        .dropdown-content a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        /* 仪表盘特定样式 */
        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr); /* 固定3列布局 */
            gap: 20px;
            margin-bottom: 30px;
            width: 100%; /* 确保占满整个容器宽度 */
            transition: none; /* 移除过渡效果，避免抖动 */
        }

        /* 小屏幕单列布局 */
        @media (max-width: 767.98px) {
            .dashboard-stats {
                grid-template-columns: 1fr;
            }
        }

        .stat-card {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: 0 5px 15px var(--shadow-color);
            padding: 20px;
            display: flex;
            align-items: flex-start;
            gap: 15px;
            transition: all var(--transition-speed);
            animation: fadeInUp 0.5s;
            border-top: 3px solid var(--primary-color);
        }

        .stat-card:hover {
            box-shadow: 0 8px 25px var(--shadow-color);
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--white-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            box-shadow: 0 5px 15px rgba(255, 107, 149, 0.3);
        }

        .stat-content {
            flex: 1;
        }

        .stat-content h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 15px;
            position: relative;
            padding-bottom: 10px;
            text-align: center;
        }

        .stat-content h3::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 3px;
            background-color: var(--primary-color);
            border-radius: 3px;
        }

        .stat-details {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            gap: 5px;
            text-align: center;
        }

        .stat-label {
            color: var(--gray-color);
            font-weight: 500;
            text-align: right;
            width: 45%;
        }

        .stat-value {
            color: var(--dark-color);
            font-weight: 600;
            text-align: left;
            width: 45%;
        }

        .status-active {
            color: var(--success-color);
        }

        .version-history {
            margin-left: 10px;
            font-size: 0.8rem;
            color: var(--primary-color);
            text-decoration: none;
        }

        .version-history:hover {
            text-decoration: underline;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
            margin-left: 10px;
        }

        .btn-xs {
            padding: 0.1rem 0.3rem;
            font-size: 0.75rem;
            line-height: 1.2;
            border-radius: 0.2rem;
        }

        .dashboard-sections {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 20px;
        }

        .section-row {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .section {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: 0 5px 15px var(--shadow-color);
            overflow: hidden;
            animation: fadeInUp 0.5s;
            height: 100%;
        }

        .section-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }

        .section-header h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark-color);
            display: flex;
            align-items: center;
        }

        .section-header h3 i {
            margin-right: 8px;
            color: var(--primary-color);
        }

        .view-all {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all var(--transition-speed);
        }

        .view-all:hover {
            color: #ff4f7e;
            text-decoration: underline;
        }

        .section-content {
            padding: 20px;
        }

        .announcement-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .announcement-item {
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
            transition: all var(--transition-speed);
        }

        .announcement-item:hover {
            transform: translateX(5px);
        }

        .announcement-item:last-child {
            padding-bottom: 0;
            border-bottom: none;
        }

        .announcement-title {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 5px;
        }

        .announcement-time {
            font-size: 0.8rem;
            color: var(--gray-color);
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .announcement-time::before {
            content: "\\F282";
            font-family: "bootstrap-icons";
            margin-right: 5px;
            font-size: 0.9rem;
        }

        .shortcut-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 15px;
        }

        .shortcut-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: var(--dark-color);
            padding: 20px 15px;
            border-radius: var(--border-radius);
            transition: all var(--transition-speed);
            background-color: #f8f9fa;
            box-shadow: 0 2px 5px var(--shadow-color);
        }

        .shortcut-item:hover {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--white-color);
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(255, 107, 149, 0.3);
        }

        .shortcut-item i {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .shortcut-item span {
            font-weight: 500;
        }

        /* 支付方式样式 */
        .payment-methods {
            display: flex;
            gap: 15px;
            margin-top: 10px;
        }

        .payment-option {
            flex: 1;
            border: 1px solid #eee;
            border-radius: var(--border-radius);
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-speed);
            position: relative;
        }

        .payment-option:hover {
            border-color: var(--primary-color);
            background-color: rgba(255, 107, 149, 0.05);
        }

        .payment-option input {
            position: absolute;
            top: 10px;
            left: 10px;
            margin: 0;
        }

        .payment-option input:checked + .payment-icon {
            color: var(--primary-color);
        }

        .payment-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: var(--gray-color);
        }

        .payment-name {
            font-weight: 500;
        }

        /* 动画 */
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 信息卡片样式 */
        .info-card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: 0 4px 10px var(--shadow-color);
            transition: all var(--transition-speed);
            overflow: hidden;
        }

        .info-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px var(--shadow-color);
        }

        .card-body {
            display: flex;
            align-items: center;
            padding: 20px;
        }

        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: var(--white-color);
            font-size: 1.5rem;
        }

        .bg-primary {
            background-color: var(--primary-color);
        }

        .bg-success {
            background-color: var(--success-color);
        }

        .bg-warning {
            background-color: var(--warning-color);
        }

        .bg-info {
            background-color: var(--info-color);
        }

        .card-info {
            flex: 1;
        }

        .card-title {
            font-size: 0.9rem;
            color: var(--gray-color);
            margin-bottom: 5px;
        }

        .card-value {
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
        }

        /* 公告样式 */
        .announcement-item {
            padding: 15px 0;
            border-bottom: 1px solid var(--light-color);
        }

        .announcement-item:last-child {
            border-bottom: none;
        }

        .announcement-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .announcement-title {
            font-weight: 600;
            margin: 0;
        }

        .announcement-date {
            font-size: 0.8rem;
            color: var(--gray-color);
        }

        .announcement-content {
            color: var(--dark-color);
            line-height: 1.6;
        }

        /* 动画效果 */
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.5s;
        }

        .animate-on-scroll.animated {
            opacity: 1;
            transform: translateY(0);
        }

        /* 卡片样式 */
        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: 0 4px 10px var(--shadow-color);
            margin-bottom: 20px;
            overflow: hidden;
        }

        .card-header {
            background-color: var(--white-color);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 15px 20px;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1001;
            overflow-y: auto;
            padding: 20px;
        }

        .modal-content {
            background-color: var(--white-color);
            margin: 5% auto;
            width: 600px;
            max-width: 90%;
            border-radius: var(--border-radius);
            box-shadow: 0 10px 30px var(--shadow-color);
            animation: slideDown 0.3s;
            overflow: hidden;
        }

        @keyframes slideDown {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .modal-header {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark-color);
        }

        .close-btn {
            background: none;
            border: none;
            color: var(--gray-color);
            font-size: 1.2rem;
            cursor: pointer;
            transition: all var(--transition-speed);
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .close-btn:hover {
            color: var(--danger-color);
            background-color: rgba(220, 53, 69, 0.1);
        }

        .modal-body {
            padding: 20px;
            max-height: 70vh;
            overflow-y: auto;
        }

        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #eee;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            background-color: #f8f9fa;
        }

        .version-item {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--light-color);
        }

        .version-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .version-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .version-number {
            font-weight: 600;
        }

        .version-date {
            font-size: 0.8rem;
            color: var(--gray-color);
        }

        .version-changes {
            list-style-type: disc;
            padding-left: 20px;
            margin: 0;
        }

        .version-changes li {
            margin-bottom: 5px;
        }

        /* 消息提示样式 */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 300px;
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: 0 5px 15px var(--shadow-color);
            overflow: hidden;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transform: translateX(30px);
            transition: all 0.3s;
        }

        .toast.show {
            opacity: 1;
            visibility: visible;
            transform: translateX(0);
        }

        .toast-content {
            display: flex;
            align-items: center;
            padding: 15px;
        }

        .toast-icon {
            font-size: 1.5rem;
            margin-right: 15px;
            display: none;
        }

        .toast-icon.success {
            color: var(--success-color);
        }

        .toast-icon.error {
            color: var(--danger-color);
        }

        .toast-icon.info {
            color: var(--info-color);
        }

        .toast-message {
            flex: 1;
            font-weight: 500;
        }

        .toast-progress {
            height: 3px;
            background-color: var(--primary-color);
            width: 100%;
            animation: progress 2s linear;
            transform-origin: left;
        }

        @keyframes progress {
            0% { width: 100%; }
            80% { width: 20%; } /* 80%时还剩20%宽度，此时触发刷新 */
            100% { width: 0%; }
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .sidebar {
                width: 0;
                padding: 0;
            }

            .sidebar.collapsed {
                width: 70px;
            }

            .main-content {
                margin-left: 0;
            }

            .main-content.expanded {
                margin-left: 70px;
            }

            .dashboard-welcome {
                flex-direction: column;
            }

            .welcome-illustration {
                display: none;
            }
        }

        .stat-value-with-btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.stat-value {
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
}

/* 修复天气和人生哲理组件样式 */
.header-info-content {
    display: flex;
    align-items: center;
    margin: 0 auto;
    flex: 1;
    padding: 0 20px;
    color: var(--text-color, #333);
    font-size: 14px;
    max-width: 1200px; /* 进一步增加最大宽度 */
    justify-content: space-between; /* 两端对齐 */
    gap: 15px; /* 减少间距，让天气组件更靠近哲理句子 */
    min-height: 40px; /* 确保最小高度 */
}

.wisdom-quote {
    display: flex;
    align-items: center;
    animation: fadeIn 0.5s;
    flex: 1; /* 占用剩余空间 */
    min-width: 0; /* 允许收缩 */
    max-width: 70%; /* 限制最大宽度，为天气信息留空间 */
}

.wisdom-quote span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    max-width: 100%;
}

.weather-info {
    display: flex;
    align-items: center;
    white-space: nowrap;
    flex-shrink: 0; /* 不收缩 */
    min-width: 150px; /* 确保天气信息有足够空间 */
}

/* 响应式处理 */
@media (max-width: 768px) {
    .header-info-content {
        max-width: 100%;
        gap: 15px;
        padding: 0 10px;
    }

    .wisdom-quote {
        max-width: 60%;
    }

    .wisdom-quote span {
        font-size: 13px;
    }

    .weather-info {
        min-width: 120px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .wisdom-quote {
        display: none; /* 在很小的屏幕上隐藏哲理句子 */
    }

    .header-info-content {
        justify-content: center;
    }
}

.wisdom-quote i, .weather-info i {
    margin-right: 8px;
    font-size: 16px;
    color: var(--primary-color);
    flex-shrink: 0;
}

.info-actions {
    display: flex;
    gap: 10px;
    margin-left: 10px;
}

.info-actions button {
    background: rgba(var(--primary-color-rgb, 255, 107, 149), 0.1);
    border: none;
    color: var(--primary-color);
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    flex-shrink: 0;
}

.info-actions button:hover {
    background: rgba(var(--primary-color-rgb, 255, 107, 149), 0.2);
    transform: rotate(15deg);
}

.info-actions button:active {
    background: rgba(var(--primary-color-rgb, 255, 107, 149), 0.3);
    transform: scale(0.95);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.rotating {
    animation: rotate 1s linear;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .wisdom-quote {
        display: none !important;
    }

    .weather-info {
        margin-right: 0;
        max-width: 150px;
    }

    .header-info-content {
        padding: 0 10px;
    }
}
</style>
</head>
<body>
    <div class="loading-overlay" id="loading-overlay">
        <div class="spinner"></div>
    </div>

    <div class="dashboard-container">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <img src="<?php echo htmlspecialchars($settings['site_logo'] ?? '/assets/images/logo.png'); ?>" alt="Logo" class="sidebar-logo" onerror="this.src='data:image/svg+xml;utf8,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 24 24%22 fill=%22%23ffffff%22%3E%3Cpath d=%22M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z%22/%3E%3C/svg%3E'">
                <h3 class="sidebar-title"><?php echo htmlspecialchars($settings['site_name'] ?? '去水印接口'); ?></h3>
                <p class="sidebar-subtitle">管理系统</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="/dashboard/index.php" class="active"><i class="bi bi-speedometer2"></i> <span>仪表盘</span></a></li>

                <!-- 全局设置 -->
                <?php if ($isAdmin): ?>
                <li class="menu-section">
                    <a href="#" class="menu-header"><i class="bi bi-gear"></i> <span>全局设置</span> <i class="bi bi-chevron-down toggle-icon"></i></a>
                    <ul class="submenu">
                        <li><a href="settings/"><i class="bi bi-globe"></i> <span>网站设置</span></a></li>
                        <li><a href="/dashboard/settings/register"><i class="bi bi-person-plus"></i> <span>注册设置</span></a></li>
                        <li><a href="/dashboard/settings/payment"><i class="bi bi-credit-card"></i> <span>支付设置</span></a></li>
                        <li><a href="/dashboard/settings/login"><i class="bi bi-box-arrow-in-right"></i> <span>登录设置</span></a></li>
                        <li><a href="/dashboard/settings/api"><i class="bi bi-code-slash"></i> <span>接口设置</span></a></li>
                        <li><a href="/dashboard/settings/homepage"><i class="bi bi-house"></i> <span>网站首页</span></a></li>
                    </ul>
                </li>
                <?php endif; ?>

                <!-- 用户管理 -->
                <li class="menu-section">
                    <a href="#" class="menu-header"><i class="bi bi-people"></i> <span>用户管理</span> <i class="bi bi-chevron-down toggle-icon"></i></a>
                    <ul class="submenu">
                        <li><a href="/dashboard/accounts/users.php"><i class="bi bi-list"></i> <span>用户列表</span></a></li>
                        <li><a href="/dashboard/accounts/increase"><i class="bi bi-plus-circle"></i> <span>账户加款</span></a></li>
                        <li><a href="/dashboard/accounts/package"><i class="bi bi-gift"></i> <span>添加套餐</span></a></li>
                    </ul>
                </li>

                <!-- 订单管理 -->
                <li class="menu-section">
                    <a href="#" class="menu-header"><i class="bi bi-cart3"></i> <span>订单管理</span> <i class="bi bi-chevron-down toggle-icon"></i></a>
                    <ul class="submenu">
                        <li><a href="/dashboard/orders"><i class="bi bi-receipt"></i> <span>订单列表</span></a></li>
                        <li><a href="/dashboard/orders/calls"><i class="bi bi-telephone"></i> <span>调用记录</span></a></li>
                        <li><a href="/dashboard/orders/ranking"><i class="bi bi-trophy"></i> <span>用户排行</span></a></li>
                        <li><a href="/dashboard/orders/errors"><i class="bi bi-exclamation-triangle"></i> <span>错误排行</span></a></li>
                    </ul>
                </li>

                <!-- 套餐管理 -->
                <li class="menu-section">
                    <a href="#" class="menu-header"><i class="bi bi-box"></i> <span>套餐管理</span> <i class="bi bi-chevron-down toggle-icon"></i></a>
                    <ul class="submenu">
                        <li><a href="/dashboard/platforms/add"><i class="bi bi-plus"></i> <span>添加套餐</span></a></li>
                        <li><a href="/dashboard/platforms"><i class="bi bi-list"></i> <span>套餐列表</span></a></li>
                    </ul>
                </li>

                <!-- 到期提醒 -->
                <li class="menu-section">
                    <a href="#" class="menu-header"><i class="bi bi-bell"></i> <span>到期提醒</span> <i class="bi bi-chevron-down toggle-icon"></i></a>
                    <ul class="submenu">
                        <li><a href="/dashboard/recycle"><i class="bi bi-gear"></i> <span>到期提醒配置</span></a></li>
                        <li><a href="/dashboard/recycle/logs"><i class="bi bi-journal-text"></i> <span>到期提醒记录</span></a></li>
                    </ul>
                </li>

                <!-- 系统管理 -->
                <?php if ($isAdmin): ?>
                <li class="menu-section">
                    <a href="#" class="menu-header"><i class="bi bi-tools"></i> <span>系统管理</span> <i class="bi bi-chevron-down toggle-icon"></i></a>
                    <ul class="submenu">
                        <li><a href="/dashboard/plugins"><i class="bi bi-info-circle"></i> <span>功能介绍</span></a></li>
                        <li><a href="/dashboard/plugins/update"><i class="bi bi-arrow-up-circle"></i> <span>系统更新</span></a></li>
                    </ul>
                </li>
                <?php endif; ?>

                <li><a href="/logout.php"><i class="bi bi-box-arrow-right"></i> <span>退出登录</span></a></li>
            </ul>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content" id="main-content">
            <div class="header">
                <button class="menu-toggle" id="menu-toggle"><i class="bi bi-list"></i></button>
                <div class="header-info-content" id="header-info-content">
                    <div class="wisdom-quote">
                        <i class="bi bi-quote"></i>
                        <span id="wisdom-text">加载中...</span>
                    </div>
                    <div class="weather-info">
                        <i class="bi bi-cloud-sun"></i>
                        <span id="weather-text">加载中...</span>
                    </div>
                    <div class="info-actions">
                        <button id="refresh-info" title="刷新"><i class="bi bi-arrow-clockwise"></i></button>
                        <button id="toggle-info-type" title="切换显示内容"><i class="bi bi-shuffle"></i></button>
                        <button id="settings-info" title="设置" style="cursor: pointer; pointer-events: auto;"><i class="bi bi-gear"></i></button>
                    </div>

                    <!-- 天气设置模态框 -->
                    <div class="modal fade" id="weatherSettingsModal" tabindex="-1" aria-labelledby="weatherSettingsModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="weatherSettingsModalLabel">
                                        <i class="bi bi-cloud-sun me-2"></i>天气设置
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <form id="weatherSettingsForm">
                                        <div class="mb-3">
                                            <label for="weatherApiProvider" class="form-label">天气API服务商</label>
                                            <select class="form-select" id="weatherApiProvider" name="api_provider" onchange="updateApiProviderInfo()">
                                                <option value="openweather">OpenWeatherMap</option>
                                            </select>
                                            <div id="apiProviderInfo" class="mt-2">
                                                <small class="text-muted">
                                                    <i class="bi bi-info-circle"></i>
                                                    <span id="providerDescription">选择API服务商后将显示官网链接</span>
                                                    <a id="providerLink" href="#" target="_blank" class="ms-2" style="display: none;">
                                                        <i class="bi bi-box-arrow-up-right"></i> 前往官网
                                                    </a>
                                                </small>
                                            </div>
                                        </div>
                                        <div class="mb-3" id="apiKeySection">
                                            <label for="weatherApiKey" class="form-label" id="apiKeyLabel">API密钥</label>
                                            <input type="text" class="form-control" id="weatherApiKey" name="api_key" placeholder="请输入API密钥">
                                            <div class="form-text" id="apiKeyHelp">
                                                请到对应服务商官网申请免费API密钥
                                                <a href="../api/api-guide.html" target="_blank" style="margin-left: 10px;">📖 获取指南</a>
                                            </div>
                                        </div>


                                        <div class="mb-3">
                                            <label for="defaultCity" class="form-label">默认城市</label>
                                            <div class="position-relative">
                                                <input type="text" class="form-control" id="defaultCity" name="default_city"
                                                       placeholder="请输入城市名称，如：北京、上海、广州"
                                                       autocomplete="off"
                                                       oninput="showCitySuggestions(this.value)">
                                                <div id="citySuggestions" class="position-absolute w-100 bg-white border rounded shadow-sm"
                                                     style="top: 100%; z-index: 1000; display: none; max-height: 200px; overflow-y: auto;">
                                                </div>
                                            </div>
                                            <div class="form-text">
                                                <i class="bi bi-geo-alt"></i>
                                                此设置将影响顶部导航栏显示的天气信息，支持中文城市名称。
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="weatherEnabled" name="enabled" checked>
                                                <label class="form-check-label" for="weatherEnabled">
                                                    启用天气功能
                                                </label>
                                            </div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="cacheTime" class="form-label">缓存时间（分钟）</label>
                                            <input type="number" class="form-control" id="cacheTime" name="cache_duration" value="30" min="5" max="1440">
                                            <div class="form-text">设置天气数据缓存时间，减少API调用次数</div>
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" onclick="closeWeatherModal()">取消</button>
                                    <button type="button" class="btn btn-primary" onclick="saveWeatherSettings()">保存设置</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="user-info dropdown">
                    <div class="user-avatar"><?php echo htmlspecialchars($userInitial); ?></div>
                    <span class="user-name"><?php echo htmlspecialchars($username); ?></span>
                    <div class="dropdown-content">
                        <a href="/profile/index.php"><i class="bi bi-person"></i> 个人资料</a>
                        <a href="/dashboard/settings"><i class="bi bi-gear"></i> 设置</a>
                        <a href="/logout.php"><i class="bi bi-box-arrow-right"></i> 退出登录</a>
                    </div>
                </div>
            </div>

            <!-- 面包屑导航 -->
            <?php
            // 设置当前页面标题
            $page_title = '仪表盘';
            // 设置当前页面路径
            $page_path = [];
            // 包含面包屑导航组件
            include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/admin-breadcrumb.php');
            ?>

            <!-- 账户概述 -->
            <div class="dashboard-stats">
                <div class="stat-card floating-card hover-card">
                    <div class="stat-icon"><i class="bi bi-graph-up rotate-icon"></i></div>
                    <div class="stat-content">
                        <h3>数据统计</h3>
                        <div class="stat-details">
                            <div class="stat-grid">
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-graph-up"></i> 今日调用</span>
                                    <span class="stat-value"><?php echo number_format($stats['total_today']); ?></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-check-circle"></i> 成功调用</span>
                                    <span class="stat-value"><?php echo number_format($stats['success_today']); ?></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-percent"></i> 成功率</span>
                                    <span class="stat-value"><?php echo $stats['success_rate']; ?>%</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-people"></i> 总用户数</span>
                                    <span class="stat-value"><?php echo number_format($stats['total_users']); ?></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-cart"></i> 总订单数</span>
                                    <span class="stat-value"><?php echo number_format($stats['total_orders']); ?></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-currency-yen"></i> 总收入</span>
                                    <span class="stat-value">¥<?php echo number_format($stats['total_revenue'], 2); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="stat-card floating-card hover-card">
                    <div class="stat-icon"><i class="bi bi-shield-check rotate-icon"></i></div>
                    <div class="stat-content">
                        <h3>系统概况</h3>
                        <div class="stat-details">
                            <div class="stat-grid">
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-shield-check"></i> 授权状态</span>
                                    <span class="stat-value status-active">已授权</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-award"></i> 授权类型</span>
                                    <span class="stat-value"><?php echo $isAdmin ? '企业版' : '标准版'; ?></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-person-badge"></i> 账户权限</span>
                                    <span class="stat-value"><?php echo $isAdmin ? '管理员' : '租户'; ?></span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-server"></i> 部署方式</span>
                                    <span class="stat-value">独立部署</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-clock-history"></i> 系统运行时间</span>
                                    <span class="stat-value">128天</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label"><i class="bi bi-hdd-stack"></i> 运行环境</span>
                                    <div class="stat-value-multiline">
                                        <span class="stat-value">PHP 7.4</span>
                                        <span class="stat-value">MySQL 5.7</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="stat-card floating-card hover-card">
    <div class="stat-icon"><i class="bi bi-calendar-check rotate-icon"></i></div>
    <div class="stat-content">
        <h3>服务信息</h3>
        <div class="stat-details">
            <div class="stat-grid">
                <div class="stat-item">
                    <span class="stat-label"><i class="bi bi-calendar-check"></i> 授权时长</span>
                    <span class="stat-value"><?php echo $isAdmin ? '永久' : '2024-12-31'; ?></span>
                    <button class="btn btn-sm btn-outline-primary btn-xs neon-btn" style="margin-top: 8px;" onclick="showUpdateVersion()">更新版本</button>
                </div>
                <div class="stat-item">
                    <span class="stat-label"><i class="bi bi-code-square"></i> 当前版本</span>
                    <span class="stat-value">v1.0.2</span>
                    <a href="javascript:void(0)" class="btn btn-sm btn-outline-primary btn-xs" style="margin-top: 8px;" onclick="showVersionHistory()">历史版本</a>
                </div>
                <div class="stat-item">
                    <span class="stat-label"><i class="bi bi-hourglass-split"></i> 服务期限</span>
                    <span class="stat-value">2024-12-31</span>
                    <button class="btn btn-sm btn-outline-primary btn-xs neon-btn" style="margin-top: 8px;" onclick="openRenewModal()">续费</button>
                </div>
                <div class="stat-item">
                    <span class="stat-label"><i class="bi bi-calendar-date"></i> 系统时间</span>
                    <span class="stat-value" id="current-date">23年5月15日</span>
                    <span class="stat-value" id="current-time" style="margin-top: 5px; color: var(--primary-color); font-family: 'Courier New', monospace; font-weight: bold;">12:00:00</span>
                </div>
            </div>
        </div>
    </div>
</div>

                <!-- 天气设置模态框CSS修复 -->
                <style>
                    /* 禁用Bootstrap默认的backdrop */
                    .modal-backdrop {
                        display: none !important;
                    }

                    .modal {
                        z-index: 1050 !important;
                    }

                    .modal-dialog {
                        z-index: 1060 !important;
                    }

                    /* 修复模态框内容显示 */
                    #weatherSettingsModal .modal-content {
                        background-color: #fff !important;
                        border: 1px solid #dee2e6 !important;
                        border-radius: 0.375rem !important;
                    }

                    #weatherSettingsModal .modal-header {
                        background-color: #f8f9fa !important;
                        border-bottom: 1px solid #dee2e6 !important;
                    }

                    #weatherSettingsModal .modal-body {
                        padding: 1rem !important;
                    }

                    #weatherSettingsModal .modal-footer {
                        background-color: #f8f9fa !important;
                        border-top: 1px solid #dee2e6 !important;
                    }

                    /* 自定义模态框背景遮罩 - 与Bootstrap默认样式一致 */
                    .custom-modal-backdrop {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background-color: rgba(0, 0, 0, 0.3);
                        z-index: 1040;
                        display: none;
                        opacity: 0;
                        transition: opacity 0.15s linear;
                    }

                    .custom-modal-backdrop.show {
                        display: block;
                        opacity: 1;
                    }

                    /* 确保图表容器正确显示 */
                    .section-content {
                        min-height: 200px;
                        position: relative;
                    }

                    .section-content canvas {
                        max-width: 100%;
                        height: auto !important;
                        display: block;
                    }
                </style>

            </div>

            <!-- 服务状态和公告 -->
            <div class="dashboard-sections">
                <div class="section-row">
                    <div class="section">
                        <div class="section-header">
                            <h3><i class="bi bi-megaphone"></i> 最新公告</h3>
                            <a href="javascript:void(0)" class="view-all" onclick="showAllAnnouncements()">查看全部</a>
                        </div>
                        <div class="section-content">
                            <div class="announcement-list">
                                <div class="announcement-item scroll-anim">
                                    <div class="announcement-title gradient-text">系统更新通知</div>
                                    <div class="announcement-time">2023-05-10</div>
                                    <div class="announcement-content">
                                        系统将于2023年5月15日进行版本更新，更新内容包括界面优化、功能增强和安全性提升。更新期间系统将暂停服务约30分钟，请提前做好准备。
                                    </div>
                                </div>
                                <div class="announcement-item scroll-anim scroll-anim-delay-1">
                                    <div class="announcement-title gradient-text">新功能上线</div>
                                    <div class="announcement-time">2023-05-05</div>
                                    <div class="announcement-content">
                                        我们新增了批量处理功能，现在您可以一次性处理多个订单，提高工作效率。详情请查看帮助文档。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-header">
                            <h3><i class="bi bi-lightning-charge"></i> 快捷入口</h3>
                        </div>
                        <div class="section-content">
                            <div class="shortcut-grid">
                                <a href="#" onclick="alert('用户管理功能开发中...')" class="shortcut-item bounce-btn">
                                    <i class="bi bi-people"></i>
                                    <span>用户管理</span>
                                </a>
                                <a href="#" onclick="alert('订单管理功能开发中...')" class="shortcut-item bounce-btn pulse">
                                    <i class="bi bi-cart3"></i>
                                    <span>订单管理</span>
                                </a>
                                <a href="#" onclick="alert('套餐管理功能开发中...')" class="shortcut-item bounce-btn">
                                    <i class="bi bi-gift"></i>
                                    <span>套餐管理</span>
                                </a>
                                <a href="#" onclick="alert('全局设置功能开发中...')" class="shortcut-item bounce-btn">
                                    <i class="bi bi-globe"></i>
                                    <span>全局设置</span>
                                </a>
                                <a href="#" onclick="alert('提醒配置功能开发中...')" class="shortcut-item bounce-btn">
                                    <i class="bi bi-bell"></i>
                                    <span>提醒配置</span>
                                </a>
                                <a href="#" onclick="alert('系统更新功能开发中...')" class="shortcut-item bounce-btn">
                                    <i class="bi bi-arrow-up-circle"></i>
                                    <span>系统更新</span>
                                </a>
                                <a href="/login" class="shortcut-item bounce-btn">
                                    <i class="bi bi-box-arrow-right"></i>
                                    <span>退出登录</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section-row">
                    <div class="section">
                        <div class="section-header">
                            <h3><i class="bi bi-graph-up"></i> 调用统计</h3>
                        </div>
                        <div class="section-content">
                            <canvas id="callChart" width="400" height="200"></canvas>
                        </div>
                    </div>

                    <div class="section">
                        <div class="section-header">
                            <h3><i class="bi bi-people"></i> 用户统计</h3>
                        </div>
                        <div class="section-content">
                            <canvas id="userChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <div class="section-row">
                    <div class="section">
                        <div class="section-header">
                            <h3><i class="bi bi-cart"></i> 订单统计</h3>
                        </div>
                        <div class="section-content">
                            <canvas id="orderChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 版本历史模态框 -->
    <div class="modal" id="version-history-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>版本历史</h3>
                <button class="close-btn" onclick="closeModal('version-history-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <div class="version-list">
                    <div class="version-item">
                        <div class="version-header">
                            <span class="version-number">v1.0.2</span>
                            <span class="version-date">2023-05-10</span>
                        </div>
                        <div class="version-content">
                            <ul>
                                <li>优化了用户界面，提升用户体验</li>
                                <li>修复了订单搜索功能的已知问题</li>
                                <li>增强了系统安全性</li>
                                <li>改进了小程序平台的管理功能</li>
                            </ul>
                        </div>
                    </div>
                    <div class="version-item">
                        <div class="version-header">
                            <span class="version-number">v1.0.1</span>
                            <span class="version-date">2023-04-15</span>
                        </div>
                        <div class="version-content">
                            <ul>
                                <li>新增了批量处理功能</li>
                                <li>修复了数据统计的显示问题</li>
                                <li>优化了系统性能</li>
                            </ul>
                        </div>
                    </div>
                    <div class="version-item">
                        <div class="version-header">
                            <span class="version-number">v1.0.0</span>
                            <span class="version-date">2023-03-01</span>
                        </div>
                        <div class="version-content">
                            <ul>
                                <li>系统正式发布</li>
                                <li>支持多租户管理</li>
                                <li>支持小程序平台管理</li>
                                <li>支持订单管理和搜索</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('version-history-modal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 续费服务模态框 -->
    <div class="modal" id="renew-service-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>续费服务</h3>
                <button class="close-btn" onclick="closeModal('renew-service-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <form id="renew-service-form">
                    <div class="form-group">
                        <label for="service-plan">服务套餐</label>
                        <select id="service-plan" class="form-control" required>
                            <option value="">选择套餐</option>
                            <option value="1">基础版 - 1年 - ¥999</option>
                            <option value="2">标准版 - 1年 - ¥1999</option>
                            <option value="3">高级版 - 1年 - ¥2999</option>
                            <option value="4">基础版 - 3年 - ¥2499</option>
                            <option value="5">标准版 - 3年 - ¥4999</option>
                            <option value="6">高级版 - 3年 - ¥7499</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>支付方式</label>
                        <div class="payment-methods">
                            <label class="payment-option">
                                <input type="radio" name="payment-method" value="wechat" checked>
                                <div class="payment-icon"><i class="bi bi-wechat"></i></div>
                                <div class="payment-name">微信支付</div>
                            </label>
                            <label class="payment-option">
                                <input type="radio" name="payment-method" value="alipay">
                                <div class="payment-icon"><i class="bi bi-credit-card"></i></div>
                                <div class="payment-name">支付宝</div>
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('renew-service-modal')">取消</button>
                <button class="btn btn-primary" onclick="confirmRenew()">确认支付</button>
            </div>
        </div>
    </div>

    <!-- 全部公告模态框 -->
    <div class="modal" id="all-announcements-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>全部公告</h3>
                <button class="close-btn" onclick="closeModal('all-announcements-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <div class="announcement-list">
                    <div class="announcement-item">
                        <div class="announcement-title gradient-text">系统更新通知</div>
                        <div class="announcement-time">2023-05-10</div>
                        <div class="announcement-content">
                            系统将于2023年5月15日进行版本更新，更新内容包括界面优化、功能增强和安全性提升。更新期间系统将暂停服务约30分钟，请提前做好准备。
                        </div>
                    </div>
                    <div class="announcement-item">
                        <div class="announcement-title gradient-text">新功能上线</div>
                        <div class="announcement-time">2023-05-05</div>
                        <div class="announcement-content">
                            我们新增了批量处理功能，现在您可以一次性处理多个订单，提高工作效率。详情请查看帮助文档。
                        </div>
                    </div>
                    <div class="announcement-item">
                        <div class="announcement-title gradient-text">系统维护通知</div>
                        <div class="announcement-time">2023-04-20</div>
                        <div class="announcement-content">
                            系统将于2023年4月25日凌晨2:00-4:00进行例行维护，期间系统将暂停服务。给您带来的不便，敬请谅解。
                        </div>
                    </div>
                    <div class="announcement-item">
                        <div class="announcement-title gradient-text">安全更新</div>
                        <div class="announcement-time">2023-04-10</div>
                        <div class="announcement-content">
                            我们发布了一项重要的安全更新，建议所有用户及时更新系统，以确保您的数据安全。
                        </div>
                    </div>
                    <div class="announcement-item">
                        <div class="announcement-title gradient-text">价格调整通知</div>
                        <div class="announcement-time">2023-03-15</div>
                        <div class="announcement-content">
                            由于运营成本上升，我们将从2023年4月1日起对部分服务价格进行调整。现有用户在当前合同期内不受影响。
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('all-announcements-modal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 更新版本模态框 -->
    <div class="modal" id="update-version-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>更新版本</h3>
                <button class="close-btn" onclick="closeModal('update-version-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <div class="update-info">
                    <div class="update-header">
                        <h4>发现新版本：v1.0.3</h4>
                        <span class="update-date">发布日期：2023-06-20</span>
                    </div>
                    <div class="update-content">
                        <h5>更新内容：</h5>
                        <ul>
                            <li>优化了用户界面，提升用户体验</li>
                            <li>新增了数据分析功能，支持更多维度的数据统计</li>
                            <li>修复了已知的安全漏洞</li>
                            <li>提高了系统性能，减少了页面加载时间</li>
                            <li>改进了移动端适配，提供更好的移动端体验</li>
                        </ul>
                        <div class="update-note">
                            <p><strong>注意：</strong>更新过程中系统将暂停服务约5分钟，请在业务低峰期进行更新。</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('update-version-modal')">取消</button>
                <button class="btn btn-primary" onclick="confirmUpdate()">立即更新</button>
            </div>
        </div>
    </div>

    <!-- 引入Bootstrap和其他JS库 -->
    <script src="/assets/vendor/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="/assets/vendor/chart/chart.min.js"></script>
    <script src="/assets/js/common.js"></script>
    <script src="/assets/js/theme-switcher.js"></script>
    <script>
        // DOM元素
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');
        const menuToggle = document.getElementById('menu-toggle');
        const loadingOverlay = document.getElementById('loading-overlay');
        const currentDateElement = document.getElementById('current-date');
        const versionHistoryModal = document.getElementById('version-history-modal');
        const renewServiceModal = document.getElementById('renew-service-modal');

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 显示加载动画
            showLoading();

            // 检查本地存储中的侧边栏状态
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (sidebarCollapsed) {
                sidebar.classList.add('collapsed');
                mainContent.classList.add('expanded');
            }

            // 设置当前日期
            const now = new Date();
            const options = { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' };
            currentDateElement.textContent = now.toLocaleDateString('zh-CN', options);

            // 初始化图表
            initCharts();

            // 添加卡片动画
            addCardAnimations();

            // 隐藏加载动画
            setTimeout(hideLoading, 500);

            // 显示欢迎提示
            setTimeout(() => {
                showToast(`欢迎回来，${document.querySelector('.user-name').textContent}！`, 'success', {
                    duration: 1800
                });
            }, 800);


        });

        // 侧边栏切换
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        });

        // 子菜单切换功能
        document.querySelectorAll('.menu-header').forEach(header => {
            header.addEventListener('click', function(e) {
                e.preventDefault();
                const menuSection = this.parentElement;
                const isActive = menuSection.classList.contains('active');

                // 关闭所有其他子菜单
                document.querySelectorAll('.menu-section').forEach(section => {
                    section.classList.remove('active');
                });

                // 切换当前子菜单
                if (!isActive) {
                    menuSection.classList.add('active');
                }
            });
        });

        // 显示加载动画
        function showLoading() {
            loadingOverlay.classList.add('show');
        }

        // 隐藏加载动画
        function hideLoading() {
            loadingOverlay.classList.remove('show');
        }

        // 显示版本历史
        function showVersionHistory() {
            openModal('version-history-modal');
        }

        // 打开续费模态框
        function openRenewModal() {
            openModal('renew-service-modal');
        }

        // 显示全部公告
        function showAllAnnouncements() {
            openModal('all-announcements-modal');
        }

        // 打开模态框
        function openModal(modalId) {
            document.getElementById(modalId).style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            if (event.target.classList.contains('modal')) {
                event.target.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        };

        // 添加卡片动画
        function addCardAnimations() {
            // 为统计卡片添加动画
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });

            // 为部分添加动画
            const sections = document.querySelectorAll('.section');
            sections.forEach((section, index) => {
                section.style.animationDelay = `${0.3 + index * 0.1}s`;
            });

            // 为快捷方式添加动画
            const shortcuts = document.querySelectorAll('.shortcut-item');
            shortcuts.forEach((shortcut, index) => {
                shortcut.style.animationDelay = `${0.5 + index * 0.05}s`;
            });
        }

        // 初始化图表
        function initCharts() {
            try {
                // 检查Chart.js是否已加载
                if (typeof Chart === 'undefined') {
                    console.error('Chart.js 未加载');
                    return;
                }

                // 初始化全局图表实例存储
                window.chartInstances = window.chartInstances || {};

                // 调用统计图表
                const callCtx = document.getElementById('callChart').getContext('2d');
                const callChart = new Chart(callCtx, {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                        datasets: [{
                            label: '调用次数',
                            data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 初始化为0，等待API数据
                            backgroundColor: 'rgba(255, 107, 149, 0.2)',
                            borderColor: 'rgba(255, 107, 149, 1)',
                            borderWidth: 2,
                            tension: 0.3
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            title: {
                                display: true,
                                text: '接口调用趋势'
                            }
                        }
                    }
                });

                // 保存图表实例
                window.chartInstances['callChart'] = callChart;

                // 用户统计图表
                const userCtx = document.getElementById('userChart').getContext('2d');
                const userChart = new Chart(userCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['包点用户', '包月用户'],
                        datasets: [{
                            data: [<?php echo $stats['point_users']; ?>, <?php echo $stats['monthly_users']; ?>],
                            backgroundColor: [
                                'rgba(255, 107, 149, 0.7)',
                                'rgba(138, 111, 214, 0.7)'
                            ],
                            borderColor: [
                                'rgba(255, 107, 149, 1)',
                                'rgba(138, 111, 214, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            title: {
                                display: true,
                                text: '用户类型分布'
                            }
                        }
                    }
                });

                // 保存图表实例
                window.chartInstances['userChart'] = userChart;

                // 订单统计图表
                const orderCtx = document.getElementById('orderChart').getContext('2d');
                const orderChart = new Chart(orderCtx, {
                    type: 'bar',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                        datasets: [{
                            label: '统计数据',
                            data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 初始化为0，等待API数据
                            backgroundColor: [
                                'rgba(40, 199, 111, 0.2)',
                                'rgba(255, 107, 149, 0.2)',
                                'rgba(255, 193, 7, 0.2)',
                                'rgba(138, 111, 214, 0.2)',
                                'rgba(23, 162, 184, 0.2)'
                            ],
                            borderColor: [
                                'rgba(40, 199, 111, 1)',
                                'rgba(255, 107, 149, 1)',
                                'rgba(255, 193, 7, 1)',
                                'rgba(138, 111, 214, 1)',
                                'rgba(23, 162, 184, 1)'
                            ],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            title: {
                                display: true,
                                text: '业务数据统计'
                            }
                        }
                    }
                });

                // 保存图表实例
                window.chartInstances['orderChart'] = orderChart;

                console.log('所有图表已初始化:', Object.keys(window.chartInstances));

                // 调试图表容器并强制渲染
                setTimeout(() => {
                    Object.keys(window.chartInstances).forEach(chartId => {
                        const canvas = document.getElementById(chartId);
                        const container = canvas ? canvas.parentElement : null;
                        const chart = window.chartInstances[chartId];

                        console.log(`图表 ${chartId}:`, {
                            canvas: canvas,
                            canvasVisible: canvas ? canvas.offsetParent !== null : false,
                            canvasSize: canvas ? `${canvas.width}x${canvas.height}` : 'N/A',
                            container: container,
                            containerVisible: container ? container.offsetParent !== null : false,
                            containerSize: container ? `${container.offsetWidth}x${container.offsetHeight}` : 'N/A'
                        });

                        // 强制重新渲染图表
                        if (chart && typeof chart.update === 'function') {
                            chart.update('none'); // 无动画更新
                            chart.render(); // 强制渲染
                        }
                    });
                }, 1000);

            } catch (error) {
                console.error('初始化图表失败:', error);
            }
        }

        // 续费服务
        function renewService() {
            const servicePlan = document.getElementById('service-plan').value;
            const paymentMethod = document.querySelector('input[name="payment-method"]:checked').value;

            if (!servicePlan) {
                showToast('请选择服务套餐', 'error');
                return;
            }

            // 显示加载动画
            showLoading();

            // 模拟支付过程
            setTimeout(() => {
                // 隐藏加载动画
                hideLoading();

                // 关闭模态框
                closeModal('renew-service-modal');

                // 显示成功提示并自动刷新页面
                showToast('服务续费成功！', 'success', {
                    duration: 1500,
                    autoRefresh: true
                });
            }, 1500);
        }

        // showToast函数现在由auto-refresh-toast.js统一处理

        // 更新日期和时间
        function updateDateTime() {
            const now = new Date();

            // 更新日期
            const year = now.getFullYear();
            const month = now.getMonth() + 1;
            const day = now.getDate();
            const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
            const weekday = weekdays[now.getDay()];

            const dateElement = document.getElementById('current-date');
            if (dateElement) {
                dateElement.textContent = `${year.toString().slice(-2)}年${month}月${day}日 星期${weekday}`;
            }

            // 更新时间
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');

            const timeElement = document.getElementById('current-time');
            if (timeElement) {
                timeElement.textContent = `${hours}:${minutes}:${seconds}`;
            }

            // 每秒更新一次
            setTimeout(updateDateTime, 1000);
        }

        // 页面加载完成后启动时钟
        document.addEventListener('DOMContentLoaded', function() {
            updateDateTime();
        });

        // 显示历史版本
        function showVersionHistory() {
            openModal('version-history-modal');
        }

        // 打开续费模态框
        function openRenewModal() {
            openModal('renew-service-modal');
        }

        // 确认续费函数已在modal-functions-fix.js中定义

        // 显示所有公告
        function showAllAnnouncements() {
            openModal('all-announcements-modal');
        }

        // 显示更新版本模态框
        function showUpdateVersion() {
            openModal('update-version-modal');
        }

        // 确认更新版本函数已在modal-functions-fix.js中定义

    </script>

    <!-- 历史版本模态框 -->
    <div class="modal" id="version-history-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>历史版本</h3>
                <button class="close-btn" onclick="closeModal('version-history-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <ul class="version-list">
                    <li class="version-item">
                        <div class="version-header">
                            <span class="version-number">v1.0.2</span>
                            <span class="version-date">2023-06-15</span>
                        </div>
                        <ul class="version-changes">
                            <li>修复了用户登录时的验证问题</li>
                            <li>优化了头像匹配算法，提高匹配准确率</li>
                            <li>改进了UI界面，提升用户体验</li>
                            <li>新增了数据统计功能</li>
                        </ul>
                    </li>
                    <li class="version-item">
                        <div class="version-header">
                            <span class="version-number">v1.0.1</span>
                            <span class="version-date">2023-05-20</span>
                        </div>
                        <ul class="version-changes">
                            <li>修复了多个已知bug</li>
                            <li>优化了系统性能</li>
                            <li>改进了移动端适配</li>
                        </ul>
                    </li>
                    <li class="version-item">
                        <div class="version-header">
                            <span class="version-number">v1.0.0</span>
                            <span class="version-date">2023-05-01</span>
                        </div>
                        <ul class="version-changes">
                            <li>首次发布</li>
                            <li>基础功能实现</li>
                            <li>支持头像上传和匹配</li>
                            <li>用户管理系统</li>
                            <li>基础数据统计</li>
                        </ul>
                    </li>
                </ul>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('version-history-modal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 查看全部公告模态框 -->
    <div class="modal" id="all-announcements-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>所有公告</h3>
                <button class="close-btn" onclick="closeModal('all-announcements-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <ul class="announcement-list">
                    <li class="announcement-item">
                        <div class="announcement-header">
                            <span class="announcement-title">系统更新通知</span>
                            <span class="announcement-date">2023-06-15</span>
                        </div>
                        <p class="announcement-content">尊敬的用户，我们将于2023年6月20日凌晨2:00-4:00进行系统升级维护，届时系统将暂停服务。给您带来的不便，敬请谅解。</p>
                    </li>
                    <li class="announcement-item">
                        <div class="announcement-header">
                            <span class="announcement-title">新功能上线通知</span>
                            <span class="announcement-date">2023-06-10</span>
                        </div>
                        <p class="announcement-content">我们很高兴地通知您，新的数据分析功能已经上线，您可以在控制面板中查看更详细的数据统计和分析报告。</p>
                    </li>
                    <li class="announcement-item">
                        <div class="announcement-header">
                            <span class="announcement-title">安全更新提醒</span>
                            <span class="announcement-date">2023-06-05</span>
                        </div>
                        <p class="announcement-content">为了保障您的账户安全，我们建议您定期修改密码，并开启两步验证功能。如有任何安全问题，请及时联系客服。</p>
                    </li>
                    <li class="announcement-item">
                        <div class="announcement-header">
                            <span class="announcement-title">服务条款更新</span>
                            <span class="announcement-date">2023-06-01</span>
                        </div>
                        <p class="announcement-content">我们已更新服务条款和隐私政策，新的条款将于2023年7月1日生效。请您仔细阅读并了解相关内容的变更。</p>
                    </li>
                    <li class="announcement-item">
                        <div class="announcement-header">
                            <span class="announcement-title">端午节放假通知</span>
                            <span class="announcement-date">2023-05-20</span>
                        </div>
                        <p class="announcement-content">尊敬的用户，我们将于2023年6月22日至6月24日放假，期间客服响应可能会有延迟。祝您端午节快乐！</p>
                    </li>
                </ul>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('all-announcements-modal')">关闭</button>
            </div>
        </div>
    </div>
<script>
    // 控制面板页面专用侧边栏修复
    document.addEventListener('DOMContentLoaded', function() {
        console.log('控制面板页面侧边栏修复脚本已加载');

        // 获取DOM元素
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');
        const menuToggle = document.getElementById('menu-toggle');

        if (sidebar && mainContent && menuToggle) {
            console.log('找到控制面板侧边栏元素');

            // 移除所有现有的点击事件
            const oldMenuToggle = menuToggle.cloneNode(true);
            menuToggle.parentNode.replaceChild(oldMenuToggle, menuToggle);

            // 添加新的点击事件
            oldMenuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('控制面板侧边栏切换按钮被点击');

                // 切换侧边栏状态
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');

                // 保存状态到本地存储
                localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
                console.log('控制面板侧边栏状态已保存:', sidebar.classList.contains('collapsed'));
            });

            // 从本地存储中恢复状态
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            console.log('控制面板从本地存储中恢复侧边栏状态:', sidebarCollapsed);

            if (sidebarCollapsed) {
                sidebar.classList.add('collapsed');
                mainContent.classList.add('expanded');
            } else {
                sidebar.classList.remove('collapsed');
                mainContent.classList.remove('expanded');
            }

            console.log('控制面板侧边栏切换已修复');
        } else {
            console.error('未找到控制面板侧边栏元素');
        }
    });
</script>
<html lang="zh-CN">
<script>
// 防止主题颜色修改图标显示
document.addEventListener('DOMContentLoaded', function() {
    // 移除已存在的主题颜色图标
    const themeColorIcon = document.getElementById('theme-color-icon');
    if (themeColorIcon) {
        themeColorIcon.remove();
    }

    // 监听并阻止新图标创建
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.addedNodes) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.id === 'theme-color-icon' ||
                        (node.classList && node.classList.contains('theme-settings-toggle'))) {
                        node.remove();
                    }
                });
            }
        });
    });

    // 开始观察文档变化
    observer.observe(document.body, { childList: true, subtree: true });
});
</script>

<!-- 网站底部 -->
<div class="site-footer">
    <div class="footer-content">
        <div class="copyright">
            <?php echo htmlspecialchars($settings['site_copyright']); ?>
        </div>
        <div class="beian">
            <?php if (!empty($settings['icp_number'])): ?>
            <a href="https://beian.miit.gov.cn/" target="_blank" class="beian-link">
                <i class="bi bi-shield-check"></i> <?php echo htmlspecialchars($settings['icp_number']); ?>
            </a>
            <?php endif; ?>
            <?php if (!empty($settings['police_number'])): ?>
            <a href="http://www.beian.gov.cn/portal/index" target="_blank" class="beian-link">
                <i class="bi bi-shield-lock"></i> <?php echo htmlspecialchars($settings['police_number']); ?>
            </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
/* 底部样式 */
.site-footer {
    background-color: var(--white-color);
    border-top: 1px solid var(--light-color);
    padding: 15px 20px;
    text-align: center;
    font-size: 0.9rem;
    color: var(--gray-color);
    margin-top: 30px;
    width: 100%;
    position: relative;
    bottom: 0;
    left: 0;
    z-index: 10;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    max-width: 1200px;
    margin: 0 auto;
}

.copyright {
    margin-bottom: 5px;
    text-align: center;
}

.beian {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
    text-align: center;
}

.beian-link {
    color: var(--gray-color);
    text-decoration: none;
    transition: color 0.3s;
    display: flex;
    align-items: center;
    gap: 5px;
}

.beian-link:hover {
    color: var(--primary-color);
}

.beian-link i {
    font-size: 1rem;
}
</style>

<!-- 确保这些脚本在最后加载 -->
<script src="/assets/js/sidebar-toggle-fix.js"></script>
<script src="/assets/js/responsive-layout.js"></script>
<script src="/assets/js/modal-functions-fix.js"></script>
<script src="/assets/js/modal-behavior-fix.js"></script>
<script src="/assets/js/admin-sidebar-fix.js"></script>
<script src="/assets/js/avatar-theme-fix.js"></script>
<!-- 响应式增强脚本 -->
<script src="/assets/js/admin-sidebar-responsive.js"></script>
<script src="/assets/js/admin-charts-responsive.js"></script>
<script src="/assets/js/admin-layout-fix.js"></script>
<!-- 数据可视化和安全性增强脚本 -->
<script src="/assets/js/enhanced-charts.js"></script>
<script src="/assets/js/security-enhancements.js"></script>
<!-- 智慧天气组件 -->
<script src="/assets/js/wisdom-weather.js"></script>
<!-- 自动刷新通知系统 -->
<script src="/assets/js/auto-refresh-toast.js"></script>
<!-- 修复天气和人生哲理组件按钮点击问题 -->
<script>
    // 天气设置按钮点击事件处理函数
    function toggleWeatherSettings(event) {
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }
        console.log('天气设置按钮被点击，打开模态框');

        // 加载当前设置
        loadWeatherSettings();

        // 打开模态框
        const modalElement = document.getElementById('weatherSettingsModal');
        if (modalElement) {
            console.log('找到模态框元素，准备显示');

            // 确保模态框没有被其他实例占用
            const existingModal = bootstrap.Modal.getInstance(modalElement);
            if (existingModal) {
                existingModal.dispose();
            }

            // 创建新的模态框实例
            const modal = new bootstrap.Modal(modalElement, {
                backdrop: false,  // 禁用backdrop，避免背景变黑
                keyboard: true,
                focus: true
            });

            // 创建自定义背景遮罩
            let backdrop = document.getElementById('customModalBackdrop');
            if (!backdrop) {
                backdrop = document.createElement('div');
                backdrop.id = 'customModalBackdrop';
                backdrop.className = 'custom-modal-backdrop';
                backdrop.onclick = function() {
                    closeWeatherModal();
                };
                document.body.appendChild(backdrop);
            }

            // 立即显示自定义遮罩
            backdrop.classList.add('show');

            // 添加事件监听器
            modalElement.addEventListener('shown.bs.modal', function () {
                console.log('模态框已显示');
                // 确保自定义遮罩显示
                backdrop.classList.add('show');
                // 调试模态框元素
                setTimeout(() => {
                    debugModalElements();
                }, 100);
            });

            modalElement.addEventListener('hidden.bs.modal', function () {
                console.log('模态框已隐藏');
                backdrop.classList.remove('show');
            });

            modal.show();
            console.log('模态框show()方法已调用');
        } else {
            console.error('未找到天气设置模态框');
        }
    }

    // 加载天气设置
    function loadWeatherSettings() {
        fetch('../api/weather-settings.php', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => {
            console.log('API响应状态:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.text(); // 先获取文本，然后尝试解析JSON
        })
        .then(text => {
            console.log('API响应内容:', text);
            try {
                const data = JSON.parse(text);
                if (data.success) {
                    document.getElementById('weatherApiProvider').value = data.settings.api_provider || 'openweather';
                    document.getElementById('weatherApiKey').value = data.settings.api_key || '';



                    // 检查输入框是否已有用户输入，如果有则不覆盖
                    const cityInput = document.getElementById('defaultCity');
                    if (!cityInput.value || cityInput.value.length <= 2) {
                        // 只有当输入框为空或内容很短时才使用数据库值
                        cityInput.value = data.settings.default_city || '北京';
                    }
                    console.log('城市输入框当前值:', cityInput.value);

                    document.getElementById('weatherEnabled').checked = data.settings.enabled !== false;
                    document.getElementById('cacheTime').value = data.settings.cache_duration || 30;

                    // 初始化API服务商信息
                    updateApiProviderInfo();
                } else {
                    console.error('API返回错误:', data.message);
                }
            } catch (parseError) {
                console.error('JSON解析失败:', parseError);
                console.error('响应内容:', text);
                // 使用默认值
                document.getElementById('weatherApiProvider').value = 'openweather';
                document.getElementById('weatherApiKey').value = '';
                document.getElementById('defaultCity').value = '北京';
                document.getElementById('weatherEnabled').checked = true;
                document.getElementById('cacheTime').value = 30;
            }
        })
        .catch(error => {
            console.error('加载天气设置失败:', error);
            // 使用默认值
            document.getElementById('weatherApiProvider').value = 'openweather';
            document.getElementById('weatherApiKey').value = '';
            document.getElementById('defaultCity').value = '北京';
            document.getElementById('weatherEnabled').checked = true;
            document.getElementById('cacheTime').value = 30;
        });
    }

    // 保存天气设置
    function saveWeatherSettings() {
        const apiKey = document.getElementById('weatherApiKey').value.trim();
        // 优先使用全局变量中的城市，如果没有则使用输入框的值
        const defaultCity = window.selectedCity || document.getElementById('defaultCity').value.trim();

        console.log('保存的城市值:', defaultCity);
        console.log('全局变量城市:', window.selectedCity);
        console.log('输入框城市:', document.getElementById('defaultCity').value);

        // 验证必填字段
        if (!apiKey) {
            if (typeof showToast === 'function') {
                showToast('请输入API密钥', 'warning');
            } else {
                alert('请输入API密钥');
            }
            return;
        }

        if (!defaultCity) {
            if (typeof showToast === 'function') {
                showToast('请输入默认城市', 'warning');
            } else {
                alert('请输入默认城市');
            }
            return;
        }

        const formData = {
            api_provider: document.getElementById('weatherApiProvider').value,
            api_key: apiKey,
            default_city: defaultCity,
            enabled: document.getElementById('weatherEnabled').checked,
            cache_duration: parseInt(document.getElementById('cacheTime').value)
        };

        fetch('../api/weather-settings.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新localStorage中的天气设置
                const weatherSettings = {
                    city: formData.default_city,
                    api_provider: formData.api_provider,
                    api_key: formData.api_key,
                    enabled: formData.enabled,
                    cache_duration: formData.cache_duration
                };
                localStorage.setItem('weatherSettings', JSON.stringify(weatherSettings));

                // 关闭模态框
                closeWeatherModal();

                // 显示成功消息并自动刷新页面（参考更新版本按钮的逻辑）
                console.log('🚀 直接调用showAutoRefreshToast - 天气设置保存');
                if (typeof window.showAutoRefreshToast === 'function') {
                    window.showAutoRefreshToast('天气设置保存成功', {
                        refreshCallback: function() {
                            console.log('天气设置保存完成，直接刷新页面');
                            // 直接刷新页面，确保设置生效
                            location.reload();
                        }
                    });
                } else {
                    console.error('❌ showAutoRefreshToast函数不存在');
                    // 备用方案
                    alert('天气设置保存成功');
                    setTimeout(() => location.reload(), 1000);
                }
            } else {
                if (typeof showToast === 'function') {
                    showToast(data.message || '保存失败', 'error');
                }
            }
        })
        .catch(error => {
            console.error('保存天气设置失败:', error);
            if (typeof showToast === 'function') {
                showToast('保存失败', 'error');
            }
        });
    }

    // 更新API服务商信息
    function updateApiProviderInfo() {
        const provider = document.getElementById('weatherApiProvider').value;
        const description = document.getElementById('providerDescription');
        const link = document.getElementById('providerLink');
        const apiKeyLabel = document.getElementById('apiKeyLabel');
        const apiKeyHelp = document.getElementById('apiKeyHelp');
        const apiKeyInput = document.getElementById('weatherApiKey');
        const privateKeySection = document.getElementById('privateKeySection');

        const providerInfo = {
            'openweather': {
                name: 'OpenWeatherMap',
                description: '全球领先的天气数据服务商，免费额度1000次/天',
                url: 'https://openweathermap.org/api',
                keyLabel: 'API密钥',
                keyPlaceholder: '请输入OpenWeatherMap API密钥',
                keyHelp: '请到 <a href="https://openweathermap.org/api" target="_blank">OpenWeatherMap</a> 申请免费API密钥',
                needPrivateKey: false
            }
        };

        if (providerInfo[provider]) {
            const info = providerInfo[provider];
            description.textContent = info.description;
            link.href = info.url;
            link.style.display = 'inline';

            // 更新API密钥字段
            if (apiKeyLabel) apiKeyLabel.textContent = info.keyLabel;
            if (apiKeyInput) apiKeyInput.placeholder = info.keyPlaceholder;
            if (apiKeyHelp) {
                // 清空现有内容
                apiKeyHelp.innerHTML = '';

                // 创建OpenWeatherMap链接
                apiKeyHelp.appendChild(document.createTextNode('请到 '));
                const link = document.createElement('a');
                link.href = 'https://openweathermap.org/api';
                link.target = '_blank';
                link.textContent = 'OpenWeatherMap';
                apiKeyHelp.appendChild(link);
                apiKeyHelp.appendChild(document.createTextNode(' 申请免费API密钥'));
            }
        } else {
            description.textContent = '选择API服务商后将显示官网链接';
            link.style.display = 'none';
        }
    }

    // 城市搜索建议
    function showCitySuggestions(value) {
        const suggestions = document.getElementById('citySuggestions');

        if (!value || value.length < 1) {
            suggestions.style.display = 'none';
            return;
        }

        // 显示加载状态
        suggestions.innerHTML = '<div class="px-3 py-2 text-muted">搜索中...</div>';
        suggestions.style.display = 'block';

        // 调用城市搜索API
        fetch(`../api/cities.php?q=${encodeURIComponent(value)}`)
            .then(response => response.json())
            .then(data => {
                const cities = data.cities || [];

                if (cities.length === 0) {
                    suggestions.style.display = 'none';
                    return;
                }

                // 生成建议列表
                suggestions.innerHTML = '';
                cities.slice(0, 8).forEach(city => {
                    const div = document.createElement('div');
                    div.className = 'px-3 py-2';
                    div.style.cursor = 'pointer';
                    div.textContent = city;

                    // 使用onclick属性，更直接
                    div.onclick = function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('点击城市项:', city);

                        // 直接设置输入框值
                        const input = document.getElementById('defaultCity');
                        if (input) {
                            input.value = city;
                            input.setAttribute('value', city);
                            console.log('直接设置输入框值为:', city);
                        }

                        // 隐藏建议框
                        const suggestions = document.getElementById('citySuggestions');
                        if (suggestions) {
                            suggestions.style.display = 'none';
                            suggestions.innerHTML = '';
                        }

                        // 存储到全局变量
                        window.selectedCity = city;

                        return false;
                    };

                    div.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = '#f8f9fa';
                    });
                    div.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = '';
                    });

                    suggestions.appendChild(div);
                });

                suggestions.style.display = 'block';
            })
            .catch(error => {
                console.error('城市搜索失败:', error);
                suggestions.innerHTML = '<div class="px-3 py-2 text-muted">搜索失败</div>';
            });
    }

    // 全局变量存储选中的城市
    window.selectedCity = '';

    // 选择城市 - 全新实现
    function selectCity(city) {
        console.log('=== 选择城市开始 ===');
        console.log('选择的城市:', city);

        // 存储到全局变量
        window.selectedCity = city;

        // 直接操作DOM
        const input = document.getElementById('defaultCity');
        const suggestions = document.getElementById('citySuggestions');

        if (input) {
            // 方法1：直接设置innerHTML
            input.outerHTML = `<input type="text" class="form-control" id="defaultCity" name="default_city" value="${city}" placeholder="请输入城市名称，如：北京、上海、广州" autocomplete="off" oninput="showCitySuggestions(this.value)">`;

            console.log('输入框已重新创建，值为:', city);
        }

        if (suggestions) {
            suggestions.style.display = 'none';
            suggestions.innerHTML = '';
        }

        // 验证设置是否成功
        setTimeout(() => {
            const newInput = document.getElementById('defaultCity');
            console.log('验证：输入框当前值为:', newInput ? newInput.value : 'null');
        }, 100);

        console.log('=== 选择城市结束 ===');
    }

    // 点击其他地方隐藏建议
    document.addEventListener('click', function(e) {
        const suggestions = document.getElementById('citySuggestions');
        const input = document.getElementById('defaultCity');
        if (suggestions && input && !suggestions.contains(e.target) && e.target !== input) {
            suggestions.style.display = 'none';
        }
    });

    // 调试函数：检查模态框内的元素
    function debugModalElements() {
        console.log('=== 模态框元素调试 ===');
        const modal = document.getElementById('weatherSettingsModal');
        const input = document.getElementById('defaultCity');
        const suggestions = document.getElementById('citySuggestions');

        console.log('模态框:', modal);
        console.log('输入框:', input);
        console.log('建议框:', suggestions);

        if (input) {
            console.log('输入框当前值:', input.value);
            console.log('输入框是否可见:', input.offsetParent !== null);
        }

        if (suggestions) {
            console.log('建议框是否可见:', suggestions.style.display);
            console.log('建议框内容:', suggestions.innerHTML);
        }
    }

    // 关闭天气设置模态框
    function closeWeatherModal() {
        const modalElement = document.getElementById('weatherSettingsModal');
        const backdrop = document.getElementById('customModalBackdrop');

        if (modalElement) {
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
        }

        if (backdrop) {
            backdrop.classList.remove('show');
        }
    }

    // 关闭天气设置下拉菜单
    function closeWeatherSettings(event) {
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }
        console.log('关闭天气设置下拉菜单');

        const dropdown = document.getElementById('weather-settings-dropdown');
        if (dropdown) {
            dropdown.style.display = 'none';
            dropdown.classList.remove('show');
            console.log('下拉菜单已关闭');
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM加载完成，开始初始化天气设置按钮');

        // 等待一段时间确保所有元素都已加载
        setTimeout(function() {
            console.log('开始查找天气设置按钮...');

            // 查找所有可能的天气设置按钮
            const settingsBtn = document.getElementById('settings-info');
            const allButtons = document.querySelectorAll('button');
            const weatherButtons = document.querySelectorAll('[title="设置"]');

            console.log('找到的元素:');
            console.log('- settings-info按钮:', settingsBtn);
            console.log('- 所有按钮数量:', allButtons.length);
            console.log('- 设置按钮数量:', weatherButtons.length);

            // 检查天气设置下拉菜单是否存在
            const dropdown = document.getElementById('weather-settings-dropdown');
            console.log('- 天气设置下拉菜单:', dropdown);

            if (settingsBtn) {
                console.log('找到天气设置按钮，开始初始化...');

                // 移除所有现有的事件监听器
                const newBtn = settingsBtn.cloneNode(true);
                settingsBtn.parentNode.replaceChild(newBtn, settingsBtn);

                // 确保按钮可点击
                newBtn.style.cursor = 'pointer';
                newBtn.style.pointerEvents = 'auto';
                newBtn.style.backgroundColor = 'rgba(255, 0, 0, 0.1)'; // 临时添加红色背景用于调试

                console.log('按钮样式已设置:', {
                    cursor: newBtn.style.cursor,
                    pointerEvents: newBtn.style.pointerEvents,
                    display: window.getComputedStyle(newBtn).display,
                    visibility: window.getComputedStyle(newBtn).visibility
                });

                // 添加多种事件监听器进行调试
                newBtn.addEventListener('click', function(e) {
                    console.log('=== 天气设置按钮点击事件触发 ===');
                    console.log('事件对象:', e);
                    console.log('目标元素:', e.target);
                    e.preventDefault();
                    e.stopPropagation();
                    toggleWeatherSettings(e);
                });

                newBtn.addEventListener('mousedown', function(e) {
                    console.log('天气设置按钮 mousedown 事件');
                });

                newBtn.addEventListener('mouseup', function(e) {
                    console.log('天气设置按钮 mouseup 事件');
                });

                newBtn.addEventListener('mouseover', function(e) {
                    console.log('天气设置按钮 mouseover 事件');
                    this.style.backgroundColor = 'rgba(255, 0, 0, 0.3)';
                });

                newBtn.addEventListener('mouseout', function(e) {
                    this.style.backgroundColor = 'rgba(255, 0, 0, 0.1)';
                });

                console.log('天气设置按钮已初始化，事件监听器已添加');
            } else {
                console.error('未找到天气设置按钮 (ID: settings-info)');
                console.log('尝试查找其他可能的按钮...');

                // 尝试通过其他方式查找按钮
                const gearButtons = document.querySelectorAll('i.bi-gear');
                console.log('找到齿轮图标数量:', gearButtons.length);
                gearButtons.forEach((icon, index) => {
                    console.log(`齿轮图标 ${index}:`, icon.parentElement);
                });
            }
        }, 1000); // 增加等待时间到1秒

        // 直接添加事件监听器到按钮
        const refreshBtn = document.getElementById('refresh-info');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', function() {
                console.log('刷新按钮被点击');
                // 添加旋转动画
                this.classList.add('rotating');
                setTimeout(() => {
                    this.classList.remove('rotating');
                }, 1000);

                // 强制刷新天气信息（请求OpenWeatherMap API）
                refreshWeatherData();
            });
        }

        // 刷新天气数据函数
        function refreshWeatherData() {
            const weatherText = document.getElementById('weather-text');
            if (!weatherText) return;

            // 显示刷新状态
            weatherText.textContent = '刷新天气中...';

            // 获取设置中的城市
            const settings = JSON.parse(localStorage.getItem('weatherSettings')) || {
                city: '北京'
            };

            // 强制请求真实天气数据（不使用缓存）
            const timestamp = new Date().getTime();
            fetch(`../api/weather.php?city=${encodeURIComponent(settings.city)}&t=${timestamp}`)
                .then(response => response.json())
                .then(data => {
                    let weatherDisplay = '';

                    if (data.success) {
                        // 使用真实天气数据
                        const weather = data.data;
                        weatherDisplay = `${data.city} ${weather.condition} ${weather.temperature}°C`;
                        console.log('天气刷新成功:', data);
                    } else {
                        // API失败时显示错误
                        weatherDisplay = `${data.city} 获取失败`;
                        console.warn('天气API错误:', data.error);
                    }

                    // 设置文本
                    weatherText.textContent = weatherDisplay;

                    // 添加淡入动画
                    weatherText.style.animation = 'none';
                    setTimeout(() => {
                        weatherText.style.animation = 'fadeIn 0.5s';
                    }, 10);
                })
                .catch(error => {
                    console.error('刷新天气失败:', error);
                    weatherText.textContent = `${settings.city} 刷新失败`;
                });
        }

        const toggleBtn = document.getElementById('toggle-info-type');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', function() {
                console.log('切换按钮被点击');
                // 获取当前类型
                const currentType = localStorage.getItem('infoType') || 'both';
                let newType;

                // 循环切换类型
                switch (currentType) {
                    case 'both':
                        newType = 'wisdom';
                        break;
                    case 'wisdom':
                        newType = 'weather';
                        break;
                    case 'weather':
                        newType = 'both';
                        break;
                    default:
                        newType = 'both';
                }

                // 保存新类型
                localStorage.setItem('infoType', newType);

                // 更新显示
                updateDisplayState(newType);
            });
        }

        // 设置按钮点击事件已在上面初始化，这里不再重复

        // 辅助函数
        function updateDisplayState(type) {
            const wisdomQuote = document.querySelector('.wisdom-quote');
            const weatherInfo = document.querySelector('.weather-info');

            if (!wisdomQuote || !weatherInfo) return;

            switch (type) {
                case 'wisdom':
                    wisdomQuote.style.display = 'flex';
                    weatherInfo.style.display = 'none';
                    break;
                case 'weather':
                    wisdomQuote.style.display = 'none';
                    weatherInfo.style.display = 'flex';
                    break;
                case 'both':
                default:
                    wisdomQuote.style.display = 'flex';
                    weatherInfo.style.display = 'flex';
                    break;
            }
        }

        function updateWisdom() {
            const wisdomText = document.getElementById('wisdom-text');
            if (!wisdomText) return;

            // 随机选择一条智慧语录
            const wisdomQuotes = [
                "生活不是等待风暴过去，而是学会在雨中跳舞。",
                "人生就像骑自行车，要保持平衡就得不断前进。",
                "成功不是最终的，失败也不是致命的，重要的是继续前进的勇气。",
                "不要为成功而努力，要为做一个有价值的人而努力。",
                "生活中最重要的事情是明确你想要什么。",
                "人生最大的挑战是超越自己。",
                "每一个不曾起舞的日子，都是对生命的辜负。",
                "微笑是世界上最好的语言。",
                "成功的秘诀在于坚持目标的始终。",
                "真正的智慧是知道自己所不知道的东西。"
            ];

            const randomIndex = Math.floor(Math.random() * wisdomQuotes.length);
            wisdomText.textContent = wisdomQuotes[randomIndex];

            // 添加淡入动画
            wisdomText.style.animation = 'none';
            setTimeout(() => {
                wisdomText.style.animation = 'fadeIn 0.5s';
            }, 10);
        }

        function updateWeather() {
            const weatherText = document.getElementById('weather-text');
            if (!weatherText) return;

            // 获取设置中的城市
            const settings = JSON.parse(localStorage.getItem('weatherSettings')) || {
                city: '北京'
            };

            // 模拟天气信息
            const conditions = ['晴', '多云', '阴', '小雨', '中雨', '大雨', '雷阵雨', '小雪', '大雪'];
            const randomCondition = conditions[Math.floor(Math.random() * conditions.length)];
            const randomTemp = Math.floor(Math.random() * 30) + 5; // 5-34度

            weatherText.textContent = `${settings.city} ${randomCondition} ${randomTemp}°C`;

            // 添加淡入动画
            weatherText.style.animation = 'none';
            setTimeout(() => {
                weatherText.style.animation = 'fadeIn 0.5s';
            }, 10);
        }

        // 初始化显示状态
        const currentType = localStorage.getItem('infoType') || 'both';
        updateDisplayState(currentType);
    });

    // 初始化城市搜索功能
    function initCitySearch() {
        const searchInput = document.getElementById('weather-city-search');
        const resultsContainer = document.getElementById('city-search-results');

        if (!searchInput || !resultsContainer) return;

        // 中国主要城市列表
        const cities = [
            '北京', '上海', '广州', '深圳', '杭州', '南京', '苏州', '成都', '重庆', '武汉',
            '西安', '天津', '青岛', '大连', '宁波', '厦门', '福州', '哈尔滨', '长春', '沈阳',
            '石家庄', '太原', '呼和浩特', '兰州', '西宁', '银川', '乌鲁木齐', '拉萨', '昆明', '贵阳',
            '南宁', '海口', '三亚', '长沙', '南昌', '合肥', '郑州', '济南', '无锡', '常州'
        ];

        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            resultsContainer.innerHTML = '';

            if (query.length === 0) {
                resultsContainer.style.display = 'none';
                return;
            }

            // 搜索匹配的城市
            const matches = cities.filter(city => city.includes(query));

            if (matches.length > 0) {
                resultsContainer.style.display = 'block';
                matches.slice(0, 8).forEach(city => {
                    const item = document.createElement('div');
                    item.className = 'city-search-item';
                    item.textContent = city;
                    item.addEventListener('click', function() {
                        selectCity(city);
                        resultsContainer.style.display = 'none';
                        searchInput.value = '';
                    });
                    resultsContainer.appendChild(item);
                });
            } else {
                resultsContainer.style.display = 'none';
            }
        });
    }

    // 选择城市
    function selectCity(city) {
        console.log('选择城市:', city);

        // 保存设置
        const settings = { city: city };
        localStorage.setItem('weatherSettings', JSON.stringify(settings));

        // 更新显示
        const selectedCity = document.getElementById('selected-city');
        if (selectedCity) selectedCity.textContent = city;

        // 获取真实天气信息
        fetchRealWeather(city);

        // 关闭下拉菜单
        closeWeatherSettings();
    }

    // 获取真实天气信息
    async function fetchRealWeather(city) {
        try {
            // 使用免费的天气API（这里使用模拟，实际项目中可以使用真实API）
            const weatherText = document.getElementById('weather-text');
            if (!weatherText) return;

            // 显示加载状态
            weatherText.textContent = '获取天气中...';

            // 模拟API调用延迟
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 模拟真实的天气数据（实际项目中替换为真实API）
            const weatherData = await simulateWeatherAPI(city);

            // 更新天气显示
            weatherText.textContent = `${city} ${weatherData.condition} ${weatherData.temperature}°C`;

            console.log('天气信息已更新:', weatherData);

        } catch (error) {
            console.error('获取天气信息失败:', error);
            const weatherText = document.getElementById('weather-text');
            if (weatherText) {
                weatherText.textContent = `${city} 天气获取失败`;
            }
        }
    }

    // 天气API函数（当前为模拟，可替换为真实API）
    async function simulateWeatherAPI(city) {
        // TODO: 替换为真实天气API
        // 示例：和风天气API调用
        /*
        const API_KEY = 'YOUR_API_KEY'; // 需要在全局设置中配置
        const API_URL = `https://devapi.qweather.com/v7/weather/now?location=${encodeURIComponent(city)}&key=${API_KEY}`;

        try {
            const response = await fetch(API_URL);
            const data = await response.json();

            if (data.code === '200') {
                return {
                    condition: data.now.text,
                    temperature: parseInt(data.now.temp),
                    humidity: parseInt(data.now.humidity)
                };
            }
        } catch (error) {
            console.error('获取真实天气失败:', error);
        }
        */

        // 当前使用模拟数据
        const weatherPatterns = {
            '北京': { condition: '晴', temperature: 25, humidity: 45 },
            '上海': { condition: '多云', temperature: 28, humidity: 65 },
            '广州': { condition: '小雨', temperature: 30, humidity: 80 },
            '深圳': { condition: '阴', temperature: 29, humidity: 75 },
            '杭州': { condition: '晴', temperature: 26, humidity: 55 },
            '成都': { condition: '多云', temperature: 23, humidity: 70 }
        };

        // 如果有预设天气，使用预设；否则生成随机天气
        if (weatherPatterns[city]) {
            return weatherPatterns[city];
        } else {
            const conditions = ['晴', '多云', '阴', '小雨', '中雨', '雷阵雨'];
            const randomCondition = conditions[Math.floor(Math.random() * conditions.length)];
            const randomTemp = Math.floor(Math.random() * 25) + 10; // 10-34度
            const randomHumidity = Math.floor(Math.random() * 40) + 40; // 40-80%

            return {
                condition: randomCondition,
                temperature: randomTemp,
                humidity: randomHumidity
            };
        }
    }

    // 真实天气API示例函数（备用）
    async function getRealWeatherAPI(city, apiKey) {
        // 和风天气API示例
        const API_URL = `https://devapi.qweather.com/v7/weather/now?location=${encodeURIComponent(city)}&key=${apiKey}`;

        try {
            const response = await fetch(API_URL);
            const data = await response.json();

            if (data.code === '200') {
                return {
                    condition: data.now.text,
                    temperature: parseInt(data.now.temp),
                    humidity: parseInt(data.now.humidity),
                    windDir: data.now.windDir,
                    windSpeed: data.now.windSpeed
                };
            } else {
                throw new Error(`API错误: ${data.code}`);
            }
        } catch (error) {
            console.error('获取真实天气失败:', error);
            throw error;
        }
    }
</script>
</body>
</html>