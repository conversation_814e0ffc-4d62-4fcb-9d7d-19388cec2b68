/**
 * 情侣头像匹配系统 - UI效果和交互
 */

// 音效管理
const SoundEffects = {
  // 音效状态
  enabled: true,

  // 音效列表
  sounds: {
    click: new Audio('/assets/sounds/click.mp3'),
    success: new Audio('/assets/sounds/success.mp3'),
    error: new Audio('/assets/sounds/error.mp3'),
    notification: new Audio('/assets/sounds/notification.mp3'),
    match: new Audio('/assets/sounds/match.mp3'),
    download: new Audio('/assets/sounds/download.mp3')
  },

  // 初始化
  init() {
    // 从本地存储加载音效状态
    this.enabled = localStorage.getItem('sound_effects') !== 'false';

    // 预加载所有音效
    Object.values(this.sounds).forEach(sound => {
      // 添加错误处理
      sound.addEventListener('error', () => {
        console.log('音效加载失败:', sound.src);
        // 标记音效为不可用
        sound.available = false;
      });

      // 添加加载成功处理
      sound.addEventListener('canplaythrough', () => {
        sound.available = true;
      });

      sound.load();
      sound.volume = 0.5;
    });

    // 添加音效开关到页面
    this.addSoundToggle();

    // 绑定按钮点击音效
    this.bindButtonSounds();
  },

  // 播放音效
  play(soundName) {
    if (!this.enabled) return;

    const sound = this.sounds[soundName];
    if (sound && sound.available !== false) {
      // 停止并重置音效
      sound.pause();
      sound.currentTime = 0;

      // 播放音效
      sound.play().catch(e => {
        console.log('音效播放失败:', e);
        // 标记音效为不可用
        sound.available = false;
      });
    }
  },

  // 切换音效状态
  toggle() {
    this.enabled = !this.enabled;
    localStorage.setItem('sound_effects', this.enabled);

    // 更新UI
    const icon = document.querySelector('.sound-toggle i');
    if (icon) {
      icon.className = this.enabled ? 'bi bi-volume-up' : 'bi bi-volume-mute';
    }

    // 播放测试音效
    if (this.enabled) {
      this.play('click');
    }
  },

  // 添加音效开关
  addSoundToggle() {
    const toggleBtn = document.createElement('button');
    toggleBtn.className = 'btn btn-sm sound-toggle';
    toggleBtn.innerHTML = `<i class="bi ${this.enabled ? 'bi-volume-up' : 'bi-volume-mute'}"></i>`;
    toggleBtn.style.position = 'fixed';
    toggleBtn.style.bottom = '20px';
    toggleBtn.style.right = '20px';
    toggleBtn.style.zIndex = '1000';
    toggleBtn.style.borderRadius = '50%';
    toggleBtn.style.width = '40px';
    toggleBtn.style.height = '40px';
    toggleBtn.style.padding = '0';
    toggleBtn.style.display = 'flex';
    toggleBtn.style.alignItems = 'center';
    toggleBtn.style.justifyContent = 'center';
    toggleBtn.style.backgroundColor = 'var(--primary-color)';
    toggleBtn.style.color = 'white';
    toggleBtn.style.boxShadow = 'var(--box-shadow)';

    toggleBtn.addEventListener('click', () => this.toggle());

    document.body.appendChild(toggleBtn);
  },

  // 绑定按钮点击音效
  bindButtonSounds() {
    document.addEventListener('click', (e) => {
      const target = e.target.closest('button, .btn, [role="button"], a.nav-link');
      if (target) {
        this.play('click');
      }
    });
  }
};

// 动画效果
const Animations = {
  // 初始化
  init() {
    this.addScrollAnimations();
    this.addHoverEffects();
  },

  // 添加滚动动画
  addScrollAnimations() {
    const animateElements = document.querySelectorAll('.animate-on-scroll');

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animated');
          observer.unobserve(entry.target);
        }
      });
    }, { threshold: 0.1 });

    animateElements.forEach(el => {
      observer.observe(el);
    });
  },

  // 添加悬停效果
  addHoverEffects() {
    const cards = document.querySelectorAll('.card');

    cards.forEach(card => {
      card.addEventListener('mouseenter', () => {
        card.style.transform = 'translateY(-5px)';
        card.style.boxShadow = 'var(--box-shadow)';
      });

      card.addEventListener('mouseleave', () => {
        card.style.transform = 'translateY(0)';
        card.style.boxShadow = 'var(--box-shadow-sm)';
      });
    });
  }
};

// 交互增强
const InteractionEnhancer = {
  // 初始化
  init() {
    this.addTooltips();
    this.addConfirmDialogs();
    this.addNotifications();
    this.addDarkModeToggle();
  },

  // 添加工具提示
  addTooltips() {
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
  },

  // 添加确认对话框
  addConfirmDialogs() {
    document.addEventListener('click', (e) => {
      const target = e.target.closest('[data-confirm]');
      if (target) {
        e.preventDefault();

        const message = target.getAttribute('data-confirm') || '确定要执行此操作吗？';

        if (confirm(message)) {
          // 如果是链接，则跳转
          if (target.tagName === 'A') {
            window.location.href = target.href;
          } else if (target.form) {
            // 如果是表单提交按钮，则提交表单
            target.form.submit();
          }
        }
      }
    });
  },

  // 添加通知系统
  addNotifications() {
    // 创建通知容器
    const container = document.createElement('div');
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '1050';
    document.body.appendChild(container);

    // 全局通知方法
    window.showNotification = (message, type = 'info') => {
      // 播放通知音效
      SoundEffects.play(type === 'success' ? 'success' : type === 'error' ? 'error' : 'notification');

      // 创建通知元素
      const toast = document.createElement('div');
      toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0`;
      toast.setAttribute('role', 'alert');
      toast.setAttribute('aria-live', 'assertive');
      toast.setAttribute('aria-atomic', 'true');

      toast.innerHTML = `
        <div class="d-flex">
          <div class="toast-body">
            ${message}
          </div>
          <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
      `;

      container.appendChild(toast);

      // 显示通知
      const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 3000
      });
      bsToast.show();

      // 通知显示后移除
      toast.addEventListener('hidden.bs.toast', () => {
        container.removeChild(toast);
      });
    };
  },

  // 添加暗黑模式切换
  addDarkModeToggle() {
    // 从本地存储加载暗黑模式状态
    const isDarkMode = localStorage.getItem('dark_mode') === 'true';

    // 应用初始模式
    if (isDarkMode) {
      document.body.classList.add('dark-mode');
    }

    // 创建切换按钮
    const toggleBtn = document.createElement('button');
    toggleBtn.className = 'btn btn-sm dark-mode-toggle';
    toggleBtn.innerHTML = `<i class="bi ${isDarkMode ? 'bi-sun' : 'bi-moon'}"></i>`;
    toggleBtn.style.position = 'fixed';
    toggleBtn.style.bottom = '20px';
    toggleBtn.style.right = '70px';
    toggleBtn.style.zIndex = '1000';
    toggleBtn.style.borderRadius = '50%';
    toggleBtn.style.width = '40px';
    toggleBtn.style.height = '40px';
    toggleBtn.style.padding = '0';
    toggleBtn.style.display = 'flex';
    toggleBtn.style.alignItems = 'center';
    toggleBtn.style.justifyContent = 'center';
    toggleBtn.style.backgroundColor = 'var(--secondary-color)';
    toggleBtn.style.color = 'white';
    toggleBtn.style.boxShadow = 'var(--box-shadow)';

    toggleBtn.addEventListener('click', () => {
      // 切换暗黑模式
      document.body.classList.toggle('dark-mode');
      const isDark = document.body.classList.contains('dark-mode');

      // 更新图标
      toggleBtn.innerHTML = `<i class="bi ${isDark ? 'bi-sun' : 'bi-moon'}"></i>`;

      // 保存状态
      localStorage.setItem('dark_mode', isDark);

      // 播放音效
      SoundEffects.play('click');
    });

    document.body.appendChild(toggleBtn);
  }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  SoundEffects.init();
  Animations.init();
  InteractionEnhancer.init();
});
