<?php
// 直接测试API密钥

header('Content-Type: application/json');

// 您的API密钥
$apiKey = '70a2c59b6b194c92bbed4dbb4bcae572';
$city = $_GET['city'] ?? '北京';
$provider = $_GET['provider'] ?? 'heweather';

echo json_encode([
    'test_info' => [
        'api_key' => $apiKey,
        'api_key_length' => strlen($apiKey),
        'city' => $city,
        'provider' => $provider
    ]
]) . "\n";

// 测试和风天气API
if ($provider === 'heweather') {
    $url = "https://devapi.qweather.com/v7/weather/now?location=" . urlencode($city) . "&key=" . $apiKey;
    
    echo json_encode(['request_url' => $url]) . "\n";
    
    // 方法1: file_get_contents
    $context = stream_context_create([
        'http' => [
            'timeout' => 15,
            'user_agent' => 'Weather App/1.0',
            'method' => 'GET'
        ],
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false
        ]
    ]);
    
    $response1 = @file_get_contents($url, false, $context);
    
    echo json_encode([
        'method' => 'file_get_contents',
        'success' => $response1 !== false,
        'response_length' => $response1 ? strlen($response1) : 0,
        'response' => $response1 ? $response1 : 'Failed'
    ]) . "\n";
    
    // 方法2: cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Weather App/1.0');
    curl_setopt($ch, CURLOPT_VERBOSE, false);
    
    $response2 = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    echo json_encode([
        'method' => 'curl',
        'success' => $response2 !== false,
        'http_code' => $httpCode,
        'curl_error' => $curlError,
        'response_length' => $response2 ? strlen($response2) : 0,
        'response' => $response2 ? $response2 : 'Failed'
    ]) . "\n";
    
    // 解析响应
    if ($response2) {
        $data = json_decode($response2, true);
        echo json_encode([
            'parsed_data' => $data,
            'api_status' => $data['code'] ?? 'unknown',
            'weather_data' => $data['now'] ?? null
        ]) . "\n";
    }
}

// 测试OpenWeatherMap
if ($provider === 'openweather') {
    // 注意：您需要OpenWeatherMap的API密钥
    $url = "https://api.openweathermap.org/data/2.5/weather?q=" . urlencode($city) . "&appid=" . $apiKey . "&units=metric&lang=zh_cn";
    
    echo json_encode(['request_url' => $url]) . "\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Weather App/1.0');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    echo json_encode([
        'method' => 'curl',
        'success' => $response !== false,
        'http_code' => $httpCode,
        'curl_error' => $curlError,
        'response_length' => $response ? strlen($response) : 0,
        'response' => $response ? $response : 'Failed'
    ]) . "\n";
}
?>
