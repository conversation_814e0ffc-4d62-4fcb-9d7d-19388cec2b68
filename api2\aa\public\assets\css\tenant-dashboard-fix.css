/**
 * 租户控制面板页面优化样式
 * 参考 admin.php 页面的样式
 */

/* 仪表盘特定样式 */
.dashboard-welcome {
    margin-bottom: 30px !important;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
    padding: 30px !important;
    border-radius: 15px !important;
    color: var(--white-color);
    box-shadow: 0 10px 25px rgba(255, 107, 149, 0.4) !important;
    animation: fadeInUp 0.6s !important;
    position: relative;
    overflow: hidden;
}

.dashboard-welcome::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 200%;
    background: rgba(255, 255, 255, 0.1);
    transform: rotate(30deg);
    pointer-events: none;
}

.dashboard-welcome h2 {
    font-size: 2.2rem !important;
    font-weight: 700 !important;
    margin-bottom: 10px !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
    position: relative;
    z-index: 1;
}

.dashboard-welcome p {
    opacity: 0.9;
    font-size: 1.1rem !important;
    position: relative;
    z-index: 1;
}

.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)) !important;
    gap: 25px !important;
    margin-bottom: 40px !important;
}

.stat-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    border-radius: 15px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08) !important;
    padding: 25px !important;
    display: flex;
    align-items: flex-start;
    gap: 20px !important;
    transition: all 0.3s ease !important;
    animation: fadeInUp 0.6s !important;
    border: none !important;
    position: relative;
    overflow: hidden;
}

.stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.stat-card:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12) !important;
    transform: translateY(-5px) !important;
}

.stat-icon {
    width: 60px !important;
    height: 60px !important;
    border-radius: 15px !important;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
    color: var(--white-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem !important;
    box-shadow: 0 10px 20px rgba(255, 107, 149, 0.3) !important;
    position: relative;
    overflow: hidden;
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 200%;
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(30deg);
}

.stat-icon i {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.stat-content {
    flex: 1;
}

.stat-content h3 {
    font-size: 1.4rem !important;
    font-weight: 700 !important;
    color: var(--dark-color);
    margin-bottom: 20px !important;
    position: relative;
    padding-bottom: 15px !important;
}

.stat-content h3::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px !important;
    height: 4px !important;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
    border-radius: 4px !important;
}

.stat-details {
    display: flex;
    flex-direction: column;
    gap: 15px !important;
}

.stat-item {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px !important;
    background-color: rgba(var(--primary-color-rgb), 0.03) !important;
    padding: 12px 15px !important;
    border-radius: 10px !important;
    transition: all 0.3s ease !important;
}

.stat-item:hover {
    background-color: rgba(var(--primary-color-rgb), 0.08) !important;
    transform: translateX(5px) !important;
}

.stat-label {
    color: var(--gray-color);
    font-weight: 600 !important;
    font-size: 0.95rem !important;
    display: flex;
    align-items: center;
    gap: 5px !important;
}

.stat-label i {
    color: var(--primary-color);
    font-size: 1.1rem !important;
}

.stat-value {
    color: var(--dark-color);
    font-weight: 700 !important;
    font-size: 1.05rem !important;
}

.status-active {
    color: var(--success-color) !important;
    font-weight: 700 !important;
    display: inline-flex;
    align-items: center;
    gap: 5px !important;
}

.status-active::before {
    content: '';
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: var(--success-color);
    border-radius: 50%;
    animation: blink 1.5s infinite;
}

@keyframes blink {
    0% { opacity: 0.4; }
    50% { opacity: 1; }
    100% { opacity: 0.4; }
}

.version-history {
    margin-left: 10px !important;
    font-size: 0.9rem !important;
    color: var(--primary-color);
    text-decoration: none;
    padding: 3px 10px !important;
    background-color: rgba(var(--primary-color-rgb), 0.1) !important;
    border-radius: 20px !important;
    transition: all 0.3s ease !important;
    display: inline-flex;
    align-items: center;
    gap: 5px !important;
}

.version-history::before {
    content: '\F61B';
    font-family: 'bootstrap-icons';
    font-size: 0.9rem !important;
}

.version-history:hover {
    background-color: rgba(var(--primary-color-rgb), 0.2) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 10px rgba(var(--primary-color-rgb), 0.1) !important;
}

.btn-sm {
    padding: 5px 12px !important;
    font-size: 0.9rem !important;
    margin-left: 10px !important;
    border-radius: 20px !important;
    transition: all 0.3s ease !important;
    display: inline-flex;
    align-items: center;
    gap: 5px !important;
}

.btn-outline-primary {
    border: 1px solid var(--primary-color) !important;
    color: var(--primary-color) !important;
    background-color: transparent !important;
}

.btn-outline-primary:hover {
    background-color: var(--primary-color) !important;
    color: var(--white-color) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(var(--primary-color-rgb), 0.3) !important;
}

.dashboard-sections {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)) !important;
    gap: 25px !important;
}

.section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
    border-radius: 15px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08) !important;
    overflow: hidden;
    animation: fadeInUp 0.6s !important;
    height: 100%;
    transition: all 0.3s ease !important;
    border: none !important;
}

.section:hover {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12) !important;
    transform: translateY(-5px) !important;
}

.section-header {
    padding: 20px 25px !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
}

.section-header h3 {
    margin: 0;
    font-size: 1.3rem !important;
    font-weight: 700 !important;
    color: var(--dark-color);
    display: flex;
    align-items: center;
}

.section-header h3 i {
    margin-right: 10px !important;
    color: var(--primary-color);
    font-size: 1.4rem !important;
}

.view-all {
    color: var(--primary-color) !important;
    text-decoration: none;
    font-size: 0.95rem !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    padding: 5px 15px !important;
    background-color: rgba(var(--primary-color-rgb), 0.1) !important;
    border-radius: 20px !important;
    display: inline-flex;
    align-items: center;
    gap: 5px !important;
}

.view-all::after {
    content: '\F231';
    font-family: 'bootstrap-icons';
    font-size: 0.9rem !important;
}

.view-all:hover {
    background-color: rgba(var(--primary-color-rgb), 0.2) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 10px rgba(var(--primary-color-rgb), 0.1) !important;
}

.section-content {
    padding: 25px !important;
}
