/**
 * 订单统计样式
 * 为订单统计图表和数据分析提供样式支持
 */

/* 订单统计卡片容器 */
.order-statistics-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 订单统计卡片 */
.order-stat-card {
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 10px var(--shadow-color);
    padding: 20px;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

.order-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px var(--shadow-color);
}

.order-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.order-stat-card-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.order-stat-card-title i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.order-stat-card-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.order-stat-card-subtitle {
    font-size: 0.9rem;
    color: var(--gray-color);
    margin-bottom: 15px;
}

.order-stat-card-trend {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
    margin-top: auto;
}

.order-stat-card-trend.up {
    color: var(--success-color);
}

.order-stat-card-trend.down {
    color: var(--danger-color);
}

.order-stat-card-trend.neutral {
    color: var(--info-color);
}

/* 订单图表容器 */
.order-chart-section {
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 10px var(--shadow-color);
    padding: 20px;
    margin-bottom: 30px;
}

.order-chart-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.order-chart-section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.order-chart-section-title i {
    color: var(--primary-color);
    font-size: 1.3rem;
}

.order-chart-container {
    height: 350px;
    position: relative;
}

/* 订单筛选器样式 */
.order-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0;
    white-space: nowrap;
}

.filter-select {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: var(--white-color);
    font-size: 0.9rem;
    color: var(--dark-color);
    transition: all 0.3s ease;
    min-width: 120px;
}

.filter-select:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.2);
}

/* 订单导出按钮样式 */
.order-export {
    margin-left: auto;
}

.order-export .dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    font-size: 0.9rem;
}

.order-export .dropdown-menu {
    min-width: 150px;
    padding: 5px 0;
    font-size: 0.9rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.order-export .dropdown-item {
    padding: 8px 15px;
    transition: all 0.2s ease;
}

.order-export .dropdown-item:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1);
}

/* 订单状态标签 */
.order-status {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.order-status.completed {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.order-status.processing {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.order-status.pending {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

.order-status.cancelled {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.order-status.refunded {
    background-color: rgba(108, 117, 125, 0.1);
    color: var(--gray-color);
}

/* 订单详情弹窗 */
.order-detail-modal .modal-content {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.order-detail-modal .modal-header {
    background-color: var(--primary-color);
    color: var(--white-color);
    padding: 15px 20px;
}

.order-detail-modal .modal-body {
    padding: 20px;
}

.order-detail-modal .modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
}

.order-detail-info {
    margin-bottom: 20px;
}

.order-detail-info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.order-detail-info-item:last-child {
    border-bottom: none;
}

.order-detail-info-label {
    font-weight: 500;
    color: var(--dark-color);
}

.order-detail-info-value {
    color: var(--gray-color);
}

.order-detail-products {
    margin-bottom: 20px;
}

.order-detail-product {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.order-detail-product:last-child {
    border-bottom: none;
}

.order-detail-product-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
}

.order-detail-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.order-detail-product-info {
    flex: 1;
}

.order-detail-product-name {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 5px;
}

.order-detail-product-price {
    color: var(--primary-color);
    font-weight: 500;
}

.order-detail-product-quantity {
    color: var(--gray-color);
    font-size: 0.9rem;
}

.order-detail-total {
    display: flex;
    justify-content: space-between;
    font-weight: 600;
    color: var(--dark-color);
    padding-top: 15px;
    border-top: 1px solid #eee;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .order-statistics-container {
        grid-template-columns: 1fr;
    }
    
    .order-filters {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .filter-group {
        width: 100%;
    }
    
    .order-export {
        margin-left: 0;
        margin-top: 10px;
    }
    
    .order-chart-container {
        height: 300px;
    }
}
