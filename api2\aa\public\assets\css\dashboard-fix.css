/**
 * 控制面板页面修复样式
 * 修复服务信息板块的排版问题
 */

/* 统计项样式修复 */
.stat-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 12px 15px;
    margin-bottom: 10px;
    background-color: rgba(var(--primary-color-rgb), 0.03);
    border-radius: 6px;
    transition: all 0.3s ease;
    overflow: hidden;
    text-align: center;
    min-width: 0;
    height: 120px; /* 固定高度 */
}

.stat-label {
    font-weight: 500;
    color: var(--text-muted);
    margin-bottom: 8px;
    line-height: 1.4;
    font-size: 0.9em;
    border-bottom: 1px dashed rgba(var(--primary-color-rgb), 0.2);
    padding-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    text-align: center;
    display: block;
}

.stat-value {
    font-weight: 600;
    color: var(--text-color);
    line-height: 1.4;
    font-size: 1.1em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    text-align: center;
    display: block;
}

.stat-value-with-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    flex-grow: 1;
    height: 100%;
}

.stat-value-with-btn .stat-value {
    margin-bottom: 8px;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
}

.stat-value-with-btn .btn {
    white-space: nowrap;
    font-size: 0.9em;
    padding: 4px 12px;
    margin-top: 2px;
}

/* 多行值样式修复 */
.stat-value-multiline {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    flex-grow: 1;
    height: 100%;
}

.stat-value-multiline .stat-value {
    margin-bottom: 4px;
    width: 100%;
    white-space: normal;
    word-break: break-word;
    line-height: 1.3;
    display: block;
}

.stat-value-multiline .stat-value:last-child {
    margin-bottom: 0;
}

/* 时间显示样式修复 */
#current-time {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: var(--primary-color);
    margin-top: 0;
    display: block;
}

/* 确保所有统计项内容垂直居中 */
.stat-grid {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 15px;
    width: 100%;
}

/* 服务信息板块特殊样式 */
.stat-card:nth-child(3) .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
}

.stat-card:nth-child(3) .stat-value-with-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    flex-grow: 1;
    height: 70px; /* 固定高度，确保按钮位置一致 */
}

.stat-card:nth-child(3) .stat-value-with-btn .stat-value {
    margin-bottom: 8px;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
}

.stat-card:nth-child(3) .stat-value-with-btn .btn {
    white-space: nowrap;
    font-size: 0.9em;
    padding: 4px 12px;
    margin-top: 2px;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .stat-item {
        height: auto;
        min-height: 120px;
    }

    .stat-grid {
        grid-template-columns: 1fr;
    }

    .stat-label {
        font-size: 0.85em;
        margin-bottom: 6px;
    }

    .stat-value {
        font-size: 1em;
    }

    .stat-value-with-btn .btn {
        padding: 3px 10px;
        font-size: 0.8em;
        margin-top: 5px;
    }

    .stat-card:nth-child(3) .stat-value-with-btn {
        height: auto;
        min-height: 70px;
    }
}
