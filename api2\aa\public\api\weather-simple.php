<?php
// 简化的天气API测试

// 关闭错误显示
error_reporting(0);
ini_set('display_errors', 0);

// 设置响应头
header('Content-Type: application/json');

try {
    $city = $_GET['city'] ?? '北京';
    
    // 返回模拟数据
    $weatherData = [
        'temperature' => rand(15, 30),
        'condition' => ['晴', '多云', '阴', '小雨'][rand(0, 3)],
        'humidity' => rand(40, 80),
        'wind_speed' => rand(1, 5)
    ];
    
    echo json_encode([
        'success' => true,
        'data' => $weatherData,
        'city' => $city,
        'provider' => 'test'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'data' => [
            'temperature' => 20,
            'condition' => '未知',
            'humidity' => 50,
            'wind_speed' => 2
        ],
        'city' => '北京'
    ]);
}
?>
