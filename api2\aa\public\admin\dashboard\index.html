﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员仪表盘 - 情侣头像匹配系统</title>
    <style>
        body {
            font-family: "Microsoft YaHei", sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .dashboard-container {
            display: flex;
            min-height: 100vh;
        }
        
        .sidebar {
            width: 250px;
            background: linear-gradient(135deg, #ff6b95 0%, #ffa5c0 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            position: fixed;
            height: 100%;
            overflow-y: auto;
        }
        
        .logo-container {
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .logo {
            width: 80px;
            height: 80px;
            margin-bottom: 10px;
        }
        
        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .sidebar-menu li {
            margin-bottom: 5px;
        }
        
        .sidebar-menu a {
            display: block;
            padding: 12px 20px;
            color: white;
            text-decoration: none;
            transition: all 0.3s;
            border-left: 4px solid transparent;
        }
        
        .sidebar-menu a:hover, .sidebar-menu a.active {
            background-color: rgba(255,255,255,0.1);
            border-left-color: white;
        }
        
        .sidebar-menu a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            flex: 1;
            padding: 20px;
            margin-left: 250px;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            color: #333;
        }
        
        .user-info {
            display: flex;
            align-items: center;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #ff6b95;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .user-name {
            font-weight: 500;
        }
        
        .dropdown {
            position: relative;
            display: inline-block;
        }
        
        .dropdown-content {
            display: none;
            position: absolute;
            right: 0;
            background-color: white;
            min-width: 160px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.1);
            z-index: 1;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .dropdown:hover .dropdown-content {
            display: block;
        }
        
        .dropdown-content a {
            color: #333;
            padding: 12px 16px;
            text-decoration: none;
            display: block;
            transition: all 0.3s;
        }
        
        .dropdown-content a:hover {
            background-color: #f5f7fa;
        }
        
        .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            transition: all 0.3s;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .card-title {
            margin: 0;
            font-size: 18px;
            color: #333;
        }
        
        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ff6b95 0%, #ffa5c0 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
        
        .card-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .card-description {
            color: #777;
            font-size: 14px;
        }
        
        .recent-activity {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
        }
        
        .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .activity-title {
            margin: 0;
            font-size: 18px;
            color: #333;
        }
        
        .activity-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .activity-item {
            padding: 15px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #f5f7fa;
            color: #ff6b95;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            margin: 0 0 5px 0;
            font-size: 16px;
            color: #333;
        }
        
        .activity-time {
            color: #777;
            font-size: 12px;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.9);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }
        
        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f5f7fa;
            border-top: 5px solid #ff6b95;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="loading-overlay" id="loading-overlay">
        <div class="spinner"></div>
        <h2>正在加载仪表盘...</h2>
        <p>请稍候，系统正在准备您的管理界面</p>
    </div>

    <div class="dashboard-container">
        <div class="sidebar">
            <div class="logo-container">
                <img src="/assets/images/logo.png" alt="Logo" class="logo" onerror="this.src='data:image/svg+xml;utf8,%3Csvg xmlns=\\\'http://www.w3.org/2000/svg\\\' viewBox=\\\'0 0 24 24\\\' fill=\\\'%23ffffff\\\'%3E%3Cpath d=\\\'M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\\\'/%3E%3C/svg%3E'">
                <h3>情侣头像匹配系统</h3>
                <p>管理员控制台</p>
            </div>
            <ul class="sidebar-menu">
                <li><a href="#" class="active"><i>📊</i> 仪表盘</a></li>
                <li><a href="#"><i>👥</i> 用户管理</a></li>
                <li><a href="#"><i>🖼️</i> 头像管理</a></li>
                <li><a href="#"><i>💰</i> 订单管理</a></li>
                <li><a href="#"><i>📱</i> 平台管理</a></li>
                <li><a href="#"><i>⚙️</i> 系统设置</a></li>
                <li><a href="/admin/logout"><i>🚪</i> 退出登录</a></li>
            </ul>
        </div>
        
        <div class="main-content">
            <div class="header">
                <h1>管理员仪表盘</h1>
                <div class="user-info dropdown">
                    <div class="user-avatar">A</div>
                    <span class="user-name">管理员</span>
                    <div class="dropdown-content">
                        <a href="#">个人资料</a>
                        <a href="#">修改密码</a>
                        <a href="/admin/logout">退出登录</a>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-cards">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">总用户数</h3>
                        <div class="card-icon">👥</div>
                    </div>
                    <div class="card-value">1,234</div>
                    <div class="card-description">较上月增长 5.2%</div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">总订单数</h3>
                        <div class="card-icon">📝</div>
                    </div>
                    <div class="card-value">567</div>
                    <div class="card-description">较上月增长 3.7%</div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">总收入</h3>
                        <div class="card-icon">💰</div>
                    </div>
                    <div class="card-value">12,345</div>
                    <div class="card-description">较上月增长 7.8%</div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">头像数量</h3>
                        <div class="card-icon">🖼️</div>
                    </div>
                    <div class="card-value">890</div>
                    <div class="card-description">较上月增长 2.5%</div>
                </div>
            </div>
            
            <div class="recent-activity">
                <div class="activity-header">
                    <h3 class="activity-title">最近活动</h3>
                </div>
                <ul class="activity-list">
                    <li class="activity-item">
                        <div class="activity-icon">👤</div>
                        <div class="activity-content">
                            <h4 class="activity-title">新用户注册</h4>
                            <p class="activity-description">用户 "张三" 完成了注册</p>
                            <span class="activity-time">10 分钟前</span>
                        </div>
                    </li>
                    <li class="activity-item">
                        <div class="activity-icon">💰</div>
                        <div class="activity-content">
                            <h4 class="activity-title">新订单</h4>
                            <p class="activity-description">用户 "李四" 购买了高级套餐</p>
                            <span class="activity-time">30 分钟前</span>
                        </div>
                    </li>
                    <li class="activity-item">
                        <div class="activity-icon">🖼️</div>
                        <div class="activity-content">
                            <h4 class="activity-title">新头像上传</h4>
                            <p class="activity-description">管理员 "王五" 上传了 10 张新头像</p>
                            <span class="activity-time">1 小时前</span>
                        </div>
                    </li>
                    <li class="activity-item">
                        <div class="activity-icon">⚙️</div>
                        <div class="activity-content">
                            <h4 class="activity-title">系统更新</h4>
                            <p class="activity-description">系统完成了自动更新</p>
                            <span class="activity-time">2 小时前</span>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后隐藏加载动画
        window.addEventListener('load', function() {
            setTimeout(function() {
                document.getElementById('loading-overlay').style.display = 'none';
            }, 1000); // 延迟 1 秒后隐藏，让用户看到加载动画
        });
    </script>
</body>
</html>