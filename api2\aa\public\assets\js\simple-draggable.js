/**
 * 简单拖动功能
 * 使任何元素可以拖动到页面任意位置
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('简单拖动功能已加载');
    
    // 获取主题颜色修改图标
    const draggableElements = document.querySelectorAll('.theme-settings-toggle');
    
    if (draggableElements.length === 0) {
        console.log('未找到可拖动元素');
        return;
    }
    
    console.log('找到', draggableElements.length, '个可拖动元素');
    
    // 为每个元素添加拖动功能
    draggableElements.forEach(element => {
        makeDraggable(element);
    });
    
    // 从localStorage加载上次保存的位置
    const savedPosition = localStorage.getItem('themeColorIconPosition');
    if (savedPosition && draggableElements[0]) {
        try {
            const position = JSON.parse(savedPosition);
            const element = draggableElements[0];
            
            // 设置元素为固定定位
            element.style.position = 'fixed';
            
            // 设置位置
            if (position.left) element.style.left = position.left;
            if (position.top) element.style.top = position.top;
            
            // 如果设置了左侧位置，则取消右侧定位
            if (position.left) element.style.right = 'auto';
            
            console.log('已加载保存的位置:', position);
        } catch (error) {
            console.error('加载位置失败:', error);
        }
    } else {
        console.log('没有保存的位置，使用默认位置');
    }
});

/**
 * 使元素可拖动
 * @param {HTMLElement} element - 要使其可拖动的元素
 */
function makeDraggable(element) {
    let isDragging = false;
    let startX, startY;
    let initialLeft, initialTop;
    
    // 设置元素样式
    element.style.position = 'fixed';
    element.style.cursor = 'move';
    element.style.zIndex = '10000';
    
    // 鼠标按下事件
    element.addEventListener('mousedown', function(e) {
        // 阻止默认行为和冒泡
        e.preventDefault();
        e.stopPropagation();
        
        // 开始拖动
        isDragging = true;
        
        // 记录初始位置
        startX = e.clientX;
        startY = e.clientY;
        
        // 获取元素当前位置
        const rect = element.getBoundingClientRect();
        initialLeft = rect.left;
        initialTop = rect.top;
        
        // 添加拖动样式
        element.classList.add('dragging');
        
        console.log('开始拖动', initialLeft, initialTop);
    });
    
    // 鼠标移动事件
    document.addEventListener('mousemove', function(e) {
        if (!isDragging) return;
        
        // 计算新位置
        const deltaX = e.clientX - startX;
        const deltaY = e.clientY - startY;
        
        let newLeft = initialLeft + deltaX;
        let newTop = initialTop + deltaY;
        
        // 确保不超出视口
        const maxLeft = window.innerWidth - element.offsetWidth;
        const maxTop = window.innerHeight - element.offsetHeight;
        
        newLeft = Math.max(0, Math.min(newLeft, maxLeft));
        newTop = Math.max(0, Math.min(newTop, maxTop));
        
        // 设置新位置
        element.style.left = newLeft + 'px';
        element.style.top = newTop + 'px';
        element.style.right = 'auto';
        element.style.bottom = 'auto';
    });
    
    // 鼠标松开事件
    document.addEventListener('mouseup', function() {
        if (!isDragging) return;
        
        // 结束拖动
        isDragging = false;
        
        // 移除拖动样式
        element.classList.remove('dragging');
        
        // 保存位置
        const position = {
            left: element.style.left,
            top: element.style.top
        };
        
        localStorage.setItem('themeColorIconPosition', JSON.stringify(position));
        console.log('保存位置:', position);
    });
    
    // 触摸事件支持
    element.addEventListener('touchstart', function(e) {
        if (e.touches.length !== 1) return;
        
        // 阻止默认行为和冒泡
        e.preventDefault();
        e.stopPropagation();
        
        // 开始拖动
        isDragging = true;
        
        // 记录初始位置
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
        
        // 获取元素当前位置
        const rect = element.getBoundingClientRect();
        initialLeft = rect.left;
        initialTop = rect.top;
        
        // 添加拖动样式
        element.classList.add('dragging');
    });
    
    document.addEventListener('touchmove', function(e) {
        if (!isDragging || e.touches.length !== 1) return;
        
        // 计算新位置
        const deltaX = e.touches[0].clientX - startX;
        const deltaY = e.touches[0].clientY - startY;
        
        let newLeft = initialLeft + deltaX;
        let newTop = initialTop + deltaY;
        
        // 确保不超出视口
        const maxLeft = window.innerWidth - element.offsetWidth;
        const maxTop = window.innerHeight - element.offsetHeight;
        
        newLeft = Math.max(0, Math.min(newLeft, maxLeft));
        newTop = Math.max(0, Math.min(newTop, maxTop));
        
        // 设置新位置
        element.style.left = newLeft + 'px';
        element.style.top = newTop + 'px';
        element.style.right = 'auto';
        element.style.bottom = 'auto';
    });
    
    document.addEventListener('touchend', function() {
        if (!isDragging) return;
        
        // 结束拖动
        isDragging = false;
        
        // 移除拖动样式
        element.classList.remove('dragging');
        
        // 保存位置
        const position = {
            left: element.style.left,
            top: element.style.top
        };
        
        localStorage.setItem('themeColorIconPosition', JSON.stringify(position));
    });
}
