<?php

/**
 * Migration for creating the avatars table
 * This table stores avatar information with tenant isolation
 */
class CreateAvatarsTable
{
    /**
     * Run the migration
     */
    public function up()
    {
        $sql = "CREATE TABLE IF NOT EXISTS `avatars` (
            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            `tenant_id` bigint(20) UNSIGNED NOT NULL COMMENT 'Reference to tenants table',
            `category_id` int(11) UNSIGNED NOT NULL COMMENT 'Reference to avatar_categories table',
            `title` varchar(64) NOT NULL COMMENT 'Avatar title',
            `image` varchar(255) NOT NULL COMMENT 'Original image URL',
            `thumb` varchar(255) NOT NULL COMMENT 'Thumbnail image URL',
            `is_vip` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=free, 1=VIP only',
            `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '0=inactive, 1=active',
            `match_count` int(11) NOT NULL DEFAULT 0 COMMENT 'Total match count',
            `download_count` int(11) NOT NULL DEFAULT 0 COMMENT 'Total download count',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `tenant_id` (`tenant_id`),
            KEY `category_id` (`category_id`),
            KEY `is_vip` (`is_vip`),
            CONSTRAINT `avatars_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE,
            CONSTRAINT `avatars_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `avatar_categories` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        
        return $sql;
    }

    /**
     * Reverse the migration
     */
    public function down()
    {
        return "DROP TABLE IF EXISTS `avatars`;";
    }
}
