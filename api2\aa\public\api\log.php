<?php
// 设置响应头
header('Content-Type: application/json');

// 创建日志目录
$logDir = $_SERVER['DOCUMENT_ROOT'] . '/storage/logs';
if (!is_dir($logDir)) {
    mkdir($logDir, 0777, true);
}

// 获取请求数据
$action = isset($_POST['action']) ? $_POST['action'] : '';
$platformId = isset($_POST['platform_id']) ? $_POST['platform_id'] : '';
$userId = isset($_POST['user_id']) ? $_POST['user_id'] : '';
$ip = $_SERVER['REMOTE_ADDR'];
$userAgent = $_SERVER['HTTP_USER_AGENT'];
$timestamp = date('Y-m-d H:i:s');

// 日志文件路径
$logFile = $logDir . '/system.log';

// 构建日志消息
$logMessage = "[{$timestamp}] IP: {$ip} | Action: {$action}";

if ($platformId) {
    $logMessage .= " | Platform ID: {$platformId}";
}

if ($userId) {
    $logMessage .= " | User ID: {$userId}";
}

$logMessage .= " | User Agent: {$userAgent}\n";

// 写入日志
file_put_contents($logFile, $logMessage, FILE_APPEND);

// 根据操作类型记录特定日志
switch ($action) {
    case 'enter_platform':
        $platformLogFile = $logDir . '/platform_access.log';
        $platformLogMessage = "[{$timestamp}] Platform ID: {$platformId} | IP: {$ip}\n";
        file_put_contents($platformLogFile, $platformLogMessage, FILE_APPEND);
        break;
        
    case 'user_login':
        $loginLogFile = $logDir . '/login.log';
        $loginLogMessage = "[{$timestamp}] User ID: {$userId} | IP: {$ip}\n";
        file_put_contents($loginLogFile, $loginLogMessage, FILE_APPEND);
        break;
        
    case 'system_error':
        $errorLogFile = $logDir . '/error.log';
        $errorMessage = isset($_POST['error_message']) ? $_POST['error_message'] : 'Unknown error';
        $errorLogMessage = "[{$timestamp}] Error: {$errorMessage} | IP: {$ip}\n";
        file_put_contents($errorLogFile, $errorLogMessage, FILE_APPEND);
        break;
}

// 返回成功响应
echo json_encode([
    'status' => 'success',
    'message' => 'Log recorded successfully',
    'timestamp' => $timestamp
]);
