/**
 * 模态框精确修复脚本
 * 只阻止点击空白处关闭模态框，不影响其他功能
 */

// 立即执行函数，避免变量污染全局作用域
(function() {
    // 等待页面加载完成
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
        // 如果页面已经加载完成，立即执行
        fixModals();
    } else {
        // 否则等待页面加载完成
        document.addEventListener('DOMContentLoaded', fixModals);
    }
    
    // 为了确保在所有脚本加载后执行，也添加window.onload事件
    window.addEventListener('load', fixModals);
    
    function fixModals() {
        console.log('模态框精确修复脚本已加载');
        
        // 移除window的点击事件
        const originalWindowOnclick = window.onclick;
        window.onclick = function(event) {
            // 如果点击的是模态框，阻止默认行为
            if (event.target.classList.contains('modal')) {
                event.stopPropagation();
                event.preventDefault();
                console.log('点击了模态框空白处，阻止关闭');
                return false;
            }
            
            // 否则执行原来的点击事件
            if (originalWindowOnclick) {
                return originalWindowOnclick(event);
            }
        };
        
        // 获取所有模态框
        const modals = document.querySelectorAll('.modal');
        
        // 为每个模态框添加点击事件（使用捕获阶段）
        modals.forEach(function(modal) {
            // 添加捕获阶段的点击事件处理
            modal.addEventListener('click', function(event) {
                // 如果点击的是模态框本身（空白区域），阻止事件传播
                if (event.target === this) {
                    event.stopPropagation();
                    event.preventDefault();
                    console.log('点击了模态框空白处，阻止关闭');
                    return false;
                }
            }, true); // true表示在捕获阶段处理事件
        });
        
        // 监听DOM变化，处理动态添加的模态框
        setupMutationObserver();
        
        // 重写模态框打开函数，确保新打开的模态框也有这个行为
        const originalOpenModal = window.openModal;
        window.openModal = function(modalId) {
            console.log('打开模态框：', modalId);
            
            // 调用原始的openModal函数
            if (originalOpenModal) {
                originalOpenModal(modalId);
            } else {
                // 如果没有原始函数，使用默认实现
                const modal = document.getElementById(modalId);
                if (modal) {
                    modal.style.display = 'block';
                    document.body.style.overflow = 'hidden';
                }
            }
            
            // 确保模态框有点击事件处理
            const modal = document.getElementById(modalId);
            if (modal) {
                // 移除之前的事件处理程序（如果有）
                const newModal = modal.cloneNode(false); // 只克隆节点本身，不克隆子节点
                
                // 复制所有子节点
                while (modal.firstChild) {
                    newModal.appendChild(modal.firstChild);
                }
                
                // 替换原始节点
                if (modal.parentNode) {
                    modal.parentNode.replaceChild(newModal, modal);
                }
                
                // 添加点击事件处理
                newModal.addEventListener('click', function(event) {
                    // 如果点击的是模态框本身（空白区域），阻止事件传播
                    if (event.target === this) {
                        event.stopPropagation();
                        event.preventDefault();
                        console.log('点击了模态框空白处，阻止关闭');
                        return false;
                    }
                }, true); // true表示在捕获阶段处理事件
            }
        };
    }
    
    /**
     * 设置MutationObserver监听DOM变化
     */
    function setupMutationObserver() {
        // 创建一个观察器实例
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // 检查是否有新的模态框被添加
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    mutation.addedNodes.forEach(function(node) {
                        // 检查添加的节点是否是模态框
                        if (node.nodeType === 1 && node.classList && node.classList.contains('modal')) {
                            console.log('检测到新的模态框被添加:', node.id);
                            // 修复这个新的模态框
                            fixModal(node);
                        }
                        
                        // 检查添加的节点内部是否包含模态框
                        if (node.nodeType === 1 && node.querySelectorAll) {
                            const modals = node.querySelectorAll('.modal');
                            if (modals.length > 0) {
                                console.log('检测到新的模态框被添加到DOM中:', modals.length, '个');
                                modals.forEach(modal => fixModal(modal));
                            }
                        }
                    });
                }
            });
        });
        
        // 配置观察选项
        const config = {
            childList: true, // 观察目标子节点的变化
            subtree: true    // 观察所有后代节点的变化
        };
        
        // 开始观察document.body的变化
        observer.observe(document.body, config);
        
        console.log('已设置MutationObserver监听DOM变化');
    }
    
    /**
     * 修复单个模态框
     */
    function fixModal(modal) {
        if (!modal) return;
        
        // 添加捕获阶段的点击事件处理
        modal.addEventListener('click', function(event) {
            // 如果点击的是模态框本身（空白区域），阻止事件传播
            if (event.target === this) {
                event.stopPropagation();
                event.preventDefault();
                console.log('点击了模态框空白处，阻止关闭');
                return false;
            }
        }, true); // true表示在捕获阶段处理事件
        
        console.log('已修复模态框:', modal.id);
    }
})();
