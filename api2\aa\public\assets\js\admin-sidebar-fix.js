/**
 * 控制面板页面侧边栏修复脚本
 * 修复控制面板页面侧边栏切换按钮不起作用的问题
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('控制面板页面侧边栏修复脚本已加载');
    
    // 获取DOM元素
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');
    const menuToggle = document.getElementById('menu-toggle');
    
    if (sidebar && mainContent && menuToggle) {
        console.log('找到侧边栏元素');
        
        // 移除所有现有的点击事件
        const oldMenuToggle = menuToggle.cloneNode(true);
        menuToggle.parentNode.replaceChild(oldMenuToggle, menuToggle);
        
        // 添加新的点击事件
        oldMenuToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('侧边栏切换按钮被点击');
            
            // 切换侧边栏状态
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
            
            // 保存状态到本地存储
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
            console.log('侧边栏状态已保存:', sidebar.classList.contains('collapsed'));
        });
        
        // 从本地存储中恢复状态
        const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        console.log('从本地存储中恢复侧边栏状态:', sidebarCollapsed);
        
        if (sidebarCollapsed) {
            sidebar.classList.add('collapsed');
            mainContent.classList.add('expanded');
        } else {
            sidebar.classList.remove('collapsed');
            mainContent.classList.remove('expanded');
        }
        
        console.log('侧边栏切换已修复');
    } else {
        console.error('未找到侧边栏元素');
    }
});
