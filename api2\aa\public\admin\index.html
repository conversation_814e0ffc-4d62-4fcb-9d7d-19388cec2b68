﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员面板 - 情侣头像匹配系统</title>
    <style>
        body {
            font-family: "Microsoft YaHei", sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            color: #333;
        }
        
        .loading-spinner {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            animation: fadeIn 0.5s;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .spinner {
            width: 80px;
            height: 80px;
            border: 5px solid rgba(255, 107, 149, 0.3);
            border-radius: 50%;
            border-top-color: #ff6b95;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        h4 {
            margin: 0 0 10px 0;
            color: #ff6b95;
        }
        
        p {
            margin: 0;
            color: #666;
        }
        
        .hearts {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .heart {
            position: absolute;
            width: 20px;
            height: 20px;
            background: url("data:image/svg+xml;utf8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'%23ff6b95\'%3E%3Cpath d=\'M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\'/%3E%3C/svg%3E") no-repeat center center;
            opacity: 0;
            animation: float 4s linear infinite;
        }
        
        @keyframes float {
            0% {
                transform: translateY(100vh) scale(0);
                opacity: 0;
            }
            20% {
                opacity: 0.8;
            }
            80% {
                opacity: 0.8;
            }
            100% {
                transform: translateY(-20vh) scale(1.5);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="hearts"></div>
    
    <div class="loading-spinner">
        <div class="spinner"></div>
        <h4>正在加载管理员面板...</h4>
        <p>请稍候，系统正在准备您的管理界面</p>
    </div>

    <script>
        // 创建漂浮的心形元素
        function createFloatingHearts() {
            const container = document.querySelector(".hearts");
            const heartCount = 15;
            
            for (let i = 0; i < heartCount; i++) {
                const heart = document.createElement("div");
                heart.classList.add("heart");
                heart.style.left = Math.random() * 100 + "vw";
                heart.style.animationDelay = Math.random() * 5 + "s";
                heart.style.opacity = Math.random() * 0.5 + 0.5;
                heart.style.transform = `scale(${Math.random() * 0.5 + 0.5})`;
                container.appendChild(heart);
            }
        }
        
        // 页面加载完成后执行
        document.addEventListener("DOMContentLoaded", function() {
            // 创建漂浮的心形
            createFloatingHearts();
            
            // 添加加载音效
            try {
                const audio = new Audio("data:audio/mpeg;base64,SUQzBAAAAAABEVRYWFgAAAAtAAADY29tbWVudABCaWdTb3VuZEJhbmsuY29tIC8gTGFzb25pY1N0dWRpb3MuY29tAFRYWFgAAAAhAAADZW5naW5lZXIAQmlnU291bmRCYW5rLmNvbQBUWFhYAAAAGwAAA3NvZnR3YXJlAExhdmY1OC43Ni4xMDAAAFRDT04AAAAbAAADZW5jb2RlZCBieQBMYXZmNTguNzYuMTAwAAAAAAAAAAAAAAD/+1AAAAA8YXVkaW8vbXBlZwAAAAAAAAAAAABUSVQyAAAAGQAAAzIwMjMtMDUtMDZUMTc6MjM6MTgAAAAAAAAAAAAA//tQxAADwAABpAAAACAAADSAAAAETEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//tQxBYDwAABpAAAACAAADSAAAAEVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//tQxBcAAAANIAAAAAA");
                audio.volume = 0.3;
                audio.play();
            } catch (e) {
                console.log("音频播放失败", e);
            }
            
            // 检查用户是否已登录
            fetch('/admin/check-auth')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0 && data.data && data.data.authenticated) {
                        // 已登录，加载管理面板
                        window.location.href = "/admin/dashboard";
                    } else {
                        // 未登录，跳转到登录页面
                        window.location.href = "/admin/login";
                    }
                })
                .catch(error => {
                    console.error('检查登录状态失败:', error);
                    // 出错时默认跳转到登录页面
                    window.location.href = "/admin/login";
                });
        });
    </script>
</body>
</html>