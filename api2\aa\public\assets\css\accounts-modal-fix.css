/**
 * 账户管理页面模态框样式改进
 * 修改模态框布局为一行两个输入项，调整取消按钮颜色
 */

/* 模态框样式修复 */
.modal {
    display: none;
    position: fixed;
    z-index: 1050;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5) !important;
}

.modal-content {
    background-color: #ffffff !important;
    margin: 5% auto;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    width: 80%;
    max-width: 800px;
    position: relative;
    animation: fadeInDown 0.3s;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
    margin-bottom: 15px;
    background-color: #ffffff !important;
}

.modal-header h3 {
    margin: 0;
    color: var(--dark-color);
    font-size: 1.2rem;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--gray-color);
    transition: color 0.3s;
}

.close-btn:hover {
    color: var(--danger-color);
}

.modal-body {
    margin-bottom: 20px;
    background-color: #ffffff !important;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding-top: 15px;
    border-top: 1px solid #eee;
    background-color: #ffffff !important;
}

/* 表单样式改进 */
.form-row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -10px;
    margin-left: -10px;
}

.form-col {
    flex: 0 0 50%;
    max-width: 50%;
    padding-right: 10px;
    padding-left: 10px;
    box-sizing: border-box;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: var(--dark-color);
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group input[type="tel"],
.form-group input[type="date"],
.form-group input[type="number"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s;
    background-color: #ffffff !important;
    color: var(--dark-color) !important;
}

.form-group input[type="text"]:focus,
.form-group input[type="email"]:focus,
.form-group input[type="password"]:focus,
.form-group input[type="tel"]:focus,
.form-group input[type="date"]:focus,
.form-group input[type="number"]:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-color);
    outline: none;
}

.form-group.checkbox {
    display: flex;
    align-items: center;
}

.form-group.checkbox input[type="checkbox"] {
    margin-right: 10px;
}

.form-group.checkbox label {
    margin-bottom: 0;
}

.required {
    color: var(--danger-color);
}

/* 禁用状态 */
.form-group input[disabled],
.form-group select[disabled],
.form-group textarea[disabled] {
    background-color: #f5f5f5 !important;
    cursor: not-allowed;
    opacity: 0.7;
}

/* 按钮样式修复 */
.btn-primary {
    color: #ffffff !important;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
    border-color: var(--primary-color) !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #ff4f7e 0%, #ffa5c0 100%) !important;
    border-color: #ff4f7e !important;
}

.btn-secondary {
    color: #ffffff !important;
    background-color: #6c757d !important;
    border-color: #6c757d !important;
}

.btn-secondary:hover {
    background-color: #5a6268 !important;
    border-color: #545b62 !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .form-col {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}
