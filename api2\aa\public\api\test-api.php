<?php
// 简单的API测试脚本

header('Content-Type: application/json');

$provider = $_GET['provider'] ?? 'openweather';
$city = $_GET['city'] ?? '北京';
$apiKey = $_GET['key'] ?? '';

if (empty($apiKey)) {
    echo json_encode(['error' => 'API密钥为空', 'provider' => $provider]);
    exit;
}

echo json_encode(['message' => '开始测试', 'provider' => $provider, 'city' => $city, 'key_length' => strlen($apiKey)]);

switch ($provider) {
    case 'openweather':
        $url = "https://api.openweathermap.org/data/2.5/weather?q=" . urlencode($city) . "&appid=" . $apiKey . "&units=metric&lang=zh_cn";
        break;
    case 'heweather':
        $url = "https://devapi.qweather.com/v7/weather/now?location=" . urlencode($city) . "&key=" . $apiKey;
        break;
    case 'seniverse':
        $url = "https://api.seniverse.com/v3/weather/now.json?key=" . $apiKey . "&location=" . urlencode($city) . "&language=zh-Hans&unit=c";
        break;
    default:
        echo json_encode(['error' => '未知服务商']);
        exit;
}

echo "\n" . json_encode(['url' => $url]);

// 测试cURL
if (function_exists('curl_init')) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Weather App/1.0');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "\n" . json_encode([
        'curl_result' => $response !== false,
        'http_code' => $httpCode,
        'error' => $error,
        'response_length' => $response ? strlen($response) : 0,
        'response' => $response ? json_decode($response, true) : null
    ]);
} else {
    echo "\n" . json_encode(['error' => 'cURL不可用']);
}
?>
