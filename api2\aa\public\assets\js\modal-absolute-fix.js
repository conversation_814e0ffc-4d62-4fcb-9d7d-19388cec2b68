/**
 * 模态框绝对修复脚本
 * 彻底修复所有页面模态框点击空白处关闭的问题
 */

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('模态框绝对修复脚本已加载');
    
    // 重写openModal函数
    window.originalOpenModal = window.openModal;
    
    window.openModal = function(modalId) {
        console.log('打开模态框：', modalId);
        const modal = document.getElementById(modalId);
        if (modal) {
            // 移除原有的点击事件
            modal.onclick = null;
            
            // 添加新的点击事件，阻止冒泡
            modal.addEventListener('click', function(e) {
                // 如果点击的是模态框本身，不做任何操作
                if (e.target === this) {
                    e.stopPropagation();
                    console.log('点击了模态框空白处，阻止关闭');
                }
            });
            
            // 显示模态框
            modal.style.display = 'block';
        }
    };
    
    // 重写closeModal函数
    window.originalCloseModal = window.closeModal;
    
    window.closeModal = function(modalId) {
        console.log('关闭模态框：', modalId);
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'none';
        }
    };
    
    // 修复所有模态框
    fixAllModals();
    
    // 修复所有模态框按钮
    fixAllModalButtons();
});

// 修复所有模态框
function fixAllModals() {
    // 获取所有模态框
    const modals = document.querySelectorAll('.modal');
    
    if (modals.length > 0) {
        console.log('找到模态框：', modals.length, '个');
        
        // 为每个模态框添加点击事件
        modals.forEach(modal => {
            // 移除原有的点击事件
            modal.onclick = null;
            
            // 添加新的点击事件
            modal.addEventListener('click', function(e) {
                // 如果点击的是模态框本身，不做任何操作
                if (e.target === this) {
                    e.stopPropagation();
                    console.log('点击了模态框空白处，阻止关闭');
                }
            });
        });
    } else {
        console.log('未找到模态框');
    }
}

// 修复所有模态框按钮
function fixAllModalButtons() {
    // 修复关闭按钮点击事件
    const closeButtons = document.querySelectorAll('.close-btn');
    
    if (closeButtons.length > 0) {
        console.log('找到关闭按钮：', closeButtons.length, '个');
        
        closeButtons.forEach(button => {
            // 移除原有的onclick属性
            const onclickValue = button.getAttribute('onclick');
            if (onclickValue) {
                const match = onclickValue.match(/closeModal\(['"](.+)['"]\)/);
                if (match) {
                    const modalId = match[1];
                    button.removeAttribute('onclick');
                    
                    // 添加新的点击事件
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('关闭按钮被点击，模态框ID：', modalId);
                        closeModal(modalId);
                    });
                    
                    console.log('已为关闭按钮添加点击事件，模态框ID：', modalId);
                }
            }
        });
    } else {
        console.log('未找到关闭按钮');
    }
    
    // 修复模态框中的取消按钮点击事件
    const cancelButtons = document.querySelectorAll('.modal-footer .btn-secondary');
    
    if (cancelButtons.length > 0) {
        console.log('找到取消按钮：', cancelButtons.length, '个');
        
        cancelButtons.forEach(button => {
            // 移除原有的onclick属性
            const onclickValue = button.getAttribute('onclick');
            if (onclickValue) {
                const match = onclickValue.match(/closeModal\(['"](.+)['"]\)/);
                if (match) {
                    const modalId = match[1];
                    button.removeAttribute('onclick');
                    
                    // 添加新的点击事件
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('取消按钮被点击，模态框ID：', modalId);
                        closeModal(modalId);
                    });
                    
                    console.log('已为取消按钮添加点击事件，模态框ID：', modalId);
                }
            }
        });
    } else {
        console.log('未找到取消按钮');
    }
    
    // 修复模态框中的确定按钮点击事件
    const confirmButtons = document.querySelectorAll('.modal-footer .btn-primary');
    
    if (confirmButtons.length > 0) {
        console.log('找到确定按钮：', confirmButtons.length, '个');
        
        confirmButtons.forEach(button => {
            // 获取原有的onclick属性
            const onclickValue = button.getAttribute('onclick');
            if (onclickValue) {
                button.removeAttribute('onclick');
                
                // 添加新的点击事件
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('确定按钮被点击，执行：', onclickValue);
                    
                    // 执行原有的点击事件
                    eval(onclickValue);
                });
                
                console.log('已为确定按钮添加点击事件');
            }
        });
    } else {
        console.log('未找到确定按钮');
    }
}
