/**
 * 插件增强样式
 * 为插件更新通知和使用统计功能提供样式支持
 */

/* 插件更新通知 */
.update-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 350px;
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    transform: translateY(100px);
    opacity: 0;
    transition: all 0.3s ease;
    overflow: hidden;
}

.update-notification.show {
    transform: translateY(0);
    opacity: 1;
}

.update-notification-header {
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: var(--primary-color);
    color: var(--white-color);
}

.update-notification-header i {
    font-size: 1.2rem;
    margin-right: 10px;
}

.update-notification-header span {
    flex: 1;
    font-weight: 600;
}

.update-notification-header .close-btn {
    background: none;
    border: none;
    color: var(--white-color);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.update-notification-header .close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.update-notification-body {
    padding: 15px;
}

.update-notification-body p {
    margin: 0 0 10px 0;
    font-weight: 500;
}

.update-notification-body ul {
    margin: 0;
    padding: 0 0 0 20px;
}

.update-notification-body li {
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.update-notification-body .plugin-name {
    font-weight: 500;
}

.update-notification-body .version-info {
    color: var(--primary-color);
    font-size: 0.9rem;
}

.update-notification-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-top: 1px solid #eee;
}

/* 更新标记 */
.update-badge {
    display: inline-flex;
    align-items: center;
    margin-left: 10px;
    padding: 3px 8px;
    background-color: rgba(var(--primary-color-rgb), 0.1);
    color: var(--primary-color);
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.update-badge i {
    margin-right: 4px;
    font-size: 0.9rem;
}

.updated-badge {
    display: inline-flex;
    align-items: center;
    margin-left: 10px;
    padding: 3px 8px;
    background-color: rgba(var(--success-color-rgb), 0.1);
    color: var(--success-color);
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.updated-badge i {
    margin-right: 4px;
    font-size: 0.9rem;
}

.up-to-date-badge {
    display: inline-flex;
    align-items: center;
    margin-left: 10px;
    padding: 3px 8px;
    background-color: rgba(var(--info-color-rgb), 0.1);
    color: var(--info-color);
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.up-to-date-badge i {
    margin-right: 4px;
    font-size: 0.9rem;
}

/* 更新设置开关 */
.switch-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:focus + .slider {
    box-shadow: 0 0 1px var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 24px;
}

.slider.round:before {
    border-radius: 50%;
}

/* 使用统计标签 */
.usage-stats-tag {
    display: flex;
    gap: 10px;
    margin-top: 5px;
}

.usage-stats-tag span {
    display: inline-flex;
    align-items: center;
    padding: 2px 8px;
    background-color: rgba(var(--info-color-rgb), 0.1);
    color: var(--info-color);
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.usage-stats-tag i {
    margin-right: 4px;
    font-size: 0.9rem;
}

.install-count {
    background-color: rgba(var(--success-color-rgb), 0.1) !important;
    color: var(--success-color) !important;
}

.active-users {
    background-color: rgba(var(--primary-color-rgb), 0.1) !important;
    color: var(--primary-color) !important;
}

/* 统计详情模态框 */
.modal-lg {
    width: 90%;
    max-width: 900px;
}

.stats-summary {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stats-card {
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 10px var(--shadow-color);
    padding: 15px;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
}

.stats-card-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 10px;
}

.stats-card-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.stats-card-trend {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
    margin-top: auto;
}

.stats-card-trend.up {
    color: var(--success-color);
}

.stats-card-trend.down {
    color: var(--danger-color);
}

.stats-chart-container {
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 10px var(--shadow-color);
    padding: 15px;
    height: 300px;
}

.stats-chart-container h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--dark-color);
}

/* 插件操作按钮 */
.update-btn, .stats-btn {
    margin-left: 5px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .update-notification {
        width: 90%;
        max-width: 350px;
        bottom: 10px;
        right: 10px;
    }
    
    .stats-summary {
        grid-template-columns: 1fr;
    }
    
    .stats-chart-container {
        height: 250px;
        margin-bottom: 20px;
    }
    
    .modal-lg {
        width: 95%;
    }
}

/* 顶部插件图表 */
.top-plugins-chart-container, .category-distribution-chart-container {
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 10px var(--shadow-color);
    padding: 20px;
    margin-bottom: 30px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
}

.chart-container {
    height: 350px;
    position: relative;
}

/* 插件评分星级 */
.plugin-rating {
    display: flex;
    align-items: center;
    margin-top: 5px;
}

.plugin-rating .stars {
    color: #ffc107;
    font-size: 0.9rem;
    margin-right: 5px;
}

.plugin-rating .rating-value {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--dark-color);
}

.plugin-rating .rating-count {
    font-size: 0.8rem;
    color: var(--gray-color);
    margin-left: 5px;
}
