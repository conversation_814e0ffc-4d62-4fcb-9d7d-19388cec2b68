-- 修复缺失的数据库字段
-- 执行时间：2024年
-- 说明：添加系统运行所需的缺失字段

-- 检查并添加外观设置字段到information表
-- 如果字段已存在会报错，可以忽略

-- 添加网站Logo字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'yh_information'
     AND column_name = 'site_logo'
     AND table_schema = DATABASE()) > 0,
    "SELECT 'site_logo字段已存在' as message",
    "ALTER TABLE `yh_information` ADD COLUMN `site_logo` varchar(255) DEFAULT '/assets/images/logo.png' COMMENT '网站Logo路径'"
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加网站图标字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'yh_information'
     AND column_name = 'site_favicon'
     AND table_schema = DATABASE()) > 0,
    "SELECT 'site_favicon字段已存在' as message",
    "ALTER TABLE `yh_information` ADD COLUMN `site_favicon` varchar(255) DEFAULT '/assets/images/favicon.ico' COMMENT '网站图标路径'"
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加登录背景字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'yh_information'
     AND column_name = 'login_bg'
     AND table_schema = DATABASE()) > 0,
    "SELECT 'login_bg字段已存在' as message",
    "ALTER TABLE `yh_information` ADD COLUMN `login_bg` varchar(255) DEFAULT '/assets/images/login-bg.jpg' COMMENT '登录页背景图片路径'"
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加私钥字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'yh_information'
     AND column_name = 'private_key'
     AND table_schema = DATABASE()) > 0,
    "SELECT 'private_key字段已存在' as message",
    "ALTER TABLE `yh_information` ADD COLUMN `private_key` varchar(255) DEFAULT '' COMMENT '私钥配置'"
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加主题模板字段到webpz表
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'yh_webpz'
     AND column_name = 'tep'
     AND table_schema = DATABASE()) > 0,
    "SELECT 'tep字段已存在' as message",
    "ALTER TABLE `yh_webpz` ADD COLUMN `tep` varchar(50) DEFAULT 'default' COMMENT '主题模板'"
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
