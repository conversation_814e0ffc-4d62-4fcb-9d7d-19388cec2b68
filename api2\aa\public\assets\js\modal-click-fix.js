/**
 * 模态框点击修复脚本
 * 修复所有页面模态框点击空白处关闭的问题
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('模态框点击修复脚本已加载');
    
    // 获取所有模态框
    const modals = document.querySelectorAll('.modal');
    
    if (modals.length > 0) {
        console.log('找到模态框：', modals.length, '个');
        
        // 为每个模态框添加点击事件
        modals.forEach(modal => {
            // 移除原有的点击事件
            const clone = modal.cloneNode(true);
            modal.parentNode.replaceChild(clone, modal);
            
            // 添加新的点击事件
            clone.addEventListener('click', function(e) {
                // 如果点击的是模态框本身，阻止事件传播
                if (e.target === this) {
                    e.stopPropagation();
                    console.log('点击了模态框空白处，阻止关闭');
                }
            });
            
            // 修复模态框内部的关闭按钮
            const closeButtons = clone.querySelectorAll('.close-btn');
            closeButtons.forEach(button => {
                // 移除原有的onclick属性
                const onclickValue = button.getAttribute('onclick');
                if (onclickValue) {
                    const match = onclickValue.match(/closeModal\('(.+)'\)/);
                    if (match) {
                        const modalId = match[1];
                        button.removeAttribute('onclick');
                        
                        // 添加新的点击事件
                        button.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log('关闭按钮被点击，模态框ID：', modalId);
                            closeModal(modalId);
                        });
                    }
                }
            });
            
            // 修复模态框内部的取消按钮
            const cancelButtons = clone.querySelectorAll('.modal-footer .btn-secondary');
            cancelButtons.forEach(button => {
                // 移除原有的onclick属性
                const onclickValue = button.getAttribute('onclick');
                if (onclickValue) {
                    const match = onclickValue.match(/closeModal\('(.+)'\)/);
                    if (match) {
                        const modalId = match[1];
                        button.removeAttribute('onclick');
                        
                        // 添加新的点击事件
                        button.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log('取消按钮被点击，模态框ID：', modalId);
                            closeModal(modalId);
                        });
                    }
                }
            });
        });
    } else {
        console.log('未找到模态框');
    }
    
    // 重写openModal函数
    if (typeof window.originalOpenModal === 'undefined') {
        window.originalOpenModal = window.openModal;
        
        window.openModal = function(modalId) {
            console.log('打开模态框：', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'block';
                
                // 阻止模态框点击事件冒泡
                modal.onclick = function(e) {
                    if (e.target === modal) {
                        e.stopPropagation();
                        console.log('点击了模态框空白处，阻止关闭');
                    }
                };
            }
        };
    }
    
    // 重写closeModal函数
    if (typeof window.originalCloseModal === 'undefined') {
        window.originalCloseModal = window.closeModal;
        
        window.closeModal = function(modalId) {
            console.log('关闭模态框：', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
            }
        };
    }
});
