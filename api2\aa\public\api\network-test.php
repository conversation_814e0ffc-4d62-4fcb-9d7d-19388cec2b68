<?php
// 网络连接诊断

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>网络连接诊断</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🌐 网络连接诊断</h1>
    
    <?php
    echo "<h2>1. PHP配置检查</h2>";
    echo "<ul>";
    echo "<li>allow_url_fopen: " . (ini_get('allow_url_fopen') ? '<span class="success">✅ 启用</span>' : '<span class="error">❌ 禁用</span>') . "</li>";
    echo "<li>cURL支持: " . (function_exists('curl_init') ? '<span class="success">✅ 支持</span>' : '<span class="error">❌ 不支持</span>') . "</li>";
    echo "<li>OpenSSL支持: " . (extension_loaded('openssl') ? '<span class="success">✅ 支持</span>' : '<span class="error">❌ 不支持</span>') . "</li>";
    echo "<li>user_agent: " . (ini_get('user_agent') ?: '未设置') . "</li>";
    echo "</ul>";
    
    echo "<h2>2. DNS解析测试</h2>";
    $hosts = [
        '和风天气' => 'devapi.qweather.com',
        'OpenWeatherMap' => 'api.openweathermap.org',
        '百度' => 'www.baidu.com',
        '谷歌' => 'www.google.com'
    ];
    
    echo "<ul>";
    foreach ($hosts as $name => $host) {
        $ip = gethostbyname($host);
        if ($ip === $host) {
            echo "<li>$name ($host): <span class='error'>❌ DNS解析失败</span></li>";
        } else {
            echo "<li>$name ($host): <span class='success'>✅ $ip</span></li>";
        }
    }
    echo "</ul>";
    
    echo "<h2>3. 基本HTTP连接测试</h2>";
    
    // 测试简单的HTTP连接
    $testUrls = [
        '百度' => 'http://www.baidu.com',
        '和风天气API' => 'https://devapi.qweather.com',
        'OpenWeatherMap' => 'https://api.openweathermap.org'
    ];
    
    foreach ($testUrls as $name => $url) {
        echo "<h3>测试 $name</h3>";
        
        // 使用cURL测试
        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_NOBODY, true); // 只获取头部
            
            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            $info = curl_getinfo($ch);
            curl_close($ch);
            
            if ($result !== false && $httpCode > 0) {
                echo "<p><span class='success'>✅ cURL连接成功</span></p>";
                echo "<ul>";
                echo "<li>HTTP状态码: $httpCode</li>";
                echo "<li>连接时间: " . round($info['connect_time'] * 1000) . "ms</li>";
                echo "<li>总时间: " . round($info['total_time'] * 1000) . "ms</li>";
                echo "</ul>";
            } else {
                echo "<p><span class='error'>❌ cURL连接失败</span></p>";
                echo "<p>错误信息: $error</p>";
                echo "<p>HTTP状态码: $httpCode</p>";
            }
        }
        
        // 使用file_get_contents测试（仅HTTP）
        if (strpos($url, 'http://') === 0 && ini_get('allow_url_fopen')) {
            $context = stream_context_create([
                'http' => [
                    'timeout' => 10,
                    'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                ]
            ]);
            
            $result = @file_get_contents($url, false, $context);
            if ($result !== false) {
                echo "<p><span class='success'>✅ file_get_contents连接成功</span></p>";
            } else {
                echo "<p><span class='error'>❌ file_get_contents连接失败</span></p>";
            }
        }
    }
    
    echo "<h2>4. 和风天气API具体测试</h2>";
    
    // 测试具体的和风天气API
    $apiKey = '70a2c59b6b194c92bbed4dbb4bcae572';
    $city = '北京';
    $apiUrl = "https://devapi.qweather.com/v7/weather/now?location=" . urlencode($city) . "&key=" . $apiKey;
    
    echo "<p><strong>API URL:</strong> $apiUrl</p>";
    
    if (function_exists('curl_init')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 15);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Weather App/1.0');
        curl_setopt($ch, CURLOPT_VERBOSE, true);
        
        // 捕获详细信息
        $verbose = fopen('php://temp', 'w+');
        curl_setopt($ch, CURLOPT_STDERR, $verbose);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $info = curl_getinfo($ch);
        
        rewind($verbose);
        $verboseLog = stream_get_contents($verbose);
        fclose($verbose);
        curl_close($ch);
        
        echo "<h3>cURL详细结果</h3>";
        if ($response !== false) {
            echo "<p><span class='success'>✅ 请求成功</span></p>";
            echo "<p><strong>HTTP状态码:</strong> $httpCode</p>";
            echo "<p><strong>响应内容:</strong></p>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
            
            $data = json_decode($response, true);
            if ($data) {
                echo "<p><strong>JSON解析结果:</strong></p>";
                echo "<pre>" . print_r($data, true) . "</pre>";
            }
        } else {
            echo "<p><span class='error'>❌ 请求失败</span></p>";
            echo "<p><strong>错误信息:</strong> $error</p>";
        }
        
        echo "<p><strong>连接信息:</strong></p>";
        echo "<ul>";
        echo "<li>DNS解析时间: " . round($info['namelookup_time'] * 1000) . "ms</li>";
        echo "<li>连接时间: " . round($info['connect_time'] * 1000) . "ms</li>";
        echo "<li>SSL握手时间: " . round($info['appconnect_time'] * 1000) . "ms</li>";
        echo "<li>总时间: " . round($info['total_time'] * 1000) . "ms</li>";
        echo "</ul>";
        
        if ($verboseLog) {
            echo "<p><strong>详细日志:</strong></p>";
            echo "<pre>" . htmlspecialchars($verboseLog) . "</pre>";
        }
    }
    
    echo "<h2>5. 系统环境信息</h2>";
    echo "<ul>";
    echo "<li>操作系统: " . PHP_OS . "</li>";
    echo "<li>PHP版本: " . PHP_VERSION . "</li>";
    echo "<li>服务器软件: " . ($_SERVER['SERVER_SOFTWARE'] ?? '未知') . "</li>";
    echo "<li>当前时间: " . date('Y-m-d H:i:s') . "</li>";
    echo "</ul>";
    ?>
    
    <h2>6. 可能的解决方案</h2>
    <div style="background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3>如果连接失败，请尝试：</h3>
        <ol>
            <li><strong>检查防火墙设置</strong> - 确保允许PHP进程访问外网</li>
            <li><strong>检查代理设置</strong> - 如果服务器在代理后面，需要配置代理</li>
            <li><strong>联系服务器管理员</strong> - 可能需要开放HTTPS出站连接</li>
            <li><strong>使用备用API</strong> - 尝试其他天气服务商</li>
            <li><strong>本地测试</strong> - 在本地环境测试API是否正常</li>
        </ol>
    </div>
    
    <div style="text-align: center; margin: 30px 0;">
        <a href="../dashboard/" style="padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;">返回控制面板</a>
    </div>
</body>
</html>
