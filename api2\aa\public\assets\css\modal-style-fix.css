/**
 * 模态框样式修复
 * 修复插件中心的模态框透明问题和取消按钮文字颜色问题
 */

/* 通用模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1050;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5) !important;
    animation: fadeIn 0.3s;
}

.modal-content {
    background-color: #ffffff !important;
    margin: 5% auto;
    width: 600px;
    max-width: 90%;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    animation: slideDown 0.3s;
    position: relative;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    color: #333;
}

.modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    padding: 15px 20px;
    border-top: 1px solid #eee;
    gap: 10px;
}

/* 关闭按钮样式 */
.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
    transition: color 0.3s;
}

.close-btn:hover {
    color: #333;
}

/* 按钮样式 */
.modal .btn {
    display: inline-block;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 8px;
    transition: all 0.3s;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.modal .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.modal .btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.modal .btn-primary {
    color: #ffffff;
    background: linear-gradient(135deg, #ff6b95 0%, #ffa5c0 100%);
    border-color: #ff6b95;
}

.modal .btn-primary:hover {
    background: linear-gradient(135deg, #ff4f7e 0%, #ffa5c0 100%);
    border-color: #ff4f7e;
}

.modal .btn-secondary {
    color: #333333 !important;
    background-color: #f8f9fa;
    border-color: #ddd;
}

.modal .btn-secondary:hover {
    background-color: #e9ecef;
    border-color: #ccc;
}

.modal .btn-danger {
    color: #ffffff;
    background-color: #dc3545;
    border-color: #dc3545;
}

.modal .btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

/* 历史版本模态框样式 */
#version-history-modal .modal-content {
    width: 800px;
    max-width: 90%;
}

#version-history-modal .version-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

#version-history-modal .version-item {
    padding: 15px;
    border-bottom: 1px solid #eee;
}

#version-history-modal .version-item:last-child {
    border-bottom: none;
}

#version-history-modal .version-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

#version-history-modal .version-number {
    font-weight: 600;
    color: #333;
}

#version-history-modal .version-date {
    color: #666;
    font-size: 0.9rem;
}

#version-history-modal .version-changes {
    margin: 0;
    padding-left: 20px;
}

#version-history-modal .version-changes li {
    margin-bottom: 5px;
}

/* 续费模态框样式 */
#renew-modal .modal-content {
    width: 500px;
    max-width: 90%;
}

#renew-modal .renew-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 15px;
}

#renew-modal .renew-option {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
}

#renew-modal .renew-option:hover {
    border-color: #ff6b95;
    background-color: rgba(255, 107, 149, 0.05);
}

#renew-modal .renew-option.selected {
    border-color: #ff6b95;
    background-color: rgba(255, 107, 149, 0.1);
}

#renew-modal .renew-option-radio {
    margin-right: 15px;
}

#renew-modal .renew-option-info {
    flex: 1;
}

#renew-modal .renew-option-name {
    font-weight: 500;
    margin-bottom: 5px;
}

#renew-modal .renew-option-price {
    color: #ff6b95;
    font-weight: 600;
}

#renew-modal .renew-option-original {
    color: #999;
    text-decoration: line-through;
    margin-left: 5px;
    font-size: 0.9rem;
}

/* 查看全部公告模态框样式 */
#all-announcements-modal .modal-content {
    width: 800px;
    max-width: 90%;
}

#all-announcements-modal .announcement-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

#all-announcements-modal .announcement-item {
    padding: 15px;
    border-bottom: 1px solid #eee;
}

#all-announcements-modal .announcement-item:last-child {
    border-bottom: none;
}

#all-announcements-modal .announcement-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

#all-announcements-modal .announcement-title {
    font-weight: 600;
    color: #333;
}

#all-announcements-modal .announcement-date {
    color: #666;
    font-size: 0.9rem;
}

#all-announcements-modal .announcement-content {
    margin: 0;
    color: #555;
}
