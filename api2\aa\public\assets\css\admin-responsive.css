/**
 * 管理面板响应式增强样式
 * 提供更多断点和响应式优化
 */

/* 超小型设备 (小型手机, 320px 以下) */
@media (max-width: 320px) {
    .stat-card {
        padding: 10px;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .stat-content h3 {
        font-size: 1rem;
    }

    .user-avatar {
        width: 30px;
        height: 30px;
    }

    .menu-toggle {
        width: 30px;
        height: 30px;
    }
}

/* 小型设备 (手机, 576px 以下) */
@media (max-width: 575.98px) {
    .dashboard-stats {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .section-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .stat-card {
        padding: 15px;
    }

    .header {
        flex-direction: column;
        align-items: flex-start;
    }

    .user-info {
        margin-top: 10px;
        margin-left: auto;
    }

    .shortcut-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .toast {
        width: 250px;
    }
}

/* 中型设备 (平板, 768px 以下) */
@media (max-width: 767.98px) {
    .sidebar {
        width: 0;
        padding: 0;
        overflow: hidden;
    }

    .sidebar.collapsed {
        width: 70px;
    }

    .main-content {
        margin-left: 0;
        padding: 15px;
    }

    .main-content.expanded {
        margin-left: 70px;
    }

    .dashboard-stats {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

    .section-row {
        grid-template-columns: 1fr;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
    }

    .stat-icon {
        margin: 0 auto 10px;
    }

    .stat-content {
        width: 100%;
    }
}

/* 大型设备 (小型笔记本, 992px 以下) */
@media (max-width: 991.98px) {
    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .section-row {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
}

/* 超大型设备 (大型笔记本和台式机, 1200px 以下) */
@media (max-width: 1199.98px) {
    .section-row {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 侧边栏响应式调整 */
.sidebar-responsive {
    transition: width 0.3s ease;
}

.main-content-responsive {
    transition: margin-left 0.3s ease;
}

/* 侧边栏收缩状态下的内容调整 */
.dashboard-stats {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    transition: none; /* 移除过渡效果，避免抖动 */
}

/* 确保在侧边栏收缩和展开时统计卡片能够保持3列布局 */
@media (min-width: 768px) {
    /* 侧边栏展开状态 */
    .sidebar:not(.collapsed) ~ .main-content .dashboard-stats {
        grid-template-columns: repeat(3, 1fr);
    }

    /* 侧边栏收缩状态 */
    .sidebar.collapsed ~ .main-content .dashboard-stats {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* 中等屏幕尺寸 */
@media (min-width: 768px) and (max-width: 1199px) {
    .dashboard-stats {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* 大屏幕尺寸 */
@media (min-width: 1200px) {
    .dashboard-stats {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* 小屏幕尺寸 */
@media (max-width: 767.98px) {
    .dashboard-stats {
        grid-template-columns: 1fr;
    }
}

/* 图表响应式调整 */
.chart-container {
    position: relative;
    height: 0;
    padding-bottom: 60%; /* 控制图表高宽比 */
    overflow: hidden;
}

.chart-container canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100% !important;
    height: 100% !important;
}

/* 卡片响应式调整 */
.responsive-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.responsive-card-body {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* 表格响应式调整 */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* 模态框响应式调整 */
@media (max-width: 576px) {
    .modal-dialog {
        margin: 0.5rem;
    }

    .modal-content {
        max-height: 90vh;
        overflow-y: auto;
    }
}

/* 导航响应式调整 */
@media (max-width: 767.98px) {
    .nav-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        overflow-y: hidden;
        -webkit-overflow-scrolling: touch;
    }

    .nav-tabs .nav-link {
        white-space: nowrap;
    }
}

/* 底部响应式调整 */
@media (max-width: 576px) {
    .site-footer {
        padding: 10px;
        font-size: 0.8rem;
    }
}
