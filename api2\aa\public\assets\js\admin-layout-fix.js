/**
 * 管理面板布局修复脚本
 * 确保在侧边栏状态变化时所有元素能够正确响应
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('布局修复脚本已加载');

    // 初始化布局
    adjustLayout();

    // 在页面完全加载后调整布局（包括图片等资源）
    window.addEventListener('load', function() {
        console.log('页面完全加载，调整布局');
        // 只调用一次，避免多次调整导致抖动
        adjustLayout();
    });

    // 监听窗口大小变化
    window.addEventListener('resize', function() {
        // 使用防抖动函数避免频繁调用
        clearTimeout(window.layoutResizeTimer);
        window.layoutResizeTimer = setTimeout(function() {
            adjustLayout();
        }, 250);
    });

    // 监听自定义的侧边栏切换事件，而不是使用MutationObserver
    document.addEventListener('sidebarToggle', function(event) {
        console.log('收到侧边栏切换事件:', event.detail.collapsed);
        // 只调整图表大小，不重新计算布局
        const charts = document.querySelectorAll('canvas[id$="Chart"]');
        if (charts.length > 0) {
            charts.forEach(chart => {
                if (chart.chart) {
                    chart.chart.resize();
                }
            });
        }
    });
});

/**
 * 调整页面布局
 * 根据侧边栏状态和窗口大小调整各元素布局
 */
function adjustLayout() {
    console.log('调整页面布局');

    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');
    const dashboardStats = document.querySelector('.dashboard-stats');
    const sectionRows = document.querySelectorAll('.section-row');

    if (!sidebar || !mainContent) {
        console.warn('找不到必要的DOM元素');
        return;
    }

    const isSidebarCollapsed = sidebar.classList.contains('collapsed');
    const windowWidth = window.innerWidth;
    const mainContentWidth = mainContent.offsetWidth;

    console.log('窗口宽度:', windowWidth, '主内容区宽度:', mainContentWidth, '侧边栏状态:', isSidebarCollapsed ? '已折叠' : '已展开');

    // 调整dashboard-stats布局
    if (dashboardStats) {
        // 获取dashboard-stats中的卡片数量
        const statCards = dashboardStats.querySelectorAll('.stat-card');
        const cardCount = statCards.length;
        console.log('统计卡片数量:', cardCount);

        // 固定为3列布局，不使用自动填充
        if (windowWidth >= 1200) {
            // 大屏幕，强制3列布局
            dashboardStats.style.gridTemplateColumns = 'repeat(3, 1fr)';
        } else if (windowWidth >= 768) {
            // 中屏幕，强制3列布局但列宽较小
            dashboardStats.style.gridTemplateColumns = 'repeat(3, 1fr)';
        } else {
            // 小屏幕，单列布局
            dashboardStats.style.gridTemplateColumns = '1fr';
        }

        console.log('已调整统计卡片布局:', dashboardStats.style.gridTemplateColumns);

        // 确保所有卡片都可见
        statCards.forEach(card => {
            card.style.display = 'flex';
        });

        // 取消强制重绘，避免闪烁
        // 只在必要时应用样式变化
        if (dashboardStats.style.display !== 'grid') {
            dashboardStats.style.display = 'grid';
        }
    }

    // 调整section-row布局
    if (sectionRows.length > 0) {
        sectionRows.forEach(row => {
            // 根据主内容区宽度决定是否使用单列布局
            if (mainContentWidth < 768) {
                row.style.gridTemplateColumns = '1fr';
            } else {
                row.style.gridTemplateColumns = 'repeat(2, 1fr)';
            }
        });
    }

    // 调整图表大小
    const charts = document.querySelectorAll('canvas[id$="Chart"]');
    if (charts.length > 0) {
        charts.forEach(chart => {
            if (chart.chart) {
                chart.chart.resize();
            }
        });
    }
}
