<?php
// 测试订单图表POST请求

// 开启会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 设置响应头
header('Content-Type: application/json');

// 检查用户是否已登录
if (!isset($_SESSION['username'])) {
    http_response_code(401);
    echo json_encode(['error' => '未授权访问']);
    exit;
}

try {
    // 模拟POST数据
    $input = [
        'chartId' => 'orderChart',
        'timeRange' => 'month'
    ];
    
    // 包含数据库配置
    $configPath = __DIR__ . '/../../config/config.php';
    $config = require_once($configPath);
    
    if (!$config || !is_array($config)) {
        throw new Exception('配置文件加载失败');
    }
    
    $dbConfig = $config['database'];
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $prefix = $dbConfig['prefix'];
    $tableName = $prefix . 'order';
    
    // 检查表是否存在
    $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
    $stmt->execute([$tableName]);
    $tableExists = $stmt->fetch() !== false;
    
    if (!$tableExists) {
        throw new Exception("数据表 {$tableName} 不存在");
    }
    
    // 先检查数据库中的时间范围
    $stmt = $pdo->prepare("SELECT MIN(intime) as min_time, MAX(intime) as max_time, COUNT(*) as total FROM {$tableName}");
    $stmt->execute();
    $timeRange = $stmt->fetch(PDO::FETCH_ASSOC);

    // 尝试执行订单图表查询 - 使用当前月份
    $labels = [];
    $data = [];
    $daysInMonth = date('t');
    $currentMonth = date('Y-m');

    for ($day = 1; $day <= $daysInMonth; $day++) {
        $labels[] = $day . '日';
        $date = "$currentMonth-" . str_pad($day, 2, '0', STR_PAD_LEFT);
        $startTime = strtotime("$date 00:00:00");
        $endTime = strtotime("$date 23:59:59");

        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM {$tableName} WHERE intime >= ? AND intime <= ?");
        $stmt->execute([$startTime, $endTime]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $count = (int)($result['count'] ?? 0);
        $data[] = $count;
    }

    // 返回调试信息
    echo json_encode([
        'time_range_info' => [
            'min_time' => $timeRange['min_time'],
            'max_time' => $timeRange['max_time'],
            'min_date' => date('Y-m-d H:i:s', $timeRange['min_time']),
            'max_date' => date('Y-m-d H:i:s', $timeRange['max_time']),
            'total_records' => $timeRange['total']
        ],
        'query_info' => [
            'current_month' => $currentMonth,
            'days_in_month' => $daysInMonth,
            'sample_start_time' => strtotime("$currentMonth-01 00:00:00"),
            'sample_end_time' => strtotime("$currentMonth-01 23:59:59")
        ],
        'chart_data' => [
            'labels' => $labels,
            'datasets' => [['data' => $data]]
        ]
    ], JSON_PRETTY_PRINT);
    
    exit;
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
