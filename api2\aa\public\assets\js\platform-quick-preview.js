/**
 * 小程序平台快速预览功能
 * 提供平台快速预览功能，无需进入详情页即可查看关键信息
 */

// 在DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('平台快速预览功能已加载');
    
    // 初始化平台快速预览功能
    initPlatformQuickPreview();
});

/**
 * 初始化平台快速预览功能
 */
function initPlatformQuickPreview() {
    // 为平台名称添加快速预览功能
    setupPlatformNamePreview();
    
    // 创建预览容器
    createPreviewContainer();
    
    // 添加文档点击事件，用于关闭预览
    document.addEventListener('click', function(e) {
        // 如果点击的不是平台名称或预览容器，则关闭预览
        if (!e.target.closest('.platform-name') && !e.target.closest('.platform-preview')) {
            hideAllPreviews();
        }
    });
}

/**
 * 为平台名称添加快速预览功能
 */
function setupPlatformNamePreview() {
    // 获取所有平台名称元素
    const platformNames = document.querySelectorAll('.platform-name');
    
    platformNames.forEach(name => {
        // 获取平台ID
        const row = name.closest('tr');
        const platformId = row.getAttribute('data-id');
        
        if (!platformId) return;
        
        // 添加鼠标悬停事件
        name.addEventListener('mouseenter', function() {
            // 延迟显示预览，避免用户快速移动鼠标时频繁触发
            this.previewTimeout = setTimeout(() => {
                showPlatformPreview(platformId, this);
            }, 300);
        });
        
        // 添加鼠标离开事件
        name.addEventListener('mouseleave', function() {
            // 清除延迟显示预览的定时器
            if (this.previewTimeout) {
                clearTimeout(this.previewTimeout);
            }
        });
        
        // 添加点击事件
        name.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // 显示或隐藏预览
            const preview = document.querySelector('.platform-preview.show');
            if (preview && preview.getAttribute('data-platform-id') === platformId) {
                hideAllPreviews();
            } else {
                showPlatformPreview(platformId, this);
            }
        });
    });
    
    console.log('平台名称快速预览功能已设置');
}

/**
 * 创建预览容器
 */
function createPreviewContainer() {
    // 检查是否已存在预览容器
    if (document.querySelector('.platform-preview')) return;
    
    // 创建预览容器
    const previewContainer = document.createElement('div');
    previewContainer.className = 'platform-preview';
    previewContainer.innerHTML = `
        <div class="preview-header">
            <div class="preview-logo">
                <img src="/assets/images/platform-logo-placeholder.png" alt="平台Logo">
            </div>
            <h3 class="preview-title">平台名称</h3>
        </div>
        <div class="preview-info">
            <div class="preview-info-item">
                <span class="preview-info-label">状态:</span>
                <span class="preview-info-value preview-status">已启用</span>
            </div>
            <div class="preview-info-item">
                <span class="preview-info-label">用户数:</span>
                <span class="preview-info-value preview-users">0</span>
            </div>
            <div class="preview-info-item">
                <span class="preview-info-label">订单数:</span>
                <span class="preview-info-value preview-orders">0</span>
            </div>
            <div class="preview-info-item">
                <span class="preview-info-label">创建时间:</span>
                <span class="preview-info-value preview-create-time">2023-01-01</span>
            </div>
        </div>
        <div class="preview-actions">
            <button class="btn btn-sm btn-outline-primary preview-detail-btn">查看详情</button>
            <button class="btn btn-sm btn-outline-secondary preview-edit-btn">编辑</button>
        </div>
    `;
    
    // 添加到文档
    document.body.appendChild(previewContainer);
    
    console.log('预览容器已创建');
}

/**
 * 显示平台预览
 * @param {string} platformId - 平台ID
 * @param {HTMLElement} triggerElement - 触发预览的元素
 */
function showPlatformPreview(platformId, triggerElement) {
    console.log(`显示平台预览: ${platformId}`);
    
    // 隐藏所有预览
    hideAllPreviews();
    
    // 获取预览容器
    const previewContainer = document.querySelector('.platform-preview');
    if (!previewContainer) return;
    
    // 设置平台ID
    previewContainer.setAttribute('data-platform-id', platformId);
    
    // 获取平台数据
    getPlatformData(platformId)
        .then(data => {
            // 更新预览内容
            updatePreviewContent(previewContainer, data);
            
            // 计算预览位置
            positionPreview(previewContainer, triggerElement);
            
            // 显示预览
            previewContainer.classList.add('show');
            
            // 添加预览按钮事件
            setupPreviewButtons(previewContainer, platformId);
        })
        .catch(error => {
            console.error('获取平台数据失败:', error);
        });
}

/**
 * 隐藏所有预览
 */
function hideAllPreviews() {
    const previews = document.querySelectorAll('.platform-preview');
    previews.forEach(preview => {
        preview.classList.remove('show');
    });
}

/**
 * 获取平台数据
 * @param {string} platformId - 平台ID
 * @returns {Promise} 包含平台数据的Promise
 */
function getPlatformData(platformId) {
    // 模拟API请求
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            // 模拟数据
            const data = {
                id: platformId,
                name: `情侣头像匹配小程序${platformId}`,
                logo: '/assets/images/platform-logo-placeholder.png',
                status: platformId % 2 === 0 ? 0 : 1,
                users: Math.floor(Math.random() * 10000),
                orders: Math.floor(Math.random() * 5000),
                createTime: '2023-01-01',
                description: `平台描述${platformId}`
            };
            
            resolve(data);
        }, 200);
    });
}

/**
 * 更新预览内容
 * @param {HTMLElement} previewContainer - 预览容器
 * @param {Object} data - 平台数据
 */
function updatePreviewContent(previewContainer, data) {
    // 更新标题
    const title = previewContainer.querySelector('.preview-title');
    if (title) {
        title.textContent = data.name;
    }
    
    // 更新Logo
    const logo = previewContainer.querySelector('.preview-logo img');
    if (logo) {
        logo.src = data.logo;
        logo.alt = data.name;
    }
    
    // 更新状态
    const status = previewContainer.querySelector('.preview-status');
    if (status) {
        status.textContent = data.status === 1 ? '已启用' : '已禁用';
        status.className = `preview-info-value preview-status ${data.status === 1 ? 'text-success' : 'text-danger'}`;
    }
    
    // 更新用户数
    const users = previewContainer.querySelector('.preview-users');
    if (users) {
        users.textContent = data.users.toLocaleString();
    }
    
    // 更新订单数
    const orders = previewContainer.querySelector('.preview-orders');
    if (orders) {
        orders.textContent = data.orders.toLocaleString();
    }
    
    // 更新创建时间
    const createTime = previewContainer.querySelector('.preview-create-time');
    if (createTime) {
        createTime.textContent = data.createTime;
    }
}

/**
 * 计算预览位置
 * @param {HTMLElement} previewContainer - 预览容器
 * @param {HTMLElement} triggerElement - 触发预览的元素
 */
function positionPreview(previewContainer, triggerElement) {
    // 获取触发元素的位置和尺寸
    const triggerRect = triggerElement.getBoundingClientRect();
    
    // 获取预览容器的尺寸
    const previewRect = previewContainer.getBoundingClientRect();
    
    // 计算预览容器的位置
    let left = triggerRect.left;
    let top = triggerRect.bottom + window.scrollY + 10; // 在触发元素下方10px
    
    // 检查是否超出右边界
    if (left + previewRect.width > window.innerWidth) {
        left = window.innerWidth - previewRect.width - 10;
    }
    
    // 检查是否超出下边界
    if (top + previewRect.height > window.scrollY + window.innerHeight) {
        // 如果超出下边界，则显示在触发元素上方
        top = triggerRect.top + window.scrollY - previewRect.height - 10;
    }
    
    // 设置预览容器的位置
    previewContainer.style.left = `${left}px`;
    previewContainer.style.top = `${top}px`;
}

/**
 * 设置预览按钮事件
 * @param {HTMLElement} previewContainer - 预览容器
 * @param {string} platformId - 平台ID
 */
function setupPreviewButtons(previewContainer, platformId) {
    // 详情按钮
    const detailBtn = previewContainer.querySelector('.preview-detail-btn');
    if (detailBtn) {
        detailBtn.onclick = function() {
            window.location.href = `/dashboard/platforms/detail/${platformId}`;
        };
    }
    
    // 编辑按钮
    const editBtn = previewContainer.querySelector('.preview-edit-btn');
    if (editBtn) {
        editBtn.onclick = function() {
            hideAllPreviews();
            openEditModal(platformId);
        };
    }
}
