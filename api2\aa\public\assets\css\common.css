/**
 * 通用CSS样式
 * 包含模态框、提示信息等通用样式
 */

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 107, 149, 0.3);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1001;
    overflow-y: auto;
    padding: 20px;
}

.modal-content {
    background-color: var(--white-color);
    margin: 5% auto;
    width: 600px;
    max-width: 90%;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px var(--shadow-color);
    animation: slideDown 0.3s;
    overflow: hidden;
}

@keyframes slideDown {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.modal-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
}

.close-btn {
    background: none;
    border: none;
    color: var(--gray-color);
    font-size: 1.2rem;
    cursor: pointer;
    transition: all var(--transition-speed);
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-btn:hover {
    color: var(--danger-color);
    background-color: rgba(220, 53, 69, 0.1);
}

.modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    background-color: #f8f9fa;
}

/* 消息提示样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 300px;
    background-color: var(--white-color);
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px var(--shadow-color);
    overflow: hidden;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transform: translateX(30px);
    transition: all 0.3s;
}

.toast.show {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

.toast-content {
    display: flex;
    align-items: center;
    padding: 15px;
}

.toast-icon {
    font-size: 1.5rem;
    margin-right: 15px;
    display: none;
}

.toast-icon.success {
    color: var(--success-color);
}

.toast-icon.error {
    color: var(--danger-color);
}

.toast-icon.info {
    color: var(--info-color);
}

.toast-message {
    flex: 1;
    font-weight: 500;
}

.toast-progress {
    height: 3px;
    background-color: var(--primary-color);
    width: 100%;
    animation: progress 3s linear;
}

@keyframes progress {
    from { width: 100%; }
    to { width: 0%; }
}

/* 按钮样式 */
.btn {
    display: inline-block;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: var(--border-radius);
    transition: all var(--transition-speed);
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    color: var(--white-color);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #ff4f7e 0%, #ffa5c0 100%);
    border-color: #ff4f7e;
}

.btn-secondary {
    color: var(--white-color);
    background-color: var(--gray-color);
    border-color: var(--gray-color);
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
}

.btn-danger {
    color: var(--white-color);
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

.btn-outline-primary {
    color: var(--primary-color);
    background-color: transparent;
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    color: var(--white-color);
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}
