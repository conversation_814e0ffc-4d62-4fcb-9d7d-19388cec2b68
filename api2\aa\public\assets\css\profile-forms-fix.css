/**
 * 个人资料页面表单美化样式
 */

/* 表单组样式 */
.form-group {
    margin-bottom: 25px !important;
}

.form-group label {
    display: block;
    margin-bottom: 10px !important;
    font-weight: 600 !important;
    color: var(--dark-color);
    font-size: 1.05rem !important;
}

.form-group input {
    width: 100%;
    padding: 12px 18px !important;
    border: 2px solid #eee !important;
    border-radius: 10px !important;
    font-size: 1rem !important;
    transition: all 0.3s ease !important;
    background-color: #f8f9fa !important;
    color: var(--dark-color);
}

.form-group input:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 4px rgba(255, 107, 149, 0.2) !important;
    outline: none;
    background-color: #fff !important;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px !important;
    margin-top: 35px !important;
}

.btn {
    display: inline-block;
    font-weight: 600 !important;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: none !important;
    padding: 12px 25px !important;
    font-size: 1rem !important;
    line-height: 1.5;
    border-radius: 10px !important;
    transition: all 0.3s ease !important;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.btn:focus:not(:active)::after {
    animation: ripple 1s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    20% {
        transform: scale(25, 25);
        opacity: 0.3;
    }
    100% {
        opacity: 0;
        transform: scale(40, 40);
    }
}

.btn-primary {
    color: var(--white-color) !important;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
    box-shadow: 0 5px 15px rgba(255, 107, 149, 0.3) !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%) !important;
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 20px rgba(255, 107, 149, 0.4) !important;
}

.btn-primary:active {
    transform: translateY(0) !important;
    box-shadow: 0 3px 10px rgba(255, 107, 149, 0.3) !important;
}

.btn-secondary {
    color: var(--dark-color) !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05) !important;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%) !important;
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08) !important;
}

.btn-secondary:active {
    transform: translateY(0) !important;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05) !important;
}

/* 密码强度指示器 */
.password-strength {
    margin-top: 10px !important;
    height: 6px !important;
    background-color: #eee !important;
    border-radius: 10px !important;
    overflow: hidden;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

.password-strength-bar {
    height: 100%;
    width: 0;
    transition: width 0.5s ease, background-color 0.5s ease !important;
    border-radius: 10px !important;
}

.password-strength-text {
    font-size: 0.9rem !important;
    margin-top: 8px !important;
    color: var(--gray-color);
    font-weight: 500 !important;
}

.password-tips {
    margin-top: 8px !important;
    font-size: 0.9rem !important;
    color: var(--gray-color);
    background-color: rgba(0, 0, 0, 0.02) !important;
    padding: 10px 15px !important;
    border-radius: 8px !important;
    border-left: 3px solid var(--primary-color) !important;
}

.password-match-text {
    font-size: 0.9rem !important;
    margin-top: 8px !important;
    font-weight: 500 !important;
    display: flex;
    align-items: center;
    gap: 5px;
}

.password-match-text.match {
    color: #28a745;
}

.password-match-text.match::before {
    content: '\f058';
    font-family: 'Bootstrap Icons';
    font-weight: normal;
}

.password-match-text.not-match {
    color: #dc3545;
}

.password-match-text.not-match::before {
    content: '\f621';
    font-family: 'Bootstrap Icons';
    font-weight: normal;
}

.required {
    color: #dc3545;
    margin-left: 3px;
}

/* 通知设置样式 */
.notification-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
    border-radius: 15px !important;
    padding: 25px !important;
    margin-bottom: 30px !important;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.03) !important;
    border: 1px solid rgba(0, 0, 0, 0.03) !important;
    transition: all 0.3s ease !important;
}

.notification-section:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.05) !important;
    transform: translateY(-2px) !important;
}

.section-title {
    font-size: 1.2rem !important;
    font-weight: 700 !important;
    margin-bottom: 20px !important;
    color: var(--dark-color);
    padding-bottom: 15px !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.03) !important;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
    border-radius: 10px !important;
}

/* 复选框样式 */
.form-group.checkbox {
    display: flex;
    align-items: flex-start;
    gap: 15px !important;
    padding: 10px 0 !important;
    position: relative;
}

.form-group.checkbox input[type="checkbox"] {
    width: 22px !important;
    height: 22px !important;
    margin-top: 3px !important;
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
    background-color: #fff !important;
    border: 2px solid #ddd !important;
    border-radius: 6px !important;
    position: relative;
    transition: all 0.3s ease !important;
}

.form-group.checkbox input[type="checkbox"]:checked {
    background-color: var(--primary-color) !important;
    border-color: var(--primary-color) !important;
}

.form-group.checkbox input[type="checkbox"]:checked::after {
    content: '\f633';
    font-family: 'Bootstrap Icons';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 14px !important;
}

.form-group.checkbox label {
    margin-bottom: 0 !important;
    cursor: pointer;
    font-weight: 600 !important;
    font-size: 1rem !important;
}

.checkbox-description {
    margin-left: 37px !important;
    font-size: 0.9rem !important;
    color: var(--gray-color);
    margin-top: 5px !important;
    line-height: 1.5 !important;
}
