/**
 * 智慧天气组件
 * 在页面头部显示天气信息和随机人生哲理
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('智慧天气组件已加载');

    // 检查当前页面是否为小程序详情页面
    if (window.location.pathname.includes('/miniprogram/detail/')) {
        console.log('当前页面为小程序详情页面，不加载天气和人生哲理组件');
        return;
    }

    // 创建头部信息内容
    createHeaderInfoContent();

    // 初始化内容
    initContent();

    // 每隔30秒更新一次内容
    setInterval(updateContent, 30000);
});

/**
 * 创建头部信息内容
 */
function createHeaderInfoContent() {
    // 检查是否已存在
    if (document.getElementById('header-info-content')) return;

    // 创建信息内容
    const infoContent = document.createElement('div');
    infoContent.id = 'header-info-content';
    infoContent.className = 'header-info-content';

    // 创建内容容器
    infoContent.innerHTML = `
        <div class="wisdom-quote">
            <i class="bi bi-quote"></i>
            <span id="wisdom-text">加载中...</span>
        </div>
        <div class="weather-info">
            <i class="bi bi-cloud-sun"></i>
            <span id="weather-text">加载中...</span>
        </div>
        <div class="info-actions">
            <button id="refresh-info" title="刷新"><i class="bi bi-arrow-clockwise"></i></button>
            <button id="toggle-info-type" title="切换显示内容"><i class="bi bi-shuffle"></i></button>
            <button id="settings-info" title="设置"><i class="bi bi-gear"></i></button>
        </div>
    `;

    // 添加样式表
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = '/assets/css/wisdom-weather.css';
    document.head.appendChild(link);

    // 找到合适的位置插入
    const header = document.querySelector('.header') || document.querySelector('.miniprogram-header');
    if (header) {
        // 找到用户信息元素
        const userInfo = header.querySelector('.user-info');
        if (userInfo) {
            // 在用户信息之前插入
            header.insertBefore(infoContent, userInfo);
        } else {
            // 如果找不到用户信息，则添加到头部的末尾
            header.appendChild(infoContent);
        }
    }

    // 添加事件监听器（使用事件委托以确保按钮功能正常）
    document.addEventListener('click', function(event) {
        // 刷新按钮
        if (event.target.id === 'refresh-info' || event.target.closest('#refresh-info')) {
            const refreshBtn = event.target.id === 'refresh-info' ? event.target : event.target.closest('#refresh-info');
            updateContent();
            refreshBtn.classList.add('rotating');
            setTimeout(() => {
                refreshBtn.classList.remove('rotating');
            }, 1000);
        }

        // 切换按钮
        if (event.target.id === 'toggle-info-type' || event.target.closest('#toggle-info-type')) {
            toggleInfoType();
        }
    });

    // 确保初始按钮也有事件（向后兼容）
    const refreshBtn = document.getElementById('refresh-info');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function(e) {
            e.stopPropagation(); // 防止事件冒泡触发两次
            updateContent();
            this.classList.add('rotating');
            setTimeout(() => {
                this.classList.remove('rotating');
            }, 1000);
        });
    }

    const toggleBtn = document.getElementById('toggle-info-type');
    if (toggleBtn) {
        toggleBtn.addEventListener('click', function(e) {
            e.stopPropagation(); // 防止事件冒泡触发两次
            toggleInfoType();
        });
    }
}

/**
 * 初始化内容
 */
function initContent() {
    // 设置初始内容类型
    localStorage.setItem('infoType', localStorage.getItem('infoType') || 'both');

    // 更新内容
    updateContent();
}

/**
 * 更新内容
 */
function updateContent() {
    const infoType = localStorage.getItem('infoType') || 'both';

    // 根据类型显示内容
    if (infoType === 'wisdom' || infoType === 'both') {
        updateWisdom();
    }

    if (infoType === 'weather' || infoType === 'both') {
        updateWeather();
    }

    // 更新显示状态
    updateDisplayState(infoType);
}

/**
 * 更新智慧语录
 */
function updateWisdom() {
    const wisdomText = document.getElementById('wisdom-text');
    if (!wisdomText) return;

    // 随机选择一条智慧语录
    const wisdom = getRandomWisdom();

    // 设置文本
    wisdomText.textContent = wisdom;

    // 添加淡入动画
    wisdomText.style.animation = 'none';
    setTimeout(() => {
        wisdomText.style.animation = 'fadeIn 0.5s';
    }, 10);
}

/**
 * 更新天气信息
 */
function updateWeather() {
    const weatherText = document.getElementById('weather-text');
    if (!weatherText) return;

    // 获取设置中的城市
    const settings = JSON.parse(localStorage.getItem('weatherSettings')) || {
        city: '北京'
    };

    // 显示加载状态
    weatherText.textContent = '获取天气中...';

    // 获取真实天气数据
    fetch(`../api/weather-simple.php?city=${encodeURIComponent(settings.city)}`)
        .then(response => response.json())
        .then(data => {
            let weatherDisplay = '';

            if (data.success) {
                // 使用真实天气数据
                const weather = data.data;
                weatherDisplay = `${data.city} ${weather.condition} ${weather.temperature}°C`;
            } else {
                // 使用备用数据
                const weather = data.data;
                weatherDisplay = `${data.city} ${weather.condition} ${weather.temperature}°C`;
                console.warn('天气API错误，使用备用数据:', data.error);
            }

            // 设置文本
            weatherText.textContent = weatherDisplay;

            // 添加淡入动画
            weatherText.style.animation = 'none';
            setTimeout(() => {
                weatherText.style.animation = 'fadeIn 0.5s';
            }, 10);
        })
        .catch(error => {
            console.error('获取天气失败:', error);
            // 使用模拟数据作为最后备用
            const fallbackWeather = getSimulatedWeather(settings.city);
            weatherText.textContent = fallbackWeather;
        });
}

/**
 * 切换信息类型
 */
function toggleInfoType() {
    const currentType = localStorage.getItem('infoType') || 'both';
    let newType;

    // 循环切换类型
    switch (currentType) {
        case 'both':
            newType = 'wisdom';
            break;
        case 'wisdom':
            newType = 'weather';
            break;
        case 'weather':
            newType = 'both';
            break;
        default:
            newType = 'both';
    }

    // 保存新类型
    localStorage.setItem('infoType', newType);

    // 更新内容
    updateContent();
}

/**
 * 更新显示状态
 */
function updateDisplayState(infoType) {
    const wisdomQuote = document.querySelector('.wisdom-quote');
    const weatherInfo = document.querySelector('.weather-info');

    if (!wisdomQuote || !weatherInfo) return;

    // 根据类型显示/隐藏元素
    switch (infoType) {
        case 'wisdom':
            wisdomQuote.style.display = 'flex';
            weatherInfo.style.display = 'none';
            break;
        case 'weather':
            wisdomQuote.style.display = 'none';
            weatherInfo.style.display = 'flex';
            break;
        case 'both':
            wisdomQuote.style.display = 'flex';
            weatherInfo.style.display = 'flex';
            break;
    }
}

/**
 * 获取随机智慧语录
 */
function getRandomWisdom() {
    const wisdomQuotes = [
        "生活不是等待风暴过去，而是学会在雨中翩翩起舞。",
        "人生最大的错误是不断担心会犯错。",
        "生命中最重要的事情是要有个远大的目标，并借助才能与坚毅来完成它。",
        "不要因为走得太远，而忘记为什么出发。",
        "成功不是将来才有的，而是从决定去做的那一刻起，持续累积而成。",
        "人生就像骑自行车，要保持平衡就得不断前进。",
        "世上没有绝望的处境，只有对处境绝望的人。",
        "机会不会来敲门，机会是你创造的。",
        "人生最大的挑战不是超越别人，而是超越自己。",
        "态度决定一切，细节决定成败。",
        "没有口水与汗水，就没有成功的泪水。",
        "每一个不曾起舞的日子，都是对生命的辜负。",
        "不要等待机会，而要创造机会。",
        "成功的秘诀在于坚持最初的梦想。",
        "人生如同故事，重要的不是长度，而是内容。"
    ];

    return wisdomQuotes[Math.floor(Math.random() * wisdomQuotes.length)];
}

/**
 * 获取模拟天气信息
 * @param {string} userCity - 用户设置的城市
 */
function getSimulatedWeather(userCity = '北京') {
    const conditions = ["晴", "多云", "阴", "小雨", "中雨", "大雨", "雷阵雨", "小雪", "大雪"];
    const temperatures = Array.from({length: 30}, (_, i) => i + 5); // 5-34度

    const condition = conditions[Math.floor(Math.random() * conditions.length)];
    const temperature = temperatures[Math.floor(Math.random() * temperatures.length)];

    return `${userCity} ${condition} ${temperature}°C`;
}
