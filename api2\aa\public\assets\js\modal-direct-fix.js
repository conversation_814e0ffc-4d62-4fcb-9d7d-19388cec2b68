/**
 * 模态框直接修复脚本
 * 直接操作DOM元素，确保模态框正确显示
 */

document.addEventListener('DOMContentLoaded', function() {
    // 直接修复续费按钮
    const renewButtons = document.querySelectorAll('button[onclick="openRenewModal()"]');
    if (renewButtons.length > 0) {
        renewButtons.forEach(button => {
            button.removeAttribute('onclick');
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const renewServiceModal = document.getElementById('renew-service-modal');
                if (renewServiceModal) {
                    renewServiceModal.style.display = 'block';
                    document.body.style.overflow = 'hidden';
                }
            });
        });
    }
    
    // 直接修复历史版本按钮
    const versionButtons = document.querySelectorAll('a[onclick="showVersionHistory()"]');
    if (versionButtons.length > 0) {
        versionButtons.forEach(button => {
            button.removeAttribute('onclick');
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const versionHistoryModal = document.getElementById('version-history-modal');
                if (versionHistoryModal) {
                    versionHistoryModal.style.display = 'block';
                    document.body.style.overflow = 'hidden';
                }
            });
        });
    }
    
    // 直接修复查看全部按钮
    const announcementButtons = document.querySelectorAll('a[onclick="showAllAnnouncements()"]');
    if (announcementButtons.length > 0) {
        announcementButtons.forEach(button => {
            button.removeAttribute('onclick');
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const allAnnouncementsModal = document.getElementById('all-announcements-modal');
                if (allAnnouncementsModal) {
                    allAnnouncementsModal.style.display = 'block';
                    document.body.style.overflow = 'hidden';
                }
            });
        });
    }
    
    // 直接修复关闭按钮
    const closeButtons = document.querySelectorAll('.close-btn');
    if (closeButtons.length > 0) {
        closeButtons.forEach(button => {
            button.removeAttribute('onclick');
            button.addEventListener('click', function() {
                const modal = this.closest('.modal');
                if (modal) {
                    modal.style.display = 'none';
                    document.body.style.overflow = 'auto';
                }
            });
        });
    }
    
    // 点击模态框外部关闭
    const modals = document.querySelectorAll('.modal');
    if (modals.length > 0) {
        modals.forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.style.display = 'none';
                    document.body.style.overflow = 'auto';
                }
            });
        });
    }
    
    // 添加调试信息
    console.log('模态框直接修复脚本已加载');
    console.log('找到续费按钮数量:', renewButtons.length);
    console.log('找到历史版本按钮数量:', versionButtons.length);
    console.log('找到查看全部按钮数量:', announcementButtons.length);
    console.log('找到关闭按钮数量:', closeButtons.length);
    console.log('找到模态框数量:', modals.length);
});
