<?php
/**
 * 图片路径检测脚本
 * 检查数据库存储的图片路径以及各个页面读取的路径
 */

// 加载配置文件
$configPath = dirname(__DIR__, 2) . '/config/config.php';
if (!file_exists($configPath)) {
    die('配置文件不存在: ' . $configPath);
}
$config = require_once($configPath);
$dbConfig = $config['database'];

try {
    // 连接数据库
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $prefix = $dbConfig['prefix'];

    echo "<h1>🔍 图片路径检测报告</h1>";
    echo "<style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .path { font-family: monospace; background: #f8f9fa; padding: 2px 5px; border-radius: 3px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .exists { color: green; font-weight: bold; }
        .not-exists { color: red; font-weight: bold; }
    </style>";

    // 1. 检查数据库中存储的路径
    echo "<div class='section'>";
    echo "<h2>📊 数据库中存储的图片路径</h2>";
    
    $stmt = $pdo->query("SELECT site_logo, site_favicon, login_bg FROM {$prefix}information WHERE id = 1");
    $dbPaths = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($dbPaths) {
        echo "<table>";
        echo "<tr><th>字段</th><th>数据库路径</th><th>文件是否存在</th></tr>";
        
        $fields = [
            'site_logo' => '网站Logo',
            'site_favicon' => '网站图标', 
            'login_bg' => '登录背景'
        ];
        
        foreach ($fields as $field => $name) {
            $path = $dbPaths[$field] ?? 'NULL';
            $filePath = dirname(__DIR__) . $path;
            $exists = $path !== 'NULL' && file_exists($filePath);
            $existsText = $exists ? '<span class="exists">✅ 存在</span>' : '<span class="not-exists">❌ 不存在</span>';
            
            echo "<tr>";
            echo "<td><strong>$name</strong></td>";
            echo "<td><span class='path'>$path</span></td>";
            echo "<td>$existsText</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='error'>❌ 数据库中没有找到配置记录</p>";
    }
    echo "</div>";

    // 2. 模拟仪表盘页面读取逻辑
    echo "<div class='section'>";
    echo "<h2>🏠 仪表盘页面读取逻辑</h2>";
    
    // 获取数据库配置
    $stmt = $pdo->query("SELECT * FROM {$prefix}webpz WHERE id = 1");
    $webConfig = $stmt->fetch(PDO::FETCH_ASSOC) ?: [];
    
    $stmt = $pdo->query("SELECT * FROM {$prefix}information WHERE id = 1");
    $infoConfig = $stmt->fetch(PDO::FETCH_ASSOC) ?: [];
    
    // 模拟仪表盘的映射逻辑
    $dashboardSettings = [
        'site_name' => $webConfig['name'] ?? '去水印接口',
        'site_logo' => $infoConfig['site_logo'] ?? '/assets/images/logo.png',
        'site_favicon' => $infoConfig['site_favicon'] ?? '/assets/images/favicon.ico',
    ];
    
    echo "<table>";
    echo "<tr><th>设置项</th><th>读取到的路径</th><th>文件是否存在</th><th>来源</th></tr>";
    
    foreach (['site_logo' => '网站Logo', 'site_favicon' => '网站图标'] as $key => $name) {
        $path = $dashboardSettings[$key];
        $filePath = dirname(__DIR__) . $path;
        $exists = file_exists($filePath);
        $existsText = $exists ? '<span class="exists">✅ 存在</span>' : '<span class="not-exists">❌ 不存在</span>';
        $source = isset($infoConfig[$key]) && $infoConfig[$key] ? '数据库' : '默认值';
        
        echo "<tr>";
        echo "<td><strong>$name</strong></td>";
        echo "<td><span class='path'>$path</span></td>";
        echo "<td>$existsText</td>";
        echo "<td>$source</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";

    // 3. 模拟网站设置页面读取逻辑
    echo "<div class='section'>";
    echo "<h2>⚙️ 网站设置页面读取逻辑</h2>";
    
    // 模拟设置页面的映射逻辑
    $settingsPageSettings = [
        'site_logo' => $infoConfig['site_logo'] ?? '/assets/images/logo.png',
        'site_favicon' => $infoConfig['site_favicon'] ?? '/assets/images/favicon.ico',
        'login_bg' => $infoConfig['login_bg'] ?? '/assets/images/login-bg.png',
    ];
    
    echo "<table>";
    echo "<tr><th>设置项</th><th>读取到的路径</th><th>文件是否存在</th><th>来源</th></tr>";
    
    foreach (['site_logo' => '网站Logo', 'site_favicon' => '网站图标', 'login_bg' => '登录背景'] as $key => $name) {
        $path = $settingsPageSettings[$key];
        $filePath = dirname(__DIR__) . $path;
        $exists = file_exists($filePath);
        $existsText = $exists ? '<span class="exists">✅ 存在</span>' : '<span class="not-exists">❌ 不存在</span>';
        $source = isset($infoConfig[$key]) && $infoConfig[$key] ? '数据库' : '默认值';
        
        echo "<tr>";
        echo "<td><strong>$name</strong></td>";
        echo "<td><span class='path'>$path</span></td>";
        echo "<td>$existsText</td>";
        echo "<td>$source</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";

    // 4. 模拟登录页面读取逻辑
    echo "<div class='section'>";
    echo "<h2>🔐 登录页面读取逻辑</h2>";
    
    // 模拟登录页面的映射逻辑
    $loginSettings = [
        'site_name' => $webConfig['name'] ?? '去水印接口',
        'site_logo' => $infoConfig['site_logo'] ?? '/assets/images/logo.png',
        'site_favicon' => $infoConfig['site_favicon'] ?? '/assets/images/favicon.ico',
        'login_bg' => $infoConfig['login_bg'] ?? '/assets/images/login-bg.png',
    ];
    
    echo "<table>";
    echo "<tr><th>设置项</th><th>读取到的路径</th><th>文件是否存在</th><th>来源</th></tr>";
    
    foreach (['site_logo' => '网站Logo', 'site_favicon' => '网站图标', 'login_bg' => '登录背景'] as $key => $name) {
        $path = $loginSettings[$key];
        $filePath = dirname(__DIR__) . $path;
        $exists = file_exists($filePath);
        $existsText = $exists ? '<span class="exists">✅ 存在</span>' : '<span class="not-exists">❌ 不存在</span>';
        $source = isset($infoConfig[$key]) && $infoConfig[$key] ? '数据库' : '默认值';
        
        echo "<tr>";
        echo "<td><strong>$name</strong></td>";
        echo "<td><span class='path'>$path</span></td>";
        echo "<td>$existsText</td>";
        echo "<td>$source</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";

    // 5. 文件系统检查
    echo "<div class='section'>";
    echo "<h2>📁 文件系统检查</h2>";
    
    $checkPaths = [
        '/assets/images/logo.png' => '自定义Logo',
        '/assets/images/favicon.ico' => '自定义图标',
        '/assets/images/login-bg.png' => '自定义登录背景',
        '/assets/defaults/logo.png' => '默认Logo',
        '/assets/defaults/favicon.ico' => '默认图标',
        '/assets/defaults/login-bg.png' => '默认登录背景',
    ];
    
    echo "<table>";
    echo "<tr><th>文件路径</th><th>描述</th><th>是否存在</th><th>文件大小</th></tr>";
    
    foreach ($checkPaths as $path => $desc) {
        $filePath = dirname(__DIR__) . $path;
        $exists = file_exists($filePath);
        $existsText = $exists ? '<span class="exists">✅ 存在</span>' : '<span class="not-exists">❌ 不存在</span>';
        $size = $exists ? number_format(filesize($filePath)) . ' 字节' : '-';
        
        echo "<tr>";
        echo "<td><span class='path'>$path</span></td>";
        echo "<td>$desc</td>";
        echo "<td>$existsText</td>";
        echo "<td>$size</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "</div>";

    // 6. 总结和建议
    echo "<div class='section'>";
    echo "<h2>📋 总结和建议</h2>";
    
    $issues = [];
    
    // 检查数据库路径对应的文件是否存在
    foreach ($fields as $field => $name) {
        $path = $dbPaths[$field] ?? null;
        if ($path) {
            $filePath = dirname(__DIR__) . $path;
            if (!file_exists($filePath)) {
                $issues[] = "数据库中的{$name}路径 <span class='path'>$path</span> 对应的文件不存在";
            }
        }
    }
    
    if (empty($issues)) {
        echo "<p class='success'>✅ 所有检查都通过，图片路径配置正常！</p>";
    } else {
        echo "<p class='warning'>⚠️ 发现以下问题：</p>";
        echo "<ul>";
        foreach ($issues as $issue) {
            echo "<li>$issue</li>";
        }
        echo "</ul>";
        
        echo "<p><strong>建议：</strong></p>";
        echo "<ul>";
        echo "<li>如果使用自定义图片，请确保文件已上传到 <span class='path'>/assets/images/</span> 目录</li>";
        echo "<li>如果要使用默认图片，请点击外观设置的重置按钮</li>";
        echo "<li>确保 <span class='path'>/assets/defaults/</span> 目录下有默认图片文件</li>";
        echo "</ul>";
    }
    echo "</div>";

} catch (PDOException $e) {
    echo "<div class='section error'>";
    echo "<h2>❌ 数据库连接失败</h2>";
    echo "<p>错误信息：" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<div class="section">
    <h2>🔄 操作</h2>
    <p>
        <a href="/aa/public/dashboard/settings/?tab=appearance" style="background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;">前往外观设置</a>
        <a href="/aa/public/dashboard/" style="background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-left: 10px;">前往仪表盘</a>
        <a href="/aa/public/login/" style="background: #6c757d; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-left: 10px;">前往登录页</a>
    </p>
</div>
