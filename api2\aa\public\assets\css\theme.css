/**
 * 情侣头像匹配系统 - 主题样式
 */

:root {
  /* 主色调 */
  --primary-color: #FF6B95;
  --primary-light: #FFA5C0;
  --primary-dark: #D84B75;
  
  /* 辅助色 */
  --secondary-color: #8A6FD6;
  --secondary-light: #B0A0E4;
  --secondary-dark: #6A4FB6;
  
  /* 中性色 */
  --neutral-100: #FFFFFF;
  --neutral-200: #F8F9FA;
  --neutral-300: #E9ECEF;
  --neutral-400: #DEE2E6;
  --neutral-500: #ADB5BD;
  --neutral-600: #6C757D;
  --neutral-700: #495057;
  --neutral-800: #343A40;
  --neutral-900: #212529;
  
  /* 功能色 */
  --success-color: #28C76F;
  --info-color: #00CFE8;
  --warning-color: #FF9F43;
  --danger-color: #EA5455;
  
  /* 字体 */
  --font-family-sans-serif: 'PingFang SC', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Se<PERSON><PERSON> UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
  
  /* 阴影 */
  --box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  
  /* 圆角 */
  --border-radius-sm: 0.25rem;
  --border-radius: 0.5rem;
  --border-radius-lg: 1rem;
  --border-radius-pill: 50rem;
  
  /* 过渡 */
  --transition-base: all 0.2s ease-in-out;
  --transition-fade: opacity 0.15s linear;
  --transition-collapse: height 0.35s ease;
}

/* 全局样式 */
body {
  font-family: var(--font-family-sans-serif);
  background-color: var(--neutral-200);
  color: var(--neutral-800);
  line-height: 1.5;
  transition: var(--transition-base);
}

/* 按钮样式 */
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
  transition: var(--transition-base);
}

.btn-primary:hover, .btn-primary:focus {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 107, 149, 0.3);
}

.btn-secondary {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
  color: white;
  transition: var(--transition-base);
}

.btn-secondary:hover, .btn-secondary:focus {
  background-color: var(--secondary-dark);
  border-color: var(--secondary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(138, 111, 214, 0.3);
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
  transition: var(--transition-base);
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 107, 149, 0.3);
}

/* 卡片样式 */
.card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-sm);
  transition: var(--transition-base);
}

.card:hover {
  box-shadow: var(--box-shadow);
  transform: translateY(-5px);
}

.card-header {
  background-color: var(--neutral-100);
  border-bottom: 1px solid var(--neutral-300);
  padding: 1.25rem 1.5rem;
  border-top-left-radius: var(--border-radius) !important;
  border-top-right-radius: var(--border-radius) !important;
}

.card-body {
  padding: 1.5rem;
}

/* 表单样式 */
.form-control {
  border-radius: var(--border-radius);
  border: 1px solid var(--neutral-400);
  padding: 0.75rem 1rem;
  transition: var(--transition-base);
}

.form-control:focus {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 0.25rem rgba(255, 107, 149, 0.25);
}

.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--neutral-700);
}

/* 导航样式 */
.navbar {
  background-color: var(--neutral-100);
  box-shadow: var(--box-shadow-sm);
}

.navbar-brand {
  font-weight: 700;
  color: var(--primary-color);
}

.nav-link {
  color: var(--neutral-700);
  font-weight: 500;
  transition: var(--transition-base);
}

.nav-link:hover, .nav-link:focus {
  color: var(--primary-color);
}

.nav-link.active {
  color: var(--primary-color);
  font-weight: 700;
}

/* 表格样式 */
.table {
  border-radius: var(--border-radius);
  overflow: hidden;
}

.table thead th {
  background-color: var(--neutral-200);
  border-bottom: none;
  font-weight: 600;
  color: var(--neutral-700);
}

.table tbody tr {
  transition: var(--transition-base);
}

.table tbody tr:hover {
  background-color: rgba(255, 107, 149, 0.05);
}

/* 徽章样式 */
.badge {
  font-weight: 500;
  padding: 0.35em 0.65em;
  border-radius: var(--border-radius-pill);
}

.badge-primary {
  background-color: var(--primary-color);
  color: white;
}

.badge-secondary {
  background-color: var(--secondary-color);
  color: white;
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-in-up {
  animation: slideInUp 0.5s ease-in-out;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .card {
    margin-bottom: 1rem;
  }
  
  .btn {
    padding: 0.5rem 1rem;
  }
}

/* 暗黑模式 */
.dark-mode {
  --neutral-100: #212529;
  --neutral-200: #343A40;
  --neutral-300: #495057;
  --neutral-400: #6C757D;
  --neutral-500: #ADB5BD;
  --neutral-600: #DEE2E6;
  --neutral-700: #E9ECEF;
  --neutral-800: #F8F9FA;
  --neutral-900: #FFFFFF;
  
  background-color: var(--neutral-200);
  color: var(--neutral-700);
}

.dark-mode .card,
.dark-mode .navbar {
  background-color: var(--neutral-100);
}

.dark-mode .form-control {
  background-color: var(--neutral-200);
  border-color: var(--neutral-300);
  color: var(--neutral-700);
}

.dark-mode .table thead th {
  background-color: var(--neutral-300);
  color: var(--neutral-700);
}

.dark-mode .table tbody tr:hover {
  background-color: var(--neutral-300);
}
