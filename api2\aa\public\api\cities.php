<?php
// 城市搜索API

// 设置响应头
header('Content-Type: application/json');

try {
    $query = $_GET['q'] ?? '';
    
    if (strlen($query) < 1) {
        echo json_encode(['cities' => []]);
        exit;
    }
    
    // 中国主要城市数据（包含更多城市）
    $cities = [
        // 直辖市
        '北京', '上海', '天津', '重庆',
        
        // 省会城市
        '石家庄', '太原', '呼和浩特', '沈阳', '长春', '哈尔滨',
        '南京', '杭州', '合肥', '福州', '南昌', '济南',
        '郑州', '武汉', '长沙', '广州', '南宁', '海口',
        '成都', '贵阳', '昆明', '拉萨', '西安', '兰州',
        '西宁', '银川', '乌鲁木齐',
        
        // 计划单列市
        '深圳', '青岛', '宁波', '厦门', '大连',
        
        // 副省级城市
        '苏州', '无锡', '常州', '南通', '徐州', '盐城', '扬州', '镇江', '泰州', '宿迁',
        '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水',
        '芜湖', '蚌埠', '淮南', '马鞍山', '淮北', '铜陵', '安庆', '黄山', '滁州', '阜阳',
        '宿州', '六安', '亳州', '池州', '宣城',
        '莆田', '三明', '泉州', '漳州', '南平', '龙岩', '宁德',
        '景德镇', '萍乡', '九江', '新余', '鹰潭', '赣州', '吉安', '宜春', '抚州', '上饶',
        '淄博', '枣庄', '东营', '烟台', '潍坊', '济宁', '泰安', '威海', '日照', '临沂',
        '德州', '聊城', '滨州', '菏泽',
        '开封', '洛阳', '平顶山', '安阳', '鹤壁', '新乡', '焦作', '濮阳', '许昌', '漯河',
        '三门峡', '南阳', '商丘', '信阳', '周口', '驻马店',
        '黄石', '十堰', '宜昌', '襄阳', '鄂州', '荆门', '孝感', '荆州', '黄冈', '咸宁',
        '随州', '恩施',
        '株洲', '湘潭', '衡阳', '邵阳', '岳阳', '常德', '张家界', '益阳', '郴州', '永州',
        '怀化', '娄底', '湘西',
        '韶关', '珠海', '汕头', '佛山', '江门', '湛江', '茂名', '肇庆', '惠州', '梅州',
        '汕尾', '河源', '阳江', '清远', '东莞', '中山', '潮州', '揭阳', '云浮',
        '柳州', '桂林', '梧州', '北海', '防城港', '钦州', '贵港', '玉林', '百色', '贺州',
        '河池', '来宾', '崇左',
        '三亚', '三沙', '儋州',
        '自贡', '攀枝花', '泸州', '德阳', '绵阳', '广元', '遂宁', '内江', '乐山', '南充',
        '眉山', '宜宾', '广安', '达州', '雅安', '巴中', '资阳', '阿坝', '甘孜', '凉山',
        '六盘水', '遵义', '安顺', '毕节', '铜仁', '黔西南', '黔东南', '黔南',
        '曲靖', '玉溪', '保山', '昭通', '丽江', '普洱', '临沧', '楚雄', '红河', '文山',
        '西双版纳', '大理', '德宏', '怒江', '迪庆',
        '日喀则', '昌都', '林芝', '山南', '那曲', '阿里',
        '宝鸡', '咸阳', '铜川', '渭南', '延安', '榆林', '汉中', '安康', '商洛',
        '嘉峪关', '金昌', '白银', '天水', '武威', '张掖', '平凉', '酒泉', '庆阳', '定西',
        '陇南', '临夏', '甘南',
        '海东', '海北', '黄南', '海南', '果洛', '玉树', '海西',
        '石嘴山', '吴忠', '固原', '中卫',
        '克拉玛依', '吐鲁番', '哈密', '昌吉', '博尔塔拉', '巴音郭楞', '阿克苏', '克孜勒苏',
        '喀什', '和田', '伊犁', '塔城', '阿勒泰',
        
        // 港澳台
        '香港', '澳门', '台北', '高雄', '台中', '台南', '新竹', '基隆'
    ];
    
    // 过滤匹配的城市
    $filtered = array_filter($cities, function($city) use ($query) {
        return stripos($city, $query) !== false || 
               strpos($city, $query) !== false;
    });
    
    // 限制返回数量
    $filtered = array_slice(array_values($filtered), 0, 20);
    
    echo json_encode(['cities' => $filtered]);
    
} catch (Exception $e) {
    echo json_encode(['cities' => [], 'error' => $e->getMessage()]);
}
?>
