/**
 * 控制面板位置修复样式
 * 调整控制面板页面账户状态和系统运行时间的位置
 * 添加更新版本模态框样式
 */

/* 账户状态样式调整 */
.stat-item .status-active {
    display: block !important;
    margin-top: 10px !important;
    text-align: center !important;
    width: 100% !important;
}

/* 系统运行时间样式调整 */
#current-date, #current-time {
    display: block !important;
    margin-top: 10px !important;
    text-align: center !important;
    width: 100% !important;
}

#current-time {
    margin-top: 5px !important;
}

/* 添加更新版本按钮样式 */
.update-version-btn {
    display: inline-block !important;
    margin-top: 8px !important;
    text-align: center !important;
}

/* 确保按钮样式一致 */
.btn-sm.btn-outline-primary, .btn-xs.btn-outline-primary {
    display: inline-block !important;
    padding: 4px 12px !important;
    font-size: 0.9em !important;
    border-radius: 4px !important;
    background-color: transparent !important;
    color: var(--primary-color) !important;
    border: 1px solid var(--primary-color) !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    margin-top: 8px !important;
    text-align: center !important;
}

.btn-sm.btn-outline-primary:hover {
    background-color: var(--primary-color) !important;
    color: white !important;
}

/* 更新版本模态框样式 */
.update-info {
    padding: 15px !important;
    background-color: #f8f9fa !important;
    border-radius: 8px !important;
}

.update-header {
    margin-bottom: 15px !important;
    padding-bottom: 10px !important;
    border-bottom: 1px solid #eee !important;
}

.update-header h4 {
    margin: 0 0 5px 0 !important;
    color: var(--primary-color) !important;
    font-weight: 600 !important;
}

.update-date {
    color: #666 !important;
    font-size: 0.9em !important;
}

.update-content h5 {
    margin-bottom: 10px !important;
    font-weight: 600 !important;
}

.update-content ul {
    padding-left: 20px !important;
    margin-bottom: 15px !important;
}

.update-content ul li {
    margin-bottom: 8px !important;
    line-height: 1.5 !important;
}

.update-note {
    padding: 10px !important;
    background-color: #fff3cd !important;
    border-left: 4px solid #ffc107 !important;
    border-radius: 4px !important;
    margin-top: 15px !important;
}

.update-note p {
    margin: 0 !important;
    color: #856404 !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .btn-sm.btn-outline-primary {
        padding: 3px 10px !important;
        font-size: 0.8em !important;
    }

    .update-info {
        padding: 10px !important;
    }

    .update-content ul {
        padding-left: 15px !important;
    }
}
