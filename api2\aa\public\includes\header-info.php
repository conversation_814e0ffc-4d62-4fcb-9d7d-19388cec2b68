<?php
// 确保用户信息可用
if (!isset($username)) {
    $username = $_SESSION['username'] ?? '管理员';
}
if (!isset($userInitial)) {
    $userInitial = mb_substr($username, 0, 1, 'UTF-8');
}
?>

<!-- 头部信息区域 -->
<div class="header">
    <button class="menu-toggle" id="menu-toggle"><i class="bi bi-list"></i></button>
    <div class="header-info-content" id="header-info-content">
        <div class="wisdom-quote">
            <i class="bi bi-quote"></i>
            <span id="wisdom-text">加载中...</span>
        </div>
        <div class="weather-info">
            <i class="bi bi-cloud-sun"></i>
            <span id="weather-text">加载中...</span>
        </div>
        <div class="info-actions">
            <button id="refresh-info" title="刷新"><i class="bi bi-arrow-clockwise"></i></button>
            <button id="toggle-info-type" title="切换显示内容"><i class="bi bi-shuffle"></i></button>
            <button id="settings-info" title="设置" style="cursor: pointer; pointer-events: auto;"><i class="bi bi-gear"></i></button>
        </div>

        <!-- 天气设置模态框 -->
        <div class="modal fade" id="weatherSettingsModal" tabindex="-1" aria-labelledby="weatherSettingsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="weatherSettingsModalLabel">天气设置</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="weatherSettingsForm">
                            <div class="mb-3">
                                <label for="weatherApiProvider" class="form-label">天气API服务商</label>
                                <select class="form-select" id="weatherApiProvider" name="api_provider" onchange="updateApiProviderInfo()">
                                    <option value="openweather">OpenWeatherMap</option>
                                </select>
                                <div id="apiProviderInfo" class="mt-2">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle"></i>
                                        OpenWeatherMap 提供准确的全球天气数据，需要免费注册获取API密钥。
                                        <a href="https://openweathermap.org/api" target="_blank" class="text-primary">获取API密钥</a>
                                    </small>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="weatherApiKey" class="form-label">API密钥</label>
                                <input type="text" class="form-control" id="weatherApiKey" name="api_key" placeholder="请输入您的API密钥">
                                <small class="text-muted">请输入从天气服务商获取的API密钥</small>
                            </div>
                            <div class="mb-3">
                                <label for="weatherCity" class="form-label">默认城市</label>
                                <input type="text" class="form-control" id="weatherCity" name="city" value="北京" placeholder="请输入城市名称">
                                <small class="text-muted">支持中文城市名称，如：北京、上海、广州等</small>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="saveWeatherSettings()">保存设置</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="user-info dropdown">
        <div class="user-avatar"><?php echo htmlspecialchars($userInitial); ?></div>
        <span class="user-name"><?php echo htmlspecialchars($username); ?></span>
        <div class="dropdown-content">
            <a href="/profile/index.php"><i class="bi bi-person"></i> 个人资料</a>
            <a href="/dashboard/settings"><i class="bi bi-gear"></i> 设置</a>
            <a href="/logout.php"><i class="bi bi-box-arrow-right"></i> 退出登录</a>
        </div>
    </div>
</div>
