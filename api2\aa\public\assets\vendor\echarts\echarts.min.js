/*!
 * ECharts, a JavaScript visualization library.
 *
 * Licensed under the Apache License, Version 2.0
 *
 * This is a simplified version of ECharts for local use
 */

(function (global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
    typeof define === 'function' && define.amd ? define(['exports'], factory) :
    (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.echarts = {}));
}(this, (function (exports) { 'use strict';

    // 简化版的ECharts核心功能
    var echarts = {};

    // 版本信息
    echarts.version = '5.4.3-simplified';

    // 图表实例集合
    var instances = {};
    var idBase = new Date() - 0;
    var DOM_ATTRIBUTE_KEY = '_echarts_instance_';

    // 初始化图表
    echarts.init = function (dom, theme, opts) {
        if (!dom) {
            console.error('Initialize failed: dom is null/undefined');
            return;
        }

        var instance = {
            id: 'ec_' + idBase++,
            dom: dom,
            option: {},
            resize: function() {
                console.log('Chart resize called');
            },
            setOption: function(option) {
                this.option = option;
                console.log('setOption called with:', option);
                this._render();
            },
            _render: function() {
                // 简化版渲染函数
                var dom = this.dom;
                dom.innerHTML = '<div style="padding: 20px; text-align: center;">' +
                    '<div style="color: #333; font-size: 16px; margin-bottom: 10px;">' +
                    (this.option.title && this.option.title.text || '图表') + '</div>' +
                    '<div style="color: #666;">简化版ECharts已加载 - 实际图表将在完整版加载后显示</div>' +
                    '</div>';
            },
            dispose: function() {
                var id = this.id;
                delete instances[id];
                this.dom.removeAttribute(DOM_ATTRIBUTE_KEY);
            }
        };

        // 存储实例
        instances[instance.id] = instance;
        dom.setAttribute(DOM_ATTRIBUTE_KEY, instance.id);

        // 初始渲染
        instance._render();

        return instance;
    };

    // 获取实例
    echarts.getInstanceByDom = function (dom) {
        var key = dom.getAttribute(DOM_ATTRIBUTE_KEY);
        return instances[key];
    };

    // 注册主题
    echarts.registerTheme = function (name, theme) {
        console.log('Theme registered:', name);
    };

    // 注册地图
    echarts.registerMap = function (mapName, geoJson, specialAreas) {
        console.log('Map registered:', mapName);
    };

    // 导出
    exports.version = echarts.version;
    exports.init = echarts.init;
    exports.getInstanceByDom = echarts.getInstanceByDom;
    exports.registerTheme = echarts.registerTheme;
    exports.registerMap = echarts.registerMap;

    Object.defineProperty(exports, '__esModule', { value: true });

})));

// 加载完成后触发事件
document.dispatchEvent(new Event('echarts_loaded'));
console.log('本地ECharts库加载完成');

// 通知页面ECharts已加载完成
setTimeout(function() {
    // 如果页面有初始化图表的函数，确保调用
    if (typeof initCharts === 'function') {
        console.log('重新初始化图表');
        try {
            initCharts();
        } catch (e) {
            console.error('初始化图表时出错:', e);
        }
    }
}, 100);
