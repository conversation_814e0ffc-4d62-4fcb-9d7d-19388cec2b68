/**
 * 插件使用统计功能
 * 提供插件安装次数、活跃用户数等统计数据
 */

/**
 * 初始化插件使用统计
 */
function initPluginUsageStats() {
    console.log('初始化插件使用统计');
    
    // 添加使用统计标签
    addUsageStatsTags();
    
    // 添加使用统计详情按钮
    addUsageStatsButtons();
    
    // 创建使用统计图表
    createUsageStatsCharts();
}

/**
 * 添加使用统计标签
 */
function addUsageStatsTags() {
    // 获取所有插件行
    const pluginRows = document.querySelectorAll('tbody tr');
    
    pluginRows.forEach(row => {
        // 获取插件ID
        const pluginId = row.getAttribute('data-id');
        if (!pluginId) return;
        
        // 获取插件名称单元格
        const nameCell = row.querySelector('td:nth-child(2)');
        if (!nameCell) return;
        
        // 获取插件描述单元格
        const descCell = row.querySelector('td:nth-child(4)');
        if (!descCell) return;
        
        // 生成使用统计数据
        const installCount = generateInstallCount(pluginId);
        const activeUsers = Math.floor(installCount * 0.7); // 假设70%的安装用户是活跃用户
        
        // 创建使用统计标签
        const statsTag = document.createElement('div');
        statsTag.className = 'usage-stats-tag';
        statsTag.innerHTML = `
            <span class="install-count" title="安装次数">
                <i class="bi bi-download"></i> ${formatNumber(installCount)}
            </span>
            <span class="active-users" title="活跃用户数">
                <i class="bi bi-people-fill"></i> ${formatNumber(activeUsers)}
            </span>
        `;
        
        // 添加到描述单元格
        descCell.appendChild(statsTag);
    });
    
    console.log('使用统计标签已添加');
}

/**
 * 生成安装次数
 * @param {string} pluginId - 插件ID
 * @returns {number} 安装次数
 */
function generateInstallCount(pluginId) {
    // 根据插件ID生成一个伪随机的安装次数
    const id = parseInt(pluginId);
    const base = id * 100;
    const random = Math.floor(Math.random() * 500);
    return base + random;
}

/**
 * 格式化数字
 * @param {number} num - 数字
 * @returns {string} 格式化后的数字
 */
function formatNumber(num) {
    if (num >= 10000) {
        return (num / 10000).toFixed(1) + 'w';
    } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'k';
    } else {
        return num.toString();
    }
}

/**
 * 添加使用统计详情按钮
 */
function addUsageStatsButtons() {
    // 获取所有插件行
    const pluginRows = document.querySelectorAll('tbody tr');
    
    pluginRows.forEach(row => {
        // 获取插件ID
        const pluginId = row.getAttribute('data-id');
        if (!pluginId) return;
        
        // 获取插件操作单元格
        const actionsCell = row.querySelector('td:last-child');
        if (!actionsCell) return;
        
        // 创建统计按钮
        const statsButton = document.createElement('button');
        statsButton.className = 'btn btn-sm btn-outline-info stats-btn';
        statsButton.innerHTML = '<i class="bi bi-bar-chart"></i> 统计';
        statsButton.setAttribute('data-plugin-id', pluginId);
        statsButton.addEventListener('click', function() {
            showPluginStats(pluginId);
        });
        
        // 添加到操作单元格
        actionsCell.appendChild(statsButton);
    });
    
    console.log('使用统计详情按钮已添加');
}

/**
 * 显示插件统计详情
 * @param {string} pluginId - 插件ID
 */
function showPluginStats(pluginId) {
    console.log(`显示插件统计详情: ${pluginId}`);
    
    // 获取插件行
    const pluginRow = document.querySelector(`tr[data-id="${pluginId}"]`);
    if (!pluginRow) return;
    
    // 获取插件名称
    const nameCell = pluginRow.querySelector('td:nth-child(2)');
    if (!nameCell) return;
    
    const pluginName = nameCell.textContent.trim();
    
    // 创建或获取统计详情模态框
    let statsModal = document.getElementById('plugin-stats-modal');
    
    if (!statsModal) {
        statsModal = document.createElement('div');
        statsModal.className = 'modal';
        statsModal.id = 'plugin-stats-modal';
        statsModal.innerHTML = `
            <div class="modal-content modal-lg">
                <div class="modal-header">
                    <h3 id="stats-plugin-name">插件使用统计</h3>
                    <button class="close-btn" onclick="closeModal('plugin-stats-modal')"><i class="bi bi-x-lg"></i></button>
                </div>
                <div class="modal-body">
                    <div class="stats-summary">
                        <div class="stats-card">
                            <div class="stats-card-title">总安装次数</div>
                            <div class="stats-card-value" id="stats-install-count">0</div>
                            <div class="stats-card-trend up"><i class="bi bi-graph-up"></i> 较上月增长 <span id="stats-install-growth">0%</span></div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-card-title">活跃用户数</div>
                            <div class="stats-card-value" id="stats-active-users">0</div>
                            <div class="stats-card-trend up"><i class="bi bi-graph-up"></i> 较上月增长 <span id="stats-active-growth">0%</span></div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-card-title">平均使用时长</div>
                            <div class="stats-card-value" id="stats-usage-time">0分钟</div>
                            <div class="stats-card-trend up"><i class="bi bi-graph-up"></i> 较上月增长 <span id="stats-time-growth">0%</span></div>
                        </div>
                        <div class="stats-card">
                            <div class="stats-card-title">平均评分</div>
                            <div class="stats-card-value" id="stats-rating">0.0</div>
                            <div class="stats-card-trend up"><i class="bi bi-graph-up"></i> 较上月增长 <span id="stats-rating-growth">0%</span></div>
                        </div>
                    </div>
                    
                    <div class="stats-charts">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="stats-chart-container">
                                    <h4>安装趋势</h4>
                                    <canvas id="install-trend-chart"></canvas>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="stats-chart-container">
                                    <h4>活跃用户趋势</h4>
                                    <canvas id="active-users-chart"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="stats-chart-container">
                                    <h4>平台分布</h4>
                                    <canvas id="platform-distribution-chart"></canvas>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="stats-chart-container">
                                    <h4>用户反馈</h4>
                                    <canvas id="user-feedback-chart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-outline-secondary" onclick="exportPluginStats('${pluginId}')">
                        <i class="bi bi-download"></i> 导出数据
                    </button>
                    <button class="btn btn-primary" onclick="closeModal('plugin-stats-modal')">关闭</button>
                </div>
            </div>
        `;
        document.body.appendChild(statsModal);
    }
    
    // 更新模态框内容
    document.getElementById('stats-plugin-name').textContent = `${pluginName} - 使用统计`;
    
    // 生成统计数据
    const installCount = generateInstallCount(pluginId);
    const activeUsers = Math.floor(installCount * 0.7);
    const usageTime = Math.floor(Math.random() * 30) + 10; // 10-40分钟
    const rating = (Math.random() * 2 + 3).toFixed(1); // 3.0-5.0
    
    const installGrowth = (Math.random() * 20 + 5).toFixed(1); // 5%-25%
    const activeGrowth = (Math.random() * 15 + 3).toFixed(1); // 3%-18%
    const timeGrowth = (Math.random() * 10 + 2).toFixed(1); // 2%-12%
    const ratingGrowth = (Math.random() * 5 + 1).toFixed(1); // 1%-6%
    
    // 更新统计数据
    document.getElementById('stats-install-count').textContent = installCount.toLocaleString();
    document.getElementById('stats-active-users').textContent = activeUsers.toLocaleString();
    document.getElementById('stats-usage-time').textContent = `${usageTime}分钟`;
    document.getElementById('stats-rating').textContent = rating;
    
    document.getElementById('stats-install-growth').textContent = `${installGrowth}%`;
    document.getElementById('stats-active-growth').textContent = `${activeGrowth}%`;
    document.getElementById('stats-time-growth').textContent = `${timeGrowth}%`;
    document.getElementById('stats-rating-growth').textContent = `${ratingGrowth}%`;
    
    // 显示模态框
    openModal('plugin-stats-modal');
    
    // 创建图表
    setTimeout(() => {
        createInstallTrendChart(pluginId);
        createActiveUsersChart(pluginId);
        createPlatformDistributionChart(pluginId);
        createUserFeedbackChart(pluginId);
    }, 100);
}

/**
 * 创建安装趋势图表
 * @param {string} pluginId - 插件ID
 */
function createInstallTrendChart(pluginId) {
    const canvas = document.getElementById('install-trend-chart');
    if (!canvas) return;
    
    // 清除现有图表
    const existingChart = Chart.getChart(canvas);
    if (existingChart) {
        existingChart.destroy();
    }
    
    // 生成数据
    const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
    const installData = generateMonthlyData(pluginId, 12);
    
    // 创建图表
    new Chart(canvas, {
        type: 'line',
        data: {
            labels: months,
            datasets: [{
                label: '安装次数',
                data: installData,
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

/**
 * 创建活跃用户图表
 * @param {string} pluginId - 插件ID
 */
function createActiveUsersChart(pluginId) {
    const canvas = document.getElementById('active-users-chart');
    if (!canvas) return;
    
    // 清除现有图表
    const existingChart = Chart.getChart(canvas);
    if (existingChart) {
        existingChart.destroy();
    }
    
    // 生成数据
    const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
    const installData = generateMonthlyData(pluginId, 12);
    const activeData = installData.map(value => Math.floor(value * 0.7));
    
    // 创建图表
    new Chart(canvas, {
        type: 'line',
        data: {
            labels: months,
            datasets: [{
                label: '活跃用户数',
                data: activeData,
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

/**
 * 创建平台分布图表
 * @param {string} pluginId - 插件ID
 */
function createPlatformDistributionChart(pluginId) {
    const canvas = document.getElementById('platform-distribution-chart');
    if (!canvas) return;
    
    // 清除现有图表
    const existingChart = Chart.getChart(canvas);
    if (existingChart) {
        existingChart.destroy();
    }
    
    // 生成数据
    const platforms = ['微信小程序', 'QQ小程序', '支付宝小程序', '百度小程序', '头条小程序'];
    const distributionData = generateDistributionData(5);
    
    // 创建图表
    new Chart(canvas, {
        type: 'doughnut',
        data: {
            labels: platforms,
            datasets: [{
                data: distributionData,
                backgroundColor: [
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 206, 86, 0.8)',
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(153, 102, 255, 0.8)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                }
            }
        }
    });
}

/**
 * 创建用户反馈图表
 * @param {string} pluginId - 插件ID
 */
function createUserFeedbackChart(pluginId) {
    const canvas = document.getElementById('user-feedback-chart');
    if (!canvas) return;
    
    // 清除现有图表
    const existingChart = Chart.getChart(canvas);
    if (existingChart) {
        existingChart.destroy();
    }
    
    // 生成数据
    const ratings = ['5星', '4星', '3星', '2星', '1星'];
    const feedbackData = generateFeedbackData();
    
    // 创建图表
    new Chart(canvas, {
        type: 'bar',
        data: {
            labels: ratings,
            datasets: [{
                label: '用户评分',
                data: feedbackData,
                backgroundColor: [
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 206, 86, 0.8)',
                    'rgba(255, 159, 64, 0.8)',
                    'rgba(255, 99, 132, 0.8)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            indexAxis: 'y',
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    beginAtZero: true
                }
            }
        }
    });
}

/**
 * 生成月度数据
 * @param {string} pluginId - 插件ID
 * @param {number} months - 月份数量
 * @returns {Array} 月度数据
 */
function generateMonthlyData(pluginId, months) {
    const id = parseInt(pluginId);
    const baseValue = id * 10;
    
    return Array.from({length: months}, (_, i) => {
        const month = i + 1;
        const growth = Math.pow(1.1, month); // 假设每月增长10%
        return Math.floor((baseValue + Math.random() * 50) * growth);
    });
}

/**
 * 生成分布数据
 * @param {number} count - 数据点数量
 * @returns {Array} 分布数据
 */
function generateDistributionData(count) {
    // 生成总和为100的随机数据
    const data = Array.from({length: count}, () => Math.random());
    const sum = data.reduce((a, b) => a + b, 0);
    
    return data.map(value => Math.floor((value / sum) * 100));
}

/**
 * 生成反馈数据
 * @returns {Array} 反馈数据
 */
function generateFeedbackData() {
    // 生成偏向高评分的数据
    return [
        Math.floor(Math.random() * 50) + 50, // 5星: 50-100
        Math.floor(Math.random() * 30) + 20, // 4星: 20-50
        Math.floor(Math.random() * 20) + 10, // 3星: 10-30
        Math.floor(Math.random() * 10) + 5,  // 2星: 5-15
        Math.floor(Math.random() * 5) + 1    // 1星: 1-6
    ];
}

/**
 * 导出插件统计数据
 * @param {string} pluginId - 插件ID
 */
function exportPluginStats(pluginId) {
    console.log(`导出插件统计数据: ${pluginId}`);
    
    // 显示加载动画
    showLoading();
    
    // 模拟导出过程
    setTimeout(() => {
        // 隐藏加载动画
        hideLoading();
        
        // 显示成功消息
        showToast('插件统计数据已导出为Excel文件', 'success');
    }, 1000);
}

/**
 * 创建使用统计图表
 */
function createUsageStatsCharts() {
    // 检查是否存在图表容器
    const topPluginsContainer = document.getElementById('top-plugins-chart');
    const categoryDistributionContainer = document.getElementById('category-distribution-chart');
    
    if (topPluginsContainer) {
        createTopPluginsChart(topPluginsContainer);
    }
    
    if (categoryDistributionContainer) {
        createCategoryDistributionChart(categoryDistributionContainer);
    }
}
