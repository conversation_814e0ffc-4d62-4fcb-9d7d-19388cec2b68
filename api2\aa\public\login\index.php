﻿<?php
// 获取网站设置信息用于登录页显示
try {
    // 加载配置文件 - 使用相对路径
    // 从 aa/public/login/ 到 aa/config/ 需要 ../../config/
    $configPath = '../../config/config.php';
    if (!file_exists($configPath)) {
        throw new Exception('配置文件不存在: ' . $configPath . '，当前工作目录: ' . getcwd());
    }
    $config = require_once($configPath);
    $dbConfig = $config['database'];

    // 连接数据库
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $stmt = $pdo->query("SELECT * FROM {$dbConfig['prefix']}webpz WHERE id = 1");
    $webConfig = $stmt->fetch(PDO::FETCH_ASSOC) ?: [];

    $stmt = $pdo->query("SELECT * FROM {$dbConfig['prefix']}information WHERE id = 1");
    $infoConfig = $stmt->fetch(PDO::FETCH_ASSOC) ?: [];

    // 映射设置数据
    $loginSettings = [
        'site_name' => $webConfig['name'] ?? '去水印接口',
        'site_logo' => $infoConfig['site_logo'] ?? '/assets/images/logo.png',
        'site_favicon' => $infoConfig['site_favicon'] ?? '/assets/images/favicon.ico',
        'site_copyright' => $infoConfig['site_copyright'] ?? ('© ' . date('Y') . ' ' . ($webConfig['name'] ?? '管理系统')),
        'icp_number' => $infoConfig['beian_icp'] ?? '',
        'police_number' => $infoConfig['beian_police'] ?? '',
        'login_bg' => $infoConfig['login_bg'] ?? '/assets/images/login-bg.png',
    ];
} catch (PDOException $e) {
    // 数据库连接失败，使用默认设置
    $loginSettings = [
        'site_name' => '去水印接口',
        'site_logo' => '/assets/images/logo.png',
        'site_favicon' => '/assets/images/favicon.ico',
        'site_copyright' => '© ' . date('Y') . ' 管理系统',
        'icp_number' => '',
        'police_number' => '',
        'login_bg' => '/assets/images/login-bg.png',
    ];
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - <?php echo htmlspecialchars($loginSettings['site_name']); ?></title>

    <!-- 动态favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo htmlspecialchars($loginSettings['site_favicon']); ?>">
    <link rel="shortcut icon" href="<?php echo htmlspecialchars($loginSettings['site_favicon']); ?>">

    <!-- Bootstrap Icons - 本地资源 -->
    <link rel="stylesheet" href="/assets/css/bootstrap-icons.css">
    <style>
        body {
            font-family: "Microsoft YaHei", sans-serif;
            <?php if (file_exists(dirname(__DIR__) . '/assets/images/login-bg.png')): ?>
            background: url('/assets/images/login-bg.png?v=<?php echo time(); ?>') center/cover no-repeat;
            <?php else: ?>
            background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
            background-size: 400% 400%;
            animation: gradientBG 15s ease infinite;
            <?php endif; ?>
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }

        @keyframes gradientBG {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .login-container {
            max-width: 400px;
            width: 100%;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            animation: fadeInUp 0.5s;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .card-header {
            background: linear-gradient(135deg, #ff6b95 0%, #ffa5c0 100%);
            color: white;
            padding: 15px 20px;
            text-align: center;
        }

        .logo {
            width: 60px;
            height: 60px;
            margin-bottom: 10px;
            animation: pulse 3s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .card-header h3 {
            margin: 0 0 5px 0;
            font-size: 1.4rem;
            font-weight: 600;
        }

        .card-header p {
            margin: 0;
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .card-body {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        .input-group {
            position: relative;
            display: flex;
        }

        .input-group-text {
            background-color: #ffa5c0;
            color: white;
            border: none;
            border-radius: 5px 0 0 5px;
            padding: 10px;
            display: flex;
            align-items: center;
        }

        .form-control {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 0 5px 5px 0;
            font-size: 16px;
        }

        .form-control:focus {
            outline: none;
            border-color: #ff6b95;
            box-shadow: 0 0 0 3px rgba(255, 107, 149, 0.25);
        }

        .form-check {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .form-check-input {
            margin-right: 10px;
        }

        .btn {
            display: block;
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #ff6b95 0%, #ffa5c0 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(255, 107, 149, 0.4);
        }

        h3, p {
            margin: 0;
        }

        .mb-0 {
            margin-bottom: 0;
        }

        .floating-hearts {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: -1;
        }

        .heart {
            position: absolute;
            width: 20px;
            height: 20px;
            background: url("data:image/svg+xml;utf8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'%23ffffff\'%3E%3Cpath d=\'M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\'/%3E%3C/svg%3E") no-repeat center center;
            opacity: 0;
            animation: floatUp 4s linear infinite;
        }

        @keyframes floatUp {
            0% {
                transform: translateY(100vh) scale(0);
                opacity: 0;
            }
            20% {
                opacity: 0.8;
            }
            80% {
                opacity: 0.8;
            }
            100% {
                transform: translateY(-20vh) scale(1.5);
                opacity: 0;
            }
        }

        .captcha-container {
            display: flex;
            align-items: center;
        }

        .captcha-input {
            flex: 1;
            margin-right: 10px;
        }

        .captcha-box {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100px;
            height: 40px;
            background-color: #f5f7fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
        }

        .error-text {
            color: #dc3545;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }

        /* 提示信息样式 */
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            border-radius: 5px;
        }

        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }

        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }

        .alert-warning {
            color: #856404;
            background-color: #fff3cd;
            border-color: #ffeeba;
        }

        .alert-info {
            color: #0c5460;
            background-color: #d1ecf1;
            border-color: #bee5eb;
        }
    </style>
</head>
<body>
    <div class="floating-hearts" id="floating-hearts"></div>

    <div class="login-container">
        <div class="card-header">
            <img src="<?php echo htmlspecialchars($loginSettings['site_logo']); ?>" alt="Logo" class="logo" onerror="this.src='data:image/svg+xml;utf8,%3Csvg xmlns=\\\'http://www.w3.org/2000/svg\\\' viewBox=\\\'0 0 24 24\\\' fill=\\\'%23ffffff\\\'%3E%3Cpath d=\\\'M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\\\'/%3E%3C/svg%3E'">
            <h3><?php echo htmlspecialchars($loginSettings['site_name']); ?></h3>
            <p>管理后台登录</p>
        </div>
        <div class="card-body">
            <div id="alert-container" style="display: none;"></div>

            <form id="login-form" action="login.php" method="post" onsubmit="return handleSubmit(event);">
                <div class="form-group">
                    <label for="username" class="form-label">用户名</label>
                    <div class="input-group">
                        <div class="input-group-text">👤</div>
                        <input type="text" id="username" name="username" class="form-control" placeholder="请输入用户名" autocomplete="username">
                    </div>
                    <div id="username-error" class="error-text">请输入用户名</div>
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">密码</label>
                    <div class="input-group">
                        <div class="input-group-text">🔒</div>
                        <input type="password" id="password" name="password" class="form-control" placeholder="请输入密码" autocomplete="current-password">
                    </div>
                    <div id="password-error" class="error-text">请输入密码</div>
                </div>

                <div class="form-group">
                    <label for="captcha" class="form-label">验证码</label>
                    <div class="captcha-container">
                        <div class="input-group captcha-input">
                            <div class="input-group-text">🔣</div>
                            <input type="text" id="captcha" name="captcha" class="form-control" placeholder="请输入验证码">
                        </div>
                        <div class="captcha-box" id="captcha-img">
                            <span id="captcha-text">加载中...</span>
                        </div>
                    </div>
                    <div id="captcha-error" class="error-text">请输入验证码</div>
                </div>

                <div class="form-check">
                    <input type="checkbox" id="remember" name="remember" class="form-check-input">
                    <label for="remember" class="form-check-label">记住我</label>
                </div>

                <button type="submit" class="btn">登录</button>
            </form>

            <!-- 底部链接 -->
            <div class="login-footer">
                <div class="footer-links">
                    <a href="/" class="footer-link">
                        <i class="bi bi-house"></i>
                        <span>回到首页</span>
                    </a>
                    <span class="divider">|</span>
                    <a href="/user/login" class="footer-link">
                        <i class="bi bi-person"></i>
                        <span>用户端登录</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 只忽略特定的浏览器扩展错误，保留其他错误用于调试
        window.addEventListener('error', function(e) {
            // 只忽略明确来自浏览器扩展的错误
            if (e.filename && (
                e.filename.includes('content-script') ||
                e.filename.includes('extension') ||
                e.filename.includes('chrome-extension') ||
                e.filename.includes('moz-extension')
            )) {
                console.log('忽略浏览器扩展错误:', e.message);
                e.preventDefault();
                return true;
            }
            // 其他错误正常显示，便于调试
            return false;
        });

        // 创建漂浮的心形
        function createHearts() {
            const container = document.getElementById('floating-hearts');
            const heartCount = 10;

            for (let i = 0; i < heartCount; i++) {
                setTimeout(() => {
                    const heart = document.createElement('div');
                    heart.className = 'heart';
                    heart.style.left = Math.random() * 100 + 'vw';
                    heart.style.animationDelay = Math.random() * 2 + 's';
                    heart.style.opacity = Math.random() * 0.5 + 0.3;
                    container.appendChild(heart);

                    // 动画结束后移除元素
                    setTimeout(() => {
                        heart.remove();
                    }, 4000);
                }, i * 400);
            }
        }

        // 页面加载完成后创建心形
        window.addEventListener('load', function() {
            createHearts();
            // 每隔4秒创建一批新的心形
            setInterval(createHearts, 4000);
        });
    </script>

    <script src="handleSubmit.js"></script>

    <!-- 网站底部 -->
    <div class="site-footer">
        <div class="footer-content">
            <div class="copyright">
                <?php echo htmlspecialchars($loginSettings['site_copyright']); ?>
            </div>
            <div class="beian">
                <?php if (!empty($loginSettings['icp_number'])): ?>
                <a href="https://beian.miit.gov.cn/" target="_blank" class="beian-link">
                    <i class="bi bi-shield-check"></i> <?php echo htmlspecialchars($loginSettings['icp_number']); ?>
                </a>
                <?php endif; ?>
                <?php if (!empty($loginSettings['police_number'])): ?>
                <a href="http://www.beian.gov.cn/portal/index" target="_blank" class="beian-link">
                    <i class="bi bi-shield-lock"></i> <?php echo htmlspecialchars($loginSettings['police_number']); ?>
                </a>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <style>
    /* 底部样式 */
    .site-footer {
        background-color: rgba(255, 255, 255, 0.8);
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        padding: 15px 20px;
        text-align: center;
        font-size: 0.9rem;
        color: #666;
        margin-top: 30px;
        width: 100%;
        position: fixed;
        bottom: 0;
        left: 0;
        z-index: 10;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    }

    .footer-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 8px;
        max-width: 1200px;
        margin: 0 auto;
    }

    .copyright {
        margin-bottom: 5px;
        text-align: center;
    }

    .beian {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
        justify-content: center;
        text-align: center;
    }

    .beian-link {
        color: #666;
        text-decoration: none;
        transition: color 0.3s;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .beian-link:hover {
        color: #ff6b95;
    }

    .beian-link i {
        font-size: 1rem;
    }

    /* 登录表单底部链接样式 */
    .login-footer {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid rgba(255, 107, 149, 0.2);
        text-align: center;
    }

    .footer-links {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;
        flex-wrap: wrap;
    }

    .footer-link {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #666;
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        padding: 8px 12px;
        border-radius: 6px;
        transition: all 0.3s ease;
        background: rgba(255, 107, 149, 0.05);
        border: 1px solid rgba(255, 107, 149, 0.1);
    }

    .footer-link:hover {
        color: #ff6b95;
        text-decoration: none;
        background: rgba(255, 107, 149, 0.1);
        border-color: rgba(255, 107, 149, 0.3);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(255, 107, 149, 0.2);
    }

    .footer-link i {
        font-size: 16px;
        transition: transform 0.3s ease;
    }

    .footer-link:hover i {
        transform: scale(1.1);
    }

    .divider {
        color: rgba(255, 107, 149, 0.4);
        font-weight: 300;
        font-size: 14px;
    }

    /* 响应式设计 */
    @media (max-width: 480px) {
        .footer-links {
            flex-direction: column;
            gap: 12px;
        }

        .divider {
            display: none;
        }

        .footer-link {
            width: 100%;
            justify-content: center;
            padding: 12px;
        }
    }
    </style>
</body>
</html>