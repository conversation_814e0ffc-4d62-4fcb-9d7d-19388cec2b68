<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>天气API密钥获取指南</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .provider { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 10px; }
        .provider h2 { color: #007bff; margin-top: 0; }
        .step { margin: 15px 0; padding: 10px; background: #f8f9fa; border-radius: 5px; }
        .key-format { background: #e7f3ff; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { background: #fff3cd; padding: 10px; border-radius: 5px; border-left: 4px solid #ffc107; }
        .success { background: #d4edda; padding: 10px; border-radius: 5px; border-left: 4px solid #28a745; }
        code { background: #f1f1f1; padding: 2px 5px; border-radius: 3px; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌤️ 天气API密钥获取指南</h1>
        

        
        <div class="provider">
            <h2>🌍 OpenWeatherMap</h2>
            <p><strong>免费额度：</strong>1000次/天 | <strong>支持：</strong>全球城市</p>
            
            <div class="step">
                <h3>步骤1：注册账号</h3>
                <p>访问：<a href="https://openweathermap.org/api" target="_blank">https://openweathermap.org/api</a></p>
                <p>点击"Sign up"注册账号</p>
            </div>
            
            <div class="step">
                <h3>步骤2：获取API密钥</h3>
                <p>登录后，进入"API keys"页面</p>
                <p>复制默认的API密钥，或创建新的密钥</p>
            </div>
            
            <div class="key-format">
                <strong>密钥格式示例：</strong><br>
                <code>a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6</code>
            </div>
        </div>
        
        <div class="provider">
            <h2>🧠 心知天气</h2>
            <p><strong>免费额度：</strong>400次/天 | <strong>支持：</strong>中国城市</p>
            
            <div class="step">
                <h3>步骤1：注册账号</h3>
                <p>访问：<a href="https://www.seniverse.com/" target="_blank">https://www.seniverse.com/</a></p>
                <p>注册开发者账号</p>
            </div>
            
            <div class="step">
                <h3>步骤2：获取密钥</h3>
                <p>登录后，进入控制台</p>
                <p>获取公钥和私钥</p>
            </div>
            
            <div class="warning">
                ⚠️ 心知天气需要公钥和私钥，配置相对复杂，建议使用和风天气。
            </div>
        </div>
        
        <div style="margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 10px;">
            <h2>🔧 配置步骤</h2>
            <ol>
                <li>获取API密钥后，回到系统</li>
                <li>点击天气组件的设置按钮</li>
                <li>选择对应的API服务商</li>
                <li>填入API密钥</li>
                <li>设置默认城市</li>
                <li>点击保存设置</li>
            </ol>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="../dashboard/" class="btn">返回控制面板</a>
            <a href="weather-debug.php" class="btn">API诊断工具</a>
        </div>
    </div>
</body>
</html>
