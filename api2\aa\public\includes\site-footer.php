<?php
// 获取系统设置
function getSystemSettings() {
    try {
        // 加载统一配置文件
        $configPath = dirname(__DIR__, 2) . '/config/config.php';
        if (!file_exists($configPath)) {
            throw new Exception('配置文件不存在');
        }
        $config = require_once($configPath);

        // 检查配置是否有效
        if (!is_array($config) || !isset($config['database'])) {
            throw new Exception('配置文件格式错误');
        }

        $dbConfig = $config['database'];

        // 检查数据库配置是否完整
        if (!is_array($dbConfig) || !isset($dbConfig['host']) || !isset($dbConfig['database']) ||
            !isset($dbConfig['username']) || !isset($dbConfig['password']) || !isset($dbConfig['charset'])) {
            throw new Exception('数据库配置不完整');
        }

        // 连接数据库
        $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
        $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $prefix = isset($dbConfig['prefix']) ? $dbConfig['prefix'] : '';

        // 获取网站基本设置
        $stmt = $pdo->prepare("SELECT * FROM {$prefix}webpz WHERE id = 1 LIMIT 1");
        $stmt->execute();
        $webConfig = $stmt->fetch(PDO::FETCH_ASSOC);

        // 获取系统信息设置
        $stmt = $pdo->prepare("SELECT beian_icp, beian_police, site_logo, site_copyright FROM {$prefix}information WHERE id = 1 LIMIT 1");
        $stmt->execute();
        $infoConfig = $stmt->fetch(PDO::FETCH_ASSOC);

        // 确保数组不为空，并设置默认值
        $siteName = '';
        $beianIcp = '';
        $beianPolice = '';
        $siteLogo = '/assets/images/logo.png';
        $siteCopyright = '';

        if (is_array($webConfig) && isset($webConfig['name'])) {
            $siteName = $webConfig['name'];
        }

        if (is_array($infoConfig)) {
            if (isset($infoConfig['beian_icp'])) {
                $beianIcp = $infoConfig['beian_icp'];
            }
            if (isset($infoConfig['beian_police'])) {
                $beianPolice = $infoConfig['beian_police'];
            }
            if (isset($infoConfig['site_logo'])) {
                $siteLogo = $infoConfig['site_logo'];
            }
            if (isset($infoConfig['site_copyright'])) {
                $siteCopyright = $infoConfig['site_copyright'];
            }
        }

        return [
            'site_name' => $siteName ?: '管理系统',
            'site_copyright' => $siteCopyright ?: ('© ' . date('Y') . ' ' . ($webConfig['name'] ?? '管理系统')),
            'icp_number' => $beianIcp,
            'police_number' => $beianPolice,
            'site_logo' => $siteLogo,
        ];

    } catch (Exception $e) {
        // 数据库连接失败，使用默认设置
        return [
            'site_name' => '管理系统',
            'site_copyright' => '© ' . date('Y') . ' 管理系统',
            'icp_number' => '',
            'police_number' => '',
            'site_logo' => '/assets/images/logo.png',
        ];
    }
}

// 获取设置
$settings = getSystemSettings();
?>

<!-- 网站底部 -->
<div class="site-footer">
    <div class="footer-content">
        <div class="copyright">
            <?php echo htmlspecialchars($settings['site_copyright']); ?>
        </div>
        <div class="beian">
            <?php if (!empty($settings['icp_number'])): ?>
            <a href="https://beian.miit.gov.cn/" target="_blank" class="beian-link">
                <i class="bi bi-shield-check"></i> <?php echo htmlspecialchars($settings['icp_number']); ?>
            </a>
            <?php endif; ?>

            <?php if (!empty($settings['police_number'])): ?>
            <a href="http://www.beian.gov.cn/portal/index" target="_blank" class="beian-link">
                <i class="bi bi-shield-lock"></i> <?php echo htmlspecialchars($settings['police_number']); ?>
            </a>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
/* 底部样式 */
.site-footer {
    background-color: var(--white-color);
    border-top: 1px solid var(--light-color);
    padding: 15px 20px;
    text-align: center;
    font-size: 0.9rem;
    color: var(--gray-color);
    margin-top: 30px;
    width: 100%;
    position: relative;
    bottom: 0;
    left: 0;
    z-index: 10;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    max-width: 1200px;
    margin: 0 auto;
}

.copyright {
    margin-bottom: 5px;
    text-align: center;
}

.beian {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
    text-align: center;
}

.beian-link {
    color: var(--gray-color);
    text-decoration: none;
    transition: color 0.3s;
    display: flex;
    align-items: center;
    gap: 5px;
}

.beian-link:hover {
    color: var(--primary-color);
}

.beian-link i {
    font-size: 1rem;
}

/* 响应式调整 */
@media (min-width: 768px) {
    .footer-content {
        flex-direction: column;
        justify-content: center;
    }

    .copyright {
        margin-bottom: 10px;
    }
}
</style>
