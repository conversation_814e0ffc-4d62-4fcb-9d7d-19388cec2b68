/**
 * 控制面板页面最终修复样式
 * 彻底修复控制面板页面的样式问题
 */

/* 重置所有样式 */
.stat-item {
    position: relative;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: flex-start !important;
    padding: 12px 15px !important;
    margin-bottom: 10px !important;
    background-color: rgba(var(--primary-color-rgb), 0.03) !important;
    border-radius: 6px !important;
    transition: all 0.3s ease !important;
    overflow: hidden !important;
    text-align: center !important;
    min-width: 0 !important;
    height: 120px !important; /* 固定高度 */
    box-sizing: border-box !important;
}

/* 统一标签样式 */
.stat-label {
    font-weight: 500 !important;
    color: var(--text-muted) !important;
    margin-bottom: 8px !important;
    line-height: 1.4 !important;
    font-size: 0.9em !important;
    border-bottom: 1px dashed rgba(var(--primary-color-rgb), 0.2) !important;
    padding-bottom: 5px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    width: 100% !important;
    text-align: center !important;
    display: block !important;
    height: 30px !important; /* 固定高度，确保所有标签高度一致 */
    box-sizing: border-box !important;
}

/* 统一值样式 */
.stat-value {
    font-weight: 600 !important;
    color: var(--text-color) !important;
    line-height: 1.4 !important;
    font-size: 1.1em !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    width: 100% !important;
    text-align: center !important;
    display: block !important;
    box-sizing: border-box !important;
}

/* 统一带按钮的值样式 */
.stat-value-with-btn {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    flex-grow: 1 !important;
    height: calc(100% - 30px) !important; /* 减去标签高度 */
    box-sizing: border-box !important;
}

/* 统一带按钮的值中的值样式 */
.stat-value-with-btn .stat-value {
    margin-bottom: 8px !important;
    width: 100% !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    display: block !important;
    box-sizing: border-box !important;
}

/* 统一带按钮的值中的按钮样式 */
.stat-value-with-btn .btn {
    white-space: nowrap !important;
    font-size: 0.9em !important;
    padding: 4px 12px !important;
    margin-top: 2px !important;
    box-sizing: border-box !important;
}

/* 统一多行值样式 */
.stat-value-multiline {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    flex-grow: 1 !important;
    height: calc(100% - 30px) !important; /* 减去标签高度 */
    box-sizing: border-box !important;
}

/* 统一多行值中的值样式 */
.stat-value-multiline .stat-value {
    margin-bottom: 4px !important;
    width: 100% !important;
    white-space: normal !important;
    word-break: break-word !important;
    line-height: 1.3 !important;
    display: block !important;
    box-sizing: border-box !important;
}

/* 统一多行值中的最后一个值样式 */
.stat-value-multiline .stat-value:last-child {
    margin-bottom: 0 !important;
}

/* 统一时间显示样式 */
#current-date, #current-time {
    font-weight: 600 !important;
    color: var(--text-color) !important;
    line-height: 1.4 !important;
    font-size: 1.1em !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    width: 100% !important;
    text-align: center !important;
    display: block !important;
    box-sizing: border-box !important;
}

#current-time {
    font-family: 'Courier New', monospace !important;
    font-weight: bold !important;
    color: var(--primary-color) !important;
    margin-top: 0 !important;
}

/* 统一网格样式 */
.stat-grid {
    display: grid !important;
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    gap: 15px !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

/* 状态样式 */
.status-active {
    color: var(--success-color) !important;
    font-weight: bold !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .stat-grid {
        grid-template-columns: 1fr !important;
    }
    
    .stat-item {
        height: auto !important;
        min-height: 120px !important;
    }
    
    .stat-label {
        font-size: 0.85em !important;
        margin-bottom: 6px !important;
        height: auto !important;
        min-height: 30px !important;
    }
    
    .stat-value {
        font-size: 1em !important;
    }
    
    .stat-value-with-btn .btn {
        padding: 3px 10px !important;
        font-size: 0.8em !important;
        margin-top: 5px !important;
    }
    
    .stat-value-with-btn, .stat-value-multiline {
        height: auto !important;
        min-height: 70px !important;
    }
}
