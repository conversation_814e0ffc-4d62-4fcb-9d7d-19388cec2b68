/**
 * 页面模态框修复脚本
 * 专门修复账户管理、小程序平台、公告管理、插件中心、应用回收站页面的模态框问题
 * 确保模态框点击空白处不会关闭，只能通过取消按钮、关闭按钮或右上角×关闭
 */

// 立即执行函数，避免变量污染全局作用域
(function() {
    // 等待DOM加载完成
    document.addEventListener('DOMContentLoaded', function() {
        console.log('页面模态框修复脚本已加载');
        
        // 立即修复现有模态框
        fixExistingModals();
        
        // 移除全局点击事件
        removeGlobalClickEvents();
        
        // 重写模态框函数
        overrideModalFunctions();
        
        // 监听DOM变化，处理动态添加的模态框
        setupMutationObserver();
    });
    
    /**
     * 修复现有模态框
     */
    function fixExistingModals() {
        // 获取所有模态框
        const modals = document.querySelectorAll('.modal');
        
        if (modals.length > 0) {
            console.log('找到模态框：', modals.length, '个');
            
            // 为每个模态框添加点击事件
            modals.forEach(function(modal) {
                fixSingleModal(modal);
            });
        } else {
            console.log('未找到模态框');
        }
    }
    
    /**
     * 修复单个模态框
     */
    function fixSingleModal(modal) {
        if (!modal) return;
        
        // 移除原有的点击事件
        const clone = modal.cloneNode(true);
        modal.parentNode.replaceChild(clone, modal);
        
        // 添加新的点击事件，阻止冒泡
        clone.addEventListener('click', function(e) {
            // 如果点击的是模态框本身（空白区域），阻止事件传播
            if (e.target === this) {
                e.stopPropagation();
                e.preventDefault();
                console.log('点击了模态框空白处，阻止关闭');
                return false;
            }
        });
        
        // 修复关闭按钮
        const closeButtons = clone.querySelectorAll('.close-btn, .modal-header .bi-x-lg, .modal-header .bi-x');
        closeButtons.forEach(function(button) {
            let parentButton = button.closest('button');
            if (parentButton) {
                // 如果有onclick属性
                if (parentButton.hasAttribute('onclick')) {
                    const onclickValue = parentButton.getAttribute('onclick');
                    const match = onclickValue.match(/closeModal\(['"](.+)['"]\)/);
                    if (match) {
                        const modalId = match[1];
                        // 添加新的点击事件，但保留原有的onclick
                        parentButton.addEventListener('click', function() {
                            console.log('关闭按钮被点击，模态框ID：', modalId);
                            closeModal(modalId);
                        });
                    }
                } else if (clone.id) {
                    // 如果按钮没有onclick属性但模态框有ID
                    parentButton.setAttribute('onclick', `closeModal('${clone.id}')`);
                    console.log('为关闭按钮添加onclick属性：', `closeModal('${clone.id}')`);
                }
            }
        });
        
        // 修复取消按钮
        const cancelButtons = clone.querySelectorAll('.btn-secondary, button:contains("取消"), button:contains("关闭")');
        cancelButtons.forEach(function(button) {
            // 检查按钮文本是否包含"取消"或"关闭"
            const buttonText = button.textContent.trim().toLowerCase();
            const isCancelButton = buttonText.includes('取消') || buttonText.includes('关闭') || 
                                  button.classList.contains('btn-secondary');
            
            if (!isCancelButton) return;
            
            // 如果有onclick属性
            if (button.hasAttribute('onclick')) {
                const onclickValue = button.getAttribute('onclick');
                const match = onclickValue.match(/closeModal\(['"](.+)['"]\)/);
                if (match) {
                    const modalId = match[1];
                    // 添加新的点击事件，但保留原有的onclick
                    button.addEventListener('click', function() {
                        console.log('取消按钮被点击，模态框ID：', modalId);
                        closeModal(modalId);
                    });
                }
            } else if (clone.id) {
                // 如果按钮没有onclick属性但模态框有ID
                button.setAttribute('onclick', `closeModal('${clone.id}')`);
                console.log('为取消按钮添加onclick属性：', `closeModal('${clone.id}')`);
            }
        });
    }
    
    /**
     * 移除全局点击事件
     */
    function removeGlobalClickEvents() {
        // 移除window的点击事件
        window.onclick = null;
        
        // 移除document的点击事件处理程序
        document.onclick = function(event) {
            // 如果点击的是模态框，阻止默认行为
            if (event.target.classList.contains('modal')) {
                event.stopPropagation();
                event.preventDefault();
                console.log('点击了模态框空白处（全局事件），阻止关闭');
                return false;
            }
        };
        
        console.log('已移除全局点击事件');
    }
    
    /**
     * 重写模态框函数
     */
    function overrideModalFunctions() {
        // 保存原始函数
        window.originalOpenModal = window.openModal;
        window.originalCloseModal = window.closeModal;
        
        // 重写openModal函数
        window.openModal = function(modalId) {
            console.log('打开模态框：', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                // 显示模态框
                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';
                
                // 确保模态框有点击事件处理
                if (!modal._hasClickHandler) {
                    // 阻止模态框点击事件冒泡
                    modal.addEventListener('click', function(e) {
                        if (e.target === modal) {
                            e.stopPropagation();
                            e.preventDefault();
                            console.log('点击了模态框空白处，阻止关闭');
                            return false;
                        }
                    });
                    modal._hasClickHandler = true;
                }
            }
        };
        
        // 重写closeModal函数
        window.closeModal = function(modalId) {
            console.log('关闭模态框：', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        };
        
        console.log('已重写模态框函数');
    }
    
    /**
     * 设置MutationObserver监听DOM变化
     */
    function setupMutationObserver() {
        // 创建一个观察器实例
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                // 检查是否有新增的节点
                if (mutation.addedNodes && mutation.addedNodes.length > 0) {
                    // 遍历新增的节点
                    mutation.addedNodes.forEach(function(node) {
                        // 检查是否是元素节点
                        if (node.nodeType === 1) {
                            // 检查是否是模态框
                            if (node.classList && node.classList.contains('modal')) {
                                console.log('检测到新增模态框:', node.id);
                                // 修复这个模态框
                                fixSingleModal(node);
                            }
                            
                            // 检查子元素中是否有模态框
                            const modals = node.querySelectorAll('.modal');
                            if (modals.length > 0) {
                                console.log('检测到新增节点中包含模态框:', modals.length, '个');
                                modals.forEach(function(modal) {
                                    fixSingleModal(modal);
                                });
                            }
                        }
                    });
                }
            });
        });
        
        // 配置观察选项
        const config = {
            childList: true, // 观察目标子节点的变化
            subtree: true    // 观察所有后代节点的变化
        };
        
        // 开始观察document.body的变化
        observer.observe(document.body, config);
        
        console.log('已设置MutationObserver监听DOM变化');
    }
})();
