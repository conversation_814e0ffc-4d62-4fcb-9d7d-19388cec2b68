﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - 页面未找到 - 情侣头像匹配系统</title>
    <style>
        body {
            font-family: "Microsoft YaHei", sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .error-container {
            max-width: 600px;
            width: 100%;
            padding: 40px 20px;
            text-align: center;
            animation: fadeIn 0.5s;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .error-code {
            font-size: 120px;
            font-weight: 700;
            color: #ff6b95;
            margin: 0;
            line-height: 1;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .error-message {
            font-size: 24px;
            margin: 20px 0;
            color: #666;
        }
        
        .error-description {
            font-size: 16px;
            margin-bottom: 30px;
            color: #888;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            background: linear-gradient(135deg, #ff6b95 0%, #ffa5c0 100%);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(255, 107, 149, 0.4);
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(255, 107, 149, 0.6);
        }
        
        .broken-heart {
            font-size: 100px;
            position: relative;
            margin: 20px auto;
            width: 100px;
            height: 100px;
        }
        
        .broken-heart:before,
        .broken-heart:after {
            content: "";
            position: absolute;
            width: 50px;
            height: 80px;
            background-color: #ff6b95;
            border-radius: 50px 50px 0 0;
            transform-origin: center bottom;
        }
        
        .broken-heart:before {
            left: 0;
            transform: rotate(-45deg);
            animation: leftPiece 1s infinite alternate;
        }
        
        .broken-heart:after {
            right: 0;
            transform: rotate(45deg);
            animation: rightPiece 1s infinite alternate;
        }
        
        @keyframes leftPiece {
            0% { transform: rotate(-45deg) translateX(0); }
            100% { transform: rotate(-45deg) translateX(-10px); }
        }
        
        @keyframes rightPiece {
            0% { transform: rotate(45deg) translateX(0); }
            100% { transform: rotate(45deg) translateX(10px); }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="broken-heart"></div>
        <h1 class="error-code">404</h1>
        <h2 class="error-message">页面未找到</h2>
        <p class="error-description">抱歉，您访问的页面不存在或已被移除。</p>
        <a href="/" class="btn">返回首页</a>
    </div>

    <script>
        // 添加音效
        document.addEventListener("DOMContentLoaded", function() {
            try {
                const audio = new Audio("data:audio/mpeg;base64,SUQzBAAAAAABEVRYWFgAAAAtAAADY29tbWVudABCaWdTb3VuZEJhbmsuY29tIC8gTGFzb25pY1N0dWRpb3MuY29tAFRYWFgAAAAhAAADZW5naW5lZXIAQmlnU291bmRCYW5rLmNvbQBUWFhYAAAAGwAAA3NvZnR3YXJlAExhdmY1OC43Ni4xMDAAAFRDT04AAAAbAAADZW5jb2RlZCBieQBMYXZmNTguNzYuMTAwAAAAAAAAAAAAAAD/+1AAAAA8YXVkaW8vbXBlZwAAAAAAAAAAAABUSVQyAAAAGQAAAzIwMjMtMDUtMDZUMTc6MjM6MTgAAAAAAAAAAAAA//tQxAADwAABpAAAACAAADSAAAAETEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//tQxBYDwAABpAAAACAAADSAAAAEVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//tQxBcAAAANIAAAAAA");
                audio.volume = 0.3;
                audio.play();
            } catch (e) {
                console.log("音频播放失败", e);
            }
        });
    </script>
</body>
</html>