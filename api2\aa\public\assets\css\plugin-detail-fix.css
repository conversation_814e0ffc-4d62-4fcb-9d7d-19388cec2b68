/**
 * 插件详情模态框样式修复
 * 美化插件中心的详情模态框样式
 */

/* 插件详情模态框样式 */
#plugin-detail-modal .modal-content {
    background-color: #ffffff !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2) !important;
    max-width: 800px !important;
    width: 90% !important;
    margin: 5% auto !important;
    animation: slideDown 0.3s ease !important;
}

#plugin-detail-modal .modal-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
    border-bottom: 1px solid #eee !important;
    padding: 20px 25px !important;
    border-radius: 12px 12px 0 0 !important;
}

#plugin-detail-modal .modal-header h3 {
    color: #333 !important;
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    margin: 0 !important;
}

#plugin-detail-modal .modal-body {
    padding: 25px !important;
    max-height: 70vh !important;
    overflow-y: auto !important;
}

#plugin-detail-modal .modal-footer {
    border-top: 1px solid #eee !important;
    padding: 15px 25px !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: 10px !important;
    border-radius: 0 0 12px 12px !important;
}

/* 插件详情内容样式 */
.plugin-detail {
    display: flex !important;
    flex-direction: column !important;
    gap: 20px !important;
}

.plugin-detail-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 10px !important;
}

.plugin-detail-header h4 {
    font-size: 1.3rem !important;
    font-weight: 600 !important;
    color: #333 !important;
    margin: 0 !important;
}

.plugin-detail-badges {
    display: flex !important;
    gap: 10px !important;
}

.plugin-badge {
    padding: 5px 10px !important;
    border-radius: 20px !important;
    font-size: 0.8rem !important;
    font-weight: 500 !important;
}

.plugin-badge.installed {
    background-color: #e8f5e9 !important;
    color: #2e7d32 !important;
}

.plugin-badge.not-installed {
    background-color: #ffebee !important;
    color: #c62828 !important;
}

.plugin-category {
    background-color: #e3f2fd !important;
    color: #0d47a1 !important;
    padding: 5px 10px !important;
    border-radius: 20px !important;
    font-size: 0.8rem !important;
    font-weight: 500 !important;
}

.plugin-detail-info {
    background-color: #f8f9fa !important;
    padding: 15px !important;
    border-radius: 8px !important;
    margin-bottom: 15px !important;
}

.plugin-detail-info p {
    margin-bottom: 8px !important;
    line-height: 1.5 !important;
}

.plugin-detail-info p:last-child {
    margin-bottom: 0 !important;
}

.plugin-detail-info strong {
    font-weight: 600 !important;
    color: #555 !important;
}

.plugin-detail-features, 
.plugin-detail-requirements {
    margin-bottom: 15px !important;
}

.plugin-detail-features h5, 
.plugin-detail-requirements h5 {
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    color: #333 !important;
    margin-bottom: 10px !important;
    padding-bottom: 5px !important;
    border-bottom: 2px solid #f0f0f0 !important;
}

.plugin-detail-features ul {
    list-style-type: none !important;
    padding-left: 5px !important;
    margin-bottom: 0 !important;
}

.plugin-detail-features ul li {
    position: relative !important;
    padding-left: 25px !important;
    margin-bottom: 8px !important;
    line-height: 1.5 !important;
}

.plugin-detail-features ul li:before {
    content: "✓" !important;
    position: absolute !important;
    left: 0 !important;
    color: #4caf50 !important;
    font-weight: bold !important;
}

.price {
    color: #ff6b95 !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
}

.price:before {
    content: "¥" !important;
}

.price-original {
    color: #999 !important;
    text-decoration: line-through !important;
    margin-left: 5px !important;
    font-size: 0.9rem !important;
}

.price-original:before {
    content: "¥" !important;
}

/* 按钮样式 */
#plugin-detail-modal .btn {
    padding: 8px 20px !important;
    border-radius: 8px !important;
    font-weight: 500 !important;
    transition: all 0.3s !important;
}

#plugin-detail-modal .btn-primary {
    background: linear-gradient(135deg, #ff6b95 0%, #ffa5c0 100%) !important;
    border-color: #ff6b95 !important;
    color: #fff !important;
}

#plugin-detail-modal .btn-primary:hover {
    background: linear-gradient(135deg, #ff4f7e 0%, #ff8aa9 100%) !important;
    border-color: #ff4f7e !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(255, 107, 149, 0.3) !important;
}

#plugin-detail-modal .btn-secondary {
    background-color: #f8f9fa !important;
    border-color: #ddd !important;
    color: #333 !important;
}

#plugin-detail-modal .btn-secondary:hover {
    background-color: #e9ecef !important;
    border-color: #ccc !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1) !important;
}
