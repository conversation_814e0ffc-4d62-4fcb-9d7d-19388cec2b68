/**
 * 安全性增强脚本
 * 实现CSRF保护、输入验证和其他安全功能
 */

// 在DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('安全性增强脚本已加载');
    
    // 初始化CSRF保护
    initCSRFProtection();
    
    // 初始化输入验证
    initInputValidation();
    
    // 初始化XSS保护
    initXSSProtection();
    
    // 初始化安全表单提交
    initSecureFormSubmission();
});

/**
 * 初始化CSRF保护
 */
function initCSRFProtection() {
    // 生成CSRF令牌
    const csrfToken = generateCSRFToken();
    
    // 将CSRF令牌存储在localStorage中
    localStorage.setItem('csrf_token', csrfToken);
    
    // 为所有表单添加CSRF令牌
    addCSRFTokenToForms(csrfToken);
    
    // 为所有AJAX请求添加CSRF令牌
    setupAjaxCSRFProtection(csrfToken);
    
    console.log('CSRF保护已初始化');
}

/**
 * 生成CSRF令牌
 * @returns {string} CSRF令牌
 */
function generateCSRFToken() {
    // 生成随机字符串作为CSRF令牌
    const tokenChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let token = '';
    
    for (let i = 0; i < 32; i++) {
        token += tokenChars.charAt(Math.floor(Math.random() * tokenChars.length));
    }
    
    return token;
}

/**
 * 为所有表单添加CSRF令牌
 * @param {string} csrfToken - CSRF令牌
 */
function addCSRFTokenToForms(csrfToken) {
    // 获取所有表单
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        // 检查表单是否已有CSRF令牌
        let hasToken = false;
        const inputs = form.querySelectorAll('input');
        
        inputs.forEach(input => {
            if (input.name === 'csrf_token') {
                hasToken = true;
                input.value = csrfToken; // 更新现有令牌
            }
        });
        
        // 如果没有令牌，添加一个
        if (!hasToken) {
            const tokenInput = document.createElement('input');
            tokenInput.type = 'hidden';
            tokenInput.name = 'csrf_token';
            tokenInput.value = csrfToken;
            form.appendChild(tokenInput);
        }
        
        // 添加表单提交事件监听器，验证CSRF令牌
        form.addEventListener('submit', function(e) {
            const formToken = this.querySelector('input[name="csrf_token"]').value;
            const storedToken = localStorage.getItem('csrf_token');
            
            if (formToken !== storedToken) {
                e.preventDefault();
                console.error('CSRF令牌验证失败');
                showSecurityAlert('安全警告', '表单提交被拒绝，因为CSRF令牌无效。请刷新页面后重试。');
            }
        });
    });
    
    console.log(`已为${forms.length}个表单添加CSRF保护`);
}

/**
 * 为所有AJAX请求添加CSRF令牌
 * @param {string} csrfToken - CSRF令牌
 */
function setupAjaxCSRFProtection(csrfToken) {
    // 如果存在jQuery，为jQuery AJAX请求添加CSRF令牌
    if (typeof $ !== 'undefined' && $.ajax) {
        $.ajaxSetup({
            beforeSend: function(xhr) {
                xhr.setRequestHeader('X-CSRF-Token', csrfToken);
            }
        });
    }
    
    // 为fetch API添加CSRF令牌
    const originalFetch = window.fetch;
    window.fetch = function() {
        let [resource, config] = arguments;
        
        // 如果config不存在，创建一个空对象
        if (!config) {
            config = {};
        }
        
        // 如果headers不存在，创建一个空对象
        if (!config.headers) {
            config.headers = {};
        }
        
        // 添加CSRF令牌到请求头
        config.headers['X-CSRF-Token'] = csrfToken;
        
        // 调用原始fetch方法
        return originalFetch(resource, config);
    };
    
    // 为XMLHttpRequest添加CSRF令牌
    const originalOpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function() {
        const originalSend = this.send;
        
        this.send = function() {
            this.setRequestHeader('X-CSRF-Token', csrfToken);
            originalSend.apply(this, arguments);
        };
        
        originalOpen.apply(this, arguments);
    };
    
    console.log('已为AJAX请求添加CSRF保护');
}

/**
 * 初始化输入验证
 */
function initInputValidation() {
    // 获取所有输入字段
    const inputs = document.querySelectorAll('input, textarea, select');
    
    inputs.forEach(input => {
        // 根据输入类型添加适当的验证
        switch (input.type) {
            case 'email':
                setupEmailValidation(input);
                break;
            case 'password':
                setupPasswordValidation(input);
                break;
            case 'tel':
                setupPhoneValidation(input);
                break;
            case 'text':
                if (input.name.includes('name')) {
                    setupNameValidation(input);
                }
                break;
            case 'number':
                setupNumberValidation(input);
                break;
        }
        
        // 为所有输入添加通用验证
        setupGenericInputValidation(input);
    });
    
    console.log(`已为${inputs.length}个输入字段添加验证`);
}

/**
 * 设置电子邮件验证
 * @param {HTMLElement} input - 输入元素
 */
function setupEmailValidation(input) {
    input.addEventListener('blur', function() {
        const value = this.value.trim();
        const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        
        if (value && !emailPattern.test(value)) {
            showInputError(this, '请输入有效的电子邮件地址');
        } else {
            clearInputError(this);
        }
    });
}

/**
 * 设置密码验证
 * @param {HTMLElement} input - 输入元素
 */
function setupPasswordValidation(input) {
    input.addEventListener('blur', function() {
        const value = this.value;
        
        if (value && value.length < 8) {
            showInputError(this, '密码长度至少为8个字符');
        } else if (value && !/[A-Z]/.test(value)) {
            showInputError(this, '密码必须包含至少一个大写字母');
        } else if (value && !/[a-z]/.test(value)) {
            showInputError(this, '密码必须包含至少一个小写字母');
        } else if (value && !/[0-9]/.test(value)) {
            showInputError(this, '密码必须包含至少一个数字');
        } else {
            clearInputError(this);
        }
    });
}

/**
 * 设置电话号码验证
 * @param {HTMLElement} input - 输入元素
 */
function setupPhoneValidation(input) {
    input.addEventListener('blur', function() {
        const value = this.value.trim();
        const phonePattern = /^1[3-9]\d{9}$/; // 中国手机号格式
        
        if (value && !phonePattern.test(value)) {
            showInputError(this, '请输入有效的手机号码');
        } else {
            clearInputError(this);
        }
    });
}

/**
 * 设置姓名验证
 * @param {HTMLElement} input - 输入元素
 */
function setupNameValidation(input) {
    input.addEventListener('blur', function() {
        const value = this.value.trim();
        
        if (value && value.length < 2) {
            showInputError(this, '名称长度至少为2个字符');
        } else if (value && /[<>]/.test(value)) {
            showInputError(this, '名称不能包含特殊字符');
        } else {
            clearInputError(this);
        }
    });
}

/**
 * 设置数字验证
 * @param {HTMLElement} input - 输入元素
 */
function setupNumberValidation(input) {
    input.addEventListener('blur', function() {
        const value = this.value.trim();
        const min = parseFloat(this.getAttribute('min') || '-Infinity');
        const max = parseFloat(this.getAttribute('max') || 'Infinity');
        const numValue = parseFloat(value);
        
        if (value && isNaN(numValue)) {
            showInputError(this, '请输入有效的数字');
        } else if (numValue < min) {
            showInputError(this, `数值不能小于 ${min}`);
        } else if (numValue > max) {
            showInputError(this, `数值不能大于 ${max}`);
        } else {
            clearInputError(this);
        }
    });
}

/**
 * 设置通用输入验证
 * @param {HTMLElement} input - 输入元素
 */
function setupGenericInputValidation(input) {
    // 检查是否为必填字段
    if (input.hasAttribute('required')) {
        input.addEventListener('blur', function() {
            const value = this.value.trim();
            
            if (!value) {
                showInputError(this, '此字段为必填项');
            }
        });
    }
    
    // 检查最大长度
    if (input.hasAttribute('maxlength')) {
        const maxLength = parseInt(input.getAttribute('maxlength'));
        
        input.addEventListener('input', function() {
            const value = this.value;
            
            if (value.length > maxLength) {
                this.value = value.slice(0, maxLength);
            }
        });
    }
    
    // 防止XSS攻击
    input.addEventListener('input', function() {
        const value = this.value;
        
        // 如果不是密码字段，对输入进行净化
        if (this.type !== 'password') {
            this.value = sanitizeInput(value);
        }
    });
}

/**
 * 显示输入错误
 * @param {HTMLElement} input - 输入元素
 * @param {string} message - 错误消息
 */
function showInputError(input, message) {
    // 清除现有错误
    clearInputError(input);
    
    // 添加错误类
    input.classList.add('is-invalid');
    
    // 创建错误消息元素
    const errorElement = document.createElement('div');
    errorElement.className = 'invalid-feedback';
    errorElement.textContent = message;
    
    // 添加错误消息到输入元素后
    input.parentNode.appendChild(errorElement);
}

/**
 * 清除输入错误
 * @param {HTMLElement} input - 输入元素
 */
function clearInputError(input) {
    // 移除错误类
    input.classList.remove('is-invalid');
    
    // 移除错误消息元素
    const errorElement = input.parentNode.querySelector('.invalid-feedback');
    if (errorElement) {
        errorElement.remove();
    }
}

/**
 * 初始化XSS保护
 */
function initXSSProtection() {
    // 净化所有动态插入的HTML
    const originalInnerHTML = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML');
    
    Object.defineProperty(Element.prototype, 'innerHTML', {
        set: function(value) {
            // 净化HTML
            const sanitizedValue = sanitizeHTML(value);
            originalInnerHTML.set.call(this, sanitizedValue);
        },
        get: function() {
            return originalInnerHTML.get.call(this);
        }
    });
    
    console.log('XSS保护已初始化');
}

/**
 * 净化输入
 * @param {string} input - 输入字符串
 * @returns {string} 净化后的字符串
 */
function sanitizeInput(input) {
    // 替换可能导致XSS的字符
    return input
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;')
        .replace(/`/g, '&#96;');
}

/**
 * 净化HTML
 * @param {string} html - HTML字符串
 * @returns {string} 净化后的HTML
 */
function sanitizeHTML(html) {
    // 创建一个临时元素
    const temp = document.createElement('div');
    temp.textContent = html;
    
    // 返回净化后的HTML
    return temp.innerHTML;
}

/**
 * 初始化安全表单提交
 */
function initSecureFormSubmission() {
    // 获取所有表单
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            // 验证所有必填字段
            const requiredInputs = this.querySelectorAll('[required]');
            let hasError = false;
            
            requiredInputs.forEach(input => {
                const value = input.value.trim();
                
                if (!value) {
                    e.preventDefault();
                    showInputError(input, '此字段为必填项');
                    hasError = true;
                }
            });
            
            // 验证所有输入
            const allInputs = this.querySelectorAll('input, textarea, select');
            
            allInputs.forEach(input => {
                if (input.classList.contains('is-invalid')) {
                    e.preventDefault();
                    hasError = true;
                }
            });
            
            if (hasError) {
                showSecurityAlert('表单验证', '请修正表单中的错误后再提交');
            }
        });
    });
    
    console.log(`已为${forms.length}个表单添加安全提交验证`);
}

/**
 * 显示安全警报
 * @param {string} title - 警报标题
 * @param {string} message - 警报消息
 */
function showSecurityAlert(title, message) {
    // 创建警报元素
    const alertElement = document.createElement('div');
    alertElement.className = 'security-alert';
    alertElement.innerHTML = `
        <div class="security-alert-content">
            <div class="security-alert-header">
                <h3>${title}</h3>
                <button class="security-alert-close">&times;</button>
            </div>
            <div class="security-alert-body">
                <p>${message}</p>
            </div>
        </div>
    `;
    
    // 添加到文档
    document.body.appendChild(alertElement);
    
    // 显示警报
    setTimeout(() => {
        alertElement.classList.add('show');
    }, 10);
    
    // 添加关闭按钮事件
    alertElement.querySelector('.security-alert-close').addEventListener('click', function() {
        alertElement.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(alertElement);
        }, 300);
    });
}
