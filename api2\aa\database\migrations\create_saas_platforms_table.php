<?php

/**
 * Migration for creating the saas_platforms table
 * This table stores platform information for SAAS accounts
 */
class CreateSaasPlatformsTable
{
    /**
     * Run the migration
     */
    public function up()
    {
        $sql = "CREATE TABLE IF NOT EXISTS `saas_platforms` (
            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            `account_id` bigint(20) UNSIGNED NOT NULL COMMENT 'Reference to saas_accounts table',
            `tenant_id` bigint(20) UNSIGNED NOT NULL COMMENT 'Reference to tenants table',
            `platform_type` varchar(32) NOT NULL COMMENT 'wechat_miniprogram/alipay_miniprogram/h5',
            `name` varchar(100) NOT NULL COMMENT 'Platform name',
            `app_id` varchar(64) DEFAULT NULL COMMENT 'App ID for miniprogram',
            `app_secret` varchar(128) DEFAULT NULL COMMENT 'App Secret for miniprogram',
            `domain` varchar(128) DEFAULT NULL COMMENT 'Domain for H5',
            `logo` varchar(255) DEFAULT NULL COMMENT 'Platform logo URL',
            `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '0=inactive, 1=active',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `account_id` (`account_id`),
            KEY `tenant_id` (`tenant_id`),
            CONSTRAINT `saas_platforms_account_id_foreign` FOREIGN KEY (`account_id`) REFERENCES `saas_accounts` (`id`) ON DELETE CASCADE,
            CONSTRAINT `saas_platforms_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        
        return $sql;
    }

    /**
     * Reverse the migration
     */
    public function down()
    {
        return "DROP TABLE IF EXISTS `saas_platforms`;";
    }
}
