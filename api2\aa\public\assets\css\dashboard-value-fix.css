/**
 * 控制面板页面值显示修复样式
 * 优先确保值正常显示，允许标题不对齐
 */

/* 重置所有样式 */
.stat-item {
    position: relative;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: flex-start !important;
    padding: 12px 15px !important;
    margin-bottom: 10px !important;
    background-color: rgba(var(--primary-color-rgb), 0.03) !important;
    border-radius: 6px !important;
    transition: all 0.3s ease !important;
    overflow: hidden !important;
    text-align: center !important;
    min-width: 0 !important;
    height: auto !important; /* 自适应高度 */
    min-height: 120px !important; /* 最小高度 */
    box-sizing: border-box !important;
}

/* 统一标签样式 */
.stat-label {
    font-weight: 500 !important;
    color: var(--text-muted) !important;
    margin-bottom: 8px !important;
    line-height: 1.4 !important;
    font-size: 0.9em !important;
    border-bottom: 1px dashed rgba(var(--primary-color-rgb), 0.2) !important;
    padding-bottom: 5px !important;
    white-space: normal !important; /* 允许换行 */
    width: 100% !important;
    text-align: center !important;
    display: block !important;
    box-sizing: border-box !important;
}

/* 统一值样式 */
.stat-value {
    font-weight: 600 !important;
    color: var(--text-color) !important;
    line-height: 1.4 !important;
    font-size: 1.1em !important;
    white-space: normal !important; /* 允许换行 */
    width: 100% !important;
    text-align: center !important;
    display: block !important;
    box-sizing: border-box !important;
    margin-bottom: 5px !important;
}

/* 统一带按钮的值样式 */
.stat-value-with-btn {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    flex-grow: 1 !important;
    box-sizing: border-box !important;
    padding: 5px 0 !important;
}

/* 统一带按钮的值中的值样式 */
.stat-value-with-btn .stat-value {
    margin-bottom: 8px !important;
    width: 100% !important;
    white-space: normal !important; /* 允许换行 */
    display: block !important;
    box-sizing: border-box !important;
}

/* 统一带按钮的值中的按钮样式 */
.stat-value-with-btn .btn {
    white-space: nowrap !important;
    font-size: 0.9em !important;
    padding: 4px 12px !important;
    margin-top: 2px !important;
    box-sizing: border-box !important;
}

/* 统一多行值样式 */
.stat-value-multiline {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    flex-grow: 1 !important;
    box-sizing: border-box !important;
    padding: 5px 0 !important;
}

/* 统一多行值中的值样式 */
.stat-value-multiline .stat-value {
    margin-bottom: 4px !important;
    width: 100% !important;
    white-space: normal !important; /* 允许换行 */
    line-height: 1.3 !important;
    display: block !important;
    box-sizing: border-box !important;
}

/* 统一多行值中的最后一个值样式 */
.stat-value-multiline .stat-value:last-child {
    margin-bottom: 0 !important;
}

/* 统一时间显示样式 */
#current-date, #current-time {
    font-weight: 600 !important;
    color: var(--text-color) !important;
    line-height: 1.4 !important;
    font-size: 1.1em !important;
    white-space: normal !important; /* 允许换行 */
    width: 100% !important;
    text-align: center !important;
    display: block !important;
    box-sizing: border-box !important;
    margin-bottom: 5px !important;
}

#current-time {
    font-family: 'Courier New', monospace !important;
    font-weight: bold !important;
    color: var(--primary-color) !important;
    margin-top: 0 !important;
}

/* 统一网格样式 */
.stat-grid {
    display: grid !important;
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
    gap: 15px !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

/* 邮箱特殊样式 */
.stat-item:has(.stat-label:contains("邮箱")) .stat-value-multiline {
    padding-top: 10px !important;
}

.stat-item:has(.stat-label:contains("邮箱")) .stat-value {
    white-space: normal !important;
    word-break: break-all !important;
    line-height: 1.3 !important;
}

/* 运行环境特殊样式 */
.stat-item:has(.stat-label:contains("运行环境")) .stat-value-multiline {
    padding-top: 10px !important;
}

.stat-item:has(.stat-label:contains("运行环境")) .stat-value {
    white-space: normal !important;
    word-break: break-all !important;
    line-height: 1.3 !important;
}

/* 授权时长特殊样式 */
.stat-item:has(.stat-label:contains("授权时长")) .stat-value-with-btn {
    padding-top: 10px !important;
}

.stat-item:has(.stat-label:contains("授权时长")) .stat-value {
    white-space: nowrap !important;
    line-height: 1.3 !important;
}

/* 当前版本特殊样式 */
.stat-item:has(.stat-label:contains("当前版本")) .stat-value-with-btn {
    padding-top: 10px !important;
}

.stat-item:has(.stat-label:contains("当前版本")) .stat-value {
    white-space: nowrap !important;
    line-height: 1.3 !important;
}

/* 服务期限特殊样式 */
.stat-item:has(.stat-label:contains("服务期限")) .stat-value-with-btn {
    padding-top: 10px !important;
}

.stat-item:has(.stat-label:contains("服务期限")) .stat-value {
    white-space: normal !important;
    word-break: break-all !important;
    line-height: 1.3 !important;
}

/* 系统时间特殊样式 */
.stat-item:has(.stat-label:contains("系统时间")) .stat-value-with-btn {
    padding-top: 10px !important;
}

.stat-item:has(.stat-label:contains("系统时间")) .stat-value {
    white-space: normal !important;
    word-break: break-all !important;
    line-height: 1.3 !important;
}

/* 状态样式 */
.status-active {
    color: var(--success-color) !important;
    font-weight: bold !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .stat-grid {
        grid-template-columns: 1fr !important;
    }
    
    .stat-item {
        height: auto !important;
        min-height: 120px !important;
    }
    
    .stat-label {
        font-size: 0.85em !important;
        margin-bottom: 6px !important;
    }
    
    .stat-value {
        font-size: 1em !important;
    }
    
    .stat-value-with-btn .btn {
        padding: 3px 10px !important;
        font-size: 0.8em !important;
        margin-top: 5px !important;
    }
}
