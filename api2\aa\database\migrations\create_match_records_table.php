<?php

/**
 * Migration for creating the match_records table
 * This table stores avatar matching records with tenant isolation
 */
class CreateMatchRecordsTable
{
    /**
     * Run the migration
     */
    public function up()
    {
        $sql = "CREATE TABLE IF NOT EXISTS `match_records` (
            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            `tenant_id` bigint(20) UNSIGNED NOT NULL COMMENT 'Reference to tenants table',
            `user_id` bigint(20) UNSIGNED NOT NULL COMMENT 'Reference to users table',
            `avatar_id` bigint(20) UNSIGNED NOT NULL COMMENT 'Reference to avatars table',
            `result_image` varchar(255) NOT NULL COMMENT 'Result image URL',
            `is_downloaded` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0=not downloaded, 1=downloaded',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            <PERSON><PERSON><PERSON> `tenant_id` (`tenant_id`),
            <PERSON><PERSON>Y `user_id` (`user_id`),
            K<PERSON>Y `avatar_id` (`avatar_id`),
            CONSTRAINT `match_records_tenant_id_foreign` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE,
            CONSTRAINT `match_records_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
            CONSTRAINT `match_records_avatar_id_foreign` FOREIGN KEY (`avatar_id`) REFERENCES `avatars` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        
        return $sql;
    }

    /**
     * Reverse the migration
     */
    public function down()
    {
        return "DROP TABLE IF EXISTS `match_records`;";
    }
}
