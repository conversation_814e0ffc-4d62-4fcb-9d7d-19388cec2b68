/**
 * 系统设置页面重置按钮修复
 * 修改重置按钮的行为，使用自定义模态框代替系统默认确认框
 */

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('系统设置页面重置按钮修复脚本已加载');

    // 获取所有重置按钮
    const resetButtons = document.querySelectorAll('button[type="reset"]');

    // 为每个重置按钮添加点击事件
    resetButtons.forEach((resetButton, index) => {
        console.log('找到重置按钮', index + 1);

        // 移除默认的重置行为
        resetButton.type = 'button';

        // 添加点击事件
        resetButton.addEventListener('click', function(e) {
            // 阻止默认行为
            e.preventDefault();
            console.log('重置按钮被点击，索引:', index);

            // 获取当前激活的选项卡
            const activeTab = document.querySelector('.nav-link.active');
            const currentTabId = activeTab ? activeTab.getAttribute('data-tab') : 'basic';

            console.log('当前选项卡:', currentTabId);

            // 显示自定义确认模态框
            showResetConfirmModal(currentTabId);
        });
    });

    // 创建重置确认模态框
    createResetConfirmModal();
});

/**
 * 创建重置确认模态框
 */
function createResetConfirmModal() {
    // 检查模态框是否已存在
    if (document.getElementById('reset-confirm-modal')) {
        return;
    }

    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.id = 'reset-confirm-modal';

    // 设置模态框内容
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>确认重置</h3>
                <button class="close-btn" onclick="closeResetConfirmModal()"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <div class="confirm-icon">
                    <i class="bi bi-exclamation-triangle"></i>
                </div>
                <p class="confirm-message">确定要重置所有设置吗？此操作将恢复所有设置为初始值，且不可撤销。</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeResetConfirmModal()">取消</button>
                <button class="btn btn-danger" onclick="confirmReset()">确认重置</button>
            </div>
        </div>
    `;

    // 添加样式
    const style = document.createElement('style');
    style.textContent = `
        #reset-confirm-modal .modal-content {
            max-width: 450px;
            border-radius: 10px;
            overflow: hidden;
            animation: modalFadeIn 0.3s ease-out;
        }

        #reset-confirm-modal .modal-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        #reset-confirm-modal .modal-header h3 {
            margin: 0;
            font-size: 1.2rem;
            color: #343a40;
        }

        #reset-confirm-modal .close-btn {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            color: #6c757d;
            transition: color 0.2s;
        }

        #reset-confirm-modal .close-btn:hover {
            color: #343a40;
        }

        #reset-confirm-modal .modal-body {
            padding: 25px 20px;
            text-align: center;
        }

        #reset-confirm-modal .confirm-icon {
            font-size: 3rem;
            color: #ffc107;
            margin-bottom: 15px;
        }

        #reset-confirm-modal .confirm-message {
            color: #495057;
            font-size: 1rem;
            line-height: 1.5;
            margin: 0;
        }

        #reset-confirm-modal .modal-footer {
            padding: 15px 20px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        #reset-confirm-modal .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        #reset-confirm-modal .btn-danger:hover {
            background-color: #c82333;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }

        @keyframes modalFadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    `;

    // 将模态框和样式添加到文档
    document.body.appendChild(style);
    document.body.appendChild(modal);

    console.log('重置确认模态框已创建');
}

/**
 * 显示重置确认模态框
 */
function showResetConfirmModal(tabId = 'basic') {
    const modal = document.getElementById('reset-confirm-modal');
    if (modal) {
        // 根据选项卡更新提示信息
        const messageElement = modal.querySelector('.confirm-message');
        const resetMessages = {
            'basic': '确定要重置基本设置吗？将恢复网站名称、描述、关键词等为系统默认值并保存到数据库，此操作不可撤销。',
            'appearance': '确定要重置外观设置吗？将删除所有自定义上传的图片文件，并将网站Logo、网站图标、登录背景设置为默认路径并保存到数据库，此操作不可撤销。',
            'security': '确定要重置密钥配置吗？将清空所有API密钥、加密密钥等配置并保存到数据库，此操作不可撤销。',
            'advanced': '确定要重置页面设置吗？将恢复统计代码、用户协议、隐私政策等页面内容为默认值并保存到数据库，此操作不可撤销。',
            'register': '确定要重置注册设置吗？将恢复注册类型、赠送点数、邮箱验证等设置为默认值并保存到数据库，此操作不可撤销。'
        };

        if (messageElement) {
            messageElement.textContent = resetMessages[tabId] || resetMessages['basic'];
        }

        // 存储当前选项卡ID，供确认重置时使用
        modal.setAttribute('data-current-tab', tabId);

        modal.style.display = 'flex';
        console.log('显示重置确认模态框，选项卡:', tabId);
    }
}

/**
 * 关闭重置确认模态框
 */
function closeResetConfirmModal() {
    const modal = document.getElementById('reset-confirm-modal');
    if (modal) {
        modal.style.display = 'none';
        console.log('关闭重置确认模态框');
    }
}

/**
 * 确认重置
 */
function confirmReset() {
    const modal = document.getElementById('reset-confirm-modal');
    const currentTab = modal ? modal.getAttribute('data-current-tab') : 'basic';

    console.log('确认重置，当前选项卡:', currentTab);

    // 关闭模态框
    closeResetConfirmModal();

    // 显示加载动画
    if (typeof showLoading === 'function') {
        showLoading();
    }

    // 根据选项卡执行不同的重置操作
    switch (currentTab) {
        case 'basic':
            resetBasicSettings();
            break;
        case 'appearance':
            resetAppearanceSettings();
            break;
        case 'security':
            resetSecuritySettings();
            break;
        case 'advanced':
            resetAdvancedSettings();
            break;
        case 'register':
            resetRegisterSettings();
            break;
        default:
            resetBasicSettings();
    }
}

/**
 * 重置基本设置
 */
function resetBasicSettings() {
    console.log('重置基本设置');

    // 发送AJAX请求重置基本设置到默认值
    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=reset_basic_settings'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('基本设置重置成功');
            showResetComplete('基本设置已重置为默认值并保存到数据库');
        } else {
            console.error('基本设置重置失败:', data.message);
            showResetComplete('基本设置重置失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('重置基本设置时发生错误:', error);
        showResetComplete('基本设置重置时发生错误');
    });
}

/**
 * 重置外观设置
 */
function resetAppearanceSettings() {
    console.log('重置外观设置');

    // 发送AJAX请求删除自定义图片文件
    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=reset_appearance_files'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('外观设置重置成功');
            const deletedCount = data.deleted_files ? data.deleted_files.length : 0;
            const message = `外观设置已重置完成，已删除 ${deletedCount} 个自定义图片文件，默认路径已保存到数据库`;

            // 立即更新页面上的预览图片，强制刷新缓存
            updateAppearancePreview();

            // 额外强制刷新favicon
            const timestamp = new Date().getTime();
            updatePageFavicon(timestamp);

            showResetComplete(message);
        } else {
            console.error('外观设置重置失败:', data.message);
            showResetComplete('外观设置重置失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('重置外观设置时发生错误:', error);
        showResetComplete('外观设置重置完成');
    });
}

/**
 * 重置安全设置
 */
function resetSecuritySettings() {
    console.log('重置安全设置');

    // 发送AJAX请求重置安全设置到默认值
    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=reset_security_settings'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('安全设置重置成功');
            showResetComplete('安全设置已重置为默认值');
        } else {
            console.error('安全设置重置失败:', data.message);
            showResetComplete('安全设置重置失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('重置安全设置时发生错误:', error);
        showResetComplete('安全设置重置时发生错误');
    });
}

/**
 * 重置高级设置
 */
function resetAdvancedSettings() {
    console.log('重置高级设置');

    // 发送AJAX请求重置高级设置到默认值
    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=reset_advanced_settings'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('高级设置重置成功');
            showResetComplete('高级设置已重置为默认值');
        } else {
            console.error('高级设置重置失败:', data.message);
            showResetComplete('高级设置重置失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('重置高级设置时发生错误:', error);
        showResetComplete('高级设置重置时发生错误');
    });
}

/**
 * 重置注册设置
 */
function resetRegisterSettings() {
    console.log('重置注册设置');

    // 发送AJAX请求重置注册设置到默认值
    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=reset_register_settings'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('注册设置重置成功');
            showResetComplete('注册设置已重置为默认值并保存到数据库');
        } else {
            console.error('注册设置重置失败:', data.message);
            showResetComplete('注册设置重置失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('重置注册设置时发生错误:', error);
        showResetComplete('注册设置重置时发生错误');
    });
}

/**
 * 更新外观预览图片，强制刷新缓存
 */
function updateAppearancePreview() {
    const timestamp = new Date().getTime();

    // 更新网站Logo预览
    const logoPreview = document.getElementById('logoPreview');
    if (logoPreview) {
        // 先尝试显示默认图片
        logoPreview.style.display = 'block';
        logoPreview.src = '/assets/images/logo.png?v=' + timestamp;

        // 如果默认图片不存在，显示占位符或隐藏
        logoPreview.onerror = function() {
            this.style.display = 'none';
            const logoContainer = this.closest('.mt-2');
            if (logoContainer) {
                logoContainer.innerHTML = '<p>当前Logo：<code>logo.png</code></p><p class="text-muted">默认Logo文件不存在，请上传自定义Logo</p>';
            }
        };

        // 确保容器可见
        const logoContainer = logoPreview.closest('.mt-2');
        if (logoContainer) {
            logoContainer.style.display = 'block';
        }
    }

    // 更新网站图标预览
    const faviconPreview = document.getElementById('faviconPreview');
    if (faviconPreview) {
        faviconPreview.style.display = 'block';
        faviconPreview.src = '/assets/images/favicon.ico?v=' + timestamp;

        faviconPreview.onerror = function() {
            this.style.display = 'none';
            const faviconContainer = this.closest('.mt-2');
            if (faviconContainer) {
                faviconContainer.innerHTML = '<p>当前图标：<code>favicon.ico</code></p><p class="text-muted">默认图标文件不存在，请上传自定义图标</p>';
            }
        };

        const faviconContainer = faviconPreview.closest('.mt-2');
        if (faviconContainer) {
            faviconContainer.style.display = 'block';
        }
    }

    // 更新登录背景预览
    const loginBgPreview = document.getElementById('loginBgPreview');
    if (loginBgPreview) {
        loginBgPreview.style.display = 'block';
        loginBgPreview.src = '/assets/images/login-bg.jpg?v=' + timestamp;

        loginBgPreview.onerror = function() {
            this.style.display = 'none';
            const bgContainer = this.closest('.mt-2');
            if (bgContainer) {
                bgContainer.innerHTML = '<p>当前背景：<code>login-bg.jpg</code></p><p class="text-muted">默认背景文件不存在，将使用渐变背景</p>';
            }
        };

        const bgContainer = loginBgPreview.closest('.mt-2');
        if (bgContainer) {
            bgContainer.style.display = 'block';
        }
    }

    // 强制刷新页面favicon
    updatePageFavicon(timestamp);

    console.log('外观预览图片已更新，时间戳:', timestamp);
}

/**
 * 强制更新页面favicon
 */
function updatePageFavicon(timestamp) {
    // 移除现有的favicon链接
    const existingFavicons = document.querySelectorAll('link[rel*="icon"]');
    existingFavicons.forEach(link => link.remove());

    // 创建新的favicon链接
    const faviconTypes = [
        { rel: 'icon', type: 'image/x-icon' },
        { rel: 'shortcut icon', type: 'image/x-icon' },
        { rel: 'apple-touch-icon', type: 'image/x-icon' }
    ];

    faviconTypes.forEach(favicon => {
        const link = document.createElement('link');
        link.rel = favicon.rel;
        link.type = favicon.type;
        link.href = '/assets/images/favicon.ico?v=' + timestamp;
        document.head.appendChild(link);
    });

    console.log('页面favicon已强制更新，时间戳:', timestamp);
}

/**
 * 显示重置完成提示
 */
function showResetComplete(message) {
    // 延迟隐藏加载动画并显示提示
    setTimeout(() => {
        // 隐藏加载动画
        if (typeof hideLoading === 'function') {
            hideLoading();
        }

        // 显示成功提示
        if (typeof showToast === 'function') {
            showToast(message, 'success');
        }

        // 设置一个标志，防止重复刷新
        sessionStorage.setItem('settingsReset', 'true');

        // 延迟刷新页面，并强制清除缓存，保持当前选项卡
        setTimeout(() => {
            // 获取当前选项卡
            const activeTab = document.querySelector('.nav-link.active');
            const currentTabId = activeTab ? activeTab.getAttribute('data-tab') : 'basic';

            // 构建URL，保持当前选项卡并添加时间戳清除缓存
            const baseUrl = window.location.pathname;
            const newUrl = baseUrl + '?tab=' + currentTabId + '&t=' + new Date().getTime();

            // 强制刷新页面并清除缓存
            window.location.href = newUrl;
        }, 2000);
    }, 800);
}
