﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修改密码 - 情侣头像匹配系统</title>
    <style>
        body {
            font-family: "Microsoft YaHei", sans-serif;
            background-color: #f8f9fa;
            background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 1440 320\'%3E%3Cpath fill=\'%23FFA5C0\' fill-opacity=\'0.2\' d=\'M0,96L48,112C96,128,192,160,288,186.7C384,213,480,235,576,224C672,213,768,171,864,149.3C960,128,1056,128,1152,149.3C1248,171,1344,213,1392,234.7L1440,256L1440,0L1392,0C1344,0,1248,0,1152,0C1056,0,960,0,864,0C768,0,672,0,576,0C480,0,384,0,288,0C192,0,96,0,48,0L0,0Z\'%3E%3C/path%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-size: 100% 300px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        
        .loading-spinner {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 400px;
            width: 100%;
            animation: fadeIn 0.5s;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .spinner {
            width: 80px;
            height: 80px;
            border: 5px solid rgba(255, 107, 149, 0.3);
            border-radius: 50%;
            border-top-color: #ff6b95;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        h3 {
            margin: 0 0 10px 0;
            color: #ff6b95;
            text-align: center;
        }
        
        p {
            margin: 0;
            color: #666;
            text-align: center;
        }
        
        .hearts {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .heart {
            position: absolute;
            width: 20px;
            height: 20px;
            background: url("data:image/svg+xml;utf8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' viewBox=\'0 0 24 24\' fill=\'%23ff6b95\'%3E%3Cpath d=\'M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\'/%3E%3C/svg%3E") no-repeat center center;
            opacity: 0;
            animation: float 4s linear infinite;
        }
        
        @keyframes float {
            0% {
                transform: translateY(100vh) scale(0);
                opacity: 0;
            }
            20% {
                opacity: 0.8;
            }
            80% {
                opacity: 0.8;
            }
            100% {
                transform: translateY(-20vh) scale(1.5);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="hearts"></div>
    
    <div class="loading-spinner">
        <div class="spinner"></div>
        <h3>正在加载...</h3>
        <p>请稍候，系统正在准备修改密码页面</p>
    </div>

    <script>
        // 创建漂浮的心形元素
        function createFloatingHearts() {
            const container = document.querySelector(".hearts");
            const heartCount = 15;
            
            for (let i = 0; i < heartCount; i++) {
                const heart = document.createElement("div");
                heart.classList.add("heart");
                heart.style.left = Math.random() * 100 + "vw";
                heart.style.animationDelay = Math.random() * 5 + "s";
                heart.style.opacity = Math.random() * 0.5 + 0.5;
                heart.style.transform = `scale(${Math.random() * 0.5 + 0.5})`;
                container.appendChild(heart);
            }
        }
        
        // 页面加载完成后执行
        document.addEventListener("DOMContentLoaded", function() {
            // 创建漂浮的心形
            createFloatingHearts();
            
            // 添加加载音效
            try {
                const audio = new Audio("data:audio/mpeg;base64,SUQzBAAAAAABEVRYWFgAAAAtAAADY29tbWVudABCaWdTb3VuZEJhbmsuY29tIC8gTGFzb25pY1N0dWRpb3MuY29tAFRYWFgAAAAhAAADZW5naW5lZXIAQmlnU291bmRCYW5rLmNvbQBUWFhYAAAAGwAAA3NvZnR3YXJlAExhdmY1OC43Ni4xMDAAAFRDT04AAAAbAAADZW5jb2RlZCBieQBMYXZmNTguNzYuMTAwAAAAAAAAAAAAAAD/+1AAAAA8YXVkaW8vbXBlZwAAAAAAAAAAAABUSVQyAAAAGQAAAzIwMjMtMDUtMDZUMTc6MjM6MTgAAAAAAAAAAAAA//tQxAADwAABpAAAACAAADSAAAAETEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//tQxBYDwAABpAAAACAAADSAAAAEVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//tQxBcAAAANIAAAAAA");
                audio.volume = 0.3;
                audio.play();
            } catch (e) {
                console.log("音频播放失败", e);
            }
            
            // 模拟加载过程
            setTimeout(() => {
                // 检查是否已登录
                window.location.href = "/admin/change-password";
            }, 2000);
        });
    </script>
</body>
</html>
