<?php
/**
 * Bootstrap application
 * 
 * This file bootstraps the application and loads all required files
 */

// ??????
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Define application root path
define('APP_ROOT', dirname(__DIR__));

// ????
$logFile = APP_ROOT . '/storage/logs/bootstrap.log';
$logDir = dirname($logFile);
if (!is_dir($logDir)) {
    mkdir($logDir, 0777, true);
}

try {
    // Load configuration
    if (file_exists(APP_ROOT . '/config/config.php')) {
        require_once APP_ROOT . '/config/config.php';
    } else {
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - ???????\n", FILE_APPEND);
        throw new Exception('???????');
    }
    
    // Load helpers
    if (file_exists(APP_ROOT . '/app/Helpers/functions.php')) {
        require_once APP_ROOT . '/app/Helpers/functions.php';
    } else {
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - ?????????\n", FILE_APPEND);
        throw new Exception('?????????');
    }
    
    // Load autoloader
    if (file_exists(APP_ROOT . '/app/Autoloader.php')) {
        require_once APP_ROOT . '/app/Autoloader.php';
    } else {
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - ??????????\n", FILE_APPEND);
        throw new Exception('??????????');
    }
    
    // Initialize autoloader
    $autoloader = new App\Autoloader();
    $autoloader->register();
    
    // Initialize router
    $router = new App\Router();
    
    // Load routes
    if (file_exists(APP_ROOT . '/routes/web.php')) {
        require_once APP_ROOT . '/routes/web.php';
    } else {
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - ???????\n", FILE_APPEND);
        throw new Exception('???????');
    }
    
    // Dispatch request
    $router->dispatch();
} catch (Exception $e) {
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - ??: " . $e->getMessage() . "\n" . $e->getTraceAsString() . "\n", FILE_APPEND);
    
    // ??????
    header('HTTP/1.1 500 Internal Server Error');
    echo '<h1>500 Internal Server Error</h1>';
    echo '<p>' . $e->getMessage() . '</p>';
    
    if (isset($config['app']['debug']) && $config['app']['debug']) {
        echo '<pre>' . $e->getTraceAsString() . '</pre>';
    }
    
    exit;
}