<?php
// 测试配置文件加载

// 设置响应头
header('Content-Type: application/json');

try {
    // 包含数据库配置
    $configPath = __DIR__ . '/../../config/config.php';
    
    echo json_encode([
        'config_path' => $configPath,
        'file_exists' => file_exists($configPath),
        'is_readable' => is_readable($configPath)
    ]);
    
    if (!file_exists($configPath)) {
        throw new Exception('配置文件不存在: ' . $configPath);
    }
    
    if (!is_readable($configPath)) {
        throw new Exception('配置文件不可读: ' . $configPath);
    }
    
    $config = require_once($configPath);
    
    if (!$config) {
        throw new Exception('配置文件返回空值');
    }
    
    echo json_encode([
        'success' => true,
        'config_loaded' => true,
        'database_config' => $config['database'] ?? null,
        'config_keys' => array_keys($config)
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
