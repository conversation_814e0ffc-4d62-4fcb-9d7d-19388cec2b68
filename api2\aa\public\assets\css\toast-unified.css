/* 统一的 Toast 通知样式 */
:root {
    --toast-success-color: #28a745;
    --toast-error-color: #dc3545;
    --toast-warning-color: #ffc107;
    --toast-info-color: #17a2b8;
    --toast-bg-color: #ffffff;
    --toast-text-color: #333333;
    --toast-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    --toast-border-radius: 8px;
    --toast-padding: 12px 16px;
    --toast-margin: 8px;
    --toast-font-size: 14px;
    --toast-line-height: 1.5;
    --toast-max-width: 350px;
    --toast-z-index: 9999;
    --toast-transition-duration: 0.3s;
}

/* Toast 容器 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: var(--toast-z-index);
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    pointer-events: none;
}

/* Toast 基础样式 */
.toast {
    max-width: var(--toast-max-width);
    padding: var(--toast-padding);
    margin-bottom: var(--toast-margin);
    background-color: var(--toast-bg-color);
    border-radius: var(--toast-border-radius);
    box-shadow: var(--toast-shadow);
    font-size: var(--toast-font-size);
    line-height: var(--toast-line-height);
    pointer-events: auto;
    display: flex;
    align-items: center;
    overflow: hidden;
    animation: toast-in-right 0.7s;
    position: relative;
}

/* Toast 类型样式 */
.toast.bg-success {
    background-color: var(--toast-success-color);
    color: white;
}

.toast.bg-danger {
    background-color: var(--toast-error-color);
    color: white;
}

.toast.bg-warning {
    background-color: var(--toast-warning-color);
    color: #212529;
}

.toast.bg-primary {
    background-color: var(--primary-color, #ff6b95);
    color: white;
}

.toast.bg-info {
    background-color: var(--toast-info-color);
    color: white;
}

/* Toast 内容 */
.toast-body {
    flex: 1;
    padding-right: 10px;
}

/* Toast 关闭按钮 */
.btn-close-white {
    color: white;
    opacity: 0.8;
    background: transparent;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
    line-height: 1;
}

.btn-close-white:hover {
    opacity: 1;
}

/* Toast 进度条 */
.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background-color: rgba(255, 255, 255, 0.7);
    animation: toast-progress 3s linear forwards;
}

/* Toast 图标 */
.toast-icon {
    margin-right: 10px;
    font-size: 1.2rem;
}

.toast-success .toast-icon::before {
    content: "✓";
}

.toast-error .toast-icon::before {
    content: "✕";
}

.toast-warning .toast-icon::before {
    content: "!";
}

.toast-info .toast-icon::before {
    content: "i";
}

/* Toast 动画 */
@keyframes toast-in-right {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes toast-out-right {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes toast-progress {
    0% {
        width: 100%;
    }
    100% {
        width: 0%;
    }
}

/* 响应式调整 */
@media (max-width: 576px) {
    .toast-container {
        top: 10px;
        right: 10px;
        left: 10px;
        align-items: stretch;
    }
    
    .toast {
        max-width: 100%;
        width: 100%;
    }
}

/* Bootstrap 兼容样式 */
.toast.align-items-center {
    align-items: center !important;
}

.toast[role="alert"] {
    display: flex;
}

.toast[aria-live="assertive"] {
    animation: toast-in-right 0.7s;
}

.toast .d-flex {
    display: flex !important;
}

.toast .me-2 {
    margin-right: 0.5rem !important;
}

.toast .m-auto {
    margin: auto !important;
}

.toast.border-0 {
    border: 0 !important;
}

.toast.text-white {
    color: white !important;
}

/* 自定义样式 */
.toast-with-progress {
    padding-bottom: 15px; /* 为进度条留出空间 */
}

.toast-with-icon {
    padding-left: 40px;
    position: relative;
}

.toast-with-icon .toast-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
}

/* 动画增强 */
.toast-bounce {
    animation: toast-bounce 0.5s;
}

@keyframes toast-bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.toast-fade {
    animation: toast-fade 0.5s;
}

@keyframes toast-fade {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 主题适配 */
.dark-theme .toast {
    background-color: #333;
    color: #fff;
}

.dark-theme .toast-success {
    background-color: #1e7e34;
}

.dark-theme .toast-error {
    background-color: #bd2130;
}

.dark-theme .toast-warning {
    background-color: #d39e00;
    color: #212529;
}

.dark-theme .toast-info {
    background-color: #117a8b;
}
