/**
 * 复选框样式修复
 * 将复选框样式修改为打勾样式
 */

/* 隐藏原始复选框 */
.form-group.checkbox input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

/* 创建自定义复选框 */
.form-group.checkbox {
    position: relative;
    padding-left: 35px;
    cursor: pointer;
    user-select: none;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
}

.form-group.checkbox label {
    cursor: pointer;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0;
    padding-left: 5px;
    margin-top: -2px;
}

/* 创建自定义复选框样式 */
.form-group.checkbox label:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 22px;
    height: 22px;
    border: 2px solid #ddd;
    background-color: #fff;
    border-radius: 4px;
    transition: all 0.3s ease;
}

/* 选中状态下的样式 */
.form-group.checkbox input[type="checkbox"]:checked + label:before {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 创建勾选标记 */
.form-group.checkbox label:after {
    content: '';
    position: absolute;
    left: 8px;
    top: 3px;
    width: 6px;
    height: 12px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    opacity: 0;
    transition: all 0.3s ease;
}

/* 选中状态下显示勾选标记 */
.form-group.checkbox input[type="checkbox"]:checked + label:after {
    opacity: 1;
}

/* 悬停效果 */
.form-group.checkbox:hover label:before {
    border-color: var(--primary-color);
}

/* 禁用状态 */
.form-group.checkbox input[type="checkbox"]:disabled + label {
    color: #b8b8b8;
    cursor: not-allowed;
}

.form-group.checkbox input[type="checkbox"]:disabled + label:before {
    background-color: #f5f5f5;
    border-color: #ddd;
}

/* 描述文本样式 */
.checkbox-description {
    margin-top: 5px;
    font-size: 0.9rem;
    color: var(--gray-color);
    margin-left: 0;
    padding-left: 5px;
    display: block;
    clear: both;
    position: relative;
    z-index: 1;
}
