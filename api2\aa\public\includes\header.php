﻿<?php
// 检查用户是否已登录
$isLoggedIn = true; // 这里应该是实际的登录检查逻辑

// 获取当前页面路径
$currentPath = $_SERVER["REQUEST_URI"];
$dashboardActive = strpos($currentPath, "/dashboard") !== false ? "active" : "";
$accountsActive = strpos($currentPath, "/dashboard/accounts") !== false ? "active" : "";
$platformsActive = strpos($currentPath, "/dashboard/platforms") !== false ? "active" : "";
$ordersActive = strpos($currentPath, "/dashboard/orders") !== false ? "active" : "";
$announcementsActive = strpos($currentPath, "/dashboard/announcements") !== false ? "active" : "";
$pluginsActive = strpos($currentPath, "/dashboard/plugins") !== false ? "active" : "";
$settingsActive = strpos($currentPath, "/dashboard/settings") !== false ? "active" : "";
$recycleActive = strpos($currentPath, "/dashboard/recycle") !== false ? "active" : "";
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>情侣头像匹配系统</title>
    <!-- Bootstrap CSS -->
    <link href="/assets/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="/assets/css/bootstrap-icons.css" rel="stylesheet">
    <!-- 自定义 CSS -->
    <link href="/assets/css/style.css" rel="stylesheet">
    <!-- 动画 CSS -->
    <link href="/assets/css/animations.css" rel="stylesheet">
    <!-- 天气和人生哲理组件样式 -->
    <link href="/assets/css/wisdom-weather.css" rel="stylesheet">
    <!-- 统一的消息提示组件 -->
    <script src="/assets/js/toast.js" defer></script>
    <!-- 动画 JS -->
    <script src="/assets/js/animations.js" defer></script>
</head>
<body>
    <div class="wrapper">
        <!-- 侧边栏 -->
        <nav id="sidebar" class="sidebar">
            <div class="sidebar-header">
                <h3>情侣头像匹配</h3>
            </div>

            <ul class="list-unstyled components">
                <li class="<?php echo $dashboardActive; ?>">
                    <a href="/dashboard" class="btn-hover">
                        <i class="bi bi-speedometer2"></i> 控制面板
                    </a>
                </li>
                <li class="<?php echo $accountsActive; ?>">
                    <a href="/dashboard/accounts" class="btn-hover">
                        <i class="bi bi-people"></i> 账户管理
                    </a>
                </li>
                <li class="<?php echo $platformsActive; ?>">
                    <a href="/dashboard/platforms" class="btn-hover">
                        <i class="bi bi-phone"></i> 小程序平台
                    </a>
                </li>
                <li class="<?php echo $ordersActive; ?>">
                    <a href="/dashboard/orders" class="btn-hover">
                        <i class="bi bi-cart"></i> 订单管理
                    </a>
                </li>
                <li class="<?php echo $announcementsActive; ?>">
                    <a href="/dashboard/announcements" class="btn-hover">
                        <i class="bi bi-megaphone"></i> 公告管理
                    </a>
                </li>
                <li class="<?php echo $pluginsActive; ?>">
                    <a href="/dashboard/plugins" class="btn-hover">
                        <i class="bi bi-puzzle"></i> 插件中心
                    </a>
                </li>
                <li class="<?php echo $settingsActive; ?>">
                    <a href="/dashboard/settings" class="btn-hover">
                        <i class="bi bi-gear"></i> 系统设置
                    </a>
                </li>
                <li class="<?php echo $recycleActive; ?>">
                    <a href="/dashboard/recycle" class="btn-hover">
                        <i class="bi bi-trash"></i> 回收站
                    </a>
                </li>
            </ul>

            <ul class="list-unstyled CTAs">
                <li>
                    <a href="/logout" class="logout btn-hover">
                        <i class="bi bi-box-arrow-right"></i> 退出登录
                    </a>
                </li>
            </ul>
        </nav>

        <!-- 页面内容 -->
        <div id="content">
            <!-- 顶部导航栏 -->
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-info btn-hover">
                        <i class="bi bi-list"></i>
                    </button>
                    <button class="btn btn-dark d-inline-block d-lg-none ml-auto btn-hover" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                        <i class="bi bi-list"></i>
                    </button>

                    <div class="collapse navbar-collapse" id="navbarSupportedContent">
                        <ul class="nav navbar-nav ms-auto">
                            <li class="nav-item">
                                <a class="nav-link btn-hover" href="#" id="notificationsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-bell"></i>
                                    <span class="badge bg-danger pulse">3</span>
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationsDropdown">
                                    <li><a class="dropdown-item" href="#">新订单通知</a></li>
                                    <li><a class="dropdown-item" href="#">系统更新通知</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#">查看所有通知</a></li>
                                </ul>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link btn-hover" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-person-circle"></i> 管理员
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                    <li><a class="dropdown-item" href="#">个人资料</a></li>
                                    <li><a class="dropdown-item" href="#">修改密码</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="/logout">退出登录</a></li>
                                </ul>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>

            <!-- 主要内容区域 -->
            <div class="content-wrapper">
