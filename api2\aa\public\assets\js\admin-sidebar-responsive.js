/**
 * 管理面板侧边栏响应式脚本
 * 实现侧边栏自动收缩和扩大功能
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('侧边栏响应式脚本已加载');

    // 获取DOM元素
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('main-content');
    const menuToggle = document.getElementById('menu-toggle');

    if (!sidebar || !mainContent) {
        console.error('找不到侧边栏或主内容区域元素');
        return;
    }

    // 添加响应式类
    sidebar.classList.add('sidebar-responsive');
    mainContent.classList.add('main-content-responsive');

    // 根据窗口大小自动调整侧边栏
    function adjustSidebar() {
        console.log('调整侧边栏，窗口宽度:', window.innerWidth);

        if (window.innerWidth < 768) {
            // 在小屏幕上自动折叠侧边栏
            sidebar.classList.add('collapsed');
            mainContent.classList.add('expanded');
            localStorage.setItem('sidebarCollapsed', 'true');
            console.log('小屏幕，侧边栏已折叠');
        } else if (localStorage.getItem('sidebarCollapsed') !== 'true') {
            // 在大屏幕上，如果用户之前没有手动折叠，则展开侧边栏
            sidebar.classList.remove('collapsed');
            mainContent.classList.remove('expanded');
            console.log('大屏幕，侧边栏已展开');
        }
    }

    // 初始调整
    adjustSidebar();

    // 窗口大小变化时调整
    window.addEventListener('resize', function() {
        // 使用防抖动函数避免频繁调用
        clearTimeout(window.resizeTimer);
        window.resizeTimer = setTimeout(function() {
            adjustSidebar();
        }, 250);
    });

    // 菜单切换按钮点击事件
    if (menuToggle) {
        // 移除现有的点击事件
        const newMenuToggle = menuToggle.cloneNode(true);
        menuToggle.parentNode.replaceChild(newMenuToggle, menuToggle);

        // 添加新的点击事件
        newMenuToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('菜单切换按钮被点击');

            // 切换侧边栏状态
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');

            // 保存状态到本地存储
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
            console.log('侧边栏状态已保存:', sidebar.classList.contains('collapsed'));

            // 只调整图表大小，避免不必要的布局重新计算
            setTimeout(function() {
                // 只调整图表大小
                const charts = document.querySelectorAll('canvas[id$="Chart"]');
                if (charts.length > 0) {
                    console.log('调整图表大小');
                    charts.forEach(chart => {
                        if (chart.chart) {
                            chart.chart.resize();
                        }
                    });
                }

                // 触发一个自定义事件，而不是resize事件
                const sidebarToggleEvent = new CustomEvent('sidebarToggle', {
                    detail: { collapsed: sidebar.classList.contains('collapsed') }
                });
                document.dispatchEvent(sidebarToggleEvent);
                console.log('触发sidebarToggle事件');
            }, 300); // 等待过渡动画完成
        });
    } else {
        console.warn('找不到菜单切换按钮');
    }

    // 在小屏幕上点击侧边栏菜单项后自动折叠侧边栏
    const sidebarMenuItems = document.querySelectorAll('.sidebar-menu li a');
    if (sidebarMenuItems.length > 0) {
        sidebarMenuItems.forEach(item => {
            item.addEventListener('click', function() {
                if (window.innerWidth < 768) {
                    sidebar.classList.add('collapsed');
                    mainContent.classList.add('expanded');
                    localStorage.setItem('sidebarCollapsed', 'true');
                    console.log('点击菜单项，小屏幕侧边栏已折叠');
                }
            });
        });
    }

    console.log('侧边栏响应式脚本初始化完成');
});
