/**
 * 主题面板关闭脚本
 * 确保点击页面任意处可以关闭配色面板
 */

document.addEventListener('DOMContentLoaded', function() {
    // 获取主题设置面板和切换按钮
    const themeSettings = document.querySelector('.theme-settings');
    const themeSettingsToggle = document.querySelector('.theme-settings-toggle');
    
    if (themeSettings && themeSettingsToggle) {
        // 点击页面任意处关闭配色面板
        document.addEventListener('click', function(e) {
            // 如果点击的不是主题设置面板内的元素，也不是切换按钮，则关闭面板
            if (themeSettings.classList.contains('show') && 
                !themeSettings.contains(e.target) && 
                e.target !== themeSettingsToggle &&
                !themeSettingsToggle.contains(e.target)) {
                themeSettings.classList.remove('show');
            }
        });
    }
});
