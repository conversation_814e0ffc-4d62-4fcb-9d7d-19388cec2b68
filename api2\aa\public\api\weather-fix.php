<?php
// 天气API一键修复工具

header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>天气API修复工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .test-success { background: #d4edda; border: 1px solid #c3e6cb; }
        .test-error { background: #f8d7da; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌤️ 天气API修复工具</h1>
        
        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $action = $_POST['action'] ?? '';
            
            switch ($action) {
                case 'test_network':
                    testNetworkConnectivity();
                    break;
                case 'test_api':
                    testSpecificAPI();
                    break;
                case 'fix_database':
                    fixDatabase();
                    break;
                case 'reset_settings':
                    resetSettings();
                    break;
            }
        }
        ?>
        
        <div style="margin: 20px 0;">
            <h2>🔧 快速操作</h2>
            
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="test_network">
                <button type="submit" class="btn btn-primary">测试网络连接</button>
            </form>
            
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="fix_database">
                <button type="submit" class="btn btn-success">修复数据库</button>
            </form>
            
            <form method="post" style="display: inline;">
                <input type="hidden" name="action" value="reset_settings">
                <button type="submit" class="btn btn-warning">重置设置</button>
            </form>
        </div>
        
        <div style="margin: 20px 0;">
            <h2>🧪 API测试</h2>
            <form method="post">
                <input type="hidden" name="action" value="test_api">
                <p>
                    <label>服务商:</label>
                    <select name="provider">
                        <option value="heweather">和风天气</option>
                        <option value="openweather">OpenWeatherMap</option>
                        <option value="seniverse">心知天气</option>
                    </select>
                </p>
                <p>
                    <label>API密钥:</label>
                    <input type="text" name="api_key" placeholder="输入API密钥" style="width: 300px;">
                </p>
                <p>
                    <label>城市:</label>
                    <input type="text" name="city" value="北京" style="width: 100px;">
                </p>
                <button type="submit" class="btn btn-primary">测试API</button>
            </form>
        </div>
        
        <div style="margin: 20px 0;">
            <h2>📋 系统信息</h2>
            <?php showSystemInfo(); ?>
        </div>
    </div>
</body>
</html>

<?php

function testNetworkConnectivity() {
    echo "<h3>🌐 网络连接测试</h3>";
    
    $hosts = [
        '和风天气' => 'devapi.qweather.com',
        'OpenWeatherMap' => 'api.openweathermap.org',
        '心知天气' => 'api.seniverse.com',
        '百度' => 'www.baidu.com'
    ];
    
    foreach ($hosts as $name => $host) {
        echo "<div class='test-result'>";
        
        // DNS测试
        $ip = gethostbyname($host);
        if ($ip === $host) {
            echo "<div class='test-error'>❌ $name ($host): DNS解析失败</div>";
            continue;
        }
        
        // 连接测试
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://$host");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        
        $result = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($result !== false && $httpCode > 0) {
            echo "<div class='test-success'>✅ $name ($host): 连接成功 (IP: $ip, HTTP: $httpCode)</div>";
        } else {
            echo "<div class='test-error'>❌ $name ($host): 连接失败 - $error</div>";
        }
        
        echo "</div>";
    }
}

function testSpecificAPI() {
    $provider = $_POST['provider'] ?? 'heweather';
    $apiKey = $_POST['api_key'] ?? '';
    $city = $_POST['city'] ?? '北京';
    
    echo "<h3>🧪 API测试结果</h3>";
    
    if (empty($apiKey)) {
        echo "<div class='test-error'>❌ 请输入API密钥</div>";
        return;
    }
    
    $urls = [
        'heweather' => "https://devapi.qweather.com/v7/weather/now?location=" . urlencode($city) . "&key=" . $apiKey,
        'openweather' => "https://api.openweathermap.org/data/2.5/weather?q=" . urlencode($city) . "&appid=" . $apiKey . "&units=metric&lang=zh_cn",
        'seniverse' => "https://api.seniverse.com/v3/weather/now.json?key=" . $apiKey . "&location=" . urlencode($city) . "&language=zh-Hans&unit=c"
    ];
    
    $url = $urls[$provider] ?? '';
    
    echo "<p><strong>请求URL:</strong> $url</p>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 15);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Weather App/1.0');
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($response === false) {
        echo "<div class='test-error'>❌ 请求失败: $error</div>";
    } else {
        echo "<div class='test-success'>✅ 请求成功 (HTTP: $httpCode)</div>";
        echo "<p><strong>响应内容:</strong></p>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
        
        $data = json_decode($response, true);
        if ($data) {
            echo "<p><strong>解析结果:</strong></p>";
            echo "<pre>" . print_r($data, true) . "</pre>";
        }
    }
}

function fixDatabase() {
    echo "<h3>🔧 数据库修复</h3>";
    
    try {
        $configPath = __DIR__ . '/../../config/config.php';
        $config = require_once($configPath);
        $dbConfig = $config['database'];
        $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
        $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $prefix = $dbConfig['prefix'];
        
        // 检查表是否存在
        $stmt = $pdo->prepare("SHOW TABLES LIKE '{$prefix}weather_settings'");
        $stmt->execute();
        $tableExists = $stmt->fetch();
        
        if (!$tableExists) {
            // 创建表
            $sql = "CREATE TABLE {$prefix}weather_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                api_provider VARCHAR(50) DEFAULT 'heweather',
                api_key VARCHAR(255) DEFAULT '',
                private_key VARCHAR(255) DEFAULT '',
                default_city VARCHAR(100) DEFAULT '北京',
                enabled TINYINT(1) DEFAULT 1,
                cache_duration INT DEFAULT 30,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $pdo->exec($sql);
            echo "<div class='test-success'>✅ 天气设置表创建成功</div>";
        } else {
            echo "<div class='test-success'>✅ 天气设置表已存在</div>";
        }
        
        // 检查private_key字段
        $stmt = $pdo->prepare("SHOW COLUMNS FROM {$prefix}weather_settings LIKE 'private_key'");
        $stmt->execute();
        $columnExists = $stmt->fetch();
        
        if (!$columnExists) {
            $pdo->exec("ALTER TABLE {$prefix}weather_settings ADD COLUMN private_key VARCHAR(255) DEFAULT '' AFTER api_key");
            echo "<div class='test-success'>✅ private_key字段添加成功</div>";
        }
        
        // 插入默认设置
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM {$prefix}weather_settings");
        $stmt->execute();
        $count = $stmt->fetchColumn();
        
        if ($count == 0) {
            $stmt = $pdo->prepare("INSERT INTO {$prefix}weather_settings (api_provider, api_key, private_key, default_city, enabled, cache_duration) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->execute(['heweather', '', '', '北京', 1, 30]);
            echo "<div class='test-success'>✅ 默认设置插入成功</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='test-error'>❌ 数据库修复失败: " . $e->getMessage() . "</div>";
    }
}

function resetSettings() {
    echo "<h3>🔄 重置设置</h3>";
    
    try {
        $configPath = __DIR__ . '/../../config/config.php';
        $config = require_once($configPath);
        $dbConfig = $config['database'];
        $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
        $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $prefix = $dbConfig['prefix'];
        
        $stmt = $pdo->prepare("UPDATE {$prefix}weather_settings SET api_provider = ?, api_key = ?, private_key = ?, default_city = ?, enabled = ?, cache_duration = ? WHERE id = 1");
        $stmt->execute(['heweather', '', '', '北京', 1, 30]);
        
        echo "<div class='test-success'>✅ 设置已重置为默认值</div>";
        
    } catch (Exception $e) {
        echo "<div class='test-error'>❌ 重置失败: " . $e->getMessage() . "</div>";
    }
}

function showSystemInfo() {
    echo "<ul>";
    echo "<li>PHP版本: " . PHP_VERSION . "</li>";
    echo "<li>cURL支持: " . (function_exists('curl_init') ? '✅ 是' : '❌ 否') . "</li>";
    echo "<li>OpenSSL支持: " . (extension_loaded('openssl') ? '✅ 是' : '❌ 否') . "</li>";
    echo "<li>allow_url_fopen: " . (ini_get('allow_url_fopen') ? '✅ 启用' : '❌ 禁用') . "</li>";
    echo "<li>时区: " . date_default_timezone_get() . "</li>";
    echo "<li>当前时间: " . date('Y-m-d H:i:s') . "</li>";
    echo "</ul>";
}
?>
