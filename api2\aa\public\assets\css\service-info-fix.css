/**
 * 服务信息板块修复样式
 * 专门修复当前版本、服务期限和系统时间显示不正常的问题
 */

/* 服务信息板块特殊样式 */
.stat-card:nth-child(3) .stat-item {
    height: auto !important;
    min-height: 120px !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: flex-start !important;
}

/* 授权时长样式 */
.stat-card:nth-child(3) .stat-item:nth-child(1) {
    padding-bottom: 15px !important;
}

.stat-card:nth-child(3) .stat-item:nth-child(1) .stat-value {
    margin-top: 10px !important;
    display: block !important;
}

/* 当前版本样式 */
.stat-card:nth-child(3) .stat-item:nth-child(2) .stat-value-with-btn {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    margin-top: 10px !important;
    width: 100% !important;
}

.stat-card:nth-child(3) .stat-item:nth-child(2) .stat-value {
    margin-bottom: 8px !important;
    display: block !important;
    text-align: center !important;
}

.stat-card:nth-child(3) .stat-item:nth-child(2) .btn {
    display: inline-block !important;
    margin-top: 5px !important;
}

/* 服务期限样式 */
.stat-card:nth-child(3) .stat-item:nth-child(3) .stat-value-with-btn {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    margin-top: 10px !important;
    width: 100% !important;
}

.stat-card:nth-child(3) .stat-item:nth-child(3) .stat-value {
    margin-bottom: 8px !important;
    display: block !important;
    text-align: center !important;
}

.stat-card:nth-child(3) .stat-item:nth-child(3) .btn {
    display: inline-block !important;
    margin-top: 5px !important;
}

/* 系统时间样式 */
.stat-card:nth-child(3) .stat-item:nth-child(4) .stat-value-with-btn {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    margin-top: 10px !important;
    width: 100% !important;
    position: relative !important;
    left: 0 !important;
    right: 0 !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

.stat-card:nth-child(3) .stat-item:nth-child(4) #current-date {
    display: block !important;
    margin-bottom: 5px !important;
    text-align: center !important;
    width: 100% !important;
    position: relative !important;
    left: 0 !important;
    right: 0 !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

.stat-card:nth-child(3) .stat-item:nth-child(4) #current-time {
    display: block !important;
    text-align: center !important;
    width: 100% !important;
    position: relative !important;
    left: 0 !important;
    right: 0 !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

/* 修复按钮样式 */
.stat-card:nth-child(3) .btn-sm {
    white-space: nowrap !important;
    font-size: 0.9em !important;
    padding: 4px 12px !important;
    margin-top: 5px !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .stat-card:nth-child(3) .stat-item {
        padding: 10px !important;
    }

    .stat-card:nth-child(3) .stat-value-with-btn .btn {
        padding: 3px 10px !important;
        font-size: 0.8em !important;
    }
}
