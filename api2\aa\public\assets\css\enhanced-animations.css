/**
 * 情侣头像匹配系统 - 增强动效样式
 */

/* 悬浮卡片效果 */
.floating-card {
  animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* 霓虹灯按钮效果 */
.neon-btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 2px solid var(--primary-color);
  box-shadow: 0 0 5px var(--primary-color), 0 0 10px var(--primary-color) inset;
}

.neon-btn:hover {
  box-shadow: 0 0 10px var(--primary-color), 0 0 20px var(--primary-color) inset;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
}

/* 气泡悬浮效果 */
.bubble-float {
  position: relative;
}

.bubble-float::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.bubble-float:hover::before {
  opacity: 1;
  animation: bubble-anim 1s ease-in-out;
}

@keyframes bubble-anim {
  0% {
    transform: scale(0.5);
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* 彩虹加载动画 */
.rainbow-loader {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: conic-gradient(
    #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #8b00ff, #ff0000
  );
  animation: spin 1.5s linear infinite;
  position: relative;
}

.rainbow-loader::before {
  content: '';
  position: absolute;
  top: 5px;
  left: 5px;
  right: 5px;
  bottom: 5px;
  background: white;
  border-radius: 50%;
}

/* 弹跳按钮效果 */
.bounce-btn {
  transition: transform 0.3s ease;
}

.bounce-btn:hover {
  animation: bounce 0.5s ease;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* 抖动效果 */
.shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

/* 心跳效果 */
.heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.1);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.1);
  }
  70% {
    transform: scale(1);
  }
}

/* 渐变文字效果 */
.gradient-text {
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: gradient-shift 3s ease infinite;
  background-size: 200% 200%;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 闪烁效果 */
.blink {
  animation: blink 1s linear infinite;
}

@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 旋转图标效果 */
.rotate-icon {
  transition: transform 0.3s ease;
}

.rotate-icon:hover {
  transform: rotate(360deg);
}

/* 滑动标记效果 */
.slide-mark {
  position: relative;
  overflow: hidden;
}

.slide-mark::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 4px;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

.slide-mark:hover::after,
.slide-mark.active::after {
  width: 100%;
}

/* 弹出提示效果 */
.tooltip-pop {
  position: relative;
}

.tooltip-pop::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(10px);
  padding: 5px 10px;
  background-color: var(--dark-color);
  color: white;
  border-radius: 4px;
  font-size: 0.8rem;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.tooltip-pop::after {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(10px);
  border: 5px solid transparent;
  border-top-color: var(--dark-color);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.tooltip-pop:hover::before,
.tooltip-pop:hover::after {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(0);
}

/* 聚焦缩放效果 */
.focus-zoom {
  transition: all 0.3s ease;
}

.focus-zoom:focus {
  transform: scale(1.05);
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

/* 进度条动画 */
.progress-bar {
  height: 6px;
  background-color: var(--neutral-200);
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 3px;
  transition: width 0.5s ease;
  animation: progress-pulse 2s ease infinite;
}

@keyframes progress-pulse {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
