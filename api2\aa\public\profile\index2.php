<?php
// 启动会话
session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    // 未登录，重定向到登录页面
    header('Location: /login');
    exit;
}

// 获取用户信息
$userId = $_SESSION['user_id'] ?? 1;
$username = $_SESSION['username'] ?? '管理员';
$userEmail = $_SESSION['email'] ?? '<EMAIL>';
$userPhone = $_SESSION['phone'] ?? '138****8888';
$userRole = $_SESSION['role'] ?? 'admin';
$userStatus = $_SESSION['status'] ?? 'active';
$userInitial = mb_substr($username, 0, 1, 'UTF-8');

// 检查用户角色
$isAdmin = $userRole === 'admin';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人资料 - 情侣头像匹配管理系统</title>

    <!-- 引入Bootstrap和其他CSS库 -->
    <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/header-scripts.php'); ?>
    <link rel="stylesheet" href="/assets/css/profile-page-fix.css">
    <link rel="stylesheet" href="/assets/css/profile-forms-fix.css">
    <link rel="stylesheet" href="/assets/css/checkbox-style-fix.css">

    <style>
        /* 个人资料页面样式 */
        .profile-container {
            width: 100%; /* 占据全宽 */
            margin: 0;
            padding: 30px; /* 增加内边距 */
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: 0 5px 15px var(--shadow-color);
            animation: fadeInUp 0.5s;
        }

        .profile-header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        .avatar-container {
            position: relative;
            margin-right: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: var(--white-color);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            font-weight: 600;
            box-shadow: 0 5px 15px rgba(255, 107, 149, 0.3);
            margin-bottom: 10px;
            overflow: hidden;
        }

        .profile-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .btn-upload {
            background-color: var(--light-color);
            color: var(--dark-color);
            border: none;
            border-radius: 20px;
            padding: 5px 10px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-upload:hover {
            background-color: var(--primary-color);
            color: var(--white-color);
        }

        .profile-info {
            flex: 1;
        }

        .profile-info h2 {
            font-size: 1.8rem;
            margin-bottom: 5px;
            color: var(--dark-color);
        }

        .profile-role {
            display: inline-block;
            padding: 3px 10px;
            background-color: rgba(255, 107, 149, 0.1);
            color: var(--primary-color);
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            margin: 0 auto;
            text-align: center;
            display: block;
            width: fit-content;
        }

        .profile-tabs {
            display: flex;
            border-bottom: 1px solid #eee;
            margin-bottom: 20px;
        }

        .profile-tab {
            padding: 10px 20px;
            cursor: pointer;
            font-weight: 500;
            color: var(--gray-color);
            border-bottom: 2px solid transparent;
            transition: all var(--transition-speed);
        }

        .profile-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .profile-content {
            display: none;
        }

        .profile-content.active {
            display: block;
            animation: fadeIn 0.3s;
        }

        .form-group {
            margin-bottom: 25px; /* 增加底部间距 */
        }

        .form-group label {
            display: block;
            margin-bottom: 8px; /* 增加标签底部间距 */
            font-weight: 500;
            color: var(--dark-color);
            font-size: 1.05rem; /* 增加字体大小 */
        }

        .form-group input {
            width: 100%;
            padding: 12px 15px; /* 增加输入框内边距 */
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: all var(--transition-speed);
            box-shadow: 0 2px 5px rgba(0,0,0,0.05); /* 添加轻微阴影 */
        }

        .form-group input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(255, 107, 149, 0.2);
            outline: none;
        }

        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 30px;
        }

        .btn {
            display: inline-block;
            font-weight: 500;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            user-select: none;
            border: 1px solid transparent;
            padding: 0.5rem 1rem;
            font-size: 1rem;
            line-height: 1.5;
            border-radius: var(--border-radius);
            transition: all var(--transition-speed);
            cursor: pointer;
        }

        .btn-primary {
            color: var(--white-color);
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #ff4f7e;
            border-color: #ff4f7e;
        }

        .btn-secondary {
            color: var(--dark-color);
            background-color: #f8f9fa;
            border-color: #ddd;
        }

        .btn-secondary:hover {
            background-color: #e9ecef;
            border-color: #ddd;
        }

        /* 信息卡片样式 */
        .info-card {
            background-color: #f8f9fa;
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
        }

        .info-item {
            display: flex;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .info-item:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }

        .info-label {
            width: 100px;
            font-weight: 500;
            color: var(--gray-color);
        }

        .info-value {
            flex: 1;
            font-weight: 500;
            color: var(--dark-color);
        }

        .info-note {
            background-color: rgba(255, 193, 7, 0.1);
            color: #856404;
            padding: 10px 15px;
            border-radius: var(--border-radius);
            margin-top: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status {
            display: inline-block;
            padding: 3px 10px;
            border-radius: 20px;
            font-size: 0.85rem;
        }

        .status.active {
            background-color: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .status.inactive {
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        /* 复选框样式 */
        .form-group.checkbox {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group.checkbox input[type="checkbox"] {
            width: auto;
            margin-right: 10px;
        }

        .form-group.checkbox label {
            margin-bottom: 0;
            cursor: pointer;
        }

        /* 密码强度指示器 */
        .password-strength {
            margin-top: 5px;
            height: 5px;
            background-color: #eee;
            border-radius: 5px;
            overflow: hidden;
        }

        .password-strength-bar {
            height: 100%;
            width: 0;
            transition: width 0.3s, background-color 0.3s;
        }

        .password-strength-text {
            font-size: 0.85rem;
            margin-top: 5px;
            color: var(--gray-color);
        }

        .password-tips {
            margin-top: 5px;
            font-size: 0.85rem;
            color: var(--gray-color);
        }

        .password-match-text {
            font-size: 0.85rem;
            margin-top: 5px;
        }

        .password-match-text.match {
            color: #28a745;
        }

        .password-match-text.not-match {
            color: #dc3545;
        }

        .required {
            color: #dc3545;
            margin-left: 3px;
        }

        /* 通知设置样式 */
        .notification-section {
            background-color: #f8f9fa;
            border-radius: var(--border-radius);
            padding: 25px; /* 增加内边距 */
            margin-bottom: 25px; /* 增加底部间距 */
            box-shadow: 0 3px 10px rgba(0,0,0,0.05); /* 添加阴影效果 */
            border: 1px solid #eee; /* 添加边框 */
        }

        .section-title {
            font-size: 1.2rem; /* 增加字体大小 */
            font-weight: 600;
            margin-bottom: 20px; /* 增加底部间距 */
            color: var(--dark-color);
            padding-bottom: 12px; /* 增加底部内边距 */
            border-bottom: 1px solid #eee;
        }

        .checkbox-description {
            margin-left: 28px; /* 增加左侧间距 */
            font-size: 0.9rem; /* 增加字体大小 */
            color: var(--gray-color);
            margin-top: 8px; /* 增加顶部间距 */
            line-height: 1.5; /* 增加行高 */
        }

        /* 页面布局修复 */
        .dashboard-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            width: 100%;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 0; /* 移除内边距 */
            width: 100%;
            max-width: 100%;
        }

        /* 底部组件样式 */
        .site-footer {
            width: 100%;
            margin-top: auto;
            text-align: center;
            padding: 20px 0;
            background-color: var(--white-color);
            border-top: 1px solid #eee;
        }

        /* 取消模态框，直接在底层面板上显示内容 */
        .profile-section {
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: 0 5px 15px var(--shadow-color);
            margin-bottom: 30px;
            overflow: hidden;
            animation: fadeIn 0.5s;
            width: 100%;
            max-width: 100%;
            margin-left: 0;
            margin-right: 0;
            padding: 30px;
        }

        .profile-section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            background-color: #f8f9fa;
        }

        .profile-section-header h3 {
            margin: 0;
            font-size: 1.2rem;
            color: var(--dark-color);
        }

        .profile-section-body {
            padding: 20px;
        }

        .profile-section-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            padding: 15px 20px;
            border-top: 1px solid #eee;
            background-color: #f8f9fa;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Toast 通知样式 */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 300px;
            background-color: var(--white-color);
            border-radius: var(--border-radius);
            box-shadow: 0 5px 15px var(--shadow-color);
            overflow: hidden;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transform: translateX(30px);
            transition: all 0.3s;
        }

        .toast.show {
            opacity: 1;
            visibility: visible;
            transform: translateX(0);
        }

        .toast-content {
            display: flex;
            align-items: center;
            padding: 15px;
        }

        .toast-icon {
            font-size: 1.5rem;
            margin-right: 15px;
            display: none;
        }

        .toast-icon.success {
            color: var(--success-color);
        }

        .toast-icon.error {
            color: var(--danger-color);
        }

        .toast-icon.info {
            color: var(--info-color);
        }

        .toast-message {
            flex: 1;
            font-weight: 500;
        }

        .toast-progress {
            height: 3px;
            background-color: var(--primary-color);
            width: 100%;
            animation: progress 3s linear;
        }

        @keyframes progress {
            from { width: 100%; }
            to { width: 0%; }
        }

        /* 加载动画样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 107, 149, 0.3);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 动画 */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="loading-overlay" id="loading-overlay">
        <div class="spinner"></div>
    </div>

    <div class="dashboard-container">
        <!-- 引入通用头部导航 -->
        <?php
        // 包含身份验证检查
        include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/auth-check.php');
        include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/header-nav.php');
        ?>

        <div class="main-content" style="width: 100%; max-width: 100%; padding: 0;">
            <div class="profile-section" style="width: 100%; max-width: 100%; margin: 0; padding: 30px;">
                <div class="profile-header">
                    <div class="avatar-container">
                        <div class="profile-avatar" id="profile-avatar"><?php echo htmlspecialchars($userInitial); ?></div>
                        <button type="button" class="btn btn-upload" id="upload-avatar-btn">
                            <i class="bi bi-camera"></i> 更换头像
                        </button>
                        <input type="file" id="avatar-upload" accept="image/*" style="display: none;">
                    </div>
                    <div class="profile-info">
                        <h2><?php echo htmlspecialchars($username); ?></h2>
                        <span class="profile-role"><?php echo $isAdmin ? '管理员' : '租户'; ?></span>
                    </div>
                </div>

                <div class="profile-tabs">
                    <div class="profile-tab active" data-tab="basic-info">基本信息</div>
                    <div class="profile-tab" data-tab="change-password">修改密码</div>
                </div>

                <div class="profile-content active" id="basic-info">
                    <div class="info-card">
                        <div class="info-item">
                            <div class="info-label">用户名</div>
                            <div class="info-value"><?php echo htmlspecialchars($username); ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">邮箱</div>
                            <div class="info-value"><?php echo htmlspecialchars($userEmail); ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">手机号</div>
                            <div class="info-value"><?php echo htmlspecialchars($userPhone); ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">角色</div>
                            <div class="info-value"><?php echo $isAdmin ? '管理员' : '租户'; ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">状态</div>
                            <div class="info-value"><span class="status <?php echo $userStatus === 'active' ? 'active' : 'inactive'; ?>"><?php echo $userStatus === 'active' ? '正常' : '禁用'; ?></span></div>
                        </div>
                        <div class="info-note">
                            <i class="bi bi-info-circle"></i> 基本信息不可修改，如需修改请联系系统管理员
                        </div>
                    </div>
                </div>

                <div class="profile-content" id="change-password">
                    <form id="change-password-form">
                        <div class="form-group">
                            <label for="current-password">当前密码<span class="required">*</span></label>
                            <input type="password" id="current-password" required>
                        </div>
                        <div class="form-group">
                            <label for="new-password">新密码<span class="required">*</span></label>
                            <input type="password" id="new-password" required onkeyup="checkPasswordStrength()">
                            <div class="password-strength">
                                <div class="password-strength-bar" id="password-strength-bar"></div>
                            </div>
                            <div class="password-strength-text" id="password-strength-text">密码强度: 未输入</div>
                            <div class="password-tips">
                                密码要求: 至少8个字符，包含大小写字母、数字和特殊符号
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="confirm-password">确认新密码<span class="required">*</span></label>
                            <input type="password" id="confirm-password" required onkeyup="checkPasswordMatch()">
                            <div class="password-match-text" id="password-match-text"></div>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn btn-secondary" onclick="resetForm('change-password-form')">重置</button>
                            <button type="button" class="btn btn-primary" onclick="changePassword()">保存</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 网站底部 -->
            <div class="footer-wrapper">
                <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/site-footer.php'); ?>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div class="toast" id="toast">
        <div class="toast-content">
            <i class="bi bi-check-circle-fill toast-icon success"></i>
            <i class="bi bi-x-circle-fill toast-icon error"></i>
            <i class="bi bi-info-circle-fill toast-icon info"></i>
            <div class="toast-message">操作成功</div>
        </div>
        <div class="toast-progress"></div>
    </div>

    <!-- 引入Bootstrap和其他JS库 -->
    <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/footer-scripts.php'); ?>
    <script src="/assets/js/toast-unified.js"></script>
    <script src="/assets/js/avatar-theme-fix.js"></script>
    <script src="/assets/js/profile-reset-fix.js"></script>
    <script src="/assets/js/sidebar-toggle-fix.js"></script>
    <!-- 添加天气和人生哲理组件 -->
    <script src="/assets/js/wisdom-weather.js"></script>

    <!-- 修复天气和人生哲理组件样式和按钮点击问题 -->
    <style>
        /* 修复天气和人生哲理组件样式 */
        .header-info-content {
            display: flex;
            align-items: center;
            margin: 0 auto;
            flex: 1;
            padding: 0 20px;
            overflow: hidden;
            color: var(--text-color, #333);
            font-size: 14px;
            max-width: 600px;
            justify-content: center;
        }

        .wisdom-quote, .weather-info {
            display: flex;
            align-items: center;
            margin-right: 20px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            animation: fadeIn 0.5s;
            max-width: 250px;
        }

        .wisdom-quote i, .weather-info i {
            margin-right: 8px;
            font-size: 16px;
            color: var(--primary-color);
            flex-shrink: 0;
        }

        .info-actions {
            display: flex;
            gap: 10px;
            margin-left: 10px;
        }

        .info-actions button {
            background: rgba(var(--primary-color-rgb, 255, 107, 149), 0.1);
            border: none;
            color: var(--primary-color);
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            flex-shrink: 0;
        }

        .info-actions button:hover {
            background: rgba(var(--primary-color-rgb, 255, 107, 149), 0.2);
            transform: rotate(15deg);
        }

        .info-actions button:active {
            background: rgba(var(--primary-color-rgb, 255, 107, 149), 0.3);
            transform: scale(0.95);
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .rotating {
            animation: rotate 1s linear;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .wisdom-quote {
                display: none !important;
            }

            .weather-info {
                margin-right: 0;
                max-width: 150px;
            }

            .header-info-content {
                padding: 0 10px;
            }
        }

        /* 修复头部样式 */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }

        /* 个人资料面板样式 */
        .profile-section {
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
            width: 100%;
            max-width: 100%;
            transition: all 0.3s ease;
        }

        .menu-toggle {
            background: none;
            border: none;
            color: var(--dark-color);
            font-size: 1.5rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            transition: all var(--transition-speed);
        }

        .user-info {
            display: flex;
            align-items: center;
            position: relative;
            cursor: pointer;
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 直接添加事件监听器到按钮
            const refreshBtn = document.getElementById('refresh-info');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function() {
                    console.log('刷新按钮被点击');
                    // 添加旋转动画
                    this.classList.add('rotating');
                    setTimeout(() => {
                        this.classList.remove('rotating');
                    }, 1000);

                    // 触发内容更新
                    updateWisdom();
                    updateWeather();
                });
            }

            const toggleBtn = document.getElementById('toggle-info-type');
            if (toggleBtn) {
                toggleBtn.addEventListener('click', function() {
                    console.log('切换按钮被点击');
                    // 获取当前类型
                    const currentType = localStorage.getItem('infoType') || 'both';
                    let newType;

                    // 循环切换类型
                    switch (currentType) {
                        case 'both':
                            newType = 'wisdom';
                            break;
                        case 'wisdom':
                            newType = 'weather';
                            break;
                        case 'weather':
                            newType = 'both';
                            break;
                        default:
                            newType = 'both';
                    }

                    // 保存新类型
                    localStorage.setItem('infoType', newType);

                    // 更新显示
                    updateDisplayState(newType);
                });
            }

            // 辅助函数
            function updateDisplayState(type) {
                const wisdomQuote = document.querySelector('.wisdom-quote');
                const weatherInfo = document.querySelector('.weather-info');

                if (!wisdomQuote || !weatherInfo) return;

                switch (type) {
                    case 'wisdom':
                        wisdomQuote.style.display = 'flex';
                        weatherInfo.style.display = 'none';
                        break;
                    case 'weather':
                        wisdomQuote.style.display = 'none';
                        weatherInfo.style.display = 'flex';
                        break;
                    case 'both':
                    default:
                        wisdomQuote.style.display = 'flex';
                        weatherInfo.style.display = 'flex';
                        break;
                }
            }

            function updateWisdom() {
                const wisdomText = document.getElementById('wisdom-text');
                if (!wisdomText) return;

                // 随机选择一条智慧语录
                const wisdomQuotes = [
                    "生活不是等待风暴过去，而是学会在雨中跳舞。",
                    "人生就像骑自行车，要保持平衡就得不断前进。",
                    "成功不是最终的，失败也不是致命的，重要的是继续前进的勇气。",
                    "不要为成功而努力，要为做一个有价值的人而努力。",
                    "生活中最重要的事情是明确你想要什么。",
                    "人生最大的挑战是超越自己。",
                    "每一个不曾起舞的日子，都是对生命的辜负。",
                    "微笑是世界上最好的语言。",
                    "成功的秘诀在于坚持目标的始终。",
                    "真正的智慧是知道自己所不知道的东西。"
                ];

                const randomIndex = Math.floor(Math.random() * wisdomQuotes.length);
                wisdomText.textContent = wisdomQuotes[randomIndex];

                // 添加淡入动画
                wisdomText.style.animation = 'none';
                setTimeout(() => {
                    wisdomText.style.animation = 'fadeIn 0.5s';
                }, 10);
            }

            function updateWeather() {
                const weatherText = document.getElementById('weather-text');
                if (!weatherText) return;

                // 模拟天气信息
                const weathers = [
                    "北京 晴 28°C",
                    "上海 多云 26°C",
                    "广州 小雨 30°C",
                    "深圳 阵雨 29°C",
                    "杭州 晴 27°C",
                    "成都 阴 25°C",
                    "武汉 雷阵雨 31°C",
                    "西安 晴 30°C",
                    "南京 多云 26°C",
                    "重庆 小雨 28°C"
                ];

                const randomIndex = Math.floor(Math.random() * weathers.length);
                weatherText.textContent = weathers[randomIndex];

                // 添加淡入动画
                weatherText.style.animation = 'none';
                setTimeout(() => {
                    weatherText.style.animation = 'fadeIn 0.5s';
                }, 10);
            }

            // 初始化显示状态
            const currentType = localStorage.getItem('infoType') || 'both';
            updateDisplayState(currentType);
        });
    </script>

    <script>
        // DOM元素
        const profileTabs = document.querySelectorAll('.profile-tab');
        const profileContents = document.querySelectorAll('.profile-content');
        const loadingOverlay = document.getElementById('loading-overlay');
        const uploadAvatarBtn = document.getElementById('upload-avatar-btn');
        const avatarUpload = document.getElementById('avatar-upload');
        const profileAvatar = document.getElementById('profile-avatar');

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 显示加载动画
            showLoading();

            // 添加标签切换功能
            profileTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');

                    // 切换标签
                    profileTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');

                    // 切换内容
                    profileContents.forEach(content => {
                        content.classList.remove('active');
                        if (content.id === tabId) {
                            content.classList.add('active');
                        }
                    });
                });
            });

            // 头像上传功能
            if (uploadAvatarBtn && avatarUpload) {
                uploadAvatarBtn.addEventListener('click', function() {
                    avatarUpload.click();
                });

                avatarUpload.addEventListener('change', function() {
                    if (this.files && this.files[0]) {
                        const reader = new FileReader();

                        reader.onload = function(e) {
                            // 创建图片元素
                            const img = document.createElement('img');
                            img.src = e.target.result;

                            // 清空头像容器并添加图片
                            profileAvatar.innerHTML = '';
                            profileAvatar.appendChild(img);

                            // 模拟上传
                            showToast('头像上传成功', 'success');
                        };

                        reader.readAsDataURL(this.files[0]);
                    }
                });
            }

            // 隐藏加载动画
            setTimeout(hideLoading, 500);
        });

        // 显示加载动画
        function showLoading() {
            loadingOverlay.classList.add('show');
        }

        // 隐藏加载动画
        function hideLoading() {
            loadingOverlay.classList.remove('show');
        }

        // 重置表单
        function resetForm(formId) {
            document.getElementById(formId).reset();

            // 重置密码强度指示器
            if (formId === 'change-password-form') {
                const strengthBar = document.getElementById('password-strength-bar');
                const strengthText = document.getElementById('password-strength-text');
                const matchText = document.getElementById('password-match-text');

                if (strengthBar) strengthBar.style.width = '0';
                if (strengthBar) strengthBar.style.backgroundColor = '#eee';
                if (strengthText) strengthText.textContent = '密码强度: 未输入';
                if (matchText) matchText.textContent = '';
            }

            showToast('表单已重置', 'info');
        }

        // 检查密码强度
        function checkPasswordStrength() {
            const password = document.getElementById('new-password').value;
            const strengthBar = document.getElementById('password-strength-bar');
            const strengthText = document.getElementById('password-strength-text');

            if (!password) {
                strengthBar.style.width = '0';
                strengthBar.style.backgroundColor = '#eee';
                strengthText.textContent = '密码强度: 未输入';
                return;
            }

            // 密码强度评分
            let score = 0;

            // 长度检查
            if (password.length >= 8) score += 1;
            if (password.length >= 12) score += 1;

            // 复杂度检查
            if (/[A-Z]/.test(password)) score += 1; // 大写字母
            if (/[a-z]/.test(password)) score += 1; // 小写字母
            if (/[0-9]/.test(password)) score += 1; // 数字
            if (/[^A-Za-z0-9]/.test(password)) score += 1; // 特殊字符

            // 设置强度条
            let strength = '';
            let color = '';
            let width = '';

            switch (score) {
                case 0:
                case 1:
                    strength = '非常弱';
                    color = '#dc3545';
                    width = '20%';
                    break;
                case 2:
                    strength = '弱';
                    color = '#ffc107';
                    width = '40%';
                    break;
                case 3:
                    strength = '中等';
                    color = '#fd7e14';
                    width = '60%';
                    break;
                case 4:
                    strength = '强';
                    color = '#20c997';
                    width = '80%';
                    break;
                case 5:
                case 6:
                    strength = '非常强';
                    color = '#28a745';
                    width = '100%';
                    break;
            }

            strengthBar.style.width = width;
            strengthBar.style.backgroundColor = color;
            strengthText.textContent = '密码强度: ' + strength;

            // 同时检查密码匹配
            checkPasswordMatch();
        }

        // 检查密码匹配
        function checkPasswordMatch() {
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            const matchText = document.getElementById('password-match-text');

            if (!confirmPassword) {
                matchText.textContent = '';
                matchText.className = 'password-match-text';
                return;
            }

            if (newPassword === confirmPassword) {
                matchText.textContent = '密码匹配';
                matchText.className = 'password-match-text match';
            } else {
                matchText.textContent = '密码不匹配';
                matchText.className = 'password-match-text not-match';
            }
        }

        // 修改密码
        function changePassword() {
            showLoading();

            // 获取表单数据
            const currentPassword = document.getElementById('current-password').value;
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;

            // 表单验证
            if (!currentPassword) {
                hideLoading();
                showToast('请输入当前密码', 'error');
                return;
            }

            if (!newPassword) {
                hideLoading();
                showToast('请输入新密码', 'error');
                return;
            }

            if (!confirmPassword) {
                hideLoading();
                showToast('请确认新密码', 'error');
                return;
            }

            if (newPassword !== confirmPassword) {
                hideLoading();
                showToast('两次输入的密码不一致', 'error');
                return;
            }

            // 密码强度检查
            const strengthBar = document.getElementById('password-strength-bar');
            const width = parseInt(strengthBar.style.width);

            if (width < 60) {
                hideLoading();
                showToast('密码强度不足，请设置更复杂的密码', 'error');
                return;
            }

            // 模拟API请求
            setTimeout(() => {
                hideLoading();
                showToast('密码修改成功', 'success', true);

                // 重置表单
                document.getElementById('change-password-form').reset();

                // 重置密码强度指示器
                strengthBar.style.width = '0';
                strengthBar.style.backgroundColor = '#eee';
                document.getElementById('password-strength-text').textContent = '密码强度: 未输入';
                document.getElementById('password-match-text').textContent = '';

                // 3秒后刷新页面
                setTimeout(() => {
                    location.reload();
                }, 3000);
            }, 800);
        }


    </script>
</body>
</html>
