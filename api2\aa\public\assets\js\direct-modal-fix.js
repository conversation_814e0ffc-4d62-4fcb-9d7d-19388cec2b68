/**
 * 直接模态框修复脚本
 * 专门修复账户管理、小程序平台、公告管理、插件中心、应用回收站页面的模态框问题
 * 确保模态框点击空白处不会关闭，只能通过取消按钮、关闭按钮或右上角×关闭
 */

// 立即执行函数，避免变量污染全局作用域
(function() {
    // 等待DOM加载完成
    document.addEventListener('DOMContentLoaded', function() {
        console.log('直接模态框修复脚本已加载');

        // 保存原始的openModal和closeModal函数
        const originalOpenModal = window.openModal;
        const originalCloseModal = window.closeModal;

        // 重写openModal函数
        window.openModal = function(modalId) {
            console.log('打开模态框：', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                // 显示模态框
                modal.style.display = 'block';
                document.body.style.overflow = 'hidden';

                // 移除原有的点击事件
                modal.onclick = null;

                // 添加新的点击事件，阻止冒泡
                modal.addEventListener('click', function(e) {
                    // 如果点击的是模态框本身（空白区域），阻止事件传播
                    if (e.target === this) {
                        e.stopPropagation();
                        e.preventDefault();
                        console.log('点击了模态框空白处，阻止关闭');
                        return false;
                    }
                });

                // 确保关闭按钮可以正常工作
                const closeButtons = modal.querySelectorAll('.close-btn, .modal-header .bi-x-lg, .modal-header .bi-x');
                closeButtons.forEach(function(button) {
                    let parentButton = button.closest('button');
                    if (parentButton) {
                        // 确保点击事件只绑定一次
                        if (!parentButton._hasCloseHandler) {
                            parentButton.addEventListener('click', function() {
                                console.log('关闭按钮被点击，模态框ID：', modalId);
                                closeModal(modalId);
                            });
                            parentButton._hasCloseHandler = true;
                        }
                    }
                });

                // 确保取消按钮可以正常工作
                const cancelButtons = modal.querySelectorAll('.btn-secondary, .modal-footer button');
                cancelButtons.forEach(function(button) {
                    // 检查按钮文本是否包含"取消"或"关闭"
                    const buttonText = button.textContent.trim().toLowerCase();
                    const isCancelButton = buttonText.includes('取消') || buttonText.includes('关闭') ||
                                          button.classList.contains('btn-secondary');

                    if (!isCancelButton) return;

                    // 移除原有的onclick属性
                    if (button.hasAttribute('onclick')) {
                        button.removeAttribute('onclick');
                    }

                    // 确保点击事件只绑定一次
                    if (!button._hasCancelHandler) {
                        button.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log('取消按钮被点击，模态框ID：', modalId);
                            closeModal(modalId);
                        });
                        button._hasCancelHandler = true;
                    }
                });

                // 确保确定按钮可以正常工作
                const confirmButtons = modal.querySelectorAll('.btn-primary, .modal-footer button');
                confirmButtons.forEach(function(button) {
                    // 检查按钮文本是否包含"确定"或"保存"
                    const buttonText = button.textContent.trim().toLowerCase();
                    const isConfirmButton = buttonText.includes('确定') || buttonText.includes('保存') ||
                                           button.classList.contains('btn-primary');

                    if (!isConfirmButton) return;

                    // 获取onclick属性
                    const onclickValue = button.getAttribute('onclick');
                    if (onclickValue) {
                        // 已有onclick属性，保留原有功能
                        console.log('确定按钮已有onclick属性:', onclickValue);
                        return;
                    }

                    // 根据模态框ID判断应该调用哪个函数
                    if (modalId === 'add-account-modal') {
                        button.onclick = function() {
                            console.log('添加账户按钮被点击');
                            if (typeof addAccount === 'function') {
                                addAccount();
                            }
                        };
                    } else if (modalId === 'edit-account-modal') {
                        button.onclick = function() {
                            console.log('更新账户按钮被点击');
                            if (typeof updateAccount === 'function') {
                                updateAccount();
                            }
                        };
                    } else if (modalId === 'reset-pwd-modal') {
                        button.onclick = function() {
                            console.log('重置密码按钮被点击');
                            if (typeof resetPassword === 'function') {
                                resetPassword();
                            }
                        };
                    } else if (modalId === 'add-platform-modal') {
                        button.onclick = function() {
                            console.log('添加平台按钮被点击');
                            if (typeof addPlatform === 'function') {
                                addPlatform();
                            }
                        };
                    } else if (modalId === 'edit-platform-modal') {
                        button.onclick = function() {
                            console.log('更新平台按钮被点击');
                            if (typeof updatePlatform === 'function') {
                                updatePlatform();
                            }
                        };
                    } else if (modalId === 'add-announcement-modal') {
                        button.onclick = function() {
                            console.log('添加公告按钮被点击');
                            if (typeof addAnnouncement === 'function') {
                                addAnnouncement();
                            }
                        };
                    } else if (modalId === 'edit-announcement-modal') {
                        button.onclick = function() {
                            console.log('更新公告按钮被点击');
                            if (typeof updateAnnouncement === 'function') {
                                updateAnnouncement();
                            }
                        };
                    }
                });
            }
        };

        // 重写closeModal函数
        window.closeModal = function(modalId) {
            console.log('关闭模态框：', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        };

        // 移除window的点击事件
        window.onclick = null;

        // 修复所有现有模态框
        const modals = document.querySelectorAll('.modal');
        modals.forEach(function(modal) {
            // 获取模态框ID
            const modalId = modal.id;
            if (!modalId) return;

            console.log('修复现有模态框:', modalId);

            // 移除原有的点击事件
            modal.onclick = null;

            // 添加新的点击事件，阻止冒泡
            modal.addEventListener('click', function(e) {
                // 如果点击的是模态框本身（空白区域），阻止事件传播
                if (e.target === this) {
                    e.stopPropagation();
                    e.preventDefault();
                    console.log('点击了模态框空白处，阻止关闭');
                    return false;
                }
            });

            // 确保关闭按钮可以正常工作
            const closeButtons = modal.querySelectorAll('.close-btn, .modal-header .bi-x-lg, .modal-header .bi-x');
            closeButtons.forEach(function(button) {
                let parentButton = button.closest('button');
                if (parentButton) {
                    // 移除原有的onclick属性
                    if (parentButton.hasAttribute('onclick')) {
                        parentButton.removeAttribute('onclick');
                    }

                    // 确保点击事件只绑定一次
                    if (!parentButton._hasCloseHandler) {
                        parentButton.addEventListener('click', function(e) {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log('关闭按钮被点击，模态框ID：', modalId);
                            closeModal(modalId);
                        });
                        parentButton._hasCloseHandler = true;
                    }
                }
            });

            // 确保取消按钮可以正常工作
            const cancelButtons = modal.querySelectorAll('.btn-secondary, .modal-footer button');
            cancelButtons.forEach(function(button) {
                // 检查按钮文本是否包含"取消"或"关闭"
                const buttonText = button.textContent.trim().toLowerCase();
                const isCancelButton = buttonText.includes('取消') || buttonText.includes('关闭') ||
                                      button.classList.contains('btn-secondary');

                if (!isCancelButton) return;

                // 移除原有的onclick属性
                if (button.hasAttribute('onclick')) {
                    button.removeAttribute('onclick');
                }

                // 确保点击事件只绑定一次
                if (!button._hasCancelHandler) {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('取消按钮被点击，模态框ID：', modalId);
                        closeModal(modalId);
                    });
                    button._hasCancelHandler = true;
                }
            });

            // 确保确定按钮可以正常工作
            const confirmButtons = modal.querySelectorAll('.btn-primary, .modal-footer button');
            confirmButtons.forEach(function(button) {
                // 检查按钮文本是否包含"确定"或"保存"
                const buttonText = button.textContent.trim().toLowerCase();
                const isConfirmButton = buttonText.includes('确定') || buttonText.includes('保存') ||
                                       button.classList.contains('btn-primary');

                if (!isConfirmButton) return;

                // 获取onclick属性
                const onclickValue = button.getAttribute('onclick');
                if (onclickValue) {
                    // 已有onclick属性，保留原有功能
                    console.log('确定按钮已有onclick属性:', onclickValue);
                    return;
                }

                // 根据模态框ID判断应该调用哪个函数
                if (modalId === 'add-account-modal') {
                    button.onclick = function() {
                        console.log('添加账户按钮被点击');
                        if (typeof addAccount === 'function') {
                            addAccount();
                        }
                    };
                } else if (modalId === 'edit-account-modal') {
                    button.onclick = function() {
                        console.log('更新账户按钮被点击');
                        if (typeof updateAccount === 'function') {
                            updateAccount();
                        }
                    };
                } else if (modalId === 'reset-pwd-modal') {
                    button.onclick = function() {
                        console.log('重置密码按钮被点击');
                        if (typeof resetPassword === 'function') {
                            resetPassword();
                        }
                    };
                } else if (modalId === 'add-platform-modal') {
                    button.onclick = function() {
                        console.log('添加平台按钮被点击');
                        if (typeof addPlatform === 'function') {
                            addPlatform();
                        }
                    };
                } else if (modalId === 'edit-platform-modal') {
                    button.onclick = function() {
                        console.log('更新平台按钮被点击');
                        if (typeof updatePlatform === 'function') {
                            updatePlatform();
                        }
                    };
                } else if (modalId === 'add-announcement-modal') {
                    button.onclick = function() {
                        console.log('添加公告按钮被点击');
                        if (typeof addAnnouncement === 'function') {
                            addAnnouncement();
                        }
                    };
                } else if (modalId === 'edit-announcement-modal') {
                    button.onclick = function() {
                        console.log('更新公告按钮被点击');
                        if (typeof updateAnnouncement === 'function') {
                            updateAnnouncement();
                        }
                    };
                }
            });
        });
    });
})();
