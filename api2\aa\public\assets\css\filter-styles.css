/**
 * 筛选器样式
 * 为状态筛选器提供统一的样式
 */

/* 筛选器容器 */
.filter-container {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 筛选器下拉菜单 */
.filter-select {
    padding: 8px 12px;
    border: 1px solid var(--border-color, #ddd);
    border-radius: var(--border-radius, 4px);
    background-color: var(--white-color, #fff);
    color: var(--dark-color, #333);
    font-size: 0.9rem;
    min-width: 150px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23333' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 12px;
    padding-right: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-select:hover {
    border-color: var(--primary-color, #ff6b95);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color, #ff6b95);
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb, 255, 107, 149), 0.2);
}

/* 筛选器标签 */
.filter-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--dark-color, #333);
    margin-right: 5px;
}

/* 筛选器组 */
.filter-group {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* 筛选器按钮 */
.filter-button {
    padding: 8px 12px;
    border: 1px solid var(--border-color, #ddd);
    border-radius: var(--border-radius, 4px);
    background-color: var(--white-color, #fff);
    color: var(--dark-color, #333);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-button:hover {
    background-color: var(--light-color, #f8f9fa);
    border-color: var(--primary-color, #ff6b95);
}

.filter-button.active {
    background-color: var(--primary-color, #ff6b95);
    color: var(--white-color, #fff);
    border-color: var(--primary-color, #ff6b95);
}

/* 筛选器日期选择器 */
.filter-date {
    padding: 8px 12px;
    border: 1px solid var(--border-color, #ddd);
    border-radius: var(--border-radius, 4px);
    background-color: var(--white-color, #fff);
    color: var(--dark-color, #333);
    font-size: 0.9rem;
    min-width: 150px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-date:hover {
    border-color: var(--primary-color, #ff6b95);
}

.filter-date:focus {
    outline: none;
    border-color: var(--primary-color, #ff6b95);
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb, 255, 107, 149), 0.2);
}

/* 筛选器搜索框 */
.filter-search {
    display: flex;
    align-items: center;
    position: relative;
}

.filter-search input {
    padding: 8px 12px;
    padding-right: 35px;
    border: 1px solid var(--border-color, #ddd);
    border-radius: var(--border-radius, 4px);
    background-color: var(--white-color, #fff);
    color: var(--dark-color, #333);
    font-size: 0.9rem;
    min-width: 200px;
    transition: all 0.3s ease;
}

.filter-search input:hover {
    border-color: var(--primary-color, #ff6b95);
}

.filter-search input:focus {
    outline: none;
    border-color: var(--primary-color, #ff6b95);
    box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb, 255, 107, 149), 0.2);
}

.filter-search button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--gray-color, #6c757d);
    cursor: pointer;
    padding: 5px;
    transition: all 0.3s ease;
}

.filter-search button:hover {
    color: var(--primary-color, #ff6b95);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .filter-container {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .filter-select,
    .filter-date,
    .filter-search input {
        width: 100%;
    }
}
