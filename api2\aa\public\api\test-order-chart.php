<?php
// 测试订单图表API

// 开启会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 设置响应头
header('Content-Type: application/json');

// 检查用户是否已登录
if (!isset($_SESSION['username'])) {
    http_response_code(401);
    echo json_encode(['error' => '未授权访问']);
    exit;
}

try {
    // 包含数据库配置
    $configPath = __DIR__ . '/../../config/config.php';
    $config = require_once($configPath);
    
    if (!$config || !is_array($config)) {
        throw new Exception('配置文件加载失败');
    }
    
    $dbConfig = $config['database'];
    $dsn = "mysql:host={$dbConfig['host']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $prefix = $dbConfig['prefix'];
    $tableName = $prefix . 'order';
    
    // 检查表是否存在
    $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
    $stmt->execute([$tableName]);
    $tableExists = $stmt->fetch() !== false;
    
    if (!$tableExists) {
        echo json_encode([
            'error' => "数据表 {$tableName} 不存在",
            'prefix' => $prefix,
            'full_table_name' => $tableName
        ]);
        exit;
    }
    
    // 检查表结构
    $stmt = $pdo->prepare("DESCRIBE {$tableName}");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // 检查是否有addtime字段
    $hasAddtime = in_array('addtime', $columns);
    
    // 获取表中的数据数量
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM {$tableName}");
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    echo json_encode([
        'success' => true,
        'table_exists' => true,
        'table_name' => $tableName,
        'columns' => $columns,
        'has_addtime' => $hasAddtime,
        'record_count' => $count
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
