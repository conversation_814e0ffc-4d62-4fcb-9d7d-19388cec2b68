/**
 * 情侣头像匹配系统 - 主题初始化脚本
 * 这个脚本在页面加载时立即执行，确保主题设置在DOM加载前应用
 */

// 立即执行函数，确保在页面加载时立即应用主题设置
(function() {
    // 从本地存储中获取主题设置
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
        document.documentElement.setAttribute('data-theme', savedTheme);
    }
    
    // 从本地存储中获取颜色方案
    const savedColor = localStorage.getItem('primary-color');
    if (savedColor) {
        // 设置主色调
        document.documentElement.style.setProperty('--primary-color', savedColor);
        
        // 设置主色调的RGB值（用于透明度）
        const rgbColor = hexToRgb(savedColor);
        document.documentElement.style.setProperty('--primary-color-rgb', rgbColor);
        
        // 根据主色调生成辅助色
        const secondaryColor = adjustColor(savedColor, 30);
        document.documentElement.style.setProperty('--secondary-color', secondaryColor);
        
        // 生成暗色版本（用于悬停效果）
        const darkColor = adjustColor(savedColor, -30);
        document.documentElement.style.setProperty('--primary-dark', darkColor);
        
        console.log('主题颜色已初始化:', savedColor);
    }
    
    // 将十六进制颜色转换为RGB格式
    function hexToRgb(hex) {
        // 移除#号
        hex = hex.replace('#', '');
        
        // 解析RGB值
        const r = parseInt(hex.substring(0, 2), 16);
        const g = parseInt(hex.substring(2, 4), 16);
        const b = parseInt(hex.substring(4, 6), 16);
        
        return `${r}, ${g}, ${b}`;
    }
    
    // 调整颜色
    function adjustColor(color, amount) {
        // 移除#号
        color = color.replace('#', '');
        
        // 将十六进制颜色转换为RGB
        let r = parseInt(color.substring(0, 2), 16);
        let g = parseInt(color.substring(2, 4), 16);
        let b = parseInt(color.substring(4, 6), 16);

        // 调整RGB值
        r = Math.max(0, Math.min(255, r + amount));
        g = Math.max(0, Math.min(255, g + amount));
        b = Math.max(0, Math.min(255, b + amount));

        // 将RGB转换回十六进制
        return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    }
})();
