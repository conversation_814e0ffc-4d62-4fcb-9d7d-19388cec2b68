<?php

/**
 * Migration for creating the admins table
 * This table stores super admin information
 */
class CreateAdminsTable
{
    /**
     * Run the migration
     */
    public function up()
    {
        $sql = "CREATE TABLE IF NOT EXISTS `admins` (
            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            `username` varchar(64) NOT NULL COMMENT 'Admin username',
            `email` varchar(128) NOT NULL COMMENT 'Admin email',
            `password` varchar(64) NOT NULL COMMENT 'Hashed password',
            `is_super_admin` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1=super admin, 0=regular admin',
            `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '0=inactive, 1=active',
            `last_login_time` datetime DEFAULT NULL COMMENT 'Last login time',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            <PERSON><PERSON><PERSON><PERSON> KEY (`id`),
            <PERSON><PERSON>Q<PERSON> KEY `username` (`username`),
            <PERSON><PERSON>Q<PERSON> KEY `email` (`email`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        
        // Insert default super admin
        $sql .= "INSERT INTO `admins` (`username`, `email`, `password`, `is_super_admin`, `status`) VALUES
            ('admin', '<EMAIL>', '" . password_hash('admin123', PASSWORD_BCRYPT) . "', 1, 1);";
        
        return $sql;
    }

    /**
     * Reverse the migration
     */
    public function down()
    {
        return "DROP TABLE IF EXISTS `admins`;";
    }
}
