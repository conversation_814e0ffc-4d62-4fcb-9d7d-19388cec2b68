/**
 * 管理面板图表响应式脚本
 * 确保图表在不同设备上有良好的显示效果
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('图表响应式脚本已加载');

    // 初始化响应式图表
    try {
        initResponsiveCharts();
    } catch (error) {
        console.error('初始化响应式图表失败:', error);
    }
    
    // 窗口大小变化时重新调整图表
    window.addEventListener('resize', function() {
        // 使用防抖动函数避免频繁调用
        clearTimeout(window.chartResizeTimer);
        window.chartResizeTimer = setTimeout(function() {
            try {
                resizeAllCharts();
            } catch (error) {
                console.error('调整图表大小失败:', error);
            }
        }, 250);
    });
});

/**
 * 初始化所有图表的响应式设置
 */
function initResponsiveCharts() {
    console.log('初始化响应式图表');
    
    // 设置Chart.js全局默认值
    if (window.Chart) {
        Chart.defaults.responsive = true;
        Chart.defaults.maintainAspectRatio = false;
        
        // 自定义响应式断点
        const breakpoints = {
            xs: 576,
            sm: 768,
            md: 992,
            lg: 1200
        };
        
        // 为不同屏幕大小设置不同的字体大小和填充
        function getResponsiveOptions() {
            const width = window.innerWidth;
            
            if (width < breakpoints.xs) {
                return {
                    scales: {
                        x: { ticks: { font: { size: 8 } } },
                        y: { ticks: { font: { size: 8 } } }
                    },
                    plugins: {
                        legend: { labels: { font: { size: 10 } } },
                        title: { font: { size: 12 } }
                    }
                };
            } else if (width < breakpoints.sm) {
                return {
                    scales: {
                        x: { ticks: { font: { size: 10 } } },
                        y: { ticks: { font: { size: 10 } } }
                    },
                    plugins: {
                        legend: { labels: { font: { size: 12 } } },
                        title: { font: { size: 14 } }
                    }
                };
            } else {
                return {
                    scales: {
                        x: { ticks: { font: { size: 12 } } },
                        y: { ticks: { font: { size: 12 } } }
                    },
                    plugins: {
                        legend: { labels: { font: { size: 14 } } },
                        title: { font: { size: 16 } }
                    }
                };
            }
        }
        
        // 应用响应式选项到所有图表
        let chartInstances = [];

        // 兼容不同版本的Chart.js
        if (typeof Chart !== 'undefined') {
            if (Chart.instances) {
                chartInstances = Object.values(Chart.instances);
            } else if (Chart.registry && Chart.registry.getAll) {
                // Chart.js v3+
                chartInstances = Chart.registry.getAll('chart') || [];
            } else {
                // 尝试从全局变量获取
                chartInstances = window.chartInstances || [];
            }
        }

        if (chartInstances && chartInstances.length > 0) {
            console.log(`找到 ${chartInstances.length} 个图表实例`);
            
            chartInstances.forEach(chart => {
                const responsiveOptions = getResponsiveOptions();
                
                // 合并选项
                chart.options = {
                    ...chart.options,
                    ...responsiveOptions
                };
                
                // 更新图表
                chart.update();
            });
        } else {
            console.log('未找到图表实例，将在图表初始化时应用响应式设置');
            
            // 监听图表创建事件
            const originalInit = Chart.prototype.initialize;
            Chart.prototype.initialize = function() {
                originalInit.apply(this, arguments);
                
                // 应用响应式选项
                const responsiveOptions = getResponsiveOptions();
                this.options = {
                    ...this.options,
                    ...responsiveOptions
                };
                
                console.log('已为新创建的图表应用响应式设置');
            };
        }
    } else {
        console.warn('Chart.js 未加载，无法设置全局默认值');
    }
    
    // 处理ECharts图表
    if (window.echarts) {
        const echartsInstances = [];
        
        // 查找所有ECharts容器
        document.querySelectorAll('[id$="Chart"]').forEach(container => {
            if (container.tagName.toLowerCase() !== 'canvas') {
                const instance = echarts.getInstanceByDom(container);
                if (instance) {
                    echartsInstances.push(instance);
                }
            }
        });
        
        if (echartsInstances.length > 0) {
            console.log(`找到 ${echartsInstances.length} 个ECharts实例`);
            
            echartsInstances.forEach(chart => {
                // 设置响应式选项
                chart.setOption({
                    grid: {
                        containLabel: true,
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        top: '8%'
                    }
                });
                
                // 使图表响应容器大小变化
                chart.resize();
            });
        }
    } else {
        console.log('ECharts 未加载');
    }
}

/**
 * 调整所有图表大小
 */
function resizeAllCharts() {
    console.log('调整所有图表大小');
    
    // 调整Chart.js图表
    if (window.Chart && Chart.instances) {
        try {
            const chartInstances = Object.values(Chart.instances);
            if (chartInstances && chartInstances.length > 0) {
                chartInstances.forEach(chart => {
                    if (chart && typeof chart.resize === 'function') {
                        chart.resize();
                    }
                });
                console.log(`已调整 ${chartInstances.length} 个Chart.js图表`);
            }
        } catch (error) {
            console.warn('调整Chart.js图表失败:', error);
        }
    }

    // 也尝试调整全局图表实例
    if (window.chartInstances) {
        try {
            const globalCharts = Object.values(window.chartInstances);
            if (globalCharts && globalCharts.length > 0) {
                globalCharts.forEach(chart => {
                    if (chart && typeof chart.resize === 'function') {
                        chart.resize();
                    }
                });
                console.log(`已调整 ${globalCharts.length} 个全局图表`);
            }
        } catch (error) {
            console.warn('调整全局图表失败:', error);
        }
    }
    
    // 调整ECharts图表
    if (window.echarts) {
        document.querySelectorAll('[id$="Chart"]').forEach(container => {
            if (container.tagName.toLowerCase() !== 'canvas') {
                const instance = echarts.getInstanceByDom(container);
                if (instance) {
                    instance.resize();
                }
            }
        });
    }
}
