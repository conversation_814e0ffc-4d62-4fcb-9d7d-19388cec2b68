/**
 * 统计项修复样式
 * 确保统计项的标题和值对齐
 */

.stat-item {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 120px; /* 确保所有统计项高度一致 */
}

.stat-value-with-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex-grow: 1;
    width: 100%;
    height: 100%;
}

.stat-value-with-btn .stat-value {
    margin-bottom: 8px;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
}

.stat-value-with-btn .btn {
    white-space: nowrap;
    font-size: 0.9em;
    padding: 4px 12px;
    margin-top: 2px;
}

/* 确保所有统计项内容垂直居中 */
.stat-grid {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 15px;
    width: 100%;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .stat-item {
        min-height: 100px;
    }
    
    .stat-value-with-btn .btn {
        padding: 3px 10px;
        font-size: 0.8em;
        margin-top: 5px;
    }
}
