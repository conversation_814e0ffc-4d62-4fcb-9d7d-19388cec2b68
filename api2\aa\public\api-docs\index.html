﻿<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>情侣头像匹配系统 - API 文档</title>
    <link href="/assets/vendor/bootstrap/bootstrap.min.css" rel="stylesheet">
    <link href="/assets/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/vendor/highlight/atom-one-dark.min.css">
    <link rel="stylesheet" href="/assets/css/theme.css">
    <style>
        body {
            font-family: "Microsoft YaHei", sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            color: #333;
        }
        
        .loading-spinner {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            animation: fadeIn 0.5s;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .spinner {
            width: 80px;
            height: 80px;
            border: 5px solid rgba(107, 149, 255, 0.3);
            border-radius: 50%;
            border-top-color: #6b95ff;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        h4 {
            margin: 0 0 10px 0;
            color: #6b95ff;
        }
        
        p {
            margin: 0;
            color: #666;
        }
        
        .code-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
            overflow: hidden;
        }
        
        .code-particle {
            position: absolute;
            font-family: monospace;
            font-size: 14px;
            color: rgba(107, 149, 255, 0.5);
            opacity: 0;
            animation: codeFloat 8s linear infinite;
        }
        
        @keyframes codeFloat {
            0% {
                opacity: 0;
                transform: translateY(100vh);
            }
            10% {
                opacity: 0.7;
            }
            90% {
                opacity: 0.7;
            }
            100% {
                opacity: 0;
                transform: translateY(-50px);
            }
        }
    </style>
</head>
<body>
    <div class="code-particles"></div>
    
    <div class="loading-spinner">
        <div class="spinner"></div>
        <h4>正在加载 API 文档...</h4>
        <p>请稍候，系统正在准备文档内容</p>
    </div>

    <script>
        // 创建代码粒子效果
        function createCodeParticles() {
            const container = document.querySelector(".code-particles");
            const particleCount = 20;
            const codeSnippets = [
                "GET /api/v1/avatars",
                "POST /api/v1/users",
                "function getMatch() { }",
                "class AvatarMatcher",
                "return response.json()",
                "const data = await fetch()",
                "if (isMatch) { }",
                "try { } catch (e) { }",
                "200 OK",
                "404 Not Found",
                "Content-Type: application/json",
                "Authorization: Bearer",
                "{ \"success\": true }",
                "new Promise((resolve, reject) => { })"
            ];
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement("div");
                particle.classList.add("code-particle");
                particle.textContent = codeSnippets[Math.floor(Math.random() * codeSnippets.length)];
                particle.style.left = Math.random() * 100 + "vw";
                particle.style.animationDelay = Math.random() * 5 + "s";
                container.appendChild(particle);
            }
        }
        
        // 页面加载完成后自动跳转
        document.addEventListener("DOMContentLoaded", function() {
            // 创建代码粒子效果
            createCodeParticles();
            
            // 添加加载音效
            try {
                const audio = new Audio("data:audio/mpeg;base64,SUQzBAAAAAABEVRYWFgAAAAtAAADY29tbWVudABCaWdTb3VuZEJhbmsuY29tIC8gTGFzb25pY1N0dWRpb3MuY29tAFRYWFgAAAAhAAADZW5naW5lZXIAQmlnU291bmRCYW5rLmNvbQBUWFhYAAAAGwAAA3NvZnR3YXJlAExhdmY1OC43Ni4xMDAAAFRDT04AAAAbAAADZW5jb2RlZCBieQBMYXZmNTguNzYuMTAwAAAAAAAAAAAAAAD/+1AAAAA8YXVkaW8vbXBlZwAAAAAAAAAAAABUSVQyAAAAGQAAAzIwMjMtMDUtMDZUMTc6MjM6MTgAAAAAAAAAAAAA//tQxAADwAABpAAAACAAADSAAAAETEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//tQxBYDwAABpAAAACAAADSAAAAEVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV//tQxBcAAAANIAAAAAA");
                audio.volume = 0.3;
                audio.play();
            } catch (e) {
                console.log("音频播放失败", e);
            }
            
            // 模拟加载过程
            setTimeout(() => {
                window.location.href = "/api-docs";
            }, 2000);
        });
    </script>
</body>
</html>