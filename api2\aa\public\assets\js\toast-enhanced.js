/**
 * Toast增强脚本
 * 进一步改进Toast提示功能，确保正确（绿色）和错误（红色）提示明确区分
 */

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('Toast增强脚本已加载');

    // 重写showToast函数
    window.originalShowToast = window.showToast;

    window.showToast = function(message, type = "success", refresh = true) {
        console.log('显示Toast提示：', message, type, '是否刷新：', refresh);

        // 获取toast元素
        let toast = document.getElementById("toast");

        // 如果toast元素不存在，则创建一个
        if (!toast) {
            toast = document.createElement("div");
            toast.className = "toast";
            toast.id = "toast";

            const toastContent = document.createElement("div");
            toastContent.className = "toast-content";

            const successIcon = document.createElement("i");
            successIcon.className = "bi bi-check-circle-fill toast-icon success";

            const errorIcon = document.createElement("i");
            errorIcon.className = "bi bi-x-circle-fill toast-icon error";

            const infoIcon = document.createElement("i");
            infoIcon.className = "bi bi-info-circle-fill toast-icon info";

            const toastMessage = document.createElement("div");
            toastMessage.className = "toast-message";

            const toastProgress = document.createElement("div");
            toastProgress.className = "toast-progress";

            toastContent.appendChild(successIcon);
            toastContent.appendChild(errorIcon);
            toastContent.appendChild(infoIcon);
            toastContent.appendChild(toastMessage);
            toast.appendChild(toastContent);
            toast.appendChild(toastProgress);

            document.body.appendChild(toast);
        }

        // 获取toast元素中的子元素
        const toastMessage = toast.querySelector(".toast-message");
        const successIcon = toast.querySelector(".toast-icon.success");
        const errorIcon = toast.querySelector(".toast-icon.error");
        const infoIcon = toast.querySelector(".toast-icon.info");

        // 设置消息
        toastMessage.textContent = message;

        // 隐藏所有图标
        if (successIcon) successIcon.style.display = "none";
        if (errorIcon) errorIcon.style.display = "none";
        if (infoIcon) infoIcon.style.display = "none";

        // 移除所有类型类
        toast.classList.remove("success-toast", "error-toast", "info-toast");

        // 根据类型设置样式
        if (type === "success") {
            toast.classList.add("success-toast");
            if (successIcon) successIcon.style.display = "inline-block";
        } else if (type === "error") {
            toast.classList.add("error-toast");
            if (errorIcon) errorIcon.style.display = "inline-block";
        } else if (type === "info") {
            toast.classList.add("info-toast");
            if (infoIcon) infoIcon.style.display = "inline-block";
        }

        // 显示toast
        toast.classList.add("show");

        // 3秒后自动隐藏
        setTimeout(() => {
            toast.classList.remove("show");

            // 如果是成功提示且需要刷新，则在提示消失后刷新页面
            if (type === "success" && refresh) {
                setTimeout(() => {
                    // 刷新页面
                    window.location.reload();
                }, 300); // 延迟300毫秒，确保提示完全消失后再刷新
            }
        }, 3000);
    };
});
