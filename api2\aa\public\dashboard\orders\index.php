﻿<?php
// 包含身份验证检查
include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/auth-check.php');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理 - 情侣头像匹配系统</title>
    <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/header-scripts.php'); ?>
    <link rel="stylesheet" href="/assets/css/filter-styles.css">
<style>

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
        font-family: "Microsoft YaHei", sans-serif;
    }

    body {
        background-color: #f5f7fa;
        color: #333;
        font-size: 14px;
        line-height: 1.5;
        overflow-x: hidden;
    }

    /* 加载动画 */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s;
    }

    .loading-overlay.show {
        opacity: 1;
        visibility: visible;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 107, 149, 0.3);
        border-top: 4px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* 布局样式 */
    .dashboard-container {
        display: flex;
        min-height: 100vh;
    }

    /* 侧边栏样式 */
    .sidebar {
        width: 250px;
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: var(--white-color);
        padding: 20px 0;
        position: fixed;
        height: 100vh;
        overflow-y: auto;
        z-index: 1000;
        box-shadow: 2px 0 10px var(--shadow-color);
        transition: all var(--transition-speed);
    }

    .sidebar.collapsed {
        width: 70px;
    }

    .sidebar-header {
        padding: 0 20px 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        text-align: center;
        margin-bottom: 20px;
    }

    .sidebar-logo {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        margin-bottom: 10px;
        object-fit: cover;
        background-color: rgba(255, 255, 255, 0.2);
    }

    .sidebar-title {
        color: var(--white-color);
        font-size: 1.5rem;
        margin-bottom: 5px;
        font-weight: 600;
    }

    .sidebar-subtitle {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
    }

    .sidebar.collapsed .sidebar-title,
    .sidebar.collapsed .sidebar-subtitle {
        display: none;
    }

    .sidebar-menu {
        list-style: none;
        padding: 0;
    }

    .sidebar-menu li {
        margin-bottom: 5px;
    }

    .sidebar-menu li a {
        color: var(--white-color);
        text-decoration: none;
        display: block;
        padding: 12px 20px;
        transition: all var(--transition-speed);
        border-left: 4px solid transparent;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .sidebar-menu li a:hover,
    .sidebar-menu li a.active {
        background-color: rgba(255, 255, 255, 0.2);
        border-left-color: var(--white-color);
    }

    .sidebar-menu li a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
        font-size: 1.1rem;
    }

    .sidebar.collapsed .sidebar-menu li a span {
        display: none;
    }

    .sidebar.collapsed .sidebar-menu li a i {
        margin-right: 0;
        font-size: 1.3rem;
    }

    /* 主内容区域样式 */
    .main-content {
        flex: 1;
        margin-left: 250px;
        padding: 20px;
        transition: all var(--transition-speed);
    }

    .main-content.expanded {
        margin-left: 70px;
    }

    /* 头部样式 */
    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }

    .menu-toggle {
        background: none;
        border: none;
        color: var(--dark-color);
        font-size: 1.5rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        transition: all var(--transition-speed);
    }

    .menu-toggle:hover {
        background-color: var(--light-color);
    }

    .user-info {
        display: flex;
        align-items: center;
        position: relative;
        cursor: pointer;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: var(--white-color);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 10px;
    }

    .user-name {
        font-weight: 500;
    }

    .dropdown-content {
        position: absolute;
        top: 100%;
        right: 0;
        background-color: var(--white-color);
        min-width: 160px;
        box-shadow: 0 8px 16px 0 var(--shadow-color);
        border-radius: var(--border-radius);
        padding: 10px 0;
        z-index: 1;
        display: none;
        animation: fadeIn 0.3s;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .user-info:hover .dropdown-content {
        display: block;
    }

    .dropdown-content a {
        color: var(--dark-color);
        padding: 10px 20px;
        text-decoration: none;
        display: block;
        transition: all var(--transition-speed);
    }

    .dropdown-content a:hover {
        background-color: var(--light-color);
    }

    .dropdown-content a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }

    /* 操作栏样式 */
    .action-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .search-container {
        display: flex;
        align-items: center;
        background-color: var(--white-color);
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: 0 2px 5px var(--shadow-color);
        width: 300px;
    }

    .search-input {
        flex: 1;
        border: none;
        padding: 10px 15px;
        outline: none;
        font-size: 14px;
    }

    .search-button {
        background-color: var(--primary-color);
        color: var(--white-color);
        border: none;
        padding: 10px 15px;
        cursor: pointer;
        transition: all var(--transition-speed);
    }

    .search-button:hover {
        background-color: #ff4f7e;
    }

    /* 按钮样式 */
    .btn {
        display: inline-block;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        user-select: none;
        border: 1px solid transparent;
        padding: 0.5rem 1rem;
        font-size: 1rem;
        line-height: 1.5;
        border-radius: var(--border-radius);
        transition: all var(--transition-speed);
        cursor: pointer;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .btn:active {
        transform: translateY(0);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }

    .btn-primary {
        color: var(--white-color);
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        border-color: var(--primary-color);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #ff4f7e 0%, #ffa5c0 100%);
        border-color: #ff4f7e;
    }

    .btn-secondary {
        color: var(--white-color);
        background-color: var(--gray-color);
        border-color: var(--gray-color);
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #545b62;
    }

    .btn-danger {
        color: var(--white-color);
        background-color: var(--danger-color);
        border-color: var(--danger-color);
    }

    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }

    .btn-outline-primary {
        color: var(--primary-color);
        background-color: transparent;
        border-color: var(--primary-color);
    }

    .btn-outline-primary:hover {
        color: var(--white-color);
        background-color: var(--primary-color);
        border-color: var(--primary-color);
    }

    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }

    /* 表格样式 */
    .table-container {
        background-color: var(--white-color);
        border-radius: var(--border-radius);
        box-shadow: 0 2px 5px var(--shadow-color);
        overflow: hidden;
        margin-bottom: 20px;
        animation: fadeInUp 0.5s;
    }

    @keyframes fadeInUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .table {
        width: 100%;
        border-collapse: collapse;
    }

    .table th,
    .table td {
        padding: 15px;
        text-align: left;
        border-bottom: 1px solid #eee;
    }

    .table th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
    }

    .table tr:last-child td {
        border-bottom: none;
    }

    .table tr:hover td {
        background-color: #f8f9fa;
    }

    .status {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
    }

    .status.active {
        background-color: rgba(40, 167, 69, 0.1);
        color: var(--success-color);
    }

    .status.inactive {
        background-color: rgba(220, 53, 69, 0.1);
        color: var(--danger-color);
    }

    .actions {
        display: flex;
        gap: 5px;
    }

    .actions button {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all var(--transition-speed);
    }

    .actions .enter {
        background-color: rgba(23, 162, 184, 0.1);
        color: var(--info-color);
    }

    .actions .enter:hover {
        background-color: var(--info-color);
        color: var(--white-color);
    }

    .actions .edit {
        background-color: rgba(255, 193, 7, 0.1);
        color: var(--warning-color);
    }

    .actions .edit:hover {
        background-color: var(--warning-color);
        color: var(--white-color);
    }

    .actions .delete {
        background-color: rgba(220, 53, 69, 0.1);
        color: var(--danger-color);
    }

    .actions .delete:hover {
        background-color: var(--danger-color);
        color: var(--white-color);
    }

    /* 分页样式 */
    .pagination-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;
    }

    .pagination-info {
        color: var(--gray-color);
    }

    .pagination-info span {
        font-weight: 500;
        color: var(--dark-color);
    }

    .pagination-controls {
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .pagination {
        display: flex;
        align-items: center;
    }

    .pagination button {
        width: 32px;
        height: 32px;
        border-radius: 4px;
        border: 1px solid #eee;
        background-color: var(--white-color);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all var(--transition-speed);
        margin: 0 2px;
    }

    .pagination button:hover:not(:disabled) {
        background-color: #f8f9fa;
    }

    .pagination button.active {
        background-color: var(--primary-color);
        color: var(--white-color);
        border-color: var(--primary-color);
    }

    .pagination button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .pagination-jump {
        display: flex;
        align-items: center;
        margin-left: 10px;
    }

    .pagination-jump span {
        margin: 0 5px;
        color: var(--gray-color);
    }

    .pagination-jump input {
        width: 40px;
        height: 32px;
        border-radius: 4px;
        border: 1px solid #eee;
        text-align: center;
        padding: 0 5px;
    }

    .pagination-jump button {
        width: auto;
        padding: 0 10px;
    }

    .pagination-size {
        display: flex;
        align-items: center;
    }

    .pagination-size span {
        margin: 0 5px;
        color: var(--gray-color);
    }

    .pagination-size select {
        height: 32px;
        border-radius: 4px;
        border: 1px solid #eee;
        padding: 0 5px;
    }

    /* 模态框样式 */
    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1001;
        overflow-y: auto;
        padding: 20px;
    }

    .modal-content {
        background-color: var(--white-color);
        margin: 5% auto;
        width: 600px;
        max-width: 90%;
        border-radius: var(--border-radius);
        box-shadow: 0 10px 30px var(--shadow-color);
        animation: slideDown 0.3s;
        overflow: hidden;
    }

    @keyframes slideDown {
        from { transform: translateY(-50px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }

    .modal-header {
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    }

    .modal-header h3 {
        margin: 0;
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--dark-color);
    }

    .close-btn {
        background: none;
        border: none;
        color: var(--gray-color);
        font-size: 1.2rem;
        cursor: pointer;
        transition: all var(--transition-speed);
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }

    .close-btn:hover {
        color: var(--danger-color);
        background-color: rgba(220, 53, 69, 0.1);
    }

    .modal-body {
        padding: 20px;
        max-height: 70vh;
        overflow-y: auto;
    }

    .modal-footer {
        padding: 15px 20px;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        background-color: #f8f9fa;
    }

    /* 表单样式 */
    .form-group {
        margin-bottom: 15px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: var(--border-radius);
        font-size: 14px;
        transition: all var(--transition-speed);
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(255, 107, 149, 0.25);
        outline: none;
    }

    .form-group.checkbox {
        display: flex;
        align-items: center;
    }

    .form-group.checkbox input {
        width: auto;
        margin-right: 10px;
    }

    .required {
        color: var(--danger-color);
    }

    /* 消息提示样式 */
    .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 300px;
        background-color: var(--white-color);
        border-radius: var(--border-radius);
        box-shadow: 0 5px 15px var(--shadow-color);
        overflow: hidden;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transform: translateX(30px);
        transition: all 0.3s;
    }

    .toast.show {
        opacity: 1;
        visibility: visible;
        transform: translateX(0);
    }

    .toast-content {
        display: flex;
        align-items: center;
        padding: 15px;
    }

    .toast-icon {
        font-size: 1.5rem;
        margin-right: 15px;
        display: none;
    }

    .toast-icon.success {
        color: var(--success-color);
    }

    .toast-icon.error {
        color: var(--danger-color);
    }

    .toast-icon.info {
        color: var(--info-color);
    }

    .toast-message {
        flex: 1;
        font-weight: 500;
    }

    .toast-progress {
        height: 3px;
        background-color: var(--primary-color);
        width: 100%;
        animation: progress 3s linear;
    }

    @keyframes progress {
        from { width: 100%; }
        to { width: 0%; }
    }

    /* 响应式样式 */
    @media (max-width: 768px) {
        .sidebar {
            width: 70px;
        }

        .sidebar-title,
        .sidebar-subtitle {
            display: none;
        }

        .sidebar-menu li a span {
            display: none;
        }

        .sidebar-menu li a i {
            margin-right: 0;
            font-size: 1.3rem;
        }

        .main-content {
            margin-left: 70px;
        }

        .action-bar {
            flex-direction: column;
            align-items: stretch;
            gap: 10px;
        }

        .search-container {
            width: 100%;
        }

        .pagination-container {
            flex-direction: column;
            gap: 10px;
        }

        .pagination-controls {
            flex-direction: column;
            gap: 10px;
        }
    }
</style>

</head>
<body>
    <div class="loading-overlay" id="loading-overlay">
        <div class="spinner"></div>
    </div>

    <div class="dashboard-container">
        <!-- 引入通用头部导航 -->
        <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/header-nav.php'); ?>

            <!-- 面包屑导航 -->
            <?php
            // 设置当前页面标题
            $page_title = '订单搜索';
            // 设置当前页面路径
            $page_path = [
                ['title' => '订单管理', 'url' => '/dashboard/orders']
            ];
            // 包含面包屑导航组件
            include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/admin-breadcrumb.php');
            ?>



            <div class="action-bar">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="搜索订单号...">
                    <button class="search-button"><i class="bi bi-search"></i></button>
                </div>
                <div class="filter-container">
                    <div class="filter-group">
                        <span class="filter-label">状态:</span>
                        <select id="status-filter" class="filter-select">
                            <option value="">全部状态</option>
                            <option value="已支付">已支付</option>
                            <option value="待支付">待支付</option>
                            <option value="已退款">已退款</option>
                            <option value="已取消">已取消</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="table-container" id="order-table-container" style="display: none;">
                <table class="table">
                    <thead>
                        <tr>
                            <th>订单号</th>
                            <th>订单类型</th>
                            <th>支付金额</th>
                            <th>状态</th>
                            <th>平台名称</th>
                            <th>下单时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="order-table-body">
                        <!-- 搜索结果将在这里显示 -->
                    </tbody>
                </table>
            </div>

            <div class="empty-state" id="empty-state">
                <div class="empty-state-icon">
                    <i class="bi bi-search"></i>
                </div>
                <h3>请输入订单号进行搜索</h3>
                <p>输入订单号后点击搜索按钮，系统将显示相关订单信息</p>
            </div>

            <div class="pagination-container" id="pagination-container" style="display: none;">
                <div class="pagination-info">
                    显示 <span id="pagination-range">0-0</span> 条，共 <span id="pagination-total">0</span> 条
                </div>
                <div class="pagination-controls">
                    <div class="pagination">
                        <button disabled><i class="bi bi-chevron-left"></i></button>
                        <button class="active">1</button>
                        <button disabled><i class="bi bi-chevron-right"></i></button>
                        <div class="pagination-jump">
                            <span>跳至</span>
                            <input type="number" min="1" max="1" value="1">
                            <span>页</span>
                            <button>GO</button>
                        </div>
                    </div>
                    <div class="pagination-size">
                        <span>每页显示</span>
                        <select>
                            <option value="10" selected>10</option>
                            <option value="20">20</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span>条</span>
                    </div>
                </div>
            </div>

            <style>
                .empty-state {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    padding: 50px 20px;
                    text-align: center;
                    background-color: #f9f9f9;
                    border-radius: var(--border-radius);
                    margin: 20px 0;
                }

                .empty-state-icon {
                    font-size: 3rem;
                    color: var(--gray-color);
                    margin-bottom: 20px;
                }

                .empty-state h3 {
                    font-size: 1.2rem;
                    margin-bottom: 10px;
                    color: var(--dark-color);
                }

                .empty-state p {
                    color: var(--gray-color);
                    max-width: 400px;
                }
            </style>
        </div>
    </div>

    <!-- 订单详情模态框 -->
    <div class="modal" id="order-detail-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>订单详情</h3>
                <button class="close-btn" onclick="closeModal('order-detail-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <div class="order-details">
                    <div class="order-info-section">
                        <h4 class="section-title"><i class="bi bi-info-circle"></i> 基本信息</h4>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <span class="detail-label">订单号：</span>
                                <span class="detail-value" id="detail-order-id">ORD20230115001</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">订单类型：</span>
                                <span class="detail-value" id="detail-order-type">会员充值</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">支付金额：</span>
                                <span class="detail-value highlight" id="detail-order-amount">¥29.90</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">支付方式：</span>
                                <span class="detail-value" id="detail-payment-method">微信支付</span>
                            </div>
                        </div>
                    </div>

                    <div class="order-info-section">
                        <h4 class="section-title"><i class="bi bi-check-circle"></i> 状态信息</h4>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <span class="detail-label">支付状态：</span>
                                <span class="detail-value status-badge" id="detail-order-status">已支付</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">下单时间：</span>
                                <span class="detail-value" id="detail-order-time">2023-01-15 14:30:25</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">支付时间：</span>
                                <span class="detail-value" id="detail-payment-time">2023-01-15 14:31:05</span>
                            </div>
                        </div>
                    </div>

                    <div class="order-info-section">
                        <h4 class="section-title"><i class="bi bi-phone"></i> 平台与用户</h4>
                        <div class="detail-grid">
                            <div class="detail-item">
                                <span class="detail-label">平台名称：</span>
                                <span class="detail-value" id="detail-platform-name">情侣头像匹配小程序1</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">用户信息：</span>
                                <span class="detail-value" id="detail-user-info">用户ID: 12345, 昵称: 小明</span>
                            </div>
                        </div>
                    </div>

                    <div class="order-info-section">
                        <h4 class="section-title"><i class="bi bi-chat-left-text"></i> 备注信息</h4>
                        <div class="detail-item full-width">
                            <span class="detail-value remark" id="detail-remark">无</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('order-detail-modal')">关闭</button>
                <button class="btn btn-info" onclick="viewMatchedAvatars()">查看匹配头像</button>
                <button class="btn btn-primary" onclick="printOrderDetail()">打印订单</button>
            </div>
        </div>
    </div>

    <style>
        /* 订单详情样式 */
        .order-details {
            padding: 0;
        }

        .order-info-section {
            margin-bottom: 20px;
            background-color: #f9f9f9;
            border-radius: var(--border-radius);
            padding: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }

        .section-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .detail-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 10px;
        }

        .detail-item {
            display: flex;
            align-items: center;
            padding: 5px 0;
        }

        .detail-item.full-width {
            grid-column: 1 / -1;
        }

        .detail-label {
            color: var(--gray-color);
            font-weight: 500;
            min-width: 90px;
        }

        .detail-value {
            color: var(--dark-color);
            font-weight: 500;
        }

        .detail-value.highlight {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 1.1rem;
        }

        .status-badge {
            display: inline-block;
            padding: 3px 10px;
            border-radius: 20px;
            background-color: rgba(40, 167, 69, 0.1);
            color: var(--success-color);
        }

        .remark {
            padding: 10px;
            background-color: #fff;
            border-radius: var(--border-radius);
            border: 1px solid #eee;
            min-height: 60px;
            width: 100%;
        }
    </style>

    <!-- 匹配头像模态框 -->
    <div class="modal" id="matched-avatars-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>匹配头像详情</h3>
                <button class="close-btn" onclick="closeModal('matched-avatars-modal')"><i class="bi bi-x-lg"></i></button>
            </div>
            <div class="modal-body">
                <div class="matched-avatars-container">
                    <div class="avatar-pair">
                        <div class="avatar-item">
                            <div class="avatar-label">原始头像</div>
                            <div class="avatar-image">
                                <img src="/assets/images/avatar-sample-1.jpg" alt="原始头像" id="original-avatar">
                            </div>
                        </div>
                        <div class="avatar-item">
                            <div class="avatar-label">匹配头像</div>
                            <div class="avatar-image">
                                <img src="/assets/images/avatar-sample-2.jpg" alt="匹配头像" id="matched-avatar">
                            </div>
                        </div>
                    </div>
                    <div class="avatar-info">
                        <div class="info-item">
                            <span class="info-label">匹配度：</span>
                            <span class="info-value" id="match-score">98%</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">匹配时间：</span>
                            <span class="info-value" id="match-time">2023-01-15 14:32:15</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">匹配算法：</span>
                            <span class="info-value" id="match-algorithm">AI深度学习匹配</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal('matched-avatars-modal')">关闭</button>
                <button class="btn btn-primary" onclick="downloadAvatars()">下载头像</button>
            </div>
        </div>
    </div>

    <style>
        /* 匹配头像样式 */
        .matched-avatars-container {
            padding: 0;
        }

        .avatar-pair {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .avatar-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .avatar-label {
            font-weight: 500;
            margin-bottom: 10px;
            color: var(--dark-color);
        }

        .avatar-image {
            width: 150px;
            height: 150px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .avatar-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .avatar-info {
            background-color: #f9f9f9;
            border-radius: var(--border-radius);
            padding: 15px;
            margin-top: 10px;
        }

        .info-item {
            display: flex;
            margin-bottom: 10px;
        }

        .info-label {
            font-weight: 500;
            color: var(--gray-color);
            min-width: 100px;
        }

        .info-value {
            font-weight: 500;
            color: var(--dark-color);
        }

        #match-score {
            color: var(--success-color);
            font-weight: 600;
        }
    </style>

    <!-- 消息提示 -->
    <div class="toast" id="toast">
        <div class="toast-content">
            <i class="bi bi-check-circle-fill toast-icon success"></i>
            <i class="bi bi-x-circle-fill toast-icon error"></i>
            <i class="bi bi-info-circle-fill toast-icon info"></i>
            <div class="toast-message">操作成功</div>
        </div>
        <div class="toast-progress"></div>
    </div>

    <!-- 引入通用JS文件 -->
    <?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/footer-scripts.php'); ?>
<script>
    // DOM元素
    const searchButton = document.querySelector(".search-button");

    // 初始化
    document.addEventListener("DOMContentLoaded", function() {
        // 显示加载动画
        showLoading();

        // 添加表格行的动画延迟
        const tableRows = document.querySelectorAll(".table tbody tr");
        tableRows.forEach((row, index) => {
            row.style.animationDelay = `${index * 0.1}s`;
        });

        // 添加搜索功能
        if (searchButton) {
            searchButton.addEventListener("click", function() {
                searchOrders();
            });
        }

        // 添加查看按钮点击事件
        setupViewButtons();

        // 隐藏加载动画
        setTimeout(hideLoading, 500);
    });

    // 设置查看按钮点击事件
    function setupViewButtons() {
        const viewButtons = document.querySelectorAll(".actions .view");
        viewButtons.forEach(button => {
            button.addEventListener("click", function() {
                const orderId = this.closest("tr").getAttribute("data-id");
                viewOrderDetail(orderId);
            });
        });
    }

    // 搜索订单
    function searchOrders() {
        const searchInput = document.querySelector(".search-input");
        const searchTerm = searchInput.value.trim();

        if (!searchTerm) {
            showToast("请输入订单号", "info");
            return;
        }

        showLoading();

        // 模拟搜索请求
        setTimeout(() => {
            hideLoading();

            // 显示搜索结果
            const tableContainer = document.getElementById("order-table-container");
            const emptyState = document.getElementById("empty-state");
            const paginationContainer = document.getElementById("pagination-container");
            const tableBody = document.getElementById("order-table-body");

            // 清空表格内容
            tableBody.innerHTML = "";

            // 模拟搜索结果
            if (searchTerm.startsWith("ORD")) {
                // 显示表格和分页
                tableContainer.style.display = "block";
                emptyState.style.display = "none";
                paginationContainer.style.display = "flex";

                // 添加搜索结果
                const orderTypes = ["会员充值", "头像匹配", "VIP升级"];
                const amounts = ["29.90", "9.90", "59.90"];
                const statuses = ["已支付", "已支付", "已退款"];
                const statusClasses = ["active", "active", "inactive"];
                const platforms = ["情侣头像匹配小程序1", "情侣头像匹配小程序2", "情侣头像匹配小程序3"];
                const times = ["2023-01-15 14:30:25", "2023-01-16 09:15:36", "2023-01-17 16:42:18"];

                // 随机选择一个结果
                const randomIndex = Math.floor(Math.random() * 3);

                const row = document.createElement("tr");
                row.setAttribute("data-id", searchTerm);
                row.innerHTML = `
                    <td>${searchTerm}</td>
                    <td>${orderTypes[randomIndex]}</td>
                    <td>${amounts[randomIndex]}</td>
                    <td><span class="status ${statusClasses[randomIndex]}">${statuses[randomIndex]}</span></td>
                    <td>${platforms[randomIndex]}</td>
                    <td>${times[randomIndex]}</td>
                    <td class="actions">
                        <button class="view" onclick="viewOrderDetail('${searchTerm}')"><i class="bi bi-eye"></i></button>
                    </td>
                `;

                tableBody.appendChild(row);

                // 更新分页信息
                document.getElementById("pagination-range").textContent = "1-1";
                document.getElementById("pagination-total").textContent = "1";

                // 添加动画效果
                const tableRows = document.querySelectorAll(".table tbody tr");
                tableRows.forEach((row, index) => {
                    row.style.animationDelay = `${index * 0.1}s`;
                });

                showToast(`已找到订单: ${searchTerm}`, "success");
            } else {
                // 显示空状态
                tableContainer.style.display = "none";
                emptyState.style.display = "flex";
                paginationContainer.style.display = "none";

                showToast("未找到相关订单", "error");
            }
        }, 800);
    }

    // 查看订单详情
    function viewOrderDetail(orderId) {
        showLoading();

        // 模拟API请求
        setTimeout(() => {
            hideLoading();

            // 填充订单详情
            document.getElementById("detail-order-id").textContent = orderId;

            // 根据订单ID设置不同的详情
            if (orderId.endsWith("001")) {
                document.getElementById("detail-order-type").textContent = "会员充值";
                document.getElementById("detail-order-amount").textContent = "¥29.90";
                document.getElementById("detail-payment-method").textContent = "微信支付";
                document.getElementById("detail-order-status").textContent = "已支付";
                document.getElementById("detail-platform-name").textContent = "情侣头像匹配小程序1";
                document.getElementById("detail-user-info").textContent = "用户ID: 12345, 昵称: 小明";
                document.getElementById("detail-order-time").textContent = "2023-01-15 14:30:25";
                document.getElementById("detail-payment-time").textContent = "2023-01-15 14:31:05";
                document.getElementById("detail-remark").textContent = "无";
            } else if (orderId.endsWith("002")) {
                document.getElementById("detail-order-type").textContent = "头像匹配";
                document.getElementById("detail-order-amount").textContent = "¥9.90";
                document.getElementById("detail-payment-method").textContent = "支付宝";
                document.getElementById("detail-order-status").textContent = "已支付";
                document.getElementById("detail-platform-name").textContent = "情侣头像匹配小程序2";
                document.getElementById("detail-user-info").textContent = "用户ID: 67890, 昵称: 小红";
                document.getElementById("detail-order-time").textContent = "2023-01-16 09:15:36";
                document.getElementById("detail-payment-time").textContent = "2023-01-16 09:16:42";
                document.getElementById("detail-remark").textContent = "用户备注：希望匹配可爱风格的头像";
            } else {
                document.getElementById("detail-order-type").textContent = "VIP升级";
                document.getElementById("detail-order-amount").textContent = "¥59.90";
                document.getElementById("detail-payment-method").textContent = "微信支付";
                document.getElementById("detail-order-status").textContent = "已退款";
                document.getElementById("detail-platform-name").textContent = "情侣头像匹配小程序3";
                document.getElementById("detail-user-info").textContent = "用户ID: 24680, 昵称: 小张";
                document.getElementById("detail-order-time").textContent = "2023-01-17 16:42:18";
                document.getElementById("detail-payment-time").textContent = "2023-01-17 16:43:27";
                document.getElementById("detail-remark").textContent = "退款原因：用户申请退款";
            }

            // 打开模态框
            openModal("order-detail-modal");
        }, 500);
    }

    // 打印订单详情
    function printOrderDetail() {
        showLoading();

        // 模拟打印请求
        setTimeout(() => {
            hideLoading();
            showToast("订单已发送到打印队列", "success");
            closeModal("order-detail-modal");
        }, 800);
    }

    // 查看匹配头像
    function viewMatchedAvatars() {
        showLoading();

        // 模拟API请求
        setTimeout(() => {
            hideLoading();

            // 打开匹配头像模态框
            openModal("matched-avatars-modal");
        }, 500);
    }

    // 下载头像
    function downloadAvatars() {
        showLoading();

        // 模拟下载请求
        setTimeout(() => {
            hideLoading();
            showToast("头像已下载到您的设备", "success");
        }, 800);
    }

    // 进入平台
    function enterPlatform(id) {
        showLoading();
        // 模拟API请求
        setTimeout(() => {
            window.location.href = `/dashboard/platforms/detail/${id}`;
        }, 500);
    }

    // 打开编辑模态框
    function openEditModal(id) {
        showLoading();
        // 模拟API请求获取平台数据
        setTimeout(() => {
            hideLoading();

            // 模拟数据
            const platformData = {
                id: id,
                name: `情侣头像匹配小程序${id}`,
                appId: `wx${id.toString().padStart(9, "0")}`,
                appSecret: `secret${id}`,
                accountId: id % 2 === 0 ? 2 : 1,
                description: `平台描述${id}`,
                status: id % 2 === 0 ? 0 : 1
            };

            // 填充表单
            document.getElementById("edit-platform-id").value = platformData.id;
            document.getElementById("edit-platform-name").value = platformData.name;
            document.getElementById("edit-platform-appid").value = platformData.appId;
            document.getElementById("edit-platform-appsecret").value = platformData.appSecret;
            document.getElementById("edit-platform-account").value = platformData.accountId;
            document.getElementById("edit-platform-desc").value = platformData.description;
            document.getElementById("edit-platform-status").checked = platformData.status === 1;

            // 打开模态框
            openModal("edit-platform-modal");
        }, 500);
    }

    // 更新平台
    function updatePlatform() {
        showLoading();
        // 模拟API请求
        setTimeout(() => {
            hideLoading();
            closeModal("edit-platform-modal");
            showToast("平台更新成功");

            // 刷新页面或更新UI
            // location.reload();
        }, 800);
    }

    // 删除平台
    function deletePlatform(id) {
        document.getElementById("delete-platform-id").value = id;
        openModal("confirm-delete-modal");
    }

    // 确认删除
    function confirmDelete() {
        const id = document.getElementById("delete-platform-id").value;
        showLoading();
        // 模拟API请求
        setTimeout(() => {
            hideLoading();
            closeModal("confirm-delete-modal");
            showToast("平台已删除");

            // 刷新页面或更新UI
            // location.reload();
        }, 800);
    }

    // 添加平台
    function addPlatform() {
        showLoading();
        // 模拟API请求
        setTimeout(() => {
            hideLoading();
            closeModal("add-platform-modal");
            showToast("平台添加成功");

            // 刷新页面或更新UI
            // location.reload();
        }, 800);
    }
</script>

<script>
    // 查看订单详情
    function viewOrderDetail(orderId) {
        showLoading();

        // 模拟API请求
        setTimeout(() => {
            hideLoading();

            // 根据订单ID设置不同的详情内容
            let orderDetail = {};

            if (orderId === 'ORD20230115001') {
                orderDetail = {
                    id: 'ORD20230115001',
                    type: '会员充值',
                    amount: '29.90',
                    paymentMethod: '微信支付',
                    status: '已支付',
                    platformName: '情侣头像匹配小程序1',
                    userInfo: '用户ID: 12345, 昵称: 小明',
                    orderTime: '2023-01-15 14:30:25',
                    paymentTime: '2023-01-15 14:31:05',
                    remark: '无'
                };
            } else if (orderId === 'ORD20230116002') {
                orderDetail = {
                    id: 'ORD20230116002',
                    type: '头像匹配',
                    amount: '9.90',
                    paymentMethod: '支付宝',
                    status: '已支付',
                    platformName: '情侣头像匹配小程序2',
                    userInfo: '用户ID: 23456, 昵称: 小红',
                    orderTime: '2023-01-16 09:15:36',
                    paymentTime: '2023-01-16 09:16:12',
                    remark: '无'
                };
            } else if (orderId === 'ORD20230117003') {
                orderDetail = {
                    id: 'ORD20230117003',
                    type: '会员充值',
                    amount: '59.90',
                    paymentMethod: '微信支付',
                    status: '已退款',
                    platformName: '情侣头像匹配小程序1',
                    userInfo: '用户ID: 34567, 昵称: 小张',
                    orderTime: '2023-01-17 16:42:18',
                    paymentTime: '2023-01-17 16:43:05',
                    remark: '用户申请退款，原因：购买错误'
                };
            }

            // 填充订单详情
            document.getElementById('detail-order-id').textContent = orderDetail.id;
            document.getElementById('detail-order-type').textContent = orderDetail.type;
            document.getElementById('detail-order-amount').textContent = orderDetail.amount;
            document.getElementById('detail-payment-method').textContent = orderDetail.paymentMethod;
            document.getElementById('detail-order-status').textContent = orderDetail.status;
            document.getElementById('detail-platform-name').textContent = orderDetail.platformName;
            document.getElementById('detail-user-info').textContent = orderDetail.userInfo;
            document.getElementById('detail-order-time').textContent = orderDetail.orderTime;
            document.getElementById('detail-payment-time').textContent = orderDetail.paymentTime;
            document.getElementById('detail-remark').textContent = orderDetail.remark;

            // 打开模态框
            openModal('order-detail-modal');
        }, 500);
    }

</script>
<!-- 网站底部 -->
<?php include_once($_SERVER['DOCUMENT_ROOT'] . '/includes/site-footer.php'); ?>

<script src="/assets/js/sidebar-toggle-fix.js"></script>
<script src="/assets/js/responsive-layout.js"></script>
</body>
</html>




