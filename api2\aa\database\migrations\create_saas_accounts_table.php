<?php

/**
 * Migration for creating the saas_accounts table
 * This table stores SAAS sub-admin account information
 */
class CreateSaasAccountsTable
{
    /**
     * Run the migration
     */
    public function up()
    {
        $sql = "CREATE TABLE IF NOT EXISTS `saas_accounts` (
            `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            `super_admin_id` bigint(20) UNSIGNED NOT NULL COMMENT 'Reference to admins table',
            `account_name` varchar(64) NOT NULL COMMENT 'Account name (unique)',
            `email` varchar(128) NOT NULL COMMENT 'Account email (unique)',
            `password` varchar(64) NOT NULL COMMENT 'Hashed password',
            `phone` varchar(20) DEFAULT NULL COMMENT 'Contact phone number',
            `organization` varchar(100) DEFAULT NULL COMMENT 'Organization name',
            `max_platforms` int(11) NOT NULL DEFAULT 0 COMMENT 'Maximum number of platforms (0=unlimited)',
            `expire_time` datetime DEFAULT NULL COMMENT 'Account expiration time (NULL=never expires)',
            `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '0=inactive, 1=active',
            `last_login_time` datetime DEFAULT NULL COMMENT 'Last login time',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `account_name` (`account_name`),
            UNIQUE KEY `email` (`email`),
            KEY `super_admin_id` (`super_admin_id`),
            CONSTRAINT `saas_accounts_super_admin_id_foreign` FOREIGN KEY (`super_admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
        
        return $sql;
    }

    /**
     * Reverse the migration
     */
    public function down()
    {
        return "DROP TABLE IF EXISTS `saas_accounts`;";
    }
}
