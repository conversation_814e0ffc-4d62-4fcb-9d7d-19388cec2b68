/**
 * 模态框修复脚本
 * 确保模态框正确显示
 */

document.addEventListener('DOMContentLoaded', function() {
    // 修复续费按钮
    const renewButtons = document.querySelectorAll('button[onclick="openRenewModal()"]');
    renewButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const renewServiceModal = document.getElementById('renew-service-modal');
            if (renewServiceModal) {
                renewServiceModal.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }
        });
    });
    
    // 修复历史版本按钮
    const versionButtons = document.querySelectorAll('a[onclick="showVersionHistory()"]');
    versionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const versionHistoryModal = document.getElementById('version-history-modal');
            if (versionHistoryModal) {
                versionHistoryModal.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }
        });
    });
    
    // 修复查看全部按钮
    const announcementButtons = document.querySelectorAll('a[onclick="showAllAnnouncements()"]');
    announcementButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const allAnnouncementsModal = document.getElementById('all-announcements-modal');
            if (allAnnouncementsModal) {
                allAnnouncementsModal.style.display = 'block';
                document.body.style.overflow = 'hidden';
            }
        });
    });
    
    // 修复关闭按钮
    const closeButtons = document.querySelectorAll('.close-btn');
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        });
    });
    
    // 点击模态框外部关闭
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                this.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        });
    });
});
