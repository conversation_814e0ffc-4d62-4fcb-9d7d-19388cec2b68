/**
 * Toast增强样式
 * 进一步改进Toast提示样式，确保正确（绿色）和错误（红色）提示明确区分
 */

/* 消息提示基础样式 */
.toast {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    width: 300px !important;
    border-radius: 8px !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
    overflow: hidden !important;
    z-index: 9999 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transform: translateX(30px) !important;
    transition: all 0.3s ease !important;
}

.toast.show {
    opacity: 1 !important;
    visibility: visible !important;
    transform: translateX(0) !important;
}

/* 隐藏所有图标 */
.toast .toast-icon {
    display: none !important;
}

/* 成功提示样式 */
.toast.success-toast .toast-content {
    background-color: #ffffff !important; /* 白色背景 */
    border-left: 4px solid #4caf50 !important; /* 绿色边框 */
}

.toast.success-toast .toast-icon.success {
    color: #4caf50 !important; /* 绿色图标 */
    display: inline-block !important;
}

.toast.success-toast .toast-icon.error,
.toast.success-toast .toast-icon.info {
    display: none !important;
}

.toast.success-toast .toast-message {
    color: #333333 !important; /* 深灰色文字 */
}

.toast.success-toast .toast-progress {
    background-color: #ff6b95 !important; /* 粉色进度条 */
    width: 100% !important;
    animation: toast-progress 3s linear forwards !important;
}

/* 错误提示样式 */
.toast.error-toast .toast-content {
    background-color: #ffffff !important; /* 白色背景 */
    border-left: 4px solid #f44336 !important; /* 红色边框 */
}

.toast.error-toast .toast-icon.error {
    color: #f44336 !important; /* 红色图标 */
    display: inline-block !important;
}

.toast.error-toast .toast-icon.success,
.toast.error-toast .toast-icon.info {
    display: none !important;
}

.toast.error-toast .toast-message {
    color: #333333 !important; /* 深灰色文字 */
}

.toast.error-toast .toast-progress {
    background-color: #ff6b95 !important; /* 粉色进度条 */
    width: 100% !important;
    animation: toast-progress 3s linear forwards !important;
}

/* 信息提示样式 */
.toast.info-toast .toast-content {
    background-color: #ffffff !important; /* 白色背景 */
    border-left: 4px solid #2196f3 !important; /* 蓝色边框 */
}

.toast.info-toast .toast-icon.info {
    color: #2196f3 !important; /* 蓝色图标 */
    display: inline-block !important;
}

.toast.info-toast .toast-icon.success,
.toast.info-toast .toast-icon.error {
    display: none !important;
}

.toast.info-toast .toast-message {
    color: #333333 !important; /* 深灰色文字 */
}

.toast.info-toast .toast-progress {
    background-color: #ff6b95 !important; /* 粉色进度条 */
    width: 100% !important;
    animation: toast-progress 3s linear forwards !important;
}

/* 内容样式 */
.toast-content {
    display: flex !important;
    align-items: center !important;
    padding: 15px !important;
    background-color: #ffffff !important; /* 白色背景 */
}

.toast-icon {
    font-size: 1.5rem !important;
    margin-right: 15px !important;
    display: none !important;
}

.toast-message {
    flex: 1 !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
}

.toast-progress {
    height: 4px !important;
    width: 100% !important;
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    transform-origin: left !important;
}

/* 所有提示的进度条 */
.toast .toast-progress {
    animation: toast-progress 3s linear forwards !important;
}

/* 提示的进度条动画 */
@keyframes toast-progress {
    0% {
        transform: scaleX(1);
        background-color: #ff6b95;
    }
    50% {
        background-color: #ff4f7e;
    }
    100% {
        transform: scaleX(0);
        background-color: #ff6b95;
    }
}
