<?php
// 简化的图表测试API

// 开启会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 设置响应头
header('Content-Type: application/json');

// 检查用户是否已登录
if (!isset($_SESSION['username'])) {
    http_response_code(401);
    echo json_encode(['error' => '未授权访问']);
    exit;
}

// 直接返回测试数据
$testData = [
    'labels' => ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
    'datasets' => [
        [
            'data' => [0, 0, 0, 0, 4, 5, 0, 0, 0, 0, 0, 0],
            'label' => '订单数量',
            'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
            'borderColor' => 'rgba(54, 162, 235, 1)',
            'borderWidth' => 1
        ]
    ]
];

echo json_encode($testData);
?>
