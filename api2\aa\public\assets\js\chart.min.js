// 简单的 Chart.js 替代方案
class Chart {
    constructor(ctx, config) {
        this.ctx = ctx;
        this.config = config;
        this.render();
    }

    render() {
        // 获取 canvas 元素
        const canvas = this.ctx.canvas;
        const ctx = this.ctx;
        
        // 清除画布
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // 设置画布大小
        canvas.width = canvas.parentNode.clientWidth;
        canvas.height = 300;
        
        // 绘制背景
        ctx.fillStyle = '#f8f9fa';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // 绘制标题
        ctx.fillStyle = '#333';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('图表数据已加载 (模拟)', canvas.width / 2, 30);
        
        // 绘制图例
        if (this.config.data && this.config.data.datasets) {
            const datasets = this.config.data.datasets;
            const legendY = 60;
            const legendX = 20;
            const legendSpacing = 100;
            
            datasets.forEach((dataset, index) => {
                const x = legendX + index * legendSpacing;
                
                // 绘制图例颜色块
                ctx.fillStyle = dataset.borderColor || '#000';
                ctx.fillRect(x, legendY, 15, 15);
                
                // 绘制图例文字
                ctx.fillStyle = '#333';
                ctx.textAlign = 'left';
                ctx.fillText(dataset.label, x + 20, legendY + 12);
            });
            
            // 绘制简单的图表线条
            const chartHeight = canvas.height - 100;
            const chartWidth = canvas.width - 40;
            const chartX = 20;
            const chartY = 100;
            
            // 绘制坐标轴
            ctx.beginPath();
            ctx.strokeStyle = '#ccc';
            ctx.moveTo(chartX, chartY);
            ctx.lineTo(chartX, chartY + chartHeight);
            ctx.lineTo(chartX + chartWidth, chartY + chartHeight);
            ctx.stroke();
            
            // 绘制数据线
            datasets.forEach((dataset, datasetIndex) => {
                const data = dataset.data;
                if (!data || data.length === 0) return;
                
                ctx.beginPath();
                ctx.strokeStyle = dataset.borderColor || '#000';
                ctx.lineWidth = 2;
                
                const pointWidth = chartWidth / (data.length - 1);
                
                data.forEach((value, index) => {
                    // 将数据值映射到图表高度
                    const maxValue = Math.max(...data) || 1;
                    const scaledValue = value / maxValue * chartHeight;
                    const x = chartX + index * pointWidth;
                    const y = chartY + chartHeight - scaledValue;
                    
                    if (index === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                });
                
                ctx.stroke();
                
                // 绘制填充区域
                if (dataset.fill) {
                    ctx.lineTo(chartX + chartWidth, chartY + chartHeight);
                    ctx.lineTo(chartX, chartY + chartHeight);
                    ctx.closePath();
                    ctx.fillStyle = dataset.backgroundColor || 'rgba(0,0,0,0.1)';
                    ctx.fill();
                }
            });
            
            // 绘制 X 轴标签
            if (this.config.data.labels) {
                const labels = this.config.data.labels;
                const labelSpacing = chartWidth / (labels.length - 1);
                
                labels.forEach((label, index) => {
                    const x = chartX + index * labelSpacing;
                    const y = chartY + chartHeight + 20;
                    
                    ctx.fillStyle = '#666';
                    ctx.textAlign = 'center';
                    ctx.fillText(label, x, y);
                });
            }
        }
    }
    
    // 模拟更新方法
    update() {
        this.render();
    }
}

// 全局对象
window.Chart = Chart;
